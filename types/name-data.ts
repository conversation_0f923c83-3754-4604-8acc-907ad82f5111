export interface NameData {
  // Core name information
  name_en: string
  name_native: string
  gender: string
  religion: string
  language: string
  meaning_en: string
  starting_letter: string

  // Optional fields for global religion files
  meaning_native: string
  pronunciation: string
  origin: string

  // Hindu-specific fields (only for Hindu religion)
  rashi?: string
  rashi_native?: string
  nakshatra?: string
  nakshatra_native?: string

  // Popularity and ranking data (for USA country files)
  popularity_rank?: number
  count?: number

  // Historical data (for USA country files)
  historical_data?: {
    [year: string]: number
  }

  // Statistics (for USA country files)
  total_occurrences?: number
  peak_year?: string
  peak_count?: number
  first_year?: string
  last_year?: string
  years_active?: number

  // Trending and modern popularity data (optional)
  popularity_change?: string // e.g., "+15%", "-3%", "new"
  popularity_trend?: string // alias for popularity_change
  year_2025_rank?: number
  year_2024_rank?: number
  trending_status?: "rising" | "falling" | "stable" | "new" | "trending"
  search_volume?: number
  regional_popularity?: {
    [region: string]: number // e.g., {"northeast": 1, "south": 5}
  }

  // Additional optional properties
  cultural_significance?: string
  usage_stats?: string
  famous_people?: Array<{
    name: string
    profession: string
    description?: string
  }>
  variations?: string[]
}

// Interface for API responses
export interface TopNamesResponse {
  success: boolean
  data: {
    country: string
    year: number
    boys: NameData[]
    girls: NameData[]
    trending: {
      rising: NameData[]
      falling: NameData[]
      new: NameData[]
    }
    regional?: {
      [region: string]: {
        boys: NameData[]
        girls: NameData[]
      }
    }
  }
  metadata: {
    total_boys: number
    total_girls: number
    last_updated: string
    data_source: string
  }
}

// Interface for trending names data
export interface TrendingNamesData {
  country: string
  year: number
  top_boy: string
  top_girl: string
  growth_percentage: string
  notable_trends: string[]
  total_rising?: number
  total_falling?: number
}

// Type for historical data entries
export interface HistoricalDataEntry {
  year: string
  count: number
}

// Type for regional popularity data
export interface RegionalPopularity {
  northeast?: number
  south?: number
  midwest?: number
  west?: number
  [region: string]: number | undefined
}

// Type for famous people associated with a name
export interface FamousPerson {
  name: string
  profession: string
  description?: string
}
