"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useTranslation } from "@/hooks/use-translation"
import { BookOpen, Globe, Heart, Star, TrendingUp } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface RelatedNamesProps {
  currentName: string
  currentGender: string
  currentLanguage: string
  currentCountry?: string
  currentReligion?: string
}

export default function RelatedNames({
  currentName,
  currentGender,
  currentLanguage,
  currentCountry,
  currentReligion
}: RelatedNamesProps) {
  const router = useRouter()
  const { t } = useTranslation()

  // Generate related names based on current context
  const getRelatedNames = () => {
    const related = []

    // Same gender, different language
    if (currentLanguage === 'english') {
      related.push(
        { name: 'Emma', meaning: 'Universal', language: 'German', url: '/germany/german-girl-names/' },
        { name: '<PERSON>', meaning: 'Wisdom', language: 'Greek', url: '/usa/english-girl-names/' },
        { name: '<PERSON>', meaning: 'Pledged to God', language: 'Italian', url: '/usa/english-girl-names/' }
      )
    } else if (currentLanguage === 'hindi') {
      related.push(
        { name: 'Aarav', meaning: 'Peaceful', language: 'Sanskrit', url: '/india/hindi-boy-names/' },
        { name: 'Vivaan', meaning: 'Full of life', language: 'Sanskrit', url: '/india/hindi-boy-names/' },
        { name: 'Arjun', meaning: 'Bright, white', language: 'Sanskrit', url: '/india/hindi-boy-names/' }
      )
    }

    // Same meaning theme
    if (currentGender === 'male') {
      related.push(
        { name: 'Liam', meaning: 'Strong-willed warrior', language: 'Irish', url: '/usa/english-boy-names/' },
        { name: 'Noah', meaning: 'Rest, comfort', language: 'Hebrew', url: '/usa/english-boy-names/' },
        { name: 'Oliver', meaning: 'Olive tree', language: 'Latin', url: '/uk/english-boy-names/' }
      )
    } else {
      related.push(
        { name: 'Olivia', meaning: 'Olive tree', language: 'Latin', url: '/usa/english-girl-names/' },
        { name: 'Ava', meaning: 'Bird', language: 'Latin', url: '/usa/english-girl-names/' },
        { name: 'Charlotte', meaning: 'Free man', language: 'French', url: '/uk/english-girl-names/' }
      )
    }

    return related.slice(0, 6)
  }

  const relatedNames = getRelatedNames()

  // Generate structured data for related names
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": `Related Baby Names to ${currentName}`,
    "description": `Discover baby names similar to ${currentName} in meaning, origin, or popularity`,
    "numberOfItems": relatedNames.length,
    "itemListElement": relatedNames.map((name, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Thing",
        "name": name.name,
        "description": `${name.name} - ${name.meaning}`,
        "url": `https://www.babynamediaries.com${name.url}`
      }
    }))
  }

  return (
    <section className="py-8 md:py-12 bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="container mx-auto px-4">
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />

        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Names Similar to {currentName}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover beautiful baby names with similar meanings, origins, or popularity to {currentName}
          </p>
        </div>

        {/* Related Names Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {relatedNames.map((name) => (
            <Card key={name.name} className="group hover:shadow-lg transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                    <Link href={name.url} className="hover:underline">
                      {name.name}
                    </Link>
                  </CardTitle>
                  <Badge variant="outline" className="text-xs">
                    {name.language}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  <BookOpen className="h-4 w-4 inline mr-1" />
                  {name.meaning}
                </p>
                <Link
                  href={name.url}
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
                >
                  <Globe className="h-4 w-4 mr-1" />
                  Explore {name.language} Names
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Internal Linking Section */}
        <div className="bg-white rounded-lg p-6 md:p-8 shadow-sm">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Explore More Baby Names
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Trending Names */}
            <Link href="/trending-names/" className="group">
              <div className="bg-gradient-to-br from-pink-50 to-rose-100 p-4 rounded-lg text-center hover:shadow-md transition-all duration-300">
                <TrendingUp className="h-8 w-8 mx-auto mb-2 text-pink-600" />
                <h4 className="font-semibold text-gray-900 group-hover:text-pink-600 transition-colors">
                  Trending Names
                </h4>
                <p className="text-sm text-gray-600">Latest popular names</p>
              </div>
            </Link>

            {/* Popular Names 2025 */}
            <Link href="/popular-names-2025/" className="group">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-4 rounded-lg text-center hover:shadow-md transition-all duration-300">
                <Star className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                  Popular 2025
                </h4>
                <p className="text-sm text-gray-600">Top names this year</p>
              </div>
            </Link>

            {/* Unique Names */}
            <Link href="/unique-baby-names-2025/" className="group">
              <div className="bg-gradient-to-br from-purple-50 to-violet-100 p-4 rounded-lg text-center hover:shadow-md transition-all duration-300">
                <Heart className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <h4 className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
                  Unique Names
                </h4>
                <p className="text-sm text-gray-600">Stand out names</p>
              </div>
            </Link>

            {/* Cultural Names */}
            <Link href="/religions/christian-boy-names/" className="group">
              <div className="bg-gradient-to-br from-green-50 to-emerald-100 p-4 rounded-lg text-center hover:shadow-md transition-all duration-300">
                <Globe className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h4 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                  Cultural Names
                </h4>
                <p className="text-sm text-gray-600">Religious & cultural</p>
              </div>
            </Link>
          </div>
        </div>

        {/* Country-Specific Links */}
        {currentCountry && (
          <div className="mt-8 bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
              More Names from {currentCountry.toUpperCase()}
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              <Link href={`/${currentCountry}/english-boy-names/`}>
                <Badge variant="secondary" className="px-4 py-2 hover:bg-blue-100 transition-colors cursor-pointer">
                  Boy Names
                </Badge>
              </Link>
              <Link href={`/${currentCountry}/english-girl-names/`}>
                <Badge variant="secondary" className="px-4 py-2 hover:bg-purple-100 transition-colors cursor-pointer">
                  Girl Names
                </Badge>
              </Link>
              <Link href={`/${currentCountry}/`}>
                <Badge variant="outline" className="px-4 py-2 hover:bg-gray-100 transition-colors cursor-pointer">
                  All Names
                </Badge>
              </Link>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
