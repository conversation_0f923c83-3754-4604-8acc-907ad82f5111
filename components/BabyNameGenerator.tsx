"use client"
import { FAQSection } from "@/components/faq-section";
import SEOStructuredData from "@/components/seo-structured-data";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
    ArrowRight,
    Baby,
    BookOpen,
    CheckCircle,
    Gift,
    Globe,
    Heart,
    Loader2,
    RefreshCw,
    Sparkles,
    Star,
    TrendingUp,
    Users,
    Zap
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

interface GeneratedName {
    name: string
    nativeName: string
    meaning: string
    origin: string
    gender: string
    language: string
    religion: string
    pronunciation: string
    startingLetter: string
    score: number
    styles: string[]
    popularityRank?: number
    trendingStatus?: string
    searchVolume?: number
}

interface GeneratorForm {
    gender: string
    country: string
    religion: string
    meaning: string
    style: string
}

export default function BabyNameGeneratorPage() {
    const [generatedNames, setGeneratedNames] = useState<GeneratedName[]>([])
    const [favorites, setFavorites] = useState<string[]>([])
    const [isGenerating, setIsGenerating] = useState(false)
    const [currentStep, setCurrentStep] = useState(1)
    const [formData, setFormData] = useState<GeneratorForm>({
        gender: "",
        country: "",
        religion: "",
        meaning: "",
        style: ""
    })

    const [suggestions, setSuggestions] = useState<string[]>([])

    // Load favorites from localStorage
    useEffect(() => {
        const savedFavorites = localStorage.getItem("babyNameFavorites")
        if (savedFavorites) {
            setFavorites(JSON.parse(savedFavorites))
        }
    }, [])

    const generateNames = async () => {
        setIsGenerating(true)
        try {
            const params = new URLSearchParams()

            // Add form data to params
            if (formData.gender) params.append("gender", formData.gender)
            if (formData.country) params.append("country", formData.country)
            if (formData.religion) params.append("religion", formData.religion)
            if (formData.meaning) params.append("meaning", formData.meaning)
            if (formData.style) params.append("style", formData.style)

            params.append("limit", "12")
            params.append("includeDetails", "true")

            const response = await fetch(`/api/names/generator?${params.toString()}`)
            const data = await response.json()

            if (data.success) {
                setGeneratedNames(data.data.names)
                setSuggestions(data.data.suggestions || [])
                setCurrentStep(3) // Show results
            } else {
                console.error("Failed to generate names:", data.error)
            }
        } catch (error) {
            console.error("Error generating names:", error)
        } finally {
            setIsGenerating(false)
        }
    }

    const toggleFavorite = (name: string) => {
        const newFavorites = favorites.includes(name)
            ? favorites.filter(fav => fav !== name)
            : [...favorites, name]

        setFavorites(newFavorites)
        localStorage.setItem("babyNameFavorites", JSON.stringify(newFavorites))
    }

    const resetForm = () => {
        setFormData({
            gender: "",
            country: "",
            religion: "",
            meaning: "",
            style: ""
        })
        setCurrentStep(1)
        setGeneratedNames([])
    }

    const nextStep = () => {
        if (currentStep < 2) {
            setCurrentStep(currentStep + 1)
        }
    }

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1)
        }
    }

    const faqs = [
        {
            question: "How does the baby name generator work?",
            answer: "Our AI-powered generator analyzes your preferences including gender, country, religion, meaning, and style to suggest personalized names from our database of 25,000+ names. It uses a scoring system to rank names based on how well they match your criteria."
        },
        {
            question: "Can I get names from specific cultures or religions?",
            answer: "Yes! You can select your preferred country and religion to get culturally appropriate names that match your heritage and preferences."
        },
        {
            question: "What do the different style options mean?",
            answer: "Traditional: Classic names with historical significance. Modern: Contemporary and trendy names. Nature: Names inspired by natural elements. Biblical: Names with religious or spiritual meanings."
        },
        {
            question: "How accurate are the name meanings and origins?",
            answer: "All name meanings and origins are verified by linguistic experts and cultural consultants. We source information from authoritative dictionaries, historical texts, and native language experts."
        },
        {
            question: "Can I save my favorite generated names?",
            answer: "Yes! Click the heart icon next to any name to add it to your favorites. Your favorites are saved locally and will persist between sessions."
        },
        {
            question: "What if I don't get enough suggestions?",
            answer: "Try different meaning keywords or style preferences. The generator works best when you provide meaningful preferences that reflect your values and aspirations."
        }
    ]

    return (
        <>
            <SEOStructuredData
                names={[]}
                pageType="tool"
                title="AI Baby Name Generator 2025 - Personalized Name Suggestions"
                description="Generate perfect baby names with our AI-powered tool. Get personalized suggestions based on meaning, origin, style, and cultural preferences from 25,000+ names."
                url="/baby-name-generator"
            />

            <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
                {/* Hero Section */}
                <section className="bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 text-white py-12 md:py-20">
                    <div className="container mx-auto px-4 text-center">
                        <div className="flex items-center justify-center mb-6">
                            <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
                                <Sparkles className="h-8 w-8 md:h-12 md:w-12" />
                            </div>
                        </div>
                        <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6">
                            AI Baby Name Generator
                        </h1>
                        <p className="text-lg md:text-xl lg:text-2xl mb-8 text-purple-100 max-w-3xl mx-auto">
                            Discover the perfect name for your baby with our intelligent generator.
                            Get personalized suggestions from 25,000+ names worldwide.
                        </p>
                        <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 px-2">
                            <Badge variant="secondary" className="text-purple-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                ✨ AI-Powered
                            </Badge>
                            <Badge variant="secondary" className="text-purple-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                🌍 25,000+ Names
                            </Badge>
                            <Badge variant="secondary" className="text-purple-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                💝 Personalized
                            </Badge>
                            <Badge variant="secondary" className="text-purple-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                🎁 Free to Use
                            </Badge>
                        </div>
                    </div>
                </section>

                {/* Main Generator Section */}
                <section className="py-12 md:py-16">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            {/* Step Indicator */}
                            <div className="flex justify-center mb-8 overflow-hidden">
                                <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4 w-full max-w-md sm:max-w-none">
                                    <div className={`flex items-center ${currentStep >= 1 ? 'text-purple-600' : 'text-gray-400'}`}>
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                                            {currentStep > 1 ? <CheckCircle className="h-5 w-5" /> : '1'}
                                        </div>
                                        <span className="ml-2 font-medium text-sm sm:text-base">Tell Us About Your Baby</span>
                                    </div>
                                    <ArrowRight className="h-5 w-5 text-gray-400 hidden sm:block" />
                                    <div className={`flex items-center ${currentStep >= 2 ? 'text-purple-600' : 'text-gray-400'}`}>
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                                            {currentStep > 2 ? <CheckCircle className="h-5 w-5" /> : '2'}
                                        </div>
                                        <span className="ml-2 font-medium text-sm sm:text-base">Add Preferences</span>
                                    </div>
                                    <ArrowRight className="h-5 w-5 text-gray-400 hidden sm:block" />
                                    <div className={`flex items-center ${currentStep >= 3 ? 'text-purple-600' : 'text-gray-400'}`}>
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 3 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                                            3
                                        </div>
                                        <span className="ml-2 font-medium text-sm sm:text-base">Get Names</span>
                                    </div>
                                </div>
                            </div>

                            {/* Step 1: Basic Info */}
                            {currentStep === 1 && (
                                <Card className="shadow-xl border-0">
                                    <CardHeader className="text-center pb-6">
                                        <div className="flex justify-center mb-4">
                                            <Baby className="h-12 w-12 text-purple-600" />
                                        </div>
                                        <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900">
                                            Tell Us About Your Baby
                                        </CardTitle>
                                        <p className="text-gray-600 text-lg">
                                            Help us understand your preferences to generate the perfect names
                                        </p>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <Label htmlFor="gender" className="text-base font-medium">Gender</Label>
                                                <Select
                                                    value={formData.gender}
                                                    onValueChange={(value) => setFormData(prev => ({ ...prev, gender: value }))}
                                                >
                                                    <SelectTrigger className="mt-2 h-12">
                                                        <SelectValue placeholder="Select gender" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="boy">👶 Boy</SelectItem>
                                                        <SelectItem value="girl">👧 Girl</SelectItem>
                                                        <SelectItem value="unisex">👶👧 Unisex</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div>
                                                <Label htmlFor="country" className="text-base font-medium">Country/Region</Label>
                                                <Select
                                                    value={formData.country}
                                                    onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}
                                                >
                                                    <SelectTrigger className="mt-2 h-12">
                                                        <SelectValue placeholder="Select country" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="usa">🇺🇸 USA</SelectItem>
                                                        <SelectItem value="uk">🇬🇧 United Kingdom</SelectItem>
                                                        <SelectItem value="canada">🇨🇦 Canada</SelectItem>
                                                        <SelectItem value="australia">🇦🇺 Australia</SelectItem>
                                                        <SelectItem value="india">🇮🇳 India</SelectItem>
                                                        <SelectItem value="germany">🇩🇪 Germany</SelectItem>
                                                        <SelectItem value="france">🇫🇷 France</SelectItem>
                                                        <SelectItem value="netherlands">🇳🇱 Netherlands</SelectItem>
                                                        <SelectItem value="sweden">🇸🇪 Sweden</SelectItem>
                                                        <SelectItem value="switzerland">🇨🇭 Switzerland</SelectItem>
                                                        <SelectItem value="austria">🇦🇹 Austria</SelectItem>
                                                        <SelectItem value="belgium">🇧🇪 Belgium</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>

                                        <div className="flex justify-center pt-4">
                                            <Button
                                                onClick={nextStep}
                                                disabled={!formData.gender || !formData.country}
                                                size="lg"
                                                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 text-lg"
                                            >
                                                Continue <ArrowRight className="ml-2 h-5 w-5" />
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Step 2: Preferences */}
                            {currentStep === 2 && (
                                <Card className="shadow-xl border-0">
                                    <CardHeader className="text-center pb-6">
                                        <div className="flex justify-center mb-4">
                                            <Gift className="h-12 w-12 text-purple-600" />
                                        </div>
                                        <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900">
                                            Add Your Preferences
                                        </CardTitle>
                                        <p className="text-gray-600 text-lg">
                                            Help us find names that match your style and values
                                        </p>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <Label htmlFor="religion" className="text-base font-medium">Religion (Optional)</Label>
                                                <Select
                                                    value={formData.religion}
                                                    onValueChange={(value) => setFormData(prev => ({ ...prev, religion: value }))}
                                                >
                                                    <SelectTrigger className="mt-2 h-12">
                                                        <SelectValue placeholder="Select religion" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="Christian">✝️ Christian</SelectItem>
                                                        <SelectItem value="Hindu">🕉️ Hindu</SelectItem>
                                                        <SelectItem value="Muslim">☪️ Muslim</SelectItem>
                                                        <SelectItem value="Sikh">🕊️ Sikh</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div>
                                                <Label htmlFor="style" className="text-base font-medium">Name Style</Label>
                                                <Select
                                                    value={formData.style}
                                                    onValueChange={(value) => setFormData(prev => ({ ...prev, style: value }))}
                                                >
                                                    <SelectTrigger className="mt-2 h-12">
                                                        <SelectValue placeholder="Select style" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="traditional">🏛️ Traditional</SelectItem>
                                                        <SelectItem value="modern">✨ Modern</SelectItem>
                                                        <SelectItem value="nature">🌿 Nature-Inspired</SelectItem>
                                                        <SelectItem value="biblical">📖 Biblical</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>

                                        <div>
                                            <Label htmlFor="meaning" className="text-base font-medium">Meaning (Optional)</Label>
                                            <Input
                                                id="meaning"
                                                placeholder="e.g., strength, love, peace, wisdom"
                                                value={formData.meaning}
                                                onChange={(e) => setFormData(prev => ({ ...prev, meaning: e.target.value }))}
                                                className="mt-2 h-12 text-base"
                                            />
                                            <p className="text-sm text-gray-500 mt-1">
                                                Enter qualities or meanings you'd like the name to represent
                                            </p>
                                        </div>

                                        <div className="flex flex-col sm:flex-row justify-center gap-4 pt-4">
                                            <Button
                                                onClick={prevStep}
                                                variant="outline"
                                                size="lg"
                                                className="px-6 sm:px-8 py-3 text-base sm:text-lg"
                                            >
                                                Back
                                            </Button>
                                            <Button
                                                onClick={generateNames}
                                                disabled={isGenerating}
                                                size="lg"
                                                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 sm:px-8 py-3 text-base sm:text-lg"
                                            >
                                                {isGenerating ? (
                                                    <>
                                                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                                        Generating...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Zap className="mr-2 h-5 w-5" />
                                                        Generate Names
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Step 3: Results */}
                            {currentStep === 3 && (
                                <div className="space-y-8">
                                    <div className="text-center">
                                        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                                            Your Personalized Names
                                        </h2>
                                        <p className="text-gray-600 text-lg mb-6">
                                            We found {generatedNames.length} perfect names for your baby
                                        </p>
                                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                            <Button
                                                onClick={resetForm}
                                                variant="outline"
                                                size="lg"
                                                className="px-6 py-3"
                                            >
                                                Start Over
                                            </Button>
                                            <Button
                                                onClick={generateNames}
                                                disabled={isGenerating}
                                                size="lg"
                                                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-3"
                                            >
                                                <RefreshCw className="mr-2 h-5 w-5" />
                                                Generate More
                                            </Button>
                                        </div>
                                    </div>

                                    {suggestions.length > 0 && (
                                        <Card className="bg-blue-50 border-blue-200">
                                            <CardContent className="pt-6">
                                                <h3 className="font-semibold text-blue-900 mb-3 text-lg">💡 Tips for better results:</h3>
                                                <ul className="text-blue-800 space-y-2">
                                                    {suggestions.map((suggestion, index) => (
                                                        <li key={index} className="flex items-start">
                                                            <span className="mr-2">•</span>
                                                            <span>{suggestion}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </CardContent>
                                        </Card>
                                    )}

                                    {generatedNames.length > 0 ? (
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                            {generatedNames.map((name, index) => (
                                                <Card key={index} className="hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer border-0 shadow-lg">
                                                    <CardContent className="pt-6">
                                                        <div className="flex items-start justify-between mb-4">
                                                            <div className="flex-1">
                                                                <Link
                                                                    href={`/name/${name.language.toLowerCase()}/${name.gender}/${name.religion.toLowerCase()}/${name.name.toLowerCase()}`}
                                                                    className="block"
                                                                >
                                                                    <h3 className="text-xl font-bold text-gray-900 hover:text-purple-600 transition-colors">
                                                                        {name.name}
                                                                    </h3>
                                                                    {name.nativeName !== name.name && (
                                                                        <p className="text-sm text-gray-600 mt-1">{name.nativeName}</p>
                                                                    )}
                                                                </Link>
                                                            </div>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => toggleFavorite(name.name)}
                                                                className={favorites.includes(name.name) ? "text-red-500" : "text-gray-400"}
                                                            >
                                                                <Heart className={`h-5 w-5 ${favorites.includes(name.name) ? "fill-current" : ""}`} />
                                                            </Button>
                                                        </div>

                                                        <div className="space-y-3">
                                                            <p className="text-gray-700">
                                                                <span className="font-semibold">Meaning:</span> {name.meaning}
                                                            </p>
                                                            <p className="text-gray-600 text-sm">
                                                                <span className="font-semibold">Origin:</span> {name.origin}
                                                            </p>
                                                            <p className="text-gray-600 text-sm">
                                                                <span className="font-semibold">Pronunciation:</span> {name.pronunciation}
                                                            </p>

                                                            <div className="flex flex-wrap gap-2 mt-4">
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {name.gender}
                                                                </Badge>
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {name.language}
                                                                </Badge>
                                                                {name.styles.map((style, styleIndex) => (
                                                                    <Badge key={styleIndex} variant="outline" className="text-xs">
                                                                        {style}
                                                                    </Badge>
                                                                ))}
                                                                {name.trendingStatus === "rising" && (
                                                                    <Badge className="bg-green-100 text-green-800 text-xs">
                                                                        <TrendingUp className="h-3 w-3 mr-1" />
                                                                        Trending
                                                                    </Badge>
                                                                )}
                                                            </div>

                                                            {name.popularityRank && (
                                                                <p className="text-xs text-gray-500 mt-2">
                                                                    Popularity Rank: #{name.popularityRank}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            ))}
                                        </div>
                                    ) : (
                                        <Card className="text-center py-12">
                                            <CardContent>
                                                <Sparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                                    No names found
                                                </h3>
                                                <p className="text-gray-600 mb-6">
                                                    Try adjusting your preferences or using different keywords.
                                                </p>
                                                <Button onClick={resetForm}>
                                                    Start Over
                                                </Button>
                                            </CardContent>
                                        </Card>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-16 bg-gradient-to-r from-purple-50 to-pink-50">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                            Why Choose Our Baby Name Generator?
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow text-center">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        <Sparkles className="h-12 w-12 text-purple-600" />
                                    </div>
                                    <CardTitle className="text-xl">AI-Powered Intelligence</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Our advanced algorithm learns your preferences and suggests names that match your style,
                                        cultural background, and personal taste.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow text-center">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        <Globe className="h-12 w-12 text-blue-600" />
                                    </div>
                                    <CardTitle className="text-xl">Global Name Database</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Access 25,000+ names from 12 countries with verified meanings, origins, and cultural significance.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow text-center">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        <Heart className="h-12 w-12 text-pink-600" />
                                    </div>
                                    <CardTitle className="text-xl">Personalized Results</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Get suggestions tailored to your preferences including style, meaning, popularity, and cultural background.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow text-center">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        <BookOpen className="h-12 w-12 text-green-600" />
                                    </div>
                                    <CardTitle className="text-xl">Detailed Information</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Each suggestion includes meaning, origin, pronunciation, popularity trends, and cultural context.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow text-center">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        <Users className="h-12 w-12 text-orange-600" />
                                    </div>
                                    <CardTitle className="text-xl">Family Collaboration</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Save your favorite names and share them with family members for collaborative decision-making.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow text-center">
                                <CardHeader>
                                    <div className="flex justify-center mb-4">
                                        <Star className="h-12 w-12 text-yellow-600" />
                                    </div>
                                    <CardTitle className="text-xl">Save & Compare</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Save your favorite names, create lists, and compare options side-by-side to make the best decision.
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6">
                            Ready to Find Your Perfect Baby Name?
                        </h2>
                        <p className="text-xl mb-8 text-purple-100 max-w-2xl mx-auto">
                            Join thousands of parents who have found their ideal baby name using our AI-powered generator.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button
                                onClick={() => setCurrentStep(1)}
                                size="lg"
                                className="bg-white text-purple-600 hover:bg-purple-50 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4"
                            >
                                <Zap className="mr-2 h-5 w-5" />
                                Start Generating Now
                            </Button>
                            <Link href="/trending-names">
                                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4">
                                    <Star className="mr-2 h-5 w-5" />
                                    View Trending Names
                                </Button>
                            </Link>
                        </div>
                    </div>
                </section>

                {/* FAQ Section */}
                <section className="py-16 bg-white">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                            Frequently Asked Questions
                        </h2>
                        <FAQSection faqs={faqs} />
                    </div>
                </section>
            </div>
        </>
    )
} 