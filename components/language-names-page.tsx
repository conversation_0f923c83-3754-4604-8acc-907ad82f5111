"use client"

import NameC<PERSON>, { NameCardSkeleton } from "@/components/name-card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import type { NameData } from "@/types/name-data"
import { Heart, Search } from "lucide-react"
import dynamic from "next/dynamic"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { useEffect, useMemo, useState } from "react"
import SEOStructuredData from "./seo-structured-data"

interface FilterOption {
  value: string
  label: string
}

interface HeaderLabels {
  title: string
  subtitle: string
  description: string
}

type ColorTheme = "orange" | "red" | "blue" | "green" | "yellow" | "purple" | "pink" | "indigo"

interface LanguageNamesPageProps {
  language: string
  country?: string
  religions?: string[]
  showReligionFilter?: boolean
  colorTheme: ColorTheme
  apiEndpoint: string
  headerLabels: HeaderLabe<PERSON>
  seoContent?: React.ReactNode
  additionalContent?: React.ReactNode
  showRashiFilter?: boolean
  showGenderFilter?: boolean
  showAlphabetFilter?: boolean
  defaultGender?: string

  // Filter options
  languages?: FilterOption[]
  religionOptions?: FilterOption[]
  genders?: FilterOption[]
  startingLetters?: string[]
}

const defaultGenders = [
  { value: "all", label: "All Genders" },
  { value: "male", label: "Boy Names" },
  { value: "female", label: "Girl Names" },
  { value: "unisex", label: "Unisex Names" },
]

const defaultStartingLetters = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"]

// Dynamic imports for better performance
const CommonPagination = dynamic(() => import("./common-pagination"), {
  loading: () => <Skeleton className="h-10 w-full" />,
  ssr: false
})

const EnhancedFilterPanel = dynamic(() => import("./enhanced-filter-panel"), {
  loading: () => <Skeleton className="h-64 w-full" />,
  ssr: false
})

const EnhancedMobileFilterDrawer = dynamic(() => import("./enhanced-mobile-filter-drawer"), {
  loading: () => <Skeleton className="h-64 w-full" />,
  ssr: false
})

// Dynamically import heavier, rarely above-the-fold components to cut initial JS payload
// and defer their parsing/execution until actually needed on the client.
const AlphabetFilter = dynamic(() => import("@/components/alphabet-filter"))
const EnhancedSideFilters = dynamic(() => import("@/components/enhanced-side-filters"), { ssr: false })

const ITEMS_PER_PAGE = 20;

export default function LanguageNamesPage({
  language,
  country,
  religions = [],
  showReligionFilter = false,
  colorTheme,
  apiEndpoint,
  headerLabels,
  seoContent,
  additionalContent,
  showRashiFilter = true,
  showGenderFilter = true,
  showAlphabetFilter = true,
  defaultGender = "all",
  languages = [],
  religionOptions = [],
  genders = defaultGenders,
  startingLetters = defaultStartingLetters,
}: LanguageNamesPageProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [names, setNames] = useState<NameData[]>([])
  const [filteredNames, setFilteredNames] = useState<NameData[]>([])
  const [favorites, setFavorites] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [activeFilters, setActiveFilters] = useState({
    gender: showGenderFilter ? "all" : defaultGender,
    letter: "all",
    rashi: "all",
    religion: religions.length === 1 ? religions[0] : "all",
  })
  const [isLoading, setIsLoading] = useState(true)
  const [showSideFilters, setShowSideFilters] = useState(false)

  // Load favorites from localStorage
  useEffect(() => {
    const loadFavorites = () => {
      const savedFavorites = localStorage.getItem("babyNameFavorites")
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites))
      }
    }
    loadFavorites()
  }, [])

  // Handle data change from pagination component
  const handleDataChange = (data: NameData[], metadata: any) => {
    setNames(data)
    setFilteredNames(data)
    // Removed automatic scrolling - only scroll on user interactions
  }

  // Handle loading state change
  const handleLoadingChange = (loading: boolean) => {
    setIsLoading(loading)
  }

  // Get the current route path using Next.js hook
  const currentPath = usePathname()

  // Build search parameters for pagination - memoized to prevent unnecessary re-renders
  const buildSearchParams = useMemo(() => {
    const params: Record<string, string> = {}

    // Ensure currentPath exists before using it
    if (currentPath) {
      params.route = currentPath
    }

    if (searchTerm) params.search = searchTerm
    // Add country parameter if specified (for backward compatibility)
    if (country) params.country = country
    // Add language parameter if specified (for backward compatibility)
    if (language) params.language = language
    // Add gender parameter if it's not "all"
    if (activeFilters.gender !== "all") {
      // Map male/female to boy/girl for API
      const genderMapping: Record<string, string> = {
        "male": "boy",
        "female": "girl"
      }
      params.gender = genderMapping[activeFilters.gender] || activeFilters.gender
    }
    // Add letter filter if not "all"
    if (activeFilters.letter !== "all") {
      params.startingLetter = activeFilters.letter
    }
    // Add rashi filter if not "all"
    if (activeFilters.rashi !== "all") {
      params.rashi = activeFilters.rashi
    }

    return params
  }, [currentPath, searchTerm, country, language, activeFilters.gender, activeFilters.letter, activeFilters.rashi])

  // Toggle favorite status
  const toggleFavorite = (nameEn: string) => {
    const nameData = names.find(name => name.name_en === nameEn)
    if (!nameData) return

    const newFavorites = favorites.includes(nameEn)
      ? favorites.filter((name) => name !== nameEn)
      : [...favorites, nameEn]
    setFavorites(newFavorites)

    // Store full name data for favorites page
    const existingFavoritesData = localStorage.getItem("babyNameFavoritesData")
    const favoritesData = existingFavoritesData ? JSON.parse(existingFavoritesData) : []

    if (favorites.includes(nameEn)) {
      // Remove from favorites data
      const updatedFavoritesData = favoritesData.filter((fav: any) => fav.name_en !== nameEn)
      localStorage.setItem("babyNameFavoritesData", JSON.stringify(updatedFavoritesData))
    } else {
      // Add to favorites data
      const updatedFavoritesData = [...favoritesData, nameData]
      localStorage.setItem("babyNameFavoritesData", JSON.stringify(updatedFavoritesData))
    }

    localStorage.setItem("babyNameFavorites", JSON.stringify(newFavorites))
  }

  // Get favorite names
  const getFavoriteNames = () => {
    return filteredNames.filter((name) => favorites.includes(name.name_en))
  }

  // Apply letter filter
  const handleLetterFilter = (letter: string) => {
    setActiveFilters({ ...activeFilters, letter: letter === activeFilters.letter ? "all" : letter })
  }

  // Handle filter changes
  const handleFilterChange = (filterType: string, value: string) => {
    setActiveFilters({ ...activeFilters, [filterType]: value })
  }

  // Clear all filters
  const clearAllFilters = () => {
    setActiveFilters({
      gender: showGenderFilter ? "all" : defaultGender,
      letter: "all",
      rashi: "all",
      religion: religions.length === 1 ? religions[0] : "all",
    })
    setSearchTerm("")
  }

  // Color theme classes
  const colorMap: Record<ColorTheme, any> = {
    orange: {
      badge: "bg-orange-100 text-orange-700 hover:bg-orange-200",
      button: "bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700",
      tab: "data-[state=active]:bg-orange-100 data-[state=active]:text-orange-700",
      grid: "",
      heart: "text-orange-300",
    },
    red: {
      badge: "bg-red-100 text-red-700 hover:bg-red-200",
      button: "bg-red-50 hover:bg-red-100 border-red-200 text-red-700",
      tab: "data-[state=active]:bg-red-100 data-[state=active]:text-red-700",
      grid: "",
      heart: "text-red-300",
    },
    blue: {
      badge: "bg-blue-100 text-blue-700 hover:bg-blue-200",
      button: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
      tab: "data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700",
      grid: "",
      heart: "text-blue-300",
    },
    green: {
      badge: "bg-green-100 text-green-700 hover:bg-green-200",
      button: "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
      tab: "data-[state=active]:bg-green-100 data-[state=active]:text-green-700",
      grid: "",
      heart: "text-green-300",
    },
    yellow: {
      badge: "bg-yellow-100 text-yellow-700 hover:bg-yellow-200",
      button: "bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-700",
      tab: "data-[state=active]:bg-yellow-100 data-[state=active]:text-yellow-700",
      grid: "",
      heart: "text-yellow-300",
    },
    purple: {
      badge: "bg-purple-100 text-purple-700 hover:bg-purple-200",
      button: "bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700",
      tab: "data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700",
      grid: "",
      heart: "text-purple-300",
    },
    pink: {
      badge: "bg-pink-100 text-pink-700 hover:bg-pink-200",
      button: "bg-pink-50 hover:bg-pink-100 border-pink-200 text-pink-700",
      tab: "data-[state=active]:bg-pink-100 data-[state=active]:text-pink-700",
      grid: "",
      heart: "text-pink-300",
    },
    indigo: {
      badge: "bg-indigo-100 text-indigo-700 hover:bg-indigo-200",
      button: "bg-indigo-50 hover:bg-indigo-100 border-indigo-200 text-indigo-700",
      tab: "data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700",
      grid: "",
      heart: "text-indigo-300",
    },
  }
  const theme = colorMap[colorTheme]

  // Determine if side filter should be shown
  const hasSideFilter = showReligionFilter || showGenderFilter || showRashiFilter;

  return (
    <>
      {/* SEO Structured Data - Generated from actual name data */}
      <SEOStructuredData
        names={filteredNames}
        pageType="name-list"
        title={headerLabels.title}
        description={headerLabels.description}
        url={currentPath || ""}
        language={language}
        country={country}
        gender={defaultGender}
      />

      <main className={hasSideFilter ? "container mx-auto px-4 py-8" : "flex justify-center w-full min-h-screen bg-gray-50"}>
        <div className={hasSideFilter ? "w-full" : "w-full max-w-4xl px-4 py-8"}>
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className={`text-4xl font-bold ${theme.badge.split(" ")[1]} mb-4`}>
              {headerLabels.title}
            </h1>
            <h2 className="text-2xl font-semibold text-gray-700 mb-2">
              {headerLabels.subtitle}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {headerLabels.description}
            </p>
          </div>

          {/* Search Bar */}
          <form onSubmit={(e) => e.preventDefault()} className="mb-6">
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder={`Search ${language} names...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-3 text-lg"
              />
            </div>
          </form>

          {/* Alphabet filter */}
          {showAlphabetFilter && (
            <div className="mb-6">
              <AlphabetFilter activeLetter={activeFilters.letter} onLetterClick={handleLetterFilter} />
            </div>
          )}

          {/* Main content area: Filters + Name Cards */}
          {hasSideFilter ? (
            <div className="w-full flex flex-col lg:flex-row gap-8">
              {/* Filters Panel - sidebar on desktop, drawer on mobile */}
              <div className="hidden lg:block w-80 shrink-0">
                <EnhancedSideFilters
                  isOpen={true}
                  onClose={() => { }}
                  activeFilters={{
                    gender: activeFilters.gender,
                    language,
                    religion: activeFilters.religion,
                    letter: activeFilters.letter,
                    rashi: activeFilters.rashi,
                  }}
                  onFilterChange={handleFilterChange}
                  onClearFilters={clearAllFilters}
                  totalResults={filteredNames.length}
                  languages={languages}
                  religions={religionOptions}
                  genders={genders}
                  startingLetters={startingLetters}
                  showLanguageFilter={false} // Hide language filter since we're on a specific language page
                  showReligionFilter={showReligionFilter}
                  showGenderFilter={showGenderFilter}
                  showAlphabetFilter={false} // Alphabet filter is handled separately
                  showRashiFilter={showRashiFilter}
                  currentLanguage={language}
                  currentReligion={religions.length === 1 ? religions[0] : undefined}
                  hideLanguageFilters={true}
                />
              </div>
              {/* Mobile filter drawer button */}
              <div className="lg:hidden mb-6">
                {/* Mobile Drawer */}
                <EnhancedMobileFilterDrawer
                  languages={languages}
                  religions={religionOptions}
                  genders={genders}
                  startingLetters={startingLetters}
                  selectedLanguage={null} // No language selection on language-specific pages
                  selectedReligion={activeFilters.religion === "all" ? null : activeFilters.religion}
                  selectedGender={activeFilters.gender === "all" ? null : activeFilters.gender}
                  selectedStartingLetter={activeFilters.letter === "all" ? null : activeFilters.letter}
                  selectedRashi={activeFilters.rashi === "all" ? null : activeFilters.rashi}
                  onLanguageChange={undefined} // No language change on language-specific pages
                  onReligionChange={showReligionFilter ? (religion) => setActiveFilters({ ...activeFilters, religion: religion || "all" }) : undefined}
                  onGenderChange={showGenderFilter ? (gender) => setActiveFilters({ ...activeFilters, gender: gender || "all" }) : undefined}
                  onStartingLetterChange={showAlphabetFilter ? (letter) => setActiveFilters({ ...activeFilters, letter: letter || "all" }) : undefined}
                  onRashiChange={showRashiFilter ? (rashi) => setActiveFilters({ ...activeFilters, rashi: rashi || "all" }) : undefined}
                  onClearAll={clearAllFilters}
                  showLanguageFilter={false}
                  showReligionFilter={showReligionFilter}
                  showGenderFilter={showGenderFilter}
                  showAlphabetFilter={false}
                  showRashiFilter={showRashiFilter}
                  currentLanguage={language}
                  currentReligion={religions.length === 1 ? religions[0] : undefined}
                  hideLanguageFilters={true}
                  colorTheme={colorTheme}
                />
              </div>
              {/* Name Cards Grid */}
              <div className="flex-1 min-w-0">
                {/* Active filters */}
                {(showGenderFilter && activeFilters.gender !== "all") || (showAlphabetFilter && activeFilters.letter !== "all") || (showRashiFilter && activeFilters.rashi !== "all") || (showReligionFilter && activeFilters.religion !== "all") || searchTerm ? (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {showGenderFilter && activeFilters.gender !== "all" && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        {activeFilters.gender === "male" ? "Boys" : "Girls"} ✕
                      </Badge>
                    )}
                    {showReligionFilter && activeFilters.religion !== "all" && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        {activeFilters.religion} ✕
                      </Badge>
                    )}
                    {showAlphabetFilter && activeFilters.letter !== "all" && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        Letter: {activeFilters.letter} ✕
                      </Badge>
                    )}
                    {showRashiFilter && activeFilters.rashi !== "all" && (
                      <Badge className={colorMap.purple.badge + " cursor-pointer"}>
                        Rashi: {activeFilters.rashi} ✕
                      </Badge>
                    )}
                    {searchTerm && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        Search: {searchTerm} ✕
                      </Badge>
                    )}
                    <Badge className={colorMap.red.badge + " cursor-pointer"} onClick={clearAllFilters}>
                      Clear All
                    </Badge>
                  </div>
                ) : null}
                {/* Tabs and Name Cards Grid */}
                <Tabs defaultValue="all" className="w-full">
                  <TabsList className={`grid w-full grid-cols-2 mb-6 ${theme.button} dark:bg-opacity-20`}>
                    <TabsTrigger
                      value="all"
                      className={theme.tab}
                    >
                      All Names
                    </TabsTrigger>
                    <TabsTrigger
                      value="favorites"
                      className={theme.tab}
                    >
                      Favorites ({favorites.length})
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="all" className="space-y-4">
                    {isLoading ? (
                      <div className="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                        {[...Array(ITEMS_PER_PAGE)].map((_, i) => (
                          <NameCardSkeleton compact key={i} />
                        ))}
                      </div>
                    ) : filteredNames.length > 0 ? (
                      <>
                        <div
                          id="names-grid-start"
                          className="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6"
                        >
                          {filteredNames.map((name, index) => (
                            <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`} id={`name-${name.name_en}`}>
                              <NameCard
                                name={name}
                                isFavorite={favorites.includes(name.name_en)}
                                onToggleFavorite={() => toggleFavorite(name.name_en)}
                                onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                                colorTheme={colorTheme}
                                compact={true}
                              />
                            </div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-10">
                        <p>No {language} names found matching your criteria.</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Try adjusting your search or filters.
                        </p>
                      </div>
                    )}

                    {/* Common Pagination Component - Always render to trigger initial data fetch */}
                    <div className="mt-8">
                      <CommonPagination
                        apiEndpoint={apiEndpoint}
                        searchParams={buildSearchParams || {}}
                        itemsPerPage={ITEMS_PER_PAGE}
                        onDataChange={handleDataChange}
                        onLoadingChange={handleLoadingChange}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="favorites" className="space-y-4">
                    {getFavoriteNames().length > 0 ? (
                      <div className="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                        {getFavoriteNames().map((name, index) => (
                          <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`} id={`name-${name.name_en}`}>
                            <NameCard
                              name={name}
                              isFavorite={true}
                              onToggleFavorite={() => toggleFavorite(name.name_en)}
                              onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                              colorTheme={colorTheme}
                              compact={true}
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-10">
                        <Heart className={`mx-auto h-12 w-12 ${theme.heart} mb-4`} />
                        <p>You haven't added any {language} names to favorites yet.</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Click the heart icon on any name to add it to your favorites.
                        </p>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          ) : (
            <div>
              {/* Name Cards Grid only, centered layout */}
              <div className="flex-1 min-w-0">
                {/* Active filters */}
                {(showGenderFilter && activeFilters.gender !== "all") || (showAlphabetFilter && activeFilters.letter !== "all") || (showRashiFilter && activeFilters.rashi !== "all") || (showReligionFilter && activeFilters.religion !== "all") || searchTerm ? (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {showGenderFilter && activeFilters.gender !== "all" && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        {activeFilters.gender === "male" ? "Boys" : "Girls"} ✕
                      </Badge>
                    )}
                    {showReligionFilter && activeFilters.religion !== "all" && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        {activeFilters.religion} ✕
                      </Badge>
                    )}
                    {showAlphabetFilter && activeFilters.letter !== "all" && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        Letter: {activeFilters.letter} ✕
                      </Badge>
                    )}
                    {showRashiFilter && activeFilters.rashi !== "all" && (
                      <Badge className={colorMap.purple.badge + " cursor-pointer"}>
                        Rashi: {activeFilters.rashi} ✕
                      </Badge>
                    )}
                    {searchTerm && (
                      <Badge className={theme.badge + " cursor-pointer"}>
                        Search: {searchTerm} ✕
                      </Badge>
                    )}
                    <Badge className={colorMap.red.badge + " cursor-pointer"} onClick={clearAllFilters}>
                      Clear All
                    </Badge>
                  </div>
                ) : null}
                {/* Tabs and Name Cards Grid */}
                <Tabs defaultValue="all" className="w-full">
                  <TabsList className={`grid w-full grid-cols-2 mb-6 ${theme.button} dark:bg-opacity-20`}>
                    <TabsTrigger
                      value="all"
                      className={theme.tab}
                    >
                      All Names
                    </TabsTrigger>
                    <TabsTrigger
                      value="favorites"
                      className={theme.tab}
                    >
                      Favorites ({favorites.length})
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="all" className="space-y-4">
                    {isLoading ? (
                      <div className="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                        {[...Array(ITEMS_PER_PAGE)].map((_, i) => (
                          <NameCardSkeleton compact key={i} />
                        ))}
                      </div>
                    ) : filteredNames.length > 0 ? (
                      <>
                        <div
                          id="names-grid-start"
                          className="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6"
                        >
                          {filteredNames.map((name, index) => (
                            <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`} id={`name-${name.name_en}`}>
                              <NameCard
                                name={name}
                                isFavorite={favorites.includes(name.name_en)}
                                onToggleFavorite={() => toggleFavorite(name.name_en)}
                                onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                                colorTheme={colorTheme}
                                compact={true}
                              />
                            </div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-10">
                        <p>No {language} names found matching your criteria.</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Try adjusting your search or filters.
                        </p>
                      </div>
                    )}

                    {/* Common Pagination Component - Always render to trigger initial data fetch */}
                    <div className="mt-8">
                      <CommonPagination
                        apiEndpoint={apiEndpoint}
                        searchParams={buildSearchParams || {}}
                        itemsPerPage={ITEMS_PER_PAGE}
                        onDataChange={handleDataChange}
                        onLoadingChange={handleLoadingChange}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="favorites" className="space-y-4">
                    {getFavoriteNames().length > 0 ? (
                      <div className="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                        {getFavoriteNames().map((name, index) => (
                          <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`} id={`name-${name.name_en}`}>
                            <NameCard
                              name={name}
                              isFavorite={true}
                              onToggleFavorite={() => toggleFavorite(name.name_en)}
                              onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                              colorTheme={colorTheme}
                              compact={true}
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-10">
                        <Heart className={`mx-auto h-12 w-12 ${theme.heart} mb-4`} />
                        <p>You haven't added any {language} names to favorites yet.</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Click the heart icon on any name to add it to your favorites.
                        </p>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
              {/* Info/SEO Section below the grid, styled as a card */}
              {seoContent && (
                <section className="bg-white rounded-lg shadow p-6 mt-8">
                  {seoContent}
                </section>
              )}

              {/* Additional Content Section */}
              {additionalContent && (
                <section className="mt-8">
                  {additionalContent}
                </section>
              )}
            </div>
          )}

          {/* Unique Tools Call-to-Action (moved to bottom) */}
          <div className="flex flex-col sm:flex-row gap-4 mt-12 mb-4 justify-center">
            <a href="/name-compatibility-checker" className="flex-1">
              <div className="bg-gradient-to-r from-blue-50 to-purple-100 border border-blue-200 rounded-xl p-5 text-center shadow hover:shadow-lg transition-all duration-200 h-full flex flex-col items-center justify-center">
                <span className="inline-block bg-blue-600 text-white rounded-full px-3 py-1 mb-3 text-sm font-semibold">New</span>
                <h3 className="text-lg sm:text-xl font-bold text-blue-700 mb-2">Name Compatibility Checker</h3>
                <p className="text-sm text-blue-800 mb-3">See how any name sounds with your surname. Instantly check flow, pronunciation, and initials!</p>
                <span className="inline-block bg-white text-blue-600 border border-blue-600 rounded px-4 py-2 font-semibold hover:bg-blue-600 hover:text-white transition-colors">Try Now</span>
              </div>
            </a>
            <a href="/cultural-fusion-names" className="flex-1">
              <div className="bg-gradient-to-r from-orange-50 to-pink-100 border border-orange-200 rounded-xl p-5 text-center shadow hover:shadow-lg transition-all duration-200 h-full flex flex-col items-center justify-center">
                <span className="inline-block bg-orange-600 text-white rounded-full px-3 py-1 mb-3 text-sm font-semibold">Unique</span>
                <h3 className="text-lg sm:text-xl font-bold text-orange-700 mb-2">Cultural Fusion Name Generator</h3>
                <p className="text-sm text-orange-800 mb-3">Blend cultures to create beautiful, meaningful multicultural names for your baby.</p>
                <span className="inline-block bg-white text-orange-600 border border-orange-600 rounded px-4 py-2 font-semibold hover:bg-orange-600 hover:text-white transition-colors">Try Now</span>
              </div>
            </a>
          </div>
        </div>
      </main>
    </>
  )
} 