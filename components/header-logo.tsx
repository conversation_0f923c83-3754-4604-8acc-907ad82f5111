import Image from "next/image";

export default function HeaderLogo() {
  return (
    <div style={{
      display: "flex",
      alignItems: "center",
      gap: "0.5rem",
      height: 40
    }}>
      <Image
        src="/icons/logo_full_hd.png"
        alt="Baby Names Mascot"
        width={32}
        height={32}
        priority={true} // Critical for LCP - load immediately
        style={{ display: 'block' }}
        sizes="32px" // Optimize for actual display size
        quality={85} // Reduce quality slightly for faster loading
      />
      <span style={{
        fontFamily: "'Baloo 2', 'Nunito', 'Montserrat', Arial, sans-serif",
        fontWeight: 600,
        fontSize: "1.12rem",
        color: "#2563eb",
        letterSpacing: "0.01em",
        lineHeight: 1.1,
        display: 'flex',
        alignItems: 'center',
      }}>
        Baby Name&nbsp;
        <span style={{
          background: 'linear-gradient(90deg, #3b82f6 0%, #a21caf 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 800,
          fontSize: '1.18rem',
          letterSpacing: '0.01em',
          paddingLeft: '2px',
        }}>
          Diaries
        </span>
      </span>
    </div>
  );
} 