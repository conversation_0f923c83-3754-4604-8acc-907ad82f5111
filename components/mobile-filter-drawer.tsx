"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Drawer<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer"
import { Filter, X } from "lucide-react"
import { useEffect, useState } from "react"
import { EnhancedFilterPanel } from "./enhanced-filter-panel"

interface MobileFilterDrawerProps {
  languages: string[]
  religions: string[]
  genders: string[]
  startingLetters: string[]
  selectedLanguage: string | null
  selectedReligion: string | null
  selectedGender: string | null
  selectedStartingLetter: string | null
  selectedRashi: string | null
  onLanguageChange: (language: string | null) => void
  onReligionChange: (religion: string | null) => void
  onGenderChange: (gender: string | null) => void
  onStartingLetterChange: (letter: string | null) => void
  onRashiChange: (rashi: string | null) => void
  onClearAll: () => void
  hideLanguageFilters?: boolean
  showSmartHiding?: boolean
}

export function MobileFilterDrawer({
  languages,
  religions,
  genders,
  startingLetters,
  selectedLanguage,
  selectedReligion,
  selectedGender,
  selectedStartingLetter,
  selectedRashi,
  onLanguageChange,
  onReligionChange,
  onGenderChange,
  onStartingLetterChange,
  onRashiChange,
  onClearAll,
  hideLanguageFilters = false,
  showSmartHiding = true,
}: MobileFilterDrawerProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Prevent body scroll when mobile drawer is open
  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll on mobile
      document.body.style.overflow = 'hidden'
    } else {
      // Restore body scroll
      document.body.style.overflow = 'unset'
    }

    // Cleanup function to restore scroll when component unmounts
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const activeFiltersCount = [
    selectedLanguage,
    selectedReligion,
    selectedGender,
    selectedStartingLetter,
    selectedRashi,
  ].filter(Boolean).length

  return (
    <div className="lg:hidden">
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerTrigger asChild>
          <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2 bg-white hover:bg-gray-50 border-gray-200"
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </DrawerTrigger>
        <DrawerContent className="max-h-[80vh] overflow-hidden">
          <DrawerHeader className="border-b">
            <div className="flex items-center justify-between">
              <DrawerTitle>Filters</DrawerTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DrawerHeader>
          <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
            <EnhancedFilterPanel
              languages={languages}
              religions={religions}
              genders={genders}
              startingLetters={startingLetters}
              selectedLanguage={selectedLanguage}
              selectedReligion={selectedReligion}
              selectedGender={selectedGender}
              selectedStartingLetter={selectedStartingLetter}
              selectedRashi={selectedRashi}
              onLanguageChange={(lang) => {
                onLanguageChange(lang)
                setIsOpen(false)
              }}
              onReligionChange={(religion) => {
                onReligionChange(religion)
                setIsOpen(false)
              }}
              onGenderChange={(gender) => {
                onGenderChange(gender)
                setIsOpen(false)
              }}
              onStartingLetterChange={(letter) => {
                onStartingLetterChange(letter)
                setIsOpen(false)
              }}
              onRashiChange={(rashi) => {
                onRashiChange(rashi)
                setIsOpen(false)
              }}
              onClearAll={() => {
                onClearAll()
                setIsOpen(false)
              }}
              hideLanguageFilters={hideLanguageFilters}
              showSmartHiding={showSmartHiding}
              className="space-y-6 px-1"
            />
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  )
} 