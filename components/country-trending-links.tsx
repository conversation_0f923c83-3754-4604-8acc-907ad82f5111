"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { navigationConfig } from "@/lib/navigation-config"
import { ArrowRight, Globe, TrendingUp } from "lucide-react"
import Link from "next/link"

interface CountryTrendingLinksProps {
  country: string
  showGlobalTrending?: boolean
  compact?: boolean
}

export default function CountryTrendingLinks({
  country,
  showGlobalTrending = true,
  compact = false
}: CountryTrendingLinksProps) {

  const countryConfig = navigationConfig.countries.find(c =>
    c.name.toLowerCase() === country.toLowerCase()
  )

  const isUSA = country.toLowerCase() === "usa"
  const isPopularCountry = ["usa", "uk", "canada", "australia"].includes(country.toLowerCase())

  if (!countryConfig) return null

  return (
    <div className={`space-y-2 sm:space-y-4 ${compact ? 'space-y-1 sm:space-y-3' : ''}`}>
      {/* Country-Specific Trending */}
      <Card className="rounded-md sm:rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader className={compact ? "pb-2 sm:pb-3" : "pb-2 sm:pb-4"}>
          <CardTitle className={`flex items-center text-blue-800 text-base sm:text-lg ${compact ? '' : ''}`}>
            <TrendingUp className={`mr-2 h-3 w-3 sm:h-5 sm:w-5`} />
            {countryConfig.flag} Trending {countryConfig.name} Names
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <p className="text-blue-700 mb-2 sm:mb-4 text-sm sm:text-base">
            {countryConfig.description}
          </p>

          <div className="space-y-2 sm:space-y-3">
            {/* USA gets special treatment with American-specific page and language links grouped together */}
            {isUSA ? (
              <div className="space-y-2 sm:space-y-3">
                <Link href="/popular-american-names-2025">
                  <Button
                    variant="outline"
                    className="w-full text-left h-12 sm:h-16 p-3 sm:p-4 rounded-md sm:rounded-lg text-sm sm:text-base flex justify-between items-center"
                  >
                    <span className="flex flex-col items-start min-w-0 flex-1 whitespace-normal break-words">
                      <span className="font-semibold text-blue-700 mb-0.5 sm:mb-1 whitespace-normal break-words text-sm sm:text-base">
                        Popular American Names 2025
                      </span>
                      <span className="text-xs sm:text-sm text-blue-600 whitespace-normal break-words">
                        Comprehensive USA market analysis with regional trends
                      </span>
                    </span>
                    <span className="shrink-0 flex items-center ml-2">
                      <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500" />
                    </span>
                  </Button>
                </Link>
                <Link href={countryConfig.languages[0].boyHref}>
                  <Button
                    variant="outline"
                    className="w-full h-12 sm:h-16 p-3 sm:p-4 rounded-md sm:rounded-lg hover:bg-blue-50 border-blue-200 hover:border-blue-300 text-xs sm:text-base shadow-sm"
                  >
                    <div className="text-center">
                      <div className="font-medium text-blue-700 text-sm sm:text-base">
                        {countryConfig.languages[0].name} Boy Names
                      </div>
                      <div className="text-[11px] sm:text-xs text-blue-600">
                        Discover trending {countryConfig.languages[0].name.toLowerCase()} names for boys
                      </div>
                    </div>
                  </Button>
                </Link>
                <Link href={countryConfig.languages[0].girlHref}>
                  <Button
                    variant="outline"
                    className="w-full h-12 sm:h-16 p-3 sm:p-4 rounded-md sm:rounded-lg hover:bg-pink-50 border-pink-200 hover:border-pink-300 text-xs sm:text-base shadow-sm"
                  >
                    <div className="text-center">
                      <div className="font-medium text-pink-700 text-sm sm:text-base">
                        {countryConfig.languages[0].name} Girl Names
                      </div>
                      <div className="text-[11px] sm:text-xs text-pink-600">
                        Discover trending {countryConfig.languages[0].name.toLowerCase()} names for girls
                      </div>
                    </div>
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-2 sm:space-y-3">
                {countryConfig.languages.map((language) => (
                  <div key={language.name} className="space-y-2 sm:space-y-3">
                    <Link href={language.boyHref}>
                      <Button
                        variant="outline"
                        className="w-full h-12 sm:h-16 p-3 sm:p-4 rounded-md sm:rounded-lg hover:bg-blue-50 border-blue-200 hover:border-blue-300 text-xs sm:text-base shadow-sm"
                      >
                        <div className="text-center">
                          <div className="font-medium text-blue-700 text-sm sm:text-base">
                            {language.name} Boy Names
                          </div>
                          <div className="text-[11px] sm:text-xs text-blue-600">
                            Discover trending {language.name.toLowerCase()} names for boys
                          </div>
                        </div>
                      </Button>
                    </Link>

                    <Link href={language.girlHref}>
                      <Button
                        variant="outline"
                        className="w-full h-12 sm:h-16 p-3 sm:p-4 rounded-md sm:rounded-lg hover:bg-pink-50 border-pink-200 hover:border-pink-300 text-xs sm:text-base shadow-sm"
                      >
                        <div className="text-center">
                          <div className="font-medium text-pink-700 text-sm sm:text-base">
                            {language.name} Girl Names
                          </div>
                          <div className="text-[11px] sm:text-xs text-pink-600">
                            Discover trending {language.name.toLowerCase()} names for girls
                          </div>
                        </div>
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Global Trending Connection */}
      {showGlobalTrending && (
        <Card className="rounded-md sm:rounded-lg p-2 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
          <CardHeader className={compact ? "pb-2 sm:pb-3" : "pb-2 sm:pb-4"}>
            <CardTitle className={`flex items-center text-green-800 text-base sm:text-lg ${compact ? '' : ''}`}>
              <Globe className={`mr-2 h-3 w-3 sm:h-5 sm:w-5`} />
              Global Trending Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-green-700 mb-2 sm:mb-4 text-sm sm:text-base">
              See how {countryConfig.name} names compare globally:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-1 sm:gap-3">
              <Link href="/trending-names" aria-label="View global trending baby names across 12 countries">
                <Button
                  variant="outline"
                  className="w-full h-12 sm:h-16 p-2 sm:p-4 rounded-md sm:rounded-lg hover:bg-green-50 border-green-200 hover:border-green-300 text-xs sm:text-base"
                >
                  <div className="text-center">
                    <div className="font-medium text-green-700 text-sm sm:text-base">Global Trending</div>
                    <div className="text-[11px] sm:text-xs text-green-700">Compare across 12 countries</div>
                  </div>
                </Button>
              </Link>

              <Link href="/popular-names-2025" aria-label="View most popular baby names worldwide">
                <Button
                  variant="outline"
                  className="w-full h-12 sm:h-16 p-2 sm:p-4 rounded-md sm:rounded-lg hover:bg-green-50 border-green-200 hover:border-green-300 text-xs sm:text-base"
                >
                  <div className="text-center">
                    <div className="font-medium text-green-700 text-sm sm:text-base">Popular Worldwide</div>
                    <div className="text-[11px] sm:text-xs text-green-700">Most loved names globally</div>
                  </div>
                </Button>
              </Link>

              {isPopularCountry && (
                <Link href="/unique-baby-names-2025">
                  <Button
                    variant="outline"
                    className="w-full h-12 sm:h-16 p-2 sm:p-4 rounded-md sm:rounded-lg hover:bg-purple-50 border-purple-200 hover:border-purple-300 text-xs sm:text-base"
                  >
                    <div className="text-center">
                      <div className="font-medium text-purple-700 text-sm sm:text-base">Unique Names</div>
                      <div className="text-[11px] sm:text-xs text-purple-600">Rare gems from {countryConfig.name}</div>
                    </div>
                  </Button>
                </Link>
              )}

              <Link href="/trending-baby-names-2025">
                <Button
                  variant="outline"
                  className="w-full h-12 sm:h-16 p-2 sm:p-4 rounded-md sm:rounded-lg hover:bg-blue-50 border-blue-200 hover:border-blue-300 text-xs sm:text-base"
                >
                  <div className="text-center">
                    <div className="font-medium text-blue-700 text-sm sm:text-base">2025 Trends</div>
                    <div className="text-[11px] sm:text-xs text-blue-600">Future predictions & insights</div>
                  </div>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 