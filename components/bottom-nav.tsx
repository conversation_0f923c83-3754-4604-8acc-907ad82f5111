"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Search, Star } from "lucide-react"

const navItems = [
  { name: "Home", href: "/", icon: Home },
  { name: "Search", href: "/", icon: Search },
  { name: "Favorites", href: "/favorites", icon: Star },
]

export default function BottomNav() {
  const pathname = usePathname()

  if (pathname === "/" || pathname === "/favorites") {
    return null
  }

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t border-pink-100 dark:border-pink-900/20 md:hidden flex justify-around items-center h-14 shadow-lg">
      {navItems.map((item) => {
        const Icon = item.icon
        const isActive = pathname === item.href || (item.href === "/" && pathname === "/")
        return (
          <Link
            key={item.href + '-' + item.name}
            href={item.href}
            className={`flex flex-col items-center justify-center text-xs font-medium transition-colors px-2 py-1 ${isActive ? "text-pink-500" : "text-muted-foreground"}`}
          >
            <Icon className="h-5 w-5 mb-0.5" />
            {item.name}
          </Link>
        )
      })}
    </nav>
  )
} 