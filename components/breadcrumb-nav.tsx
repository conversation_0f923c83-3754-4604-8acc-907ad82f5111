"use client"

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>read<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb"
import { navigationConfig } from "@/lib/navigation-config"
import { Home } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

interface BreadcrumbItem {
  label: string
  href?: string
  isCurrent?: boolean
}

export default function IBreadcrumbNav() {
  const pathname = usePathname()

  // Don't show breadcrumbs on home page
  if (pathname === "/") {
    return null
  }

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split("/").filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []

    // Always add Home as first item
    breadcrumbs.push({
      label: "Home",
      href: "/"
    })

    // Handle individual name pages (/name/[language]/[gender]/[religion]/[slug])
    if (segments[0] === 'name' && segments.length >= 4) {
      const language = decodeURIComponent(segments[1]);
      const gender = decodeURIComponent(segments[2]);
      const nameSlug = decodeURIComponent(segments[segments.length - 1]);

      const languageConfig = navigationConfig.countries
        .flatMap(c => c.languages)
        .find(l => l.name.toLowerCase() === language.toLowerCase());

      let listPageHref: string | undefined = undefined;
      let listPageLabel: string | undefined = undefined;

      if (languageConfig) {
        if (gender === 'boy') {
          listPageHref = languageConfig.boyHref;
          listPageLabel = `${languageConfig.name} Boy Names`;
        } else if (gender === 'girl') {
          listPageHref = languageConfig.girlHref;
          listPageLabel = `${languageConfig.name} Girl Names`;
        }
      }

      if (listPageHref && listPageLabel) {
        breadcrumbs.push({
          label: listPageLabel,
          href: listPageHref,
        });
      }

      breadcrumbs.push({
        label: nameSlug.replace(/-/g, ' '),
        isCurrent: true,
      });

      return breadcrumbs;
    }

    // For all other pages, create simple two-level breadcrumbs
    const lastSegment = segments[segments.length - 1]
    let categoryLabel = ""
    let categoryHref = ""

    // Handle different page types
    if (lastSegment === "blog") {
      categoryLabel = "Blog"
      categoryHref = "/blog"
    } else if (lastSegment.includes("-boy-names") || lastSegment.includes("-girl-names")) {
      // Handle name category pages like "english-boy-names", "christian-girl-names"
      const parts = lastSegment.split("-")
      if (parts.length >= 2) {
        const firstPart = parts[0].charAt(0).toUpperCase() + parts[0].slice(1)
        const gender = parts[1] === "boy" ? "Boy" : "Girl"
        categoryLabel = `${firstPart} ${gender} Names`
      } else {
        categoryLabel = lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1)
      }
      categoryHref = pathname
    } else if (segments.includes("blog") && segments.length > 1) {
      // Blog post pages
      categoryLabel = "Blog"
      categoryHref = "/blog"

      // Add current post title as the final breadcrumb
      const postTitle = lastSegment
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")

      breadcrumbs.push({
        label: categoryLabel,
        href: categoryHref
      })

      breadcrumbs.push({
        label: postTitle,
        isCurrent: true
      })

      return breadcrumbs
    } else {
      // Default formatting for other pages
      categoryLabel = lastSegment
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
      categoryHref = pathname
    }

    // Add the category page as the second level
    breadcrumbs.push({
      label: categoryLabel,
      isCurrent: true
    })

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  return (
    <div className="bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700">
      <div className="container mx-auto px-4 py-2">
        <Breadcrumb>
          <BreadcrumbList className="flex-wrap items-center gap-1 text-xs">
            {breadcrumbs.map((item, index) => (
              <BreadcrumbItem key={index} className="flex items-center">
                {item.isCurrent ? (
                  <BreadcrumbPage className="font-medium text-gray-900 dark:text-gray-100">
                    <span className="truncate max-w-[150px]">{item.label}</span>
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link
                      href={item.href || "#"}
                      className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors px-2 py-1 rounded-md hover:bg-white dark:hover:bg-gray-800"
                    >
                      {index === 0 ? (
                        <div className="flex items-center gap-1">
                          <Home className="h-3 w-3" />
                          <span className="hidden sm:inline">{item.label}</span>
                        </div>
                      ) : (
                        <span className="truncate max-w-[120px]">{item.label}</span>
                      )}
                    </Link>
                  </BreadcrumbLink>
                )}
                {index < breadcrumbs.length - 1 && (
                  <BreadcrumbSeparator className="text-gray-400 mx-1" />
                )}
              </BreadcrumbItem>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </div>
  )
} 