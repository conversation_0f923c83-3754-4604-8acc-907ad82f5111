"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useTranslation } from "@/hooks/use-translation"
import { cn } from "@/lib/utils"
import type { NameData } from "@/types/name-data"
import { Heart, Share2 } from "lucide-react"

interface NameCardProps {
  name: NameData
  isFavorite: boolean
  onToggleFavorite: () => void
  onClick: () => void
  colorTheme?: "pink" | "orange" | "green" | "blue" | "yellow" | "red" | "purple" | "indigo"
  compact?: boolean
  className?: string
  nameTitleClass?: string
}

export default function NameCard({
  name,
  isFavorite,
  onToggleFavorite,
  onClick,
  colorTheme = "pink",
  compact = false,
  className = "",
  nameTitleClass = "",
}: NameCardProps) {
  const { t, currentLanguage } = useTranslation()

  const colorClasses = {
    card: {
      pink: "hover:border-pink-200 dark:hover:border-pink-800",
      orange: "hover:border-orange-200 dark:hover:border-orange-800",
      green: "hover:border-green-200 dark:hover:border-green-800",
      blue: "hover:border-blue-200 dark:hover:border-blue-800",
      yellow: "hover:border-yellow-200 dark:hover:border-yellow-800",
      red: "hover:border-red-200 dark:hover:border-red-800",
      purple: "hover:border-purple-200 dark:hover:border-purple-800",
      indigo: "hover:border-indigo-200 dark:hover:border-indigo-800",
    },
    badge: {
      pink: "bg-pink-50 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300",
      orange: "bg-orange-50 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300",
      green: "bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300",
      blue: "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
      yellow: "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300",
      red: "bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-300",
      purple: "bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
      indigo: "bg-indigo-50 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300",
    },
    heart: {
      pink: "fill-pink-500 text-pink-500",
      orange: "fill-orange-500 text-orange-500",
      green: "fill-green-500 text-green-500",
      blue: "fill-blue-500 text-blue-500",
      yellow: "fill-yellow-500 text-yellow-500",
      red: "fill-red-500 text-red-500",
      purple: "fill-purple-500 text-purple-500",
      indigo: "fill-indigo-500 text-indigo-500",
    },
  }

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation()

    if (navigator.share) {
      navigator.share({
        title: `${name.name_en} - ${t("indianBabyName")}`,
        text: `${t("checkOutName")}: ${name.name_en} - ${name.meaning_en}`,
        url: `${window.location.origin}/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`,
      })
    }
  }

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleFavorite()
  }

  if (compact) {
    return (
      <Card
        className={cn(
          "overflow-hidden hover:shadow-sm transition-all cursor-pointer border h-full",
          colorClasses.card[colorTheme],
          className
        )}
        onClick={onClick}
      >
        <CardContent className="p-3 h-full flex flex-col">
          <div className="flex justify-between items-start mb-2">
            <div className="flex-1 min-w-0">
              <h3 className={cn("font-semibold text-sm md:text-base truncate", nameTitleClass)}>{name.name_en}</h3>
              <p className="text-xs text-muted-foreground truncate">
                {name.name_native === name.name_en && name.pronunciation
                  ? name.pronunciation
                  : name.name_native !== name.name_en
                    ? name.name_native
                    : name.origin || name.language}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 flex-shrink-0 ml-1"
              onClick={handleFavoriteClick}
              aria-label={isFavorite ? t("removeFromFavorites") : t("addToFavorites")}
            >
              <Heart className={`h-3 w-3 ${isFavorite ? colorClasses.heart[colorTheme] : "text-muted-foreground"}`} />
            </Button>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2 leading-tight">
              {currentLanguage === "en" ? name.meaning_en : name.meaning_native}
            </p>
          </div>
          {name.meaning_native && name.meaning_native !== name.meaning_en && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 line-clamp-2">
              {currentLanguage === "en" ? name.meaning_native : name.meaning_en}
            </p>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card
      className={cn(
        "overflow-hidden hover:shadow-lg transition-all cursor-pointer border rounded-2xl p-1 bg-white dark:bg-zinc-900",
        colorClasses.card[colorTheme],
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-dashed">
        <CardTitle className="text-2xl font-bold tracking-tight">
          {name.name_en}
          <span className="text-base font-normal text-muted-foreground ml-2">
            ({t(name.gender as "male" | "female" | "unisex")})
          </span>
        </CardTitle>
        <div className="flex gap-2 items-center">
          <Button variant="ghost" size="icon" onClick={handleShare} aria-label={t("shareName")}
            className="hover:bg-gray-100 dark:hover:bg-zinc-800 rounded-full">
            <Share2 className="h-5 w-5 text-muted-foreground" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleFavoriteClick}
            aria-label={isFavorite ? t("removeFromFavorites") : t("addToFavorites")}
            className="hover:bg-gray-100 dark:hover:bg-zinc-800 rounded-full"
          >
            <Heart className={`h-5 w-5 ${isFavorite ? colorClasses.heart[colorTheme] : "text-muted-foreground"}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-3 pb-4 px-2">
        <div className="grid gap-2">
          <div className="text-xl font-semibold text-gray-900 dark:text-white mb-1 tracking-wide">
            {name.name_native}
          </div>
          <div className="text-sm text-gray-700 dark:text-gray-200 mb-2">
            <span className="font-medium text-gray-800 dark:text-gray-100">{t("meaning")}:</span>{" "}
            {name.meaning_en}
            {name.meaning_native && name.meaning_native !== name.meaning_en && (
              <div className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                <span className="font-medium">Meaning (Native):</span> {name.meaning_native}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Skeleton for loading state
export function NameCardSkeleton({ compact = false }: { compact?: boolean }) {
  if (compact) {
    return (
      <div className="overflow-hidden border rounded-md h-full bg-white dark:bg-zinc-900">
        <div className="p-3 h-full flex flex-col">
          <div className="flex justify-between items-start mb-2">
            <div className="flex-1 min-w-0">
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-3 w-16" />
            </div>
            <Skeleton className="h-6 w-6 ml-1 rounded-full" />
          </div>
          <div className="flex-1">
            <Skeleton className="h-3 w-full mb-1" />
            <Skeleton className="h-3 w-2/3" />
          </div>
        </div>
      </div>
    )
  }
  // Full card skeleton (not compact)
  return (
    <div className="overflow-hidden border rounded-2xl p-1 bg-white dark:bg-zinc-900">
      <div className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-dashed px-4 pt-4">
        <div className="flex-1">
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-20" />
        </div>
        <div className="flex gap-2 items-center">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>
      <div className="pt-3 pb-4 px-4">
        <Skeleton className="h-5 w-24 mb-2" />
        <Skeleton className="h-4 w-40 mb-2" />
        <Skeleton className="h-3 w-32" />
      </div>
    </div>
  )
}
