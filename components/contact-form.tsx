"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import emailjs from "@emailjs/browser"
import { AlertCircle, CheckCircle, Loader2, MessageSquare } from "lucide-react"
import { useEffect, useState } from "react"

interface FormData {
    firstName: string
    lastName: string
    email: string
    subject: string
    message: string
}

interface FormStatus {
    type: "idle" | "loading" | "success" | "error"
    message: string
}

export default function ContactForm() {
    const [formData, setFormData] = useState<FormData>({
        firstName: "",
        lastName: "",
        email: "",
        subject: "General Inquiry",
        message: ""
    })

    const [status, setStatus] = useState<FormStatus>({
        type: "idle",
        message: ""
    })

    // Initialize EmailJS
    useEffect(() => {
        // Initialize EmailJS with your public key
        const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || "YOUR_EMAILJS_PUBLIC_KEY"
        if (publicKey !== "YOUR_EMAILJS_PUBLIC_KEY") {
            emailjs.init(publicKey)
        }
    }, [])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({
            ...prev,
            [name]: value
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        // Validate form
        if (!formData.firstName || !formData.lastName || !formData.email || !formData.message) {
            setStatus({
                type: "error",
                message: "Please fill in all required fields."
            })
            return
        }

        if (!formData.email.includes("@")) {
            setStatus({
                type: "error",
                message: "Please enter a valid email address."
            })
            return
        }

        setStatus({
            type: "loading",
            message: "Sending your message..."
        })

        try {
            // EmailJS configuration - Use environment variables or fallback to placeholders
            const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || "YOUR_EMAILJS_SERVICE_ID"
            const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || "YOUR_EMAILJS_TEMPLATE_ID"
            const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || "YOUR_EMAILJS_PUBLIC_KEY"

            // Check if EmailJS is properly configured
            if (serviceId === "YOUR_EMAILJS_SERVICE_ID" || templateId === "YOUR_EMAILJS_TEMPLATE_ID" || publicKey === "YOUR_EMAILJS_PUBLIC_KEY") {
                throw new Error("EmailJS not configured. Please set up your EmailJS credentials.")
            }

            // Get current time
            const now = new Date()
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short'
            })

            const templateParams = {
                name: `${formData.firstName} ${formData.lastName}`,
                time: timeString,
                message: formData.message,
                email: formData.email,
                subject: formData.subject
            }

            // Send email using EmailJS
            const result = await emailjs.send(serviceId, templateId, templateParams, publicKey)

            if (result.status === 200) {
                setStatus({
                    type: "success",
                    message: "Thank you! Your message has been sent successfully. We'll get back to you soon."
                })

                // Reset form
                setFormData({
                    firstName: "",
                    lastName: "",
                    email: "",
                    subject: "General Inquiry",
                    message: ""
                })
            } else {
                throw new Error("Failed to send email")
            }

        } catch (error) {
            console.error("EmailJS error:", error)

            // Provide more specific error messages
            let errorMessage = "Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>"

            if (error instanceof Error) {
                if (error.message.includes("not configured")) {
                    errorMessage = "Contact form is not configured yet. Please contact us <NAME_EMAIL>"
                } else if (error.message.includes("Failed to fetch")) {
                    errorMessage = "Network error. Please check your internet connection and try again."
                }
            }

            setStatus({
                type: "error",
                message: errorMessage
            })
        }
    }

    const getStatusIcon = () => {
        switch (status.type) {
            case "success":
                return <CheckCircle className="h-4 w-4 text-green-600" />
            case "error":
                return <AlertCircle className="h-4 w-4 text-red-600" />
            default:
                return null
        }
    }

    const getStatusColor = () => {
        switch (status.type) {
            case "success":
                return "text-green-600 bg-green-50 border-green-200"
            case "error":
                return "text-red-600 bg-red-50 border-red-200"
            default:
                return "text-blue-600 bg-blue-50 border-blue-200"
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Send us a Message
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">
                                First Name <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                name="firstName"
                                value={formData.firstName}
                                onChange={handleInputChange}
                                className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Your first name"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1">
                                Last Name <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                name="lastName"
                                value={formData.lastName}
                                onChange={handleInputChange}
                                className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Your last name"
                                required
                            />
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Email <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="<EMAIL>"
                            required
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium mb-1">Subject</label>
                        <select
                            name="subject"
                            value={formData.subject}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value="General Inquiry">General Inquiry</option>
                            <option value="Name Suggestion">Name Suggestion</option>
                            <option value="Technical Issue">Technical Issue</option>
                            <option value="Privacy Concern">Privacy Concern</option>
                            <option value="Business Partnership">Business Partnership</option>
                            <option value="Feedback">Feedback</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium mb-1">
                            Message <span className="text-red-500">*</span>
                        </label>
                        <textarea
                            name="message"
                            value={formData.message}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md h-32 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Tell us how we can help you..."
                            required
                        ></textarea>
                    </div>

                    {/* Status Message */}
                    {(status.type === "success" || status.type === "error") && (
                        <div className={`p-3 rounded-md border flex items-center gap-2 ${getStatusColor()}`}>
                            {getStatusIcon()}
                            <span className="text-sm">{status.message}</span>
                        </div>
                    )}

                    <Button
                        type="submit"
                        disabled={status.type === "loading"}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {status.type === "loading" ? (
                            <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Sending Message...
                            </>
                        ) : (
                            "Send Message"
                        )}
                    </Button>
                </form>
            </CardContent>
        </Card>
    )
} 