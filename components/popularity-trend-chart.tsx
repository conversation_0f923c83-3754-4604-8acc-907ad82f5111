"use client"

import ChartErrorBoundary from '@/components/chart-error-boundary'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { NameData } from '@/types/name-data'
import { BarChart3, ExternalLink, TrendingUp } from 'lucide-react'
import dynamic from 'next/dynamic'

interface PopularityTrendChartProps {
    nameData: NameData
    className?: string
}

// Loading component
const ChartSkeleton = () => (
    <div className="h-48 sm:h-64 w-full bg-gray-100 rounded-lg animate-pulse flex items-center justify-center">
        <div className="text-gray-400 flex items-center gap-2 text-sm sm:text-base">
            <BarChart3 className="h-4 w-4 sm:h-6 sm:w-6" />
            <span>Loading chart...</span>
        </div>
    </div>
)

// Dynamic chart component to avoid SSR issues
const DynamicChart = dynamic(() => import('./chart-components/trend-chart'), {
    loading: () => <ChartSkeleton />,
    ssr: false
})

// Client-side only wrapper
const ClientOnlyChart = dynamic(() => Promise.resolve(DynamicChart), {
    loading: () => <ChartSkeleton />,
    ssr: false
})

export default function PopularityTrendChart({ nameData, className }: PopularityTrendChartProps) {
    // Only show chart for USA names with historical data
    if (!nameData.historical_data || Object.keys(nameData.historical_data).length === 0) {
        return null
    }

    // Convert historical data to chart format
    const chartData = Object.entries(nameData.historical_data)
        .map(([year, count]) => ({
            year: parseInt(year),
            count: count,
            displayYear: year
        }))
        .sort((a, b) => a.year - b.year)

    if (chartData.length === 0) {
        return null
    }

    // Calculate trend direction
    const firstCount = chartData[0]?.count || 0
    const lastCount = chartData[chartData.length - 1]?.count || 0
    const trendDirection = lastCount > firstCount ? 'rising' : lastCount < firstCount ? 'falling' : 'stable'

    // Get peak data
    const peakData = chartData.reduce((max, current) =>
        current.count > max.count ? current : max, chartData[0] || { year: 0, count: 0 }
    )

    const getTrendIcon = () => {
        switch (trendDirection) {
            case 'rising':
                return <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
            case 'falling':
                return <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-red-600 rotate-180" />
            default:
                return <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
        }
    }

    const getTrendText = () => {
        switch (trendDirection) {
            case 'rising':
                return 'Rising Trend'
            case 'falling':
                return 'Declining Trend'
            default:
                return 'Stable Trend'
        }
    }

    const getTrendColor = () => {
        switch (trendDirection) {
            case 'rising':
                return 'text-green-600'
            case 'falling':
                return 'text-red-600'
            default:
                return 'text-blue-600'
        }
    }

    return (
        <Card className={`w-full ${className || ''}`}>
            <CardHeader className="pb-3 sm:pb-4">
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-0">
                    <div className="flex-1">
                        <CardTitle className="text-base sm:text-lg lg:text-xl font-bold text-gray-800 flex items-center gap-2">
                            <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                            Popularity Trend Chart
                        </CardTitle>
                        <p className="text-xs sm:text-sm text-gray-600 mt-1">
                            Roll over the visual for detailed rankings by year.
                        </p>
                    </div>
                    <div className="flex items-center gap-2 text-xs sm:text-sm">
                        {getTrendIcon()}
                        <span className={`font-medium ${getTrendColor()}`}>
                            {getTrendText()}
                        </span>
                    </div>
                </div>

                {/* Legend */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mt-2 sm:mt-3 pt-2 sm:pt-3 border-t border-gray-100">
                    <div className="flex items-center gap-2">
                        <div className={`w-3 sm:w-4 h-0.5 rounded`}
                            style={{ backgroundColor: nameData.gender === 'girl' ? '#ec4899' : '#3b82f6' }}></div>
                        <span className="text-xs sm:text-sm text-gray-600 capitalize">{nameData.gender}</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-3 sm:w-4 h-0.5 bg-gray-800 rounded"></div>
                        <span className="text-xs sm:text-sm text-gray-600">Peak: {peakData.year} ({peakData.count.toLocaleString()} babies)</span>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="pb-4 sm:pb-6 px-1 sm:px-2 lg:px-4">
                <div className="h-48 sm:h-64 w-full">
                    <ChartErrorBoundary>
                        <ClientOnlyChart
                            data={chartData}
                            nameData={nameData}
                        />
                    </ChartErrorBoundary>
                </div>

                {/* Source Attribution */}
                <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-100">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                            <span>Source: </span>
                            <a
                                href="https://www.ssa.gov/oact/babynames"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline flex items-center gap-1"
                            >
                                Social Security Administration
                                <ExternalLink className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                            </a>
                        </div>
                        <div className="text-left sm:text-right">
                            <span>Data Range: {chartData[0]?.year} - {chartData[chartData.length - 1]?.year}</span>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
} 