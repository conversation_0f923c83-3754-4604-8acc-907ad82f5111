"use client"

import { useState, useEffect, useCallback } from "react"
import { Search, X, Globe, Users, Sparkles } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { getRelevantFilters, languageDisplayNames, religionDisplayNames } from "@/lib/language-detection"
import { cn } from "@/lib/utils"

interface EnhancedSearchProps {
  onSearch: (query: string) => void
  onLanguageFilter?: (language: string | null) => void
  onGenderFilter?: (gender: string | null) => void
  onReligionFilter?: (religion: string | null) => void
  className?: string
  placeholder?: string
  showSmartFeatures?: boolean
}

export function EnhancedSearch({
  onSearch,
  onLanguageFilter,
  onGenderFilter,
  onReligionFilter,
  className,
  placeholder = "Search names in any language...",
  showSmartFeatures = true
}: EnhancedSearchProps) {
  const [query, setQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [detectedFilters, setDetectedFilters] = useState<any>(null)
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Debounced search
  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      if (searchQuery.trim()) {
        setIsSearching(true)
        onSearch(searchQuery)
        setIsSearching(false)
      }
    }, 300),
    [onSearch]
  )

  // Handle input change
  const handleInputChange = (value: string) => {
    setQuery(value)
    
    if (value.trim()) {
      // Detect language and gender
      const filters = getRelevantFilters(value)
      setDetectedFilters(filters)
      
      // Apply smart filters
      if (showSmartFeatures) {
        if (filters.detectedLanguage && filters.languageConfidence > 30) {
          onLanguageFilter?.(filters.detectedLanguage)
        }
        if (filters.detectedGender && filters.genderConfidence > 30) {
          onGenderFilter?.(filters.detectedGender)
        }
      }
      
      debouncedSearch(value)
    } else {
      setDetectedFilters(null)
      onSearch("")
    }
  }

  // Clear search
  const clearSearch = () => {
    setQuery("")
    setDetectedFilters(null)
    onSearch("")
    onLanguageFilter?.(null)
    onGenderFilter?.(null)
    onReligionFilter?.(null)
  }

  // Search suggestions
  const searchSuggestions = [
    "Gujarati boy names",
    "Hindi girl names",
    "Muslim baby names",
    "Sikh names",
    "Christian names",
    "Tamil names",
    "Urdu names",
    "Bengali names",
    "Marathi names"
  ]

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          placeholder={placeholder}
          className="pl-10 pr-10 h-12 text-lg"
          onFocus={() => setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
        {isSearching && (
          <div className="absolute right-10 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {/* Smart Detection Banner */}
      {detectedFilters && showSmartFeatures && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">Smart Detection</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {detectedFilters.detectedLanguage && (
                <Badge variant="secondary" className="gap-1">
                  <Globe className="h-3 w-3" />
                  {languageDisplayNames[detectedFilters.detectedLanguage] || detectedFilters.detectedLanguage}
                  <span className="text-xs opacity-70">
                    ({Math.round(detectedFilters.languageConfidence)}%)
                  </span>
                </Badge>
              )}
              {detectedFilters.detectedGender && (
                <Badge variant="secondary" className="gap-1">
                  <Users className="h-3 w-3" />
                  {detectedFilters.detectedGender}
                  <span className="text-xs opacity-70">
                    ({Math.round(detectedFilters.genderConfidence)}%)
                  </span>
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Filters applied automatically based on your search
            </p>
          </CardContent>
        </Card>
      )}

      {/* Search Suggestions */}
      {showSuggestions && !query && (
        <Card>
          <CardContent className="p-4">
            <h4 className="text-sm font-medium mb-3">Popular Searches</h4>
            <div className="flex flex-wrap gap-2">
              {searchSuggestions.map((suggestion) => (
                <Button
                  key={suggestion}
                  variant="outline"
                  size="sm"
                  onClick={() => handleInputChange(suggestion)}
                  className="text-xs"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Tips */}
      {!query && (
        <div className="text-xs text-muted-foreground space-y-1">
          <p>💡 <strong>Search Tips:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Type in any language script (Gujarati, Hindi, Arabic, etc.)</li>
            <li>Search by meaning or characteristics</li>
            <li>Add "boy" or "girl" to filter by gender</li>
            <li>Include religion names for specific results</li>
          </ul>
        </div>
      )}
    </div>
  )
}

// Debounce utility
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
} 