import Script from 'next/script'
import { generateFAQStructuredData } from './seo-structured-data'

interface FAQ {
    question: string
    answer: string
}

interface FAQSectionProps {
    /**
     * Array of FAQ objects to display.
     */
    faqs: FAQ[]
    /**
     * Optional section heading. Defaults to "Frequently Asked Questions".
     */
    heading?: string
    /**
     * Disable JSON-LD injection if you only want visual output.
     * Enabled by default.
     */
    injectStructuredData?: boolean
    /**
     * Optional CSS className for the wrapping <section>.
     */
    className?: string
}

export function FAQSection({
    faqs,
    heading = 'Frequently Asked Questions',
    injectStructuredData = true,
    className = 'my-12',
}: FAQSectionProps) {
    if (!faqs || faqs.length === 0) return null

    const structuredData = injectStructuredData
        ? generateFAQStructuredData(faqs)
        : null

    return (
        <section className={className}>
            <h2 className="text-2xl font-bold mb-6">{heading}</h2>

            <div className="space-y-6">
                {faqs.map((faq, idx) => (
                    <div key={idx} className="bg-muted/50 p-4 rounded-lg">
                        <h3 className="font-semibold mb-2">{faq.question}</h3>
                        <p className="text-muted-foreground">{faq.answer}</p>
                    </div>
                ))}
            </div>

            {injectStructuredData && structuredData && (
                <Script
                    id="faq-structured-data"
                    type="application/ld+json"
                    strategy="beforeInteractive"
                    dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
                />
            )}
        </section>
    )
} 