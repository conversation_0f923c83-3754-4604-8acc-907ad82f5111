"use client"

import React from 'react'

interface ChartErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ChartErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

class ChartErrorBoundary extends React.Component<ChartErrorBoundaryProps, ChartErrorBoundaryState> {
  constructor(props: ChartErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Chart Error Boundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="h-64 w-full bg-gray-50 rounded-lg flex items-center justify-center border border-gray-200">
          <div className="text-center p-6">
            <div className="text-gray-400 mb-2">📊</div>
            <p className="text-gray-600 text-sm">Chart temporarily unavailable</p>
            <p className="text-gray-500 text-xs mt-1">Data is still accessible below</p>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ChartErrorBoundary
