"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getDisplayText, rashiData } from "@/lib/astrological-data"
import { languageDisplayNames, religionDisplayNames } from "@/lib/language-detection"
import { cn } from "@/lib/utils"
import { Eye, EyeOff, Filter, Globe, Heart, Star, Users, X } from "lucide-react"
import { useEffect, useState } from "react"

interface EnhancedFilterPanelProps {
  languages: string[]
  religions: string[]
  genders: string[]
  startingLetters: string[]
  selectedLanguage: string | null
  selectedReligion: string | null
  selectedGender: string | null
  selectedStartingLetter: string | null
  selectedRashi: string | null
  onLanguageChange: (language: string | null) => void
  onReligionChange: (religion: string | null) => void
  onGenderChange: (gender: string | null) => void
  onStartingLetterChange: (letter: string | null) => void
  onRashiChange: (rashi: string | null) => void
  onClearAll: () => void
  className?: string
  hideLanguageFilters?: boolean
  hideGenderFilters?: boolean
  hideReligionFilters?: boolean
  showSmartHiding?: boolean
}

export function EnhancedFilterPanel({
  languages,
  religions,
  genders,
  startingLetters,
  selectedLanguage,
  selectedReligion,
  selectedGender,
  selectedStartingLetter,
  selectedRashi,
  onLanguageChange,
  onReligionChange,
  onGenderChange,
  onStartingLetterChange,
  onRashiChange,
  onClearAll,
  className,
  hideLanguageFilters = false,
  hideGenderFilters = false,
  hideReligionFilters = true,
  showSmartHiding = true
}: EnhancedFilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [activeFilters, setActiveFilters] = useState(0)

  // Count active filters
  useEffect(() => {
    let count = 0
    if (selectedLanguage) count++
    if (!hideReligionFilters && selectedReligion) count++
    if (selectedGender) count++
    if (selectedStartingLetter) count++
    if (selectedRashi) count++
    setActiveFilters(count)
  }, [selectedLanguage, selectedReligion, selectedGender, selectedStartingLetter, selectedRashi])

  // Clear specific filter
  const clearFilter = (type: 'language' | 'religion' | 'gender' | 'startingLetter' | 'rashi') => {
    switch (type) {
      case 'language':
        onLanguageChange(null)
        break
      case 'religion':
        onReligionChange(null)
        break
      case 'gender':
        onGenderChange(null)
        break
      case 'startingLetter':
        onStartingLetterChange(null)
        break
      case 'rashi':
        onRashiChange(null)
        break
    }
  }

  // Get display name for language
  const getLanguageDisplayName = (lang: string) => {
    return languageDisplayNames[lang.toLowerCase()] || lang
  }

  // Get display name for religion
  const getReligionDisplayName = (religion: string) => {
    return religionDisplayNames[religion.toLowerCase()] || religion
  }

  const isHinduReligion = selectedReligion === "Hindu"

  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <h3 className="font-medium">Filters</h3>
          {activeFilters > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFilters} active
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
          {activeFilters > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearAll}
              className="text-red-500 hover:text-red-700"
            >
              Clear All
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters */}
      {activeFilters > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedLanguage && (
            <Badge variant="outline" className="gap-1 bg-blue-50 text-blue-700 border-blue-200">
              <Globe className="h-3 w-3" />
              {getLanguageDisplayName(selectedLanguage)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('language')}
                className="h-3 w-3 p-0 ml-1"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          {selectedReligion && (
            <Badge variant="outline" className="gap-1 bg-green-50 text-green-700 border-green-200">
              <Heart className="h-3 w-3" />
              {getReligionDisplayName(selectedReligion)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('religion')}
                className="h-3 w-3 p-0 ml-1"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          {selectedGender && (
            <Badge variant="outline" className="gap-1 bg-pink-50 text-pink-700 border-pink-200">
              <Users className="h-3 w-3" />
              {selectedGender}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('gender')}
                className="h-3 w-3 p-0 ml-1"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          {selectedStartingLetter && (
            <Badge variant="outline" className="gap-1 bg-orange-50 text-orange-700 border-orange-200">
              Letter: {selectedStartingLetter}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('startingLetter')}
                className="h-3 w-3 p-0 ml-1"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          {selectedRashi && (
            <Badge variant="outline" className="gap-1 bg-purple-50 text-purple-700 border-purple-200">
              <Star className="h-3 w-3" />
              {selectedRashi}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('rashi')}
                className="h-3 w-3 p-0 ml-1"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Filter Options */}
      {isExpanded && (
        <Card>
          <CardContent className="p-4 space-y-4">
            {/* Language Filter */}
            {!hideLanguageFilters && languages.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-500" />
                  Language
                </label>
                <Select value={selectedLanguage ?? "all"} onValueChange={(value) => onLanguageChange(value === "all" ? null : value)}>
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Languages</SelectItem>
                    {languages.map((lang) => (
                      <SelectItem key={lang} value={lang} className="py-3 text-base">
                        {getLanguageDisplayName(lang)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Religion Filter */}
            {!hideReligionFilters && religions.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Heart className="h-4 w-4 text-green-500" />
                  Religion
                </label>
                <Select value={selectedReligion ?? "all"} onValueChange={(value) => onReligionChange(value === "all" ? null : value)}>
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="Select religion" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Religions</SelectItem>
                    {religions.map((religion) => (
                      <SelectItem key={religion} value={religion} className="py-3 text-base">
                        {getReligionDisplayName(religion)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Gender Filter */}
            {!hideGenderFilters && genders.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Users className="h-4 w-4 text-pink-500" />
                  Gender
                </label>
                <Select value={selectedGender ?? "all"} onValueChange={(value) => onGenderChange(value === "all" ? null : value)}>
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genders</SelectItem>
                    {genders.map((gender) => (
                      <SelectItem key={gender} value={gender} className="py-3 text-base">
                        {gender}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Starting Letter Filter */}
            {startingLetters.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Starting Letter</label>
                <Select value={selectedStartingLetter ?? "all"} onValueChange={(value) => onStartingLetterChange(value === "all" ? null : value)}>
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="Select starting letter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Letters</SelectItem>
                    {startingLetters.map((letter) => (
                      <SelectItem key={letter} value={letter} className="py-3 text-base">
                        {letter}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Astrological Filters - Only for Hindu religion */}
            {isHinduReligion && (
              <>
                {/* Rashi Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Star className="h-4 w-4 text-orange-500" />
                    Rashi (Zodiac Sign)
                  </label>
                  <Select value={selectedRashi ?? "all"} onValueChange={(value) => onRashiChange(value === "all" ? null : value)}>
                    <SelectTrigger className="h-12 text-base">
                      <SelectValue placeholder="Select rashi" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Rashis</SelectItem>
                      {rashiData.map((rashi) => (
                        <SelectItem key={rashi.value} value={rashi.value} className="py-3 text-base">
                          {getDisplayText(rashiData, rashi.value, selectedLanguage || "en")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* Smart Hiding Notice */}
      {showSmartHiding && (hideLanguageFilters || hideGenderFilters || hideReligionFilters) && (
        <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded-md">
          <p>💡 Some filters are hidden based on your search context</p>
        </div>
      )}
    </div>
  )
}

export default EnhancedFilterPanel; 