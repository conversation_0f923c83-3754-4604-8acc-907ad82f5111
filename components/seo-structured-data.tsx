import type { NameData } from "@/types/name-data";

interface SEOStructuredDataProps {
  names: NameData[];
  pageType: "name-list" | "name-detail" | "blog-post" | "tool";
  title: string;
  description: string;
  url: string;
  language?: string;
  country?: string;
  gender?: string;
  datePublished?: string; // optional, ISO string
  dateModified?: string;  // optional, ISO string
}

// Helper function to create safe URL slugs
function slugify(text: string): string {
  return encodeURIComponent(
    text
      .toLowerCase()
      .trim()
      .replace(/\s+/g, "-")
      .replace(/[^\w\-]+/g, "")
  );
}

export default function SEOStructuredData({
  names,
  pageType,
  title,
  description,
  url,
  language,
  country,
  gender,
  datePublished,
  dateModified,
}: SEOStructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://www.babynamediaries.com";
  const pageUrl = `${baseUrl}${url}`;
  const safeLanguage = language || "en-US";
  const safeCountry = country || "Names";
  const safeGender = gender || "";

  const generateStructuredData = () => {
    if (pageType === "name-list" && Array.isArray(names)) {
      return {
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "ItemList",
            "@id": `${pageUrl}#itemlist`,
            name: title,
            description,
            url: pageUrl,
            numberOfItems: names.length,
            itemListOrder: "https://schema.org/ItemListOrderAscending",
            itemListElement: names.slice(0, 50).map((name, index) => {
              const nameSlug = slugify(name.name_en);
              const languageSlug = slugify(name.language);
              const genderSlug = slugify(name.gender);
              const religionSlug = slugify(name.religion);
              const canonicalUrl = `${baseUrl}/name/${languageSlug}/${genderSlug}/${religionSlug}/${nameSlug}`;

              return {
                "@type": "ListItem",
                position: index + 1,
                item: {
                  "@type": "Person",
                  "@id": canonicalUrl,
                  name: name.name_en,
                  description: `${name.name_en} - ${name.meaning_en}`,
                  url: canonicalUrl,
                  gender: name.gender,
                  additionalProperty: [
                    {
                      "@type": "PropertyValue",
                      name: "Origin",
                      value: name.origin,
                    },
                    {
                      "@type": "PropertyValue",
                      name: "Meaning",
                      value: name.meaning_en,
                    },
                    {
                      "@type": "PropertyValue",
                      name: "Language",
                      value: name.language,
                    },
                  ],
                },
              };
            }),
          },
          {
            "@type": "WebPage",
            "@id": pageUrl,
            name: title,
            description,
            url: pageUrl,
            inLanguage: safeLanguage,
            isPartOf: {
              "@type": "WebSite",
              "@id": `${baseUrl}/#website`,
              name: "Baby Name Diaries",
              url: baseUrl,
              publisher: {
                "@type": "Organization",
                "@id": `${baseUrl}/#organization`,
                name: "Baby Name Diaries",
                url: baseUrl,
                logo: {
                  "@type": "ImageObject",
                  url: `${baseUrl}/icons/logo_full_hd.png`,
                  width: 512,
                  height: 512,
                },
              },
            },
            mainEntity: {
              "@type": "ItemList",
              name: `${safeLanguage} ${safeGender} Names`.trim(),
              numberOfItems: names.length,
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: baseUrl,
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: safeCountry,
                  item: `${baseUrl}/${safeCountry.toLowerCase()}`,
                },
                {
                  "@type": "ListItem",
                  position: 3,
                  name: title,
                  item: pageUrl,
                },
              ],
            },
            potentialAction: {
              "@type": "SearchAction",
              target: `${baseUrl}/search?q={search_term_string}`,
              "query-input": "required name=search_term_string",
            },
          },
        ],
      };
    }

    if (pageType === "name-detail" && names.length > 0) {
      const name = names[0];
      const nameSlug = slugify(name.name_en);
      const languageSlug = slugify(name.language);
      const genderSlug = slugify(name.gender);
      const religionSlug = slugify(name.religion);
      const canonicalUrl = `${baseUrl}/name/${languageSlug}/${genderSlug}/${religionSlug}/${nameSlug}`;

      return {
        "@context": "https://schema.org",
        "@type": "Article",
        "@id": pageUrl,
        headline: `${name.name_en} - ${name.meaning_en}`,
        description: `${name.name_en} is a ${name.gender} name of ${name.origin} origin meaning "${name.meaning_en}".`,
        url: pageUrl,
        datePublished: datePublished,
        dateModified: dateModified,
        author: {
          "@type": "Organization",
          "@id": `${baseUrl}/#organization`,
          name: "Baby Name Diaries",
          url: baseUrl,
        },
        publisher: {
          "@type": "Organization",
          "@id": `${baseUrl}/#organization`,
          name: "Baby Name Diaries",
          url: baseUrl,
          logo: {
            "@type": "ImageObject",
            url: `${baseUrl}/icons/logo_full_hd.png`,
            width: 512,
            height: 512,
          },
        },
        mainEntity: {
          "@type": "Person",
          "@id": `${pageUrl}#person`,
          name: name.name_en,
          description: name.meaning_en,
          gender: name.gender,
          additionalProperty: [
            {
              "@type": "PropertyValue",
              name: "Origin",
              value: name.origin,
            },
            {
              "@type": "PropertyValue",
              name: "Meaning",
              value: name.meaning_en,
            },
            {
              "@type": "PropertyValue",
              name: "Gender",
              value: name.gender,
            },
            {
              "@type": "PropertyValue",
              name: "Language",
              value: name.language,
            },
            {
              "@type": "PropertyValue",
              name: "Religion",
              value: name.religion,
            },
          ],
          url: canonicalUrl,
        },
        breadcrumb: {
          "@type": "BreadcrumbList",
          itemListElement: [
            {
              "@type": "ListItem",
              position: 1,
              name: "Home",
              item: baseUrl,
            },
            {
              "@type": "ListItem",
              position: 2,
              name: `${name.language} Names`,
              item: `${baseUrl}/${languageSlug}-names`,
            },
            {
              "@type": "ListItem",
              position: 3,
              name: name.name_en,
              item: pageUrl,
            },
          ],
        },
      };
    }

    if (pageType === "blog-post") {
      return {
        "@context": "https://schema.org",
        "@type": "Article",
        "@id": pageUrl,
        headline: title,
        description,
        url: pageUrl,
        datePublished: datePublished,
        dateModified: dateModified,
        author: {
          "@type": "Organization",
          "@id": `${baseUrl}/#organization`,
          name: "Baby Name Diaries",
          url: baseUrl,
        },
        publisher: {
          "@type": "Organization",
          "@id": `${baseUrl}/#organization`,
          name: "Baby Name Diaries",
          url: baseUrl,
          logo: {
            "@type": "ImageObject",
            url: `${baseUrl}/icons/logo_full_hd.png`,
            width: 512,
            height: 512,
          },
        },
        mainEntity: {
          "@type": "Article",
          name: title,
          description,
        },
        breadcrumb: {
          "@type": "BreadcrumbList",
          itemListElement: [
            {
              "@type": "ListItem",
              position: 1,
              name: "Home",
              item: baseUrl,
            },
            {
              "@type": "ListItem",
              position: 2,
              name: "Blog",
              item: `${baseUrl}/blog`,
            },
            {
              "@type": "ListItem",
              position: 3,
              name: title,
              item: pageUrl,
            },
          ],
        },
      };
    }

    if (pageType === "tool") {
      return {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "@id": pageUrl,
        name: title,
        description,
        url: pageUrl,
        applicationCategory: "LifestyleApplication",
        operatingSystem: "Web Browser",
        offers: {
          "@type": "Offer",
          price: "0",
          priceCurrency: "USD",
        },
        featureList: [
          "AI-powered name suggestions",
          "25,000+ names from 12 countries",
          "Personalized recommendations",
          "Cultural and religious filtering",
          "Name meanings and origins",
          "Trending name analysis",
        ],
        breadcrumb: {
          "@type": "BreadcrumbList",
          itemListElement: [
            {
              "@type": "ListItem",
              position: 1,
              name: "Home",
              item: baseUrl,
            },
            {
              "@type": "ListItem",
              position: 2,
              name: "Tools",
              item: `${baseUrl}/tools`,
            },
            {
              "@type": "ListItem",
              position: 3,
              name: title,
              item: pageUrl,
            },
          ],
        },
      };
    }

    return null;
  };

  const structuredData = generateStructuredData();

  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      // Pretty formatted JSON for easier debugging; remove spacing for production if needed
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData, null, 2) }}
    />
  );
}

// Helper function to generate breadcrumb structured data
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

// Helper function to generate FAQ structured data
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };
}
