"use client"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { getDisplayText, rashiData } from "@/lib/astrological-data"
import { cn } from "@/lib/utils"
import { Filter, Globe, Star, X } from "lucide-react"
import { useEffect } from "react"

interface SideFiltersProps {
  isOpen: boolean
  onClose: () => void
  activeFilters: {
    gender: string
    language: string
    religion: string
    letter: string
    rashi?: string
  }
  onFilterChange: (filterType: string, value: string) => void
  onClearFilters: () => void
  totalResults: number
  hideReligionFilter?: boolean
}

const languages = [
  { value: "all", label: "All Languages" },
  { value: "Gujarati", label: "Gujarati" },
  { value: "Hindi", label: "Hindi" },
  { value: "Tamil", label: "Tamil" },
  { value: "Urdu", label: "Urdu" },
  { value: "Punjabi", label: "Punjabi" },
  { value: "Bengali", label: "Bengali" },
  { value: "Marathi", label: "Marathi" },
]

const religions = [
  { value: "all", label: "All Religions" },
  { value: "Hindu", label: "Hindu" },
  { value: "Muslim", label: "Muslim" },
  { value: "Sikh", label: "Sikh" },
  { value: "Christian", label: "Christian" },
]

const genders = [
  { value: "all", label: "All Genders" },
  { value: "male", label: "Boy Names" },
  { value: "female", label: "Girl Names" },
  { value: "unisex", label: "Unisex Names" },
]

export default function SideFilters({
  isOpen,
  onClose,
  activeFilters,
  onFilterChange,
  onClearFilters,
  totalResults,
  hideReligionFilter,
}: SideFiltersProps) {
  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll on mobile
      document.body.style.overflow = 'hidden'
    } else {
      // Restore body scroll
      document.body.style.overflow = 'unset'
    }

    // Cleanup function to restore scroll when component unmounts
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const hasActiveFilters =
    activeFilters.gender !== "all" ||
    activeFilters.language !== "all" ||
    activeFilters.religion !== "all" ||
    activeFilters.letter !== "all" ||
    activeFilters.rashi !== "all"

  const isLanguageSpecific = activeFilters.language !== "all"
  const isHinduReligion = activeFilters.religion === "Hindu"

  // Get language display name
  const getLanguageDisplayName = (language: string) => {
    const lang = languages.find(l => l.value === language)
    return lang ? lang.label : language
  }

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed top-0 right-0 h-full w-80 max-w-xs bg-white dark:bg-zinc-900 border-l z-50 shadow-xl rounded-l-2xl transition-transform duration-300 ease-in-out lg:relative lg:transform-none lg:z-auto lg:shadow-none lg:rounded-none lg:bg-background",
        isOpen ? "translate-x-0" : "translate-x-full lg:translate-x-0"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-900/20 dark:to-purple-900/20 rounded-t-2xl lg:rounded-none">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              <h3 className="font-semibold">Filters</h3>
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-2">
                  {totalResults} results
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="lg:hidden"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Language Context Banner */}
          {isLanguageSpecific && (
            <div className="px-4 pt-4 pb-2">
              <div className="flex items-center gap-2 mb-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                <Globe className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-semibold text-blue-800">
                  Viewing {getLanguageDisplayName(activeFilters.language)} Names
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onFilterChange("language", "all")}
                className="text-xs h-7 bg-white hover:bg-blue-100 border-blue-300 text-blue-700 rounded-full shadow-sm"
              >
                See All Languages
              </Button>
            </div>
          )}

          {/* Filters Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* Language Filter - Only show if no specific language is selected */}
            {!isLanguageSpecific && (
              <>
                <div>
                  <h4 className="font-medium mb-3">Language</h4>
                  <div className="space-y-2">
                    {languages.map((lang) => (
                      <Button
                        key={lang.value}
                        variant={activeFilters.language === lang.value ? "default" : "outline"}
                        size="sm"
                        className="w-full justify-start rounded-lg"
                        onClick={() => onFilterChange("language", lang.value)}
                      >
                        {lang.label}
                      </Button>
                    ))}
                  </div>
                </div>
                <Separator className="my-4" />
              </>
            )}

            {/* Religion Filter */}
            {!hideReligionFilter && (
              <div>
                <h4 className="font-medium mb-3">Religion</h4>
                <div className="space-y-2">
                  {religions.map((religion) => (
                    <Button
                      key={religion.value}
                      variant={activeFilters.religion === religion.value ? "default" : "outline"}
                      size="sm"
                      className="w-full justify-start rounded-lg"
                      onClick={() => onFilterChange("religion", religion.value)}
                    >
                      {religion.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            <Separator className="my-4" />

            {/* Gender Filter */}
            <div>
              <h4 className="font-medium mb-3">Gender</h4>
              <div className="space-y-2">
                {genders.map((gender) => (
                  <Button
                    key={gender.value}
                    variant={activeFilters.gender === gender.value ? "default" : "outline"}
                    size="sm"
                    className="w-full justify-start rounded-lg"
                    onClick={() => onFilterChange("gender", gender.value)}
                  >
                    {gender.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Astrological Filters - Only show for Hindu religion */}
            {isHinduReligion && (
              <>
                <Separator className="my-4" />

                {/* Rashi Filter */}
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Star className="h-4 w-4 text-orange-500" />
                    Rashi (Zodiac Sign)
                  </h4>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    <Button
                      variant={activeFilters.rashi === "all" ? "default" : "outline"}
                      size="sm"
                      className="w-full justify-start rounded-lg"
                      onClick={() => onFilterChange("rashi", "all")}
                    >
                      All Rashis
                    </Button>
                    {rashiData.map((rashi) => (
                      <Button
                        key={rashi.value}
                        variant={activeFilters.rashi === rashi.value ? "default" : "outline"}
                        size="sm"
                        className="w-full justify-start rounded-lg text-sm"
                        onClick={() => onFilterChange("rashi", rashi.value)}
                      >
                        <div className="text-left text-sm">
                          {isLanguageSpecific
                            ? getDisplayText(rashiData, rashi.value, activeFilters.language)
                            : rashi.label}
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}

            <Separator className="my-4" />

            {/* Quick Language-Specific Section - Only show if a language is selected */}
            {isLanguageSpecific && (
              <div>
                <h4 className="font-medium mb-3">Popular {getLanguageDisplayName(activeFilters.language)} Names</h4>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      onFilterChange("language", activeFilters.language)
                      onFilterChange("gender", "male")
                    }}
                  >
                    {getLanguageDisplayName(activeFilters.language)} Boy Names
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      onFilterChange("language", activeFilters.language)
                      onFilterChange("gender", "female")
                    }}
                  >
                    {getLanguageDisplayName(activeFilters.language)} Girl Names
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      onFilterChange("language", activeFilters.language)
                      onFilterChange("religion", "Hindu")
                    }}
                  >
                    {getLanguageDisplayName(activeFilters.language)} Names
                  </Button>
                </div>
              </div>
            )}

            {/* Quick Gujarati Section - Only show if no language is selected */}
            {!isLanguageSpecific && (
              <div>
                <h4 className="font-medium mb-3">Popular Gujarati Names</h4>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      onFilterChange("language", "Gujarati")
                      onFilterChange("gender", "male")
                    }}
                  >
                    Gujarati Boy Names
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      onFilterChange("language", "Gujarati")
                      onFilterChange("gender", "female")
                    }}
                  >
                    Gujarati Girl Names
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      onFilterChange("language", "Gujarati")
                      onFilterChange("religion", "Hindu")
                    }}
                  >
                    Gujarati Names
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t">
            {hasActiveFilters && (
              <Button
                variant="outline"
                className="w-full"
                onClick={onClearFilters}
              >
                Clear All Filters
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
