"use client"

import AlphabetFilter from "@/components/alphabet-filter"
import NameCard, { NameCardSkeleton } from "@/components/name-card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { useTrendingSummary } from "@/hooks/use-top-names"
import type { NameData } from "@/types/name-data"
import { Heart } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

interface FilterOption {
  value: string
  label: string
}

interface HeaderLabels {
  title: string
  subtitle: string
  description: string
}

type ColorTheme = "orange" | "red" | "blue" | "green" | "yellow" | "purple" | "pink" | "indigo"

interface TrendingNamesPageProps {
  type: "global" | "regional" | "country"
  countries: string[]
  region?: string
  colorTheme: ColorTheme
  headerLabels: HeaderLabels
  seoContent?: React.ReactNode
  additionalContent?: React.ReactNode
  showCountryFilter?: boolean
  showTrendFilter?: boolean
  showAlphabetFilter?: boolean
}

const defaultStartingLetters = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"]

export default function TrendingNamesPage({
  type,
  countries,
  region,
  colorTheme,
  headerLabels,
  seoContent,
  additionalContent,
  showCountryFilter = true,
  showTrendFilter = true,
  showAlphabetFilter = true,
}: TrendingNamesPageProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [allNames, setAllNames] = useState<NameData[]>([])
  const [filteredNames, setFilteredNames] = useState<NameData[]>([])
  const [favorites, setFavorites] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [activeFilters, setActiveFilters] = useState({
    country: "all",
    trend: "all", // rising, falling, stable, new
    letter: "all",
    gender: "all"
  })
  const [isLoading, setIsLoading] = useState(true)

  // Fetch trending summary data
  const { data: trendingSummary, loading: trendingLoading } = useTrendingSummary({
    countries,
    autoFetch: true
  })

  // Color theme classes
  const colorMap: Record<ColorTheme, any> = {
    orange: {
      badge: "bg-orange-100 text-orange-700 hover:bg-orange-200",
      button: "bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700",
      tab: "data-[state=active]:bg-orange-100 data-[state=active]:text-orange-700",
      heart: "text-orange-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    red: {
      badge: "bg-red-100 text-red-700 hover:bg-red-200",
      button: "bg-red-50 hover:bg-red-100 border-red-200 text-red-700",
      tab: "data-[state=active]:bg-red-100 data-[state=active]:text-red-700",
      heart: "text-red-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    blue: {
      badge: "bg-blue-100 text-blue-700 hover:bg-blue-200",
      button: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
      tab: "data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700",
      heart: "text-blue-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    green: {
      badge: "bg-green-100 text-green-700 hover:bg-green-200",
      button: "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
      tab: "data-[state=active]:bg-green-100 data-[state=active]:text-green-700",
      heart: "text-green-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    yellow: {
      badge: "bg-yellow-100 text-yellow-700 hover:bg-yellow-200",
      button: "bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-700",
      tab: "data-[state=active]:bg-yellow-100 data-[state=active]:text-yellow-700",
      heart: "text-yellow-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    purple: {
      badge: "bg-purple-100 text-purple-700 hover:bg-purple-200",
      button: "bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700",
      tab: "data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700",
      heart: "text-purple-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    pink: {
      badge: "bg-pink-100 text-pink-700 hover:bg-pink-200",
      button: "bg-pink-50 hover:bg-pink-100 border-pink-200 text-pink-700",
      tab: "data-[state=active]:bg-pink-100 data-[state=active]:text-pink-700",
      heart: "text-pink-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    },
    indigo: {
      badge: "bg-indigo-100 text-indigo-700 hover:bg-indigo-200",
      button: "bg-indigo-50 hover:bg-indigo-100 border-indigo-200 text-indigo-700",
      tab: "data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700",
      heart: "text-indigo-300",
      rising: "bg-green-100 text-green-700",
      falling: "bg-red-100 text-red-700",
      stable: "bg-blue-100 text-blue-700",
      new: "bg-purple-100 text-purple-700"
    }
  }

  const theme = colorMap[colorTheme]

  // Load favorites from localStorage
  useEffect(() => {
    const loadFavorites = () => {
      const savedFavorites = localStorage.getItem("babyNameFavorites")
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites))
      }
    }
    loadFavorites()
  }, [])

  // Simulate fetching trending data from multiple countries
  useEffect(() => {
    const fetchTrendingData = async () => {
      setIsLoading(true)
      try {
        // This would be replaced with actual API calls to aggregate data from multiple countries
        // For now, simulate aggregated trending data
        const mockTrendingNames: NameData[] = [
          {
            name_en: "Liam",
            name_native: "Liam",
            gender: "boy",
            religion: "Christian",
            language: "English",
            meaning_en: "Strong-willed warrior",
            meaning_native: "Protector",
            starting_letter: "L",
            pronunciation: "LEE-am",
            origin: "Irish",
            popularity_rank: 1,
            popularity_change: "+15%",
            year_2025_rank: 1,
            year_2024_rank: 2,
            trending_status: "rising",
            search_volume: 95847,
            regional_popularity: {
              "usa": 1,
              "uk": 3,
              "canada": 2,
              "australia": 1
            }
          },
          {
            name_en: "Oliver",
            name_native: "Oliver",
            gender: "boy",
            religion: "Christian",
            language: "English",
            meaning_en: "Olive tree",
            meaning_native: "Peace",
            starting_letter: "O",
            pronunciation: "OH-li-ver",
            origin: "Latin",
            popularity_rank: 2,
            popularity_change: "+8%",
            year_2025_rank: 2,
            year_2024_rank: 4,
            trending_status: "rising",
            search_volume: 84200,
            regional_popularity: {
              "usa": 3,
              "uk": 1,
              "canada": 1,
              "australia": 2
            }
          },
          {
            name_en: "Emma",
            name_native: "Emma",
            gender: "girl",
            religion: "Christian",
            language: "English",
            meaning_en: "Universal",
            meaning_native: "Whole",
            starting_letter: "E",
            pronunciation: "EM-ah",
            origin: "Germanic",
            popularity_rank: 1,
            popularity_change: "+12%",
            year_2025_rank: 1,
            year_2024_rank: 2,
            trending_status: "rising",
            search_volume: 89500,
            regional_popularity: {
              "usa": 1,
              "uk": 2,
              "canada": 3,
              "australia": 1
            }
          },
          {
            name_en: "Olivia",
            name_native: "Olivia",
            gender: "girl",
            religion: "Christian",
            language: "English",
            meaning_en: "Olive tree",
            meaning_native: "Peace",
            starting_letter: "O",
            pronunciation: "oh-LIV-ee-ah",
            origin: "Latin",
            popularity_rank: 2,
            popularity_change: "+6%",
            year_2025_rank: 2,
            year_2024_rank: 3,
            trending_status: "rising",
            search_volume: 78400,
            regional_popularity: {
              "usa": 2,
              "uk": 1,
              "canada": 1,
              "australia": 2
            }
          },
          {
            name_en: "Aarav",
            name_native: "आरव",
            gender: "boy",
            religion: "Hindu",
            language: "Hindi",
            meaning_en: "Peaceful, calm",
            meaning_native: "शांत, शांतिपूर्ण",
            starting_letter: "A",
            pronunciation: "Aarav",
            origin: "Sanskrit",
            popularity_rank: 1,
            popularity_change: "+22%",
            year_2025_rank: 1,
            year_2024_rank: 3,
            trending_status: "rising",
            search_volume: 125000,
            regional_popularity: {
              "india": 1
            }
          },
          {
            name_en: "Aadhya",
            name_native: "आध्या",
            gender: "girl",
            religion: "Hindu",
            language: "Hindi",
            meaning_en: "First power",
            meaning_native: "प्रथम शक्ति",
            starting_letter: "A",
            pronunciation: "Aadhya",
            origin: "Sanskrit",
            popularity_rank: 1,
            popularity_change: "+18%",
            year_2025_rank: 1,
            year_2024_rank: 2,
            trending_status: "rising",
            search_volume: 98500,
            regional_popularity: {
              "india": 1
            }
          }
        ]

        setAllNames(mockTrendingNames)
        setFilteredNames(mockTrendingNames)
      } catch (error) {
        console.error("Error fetching trending data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTrendingData()
  }, [countries])

  // Apply filters
  useEffect(() => {
    let filtered = [...allNames]

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(name =>
        name.name_en.toLowerCase().includes(searchTerm.toLowerCase()) ||
        name.meaning_en.toLowerCase().includes(searchTerm.toLowerCase()) ||
        name.origin.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply trend filter
    if (activeFilters.trend !== "all") {
      filtered = filtered.filter(name => name.trending_status === activeFilters.trend)
    }

    // Apply letter filter
    if (activeFilters.letter !== "all") {
      filtered = filtered.filter(name => name.starting_letter === activeFilters.letter)
    }

    // Apply gender filter
    if (activeFilters.gender !== "all") {
      filtered = filtered.filter(name => name.gender === activeFilters.gender)
    }

    // Apply alphabetical sorting by default
    filtered.sort((a, b) => a.name_en.localeCompare(b.name_en))

    setFilteredNames(filtered)
  }, [allNames, searchTerm, activeFilters])

  // Toggle favorite status
  const toggleFavorite = (nameEn: string) => {
    const newFavorites = favorites.includes(nameEn)
      ? favorites.filter((name) => name !== nameEn)
      : [...favorites, nameEn]
    setFavorites(newFavorites)
    localStorage.setItem("babyNameFavorites", JSON.stringify(newFavorites))
  }

  // Get favorite names
  const getFavoriteNames = () => {
    return filteredNames.filter((name) => favorites.includes(name.name_en))
  }

  // Apply letter filter
  const handleLetterFilter = (letter: string) => {
    setActiveFilters({ ...activeFilters, letter: letter === activeFilters.letter ? "all" : letter })
  }

  // Handle filter changes
  const handleFilterChange = (filterType: string, value: string) => {
    setActiveFilters({ ...activeFilters, [filterType]: value })
  }

  // Clear all filters
  const clearAllFilters = () => {
    setActiveFilters({
      country: "all",
      trend: "all",
      letter: "all",
      gender: "all"
    })
    setSearchTerm("")
  }

  // Get trending stats
  const getTrendingStats = () => {
    const rising = filteredNames.filter(name => name.trending_status === "rising").length
    const falling = filteredNames.filter(name => name.trending_status === "falling").length
    const stable = filteredNames.filter(name => name.trending_status === "stable").length
    const newNames = filteredNames.filter(name => name.trending_status === "new").length

    return { rising, falling, stable, newNames }
  }

  const trendingStats = getTrendingStats()

  return (
    <main className="container mx-auto px-2 sm:px-4 py-4 sm:py-6 md:py-8 max-w-7xl">
      <div className="w-full">
        {/* Header - Responsive */}
        <div className="text-center mb-8">
          <h1 className={`text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold ${theme.badge.split(" ")[1]} mb-2 sm:mb-3 md:mb-4`}>
            {headerLabels.title}
          </h1>
          <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold text-gray-700 mb-1 sm:mb-2">
            {headerLabels.subtitle}
          </h2>
          <p className="text-xs sm:text-sm md:text-base text-gray-600 max-w-2xl mx-auto px-2">
            {headerLabels.description}
          </p>
        </div>

        {/* Filter Bar */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row flex-wrap gap-3">
            {showTrendFilter && (
              <select
                value={activeFilters.trend}
                onChange={(e) => handleFilterChange("trend", e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto focus:ring-2 focus:ring-blue-400"
              >
                <option value="all">All Trends</option>
                <option value="rising">Rising</option>
                <option value="falling">Falling</option>
                <option value="stable">Stable</option>
                <option value="new">New</option>
              </select>
            )}
            <select
              value={activeFilters.gender}
              onChange={(e) => handleFilterChange("gender", e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto focus:ring-2 focus:ring-blue-400"
            >
              <option value="all">All Genders</option>
              <option value="boy">Boy Names</option>
              <option value="girl">Girl Names</option>
            </select>
            {(activeFilters.trend !== "all" || activeFilters.gender !== "all" || activeFilters.letter !== "all" || searchTerm) && (
              <Button onClick={clearAllFilters} variant="outline" size="sm" className="w-full sm:w-auto">
                Clear Filters
              </Button>
            )}
          </div>
          {/* Alphabet filter */}
          {showAlphabetFilter && (
            <div className="mt-3">
              <AlphabetFilter activeLetter={activeFilters.letter} onLetterClick={handleLetterFilter} />
            </div>
          )}
        </div>

        {/* Divider for visual separation */}
        <hr className="my-4" />

        {/* Main content: Trending Names - Responsive Tabs */}
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="all" className={`${theme.tab} text-base`}>
              <span className="hidden sm:inline">All Trending Names</span>
              <span className="sm:hidden">All Names</span>
            </TabsTrigger>
            <TabsTrigger value="favorites" className={`${theme.tab} text-base flex items-center gap-2`}>
              <Heart className="h-4 w-4 text-pink-500" />
              Favorites
              {favorites.length > 0 && (
                <span className="inline-block bg-pink-100 text-pink-700 rounded-full px-2 py-0.5 text-xs ml-1">{favorites.length}</span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            {/* Active filters - Responsive */}
            {(activeFilters.trend !== "all" || activeFilters.gender !== "all" || activeFilters.letter !== "all" || searchTerm) && (
              <div className="flex flex-wrap gap-2 mb-4">
                {activeFilters.trend !== "all" && (
                  <Badge className={`${theme.badge} cursor-pointer text-sm`} onClick={() => setActiveFilters({ ...activeFilters, trend: "all" })}>
                    Trend: {activeFilters.trend} ✕
                  </Badge>
                )}
                {activeFilters.gender !== "all" && (
                  <Badge className={`${theme.badge} cursor-pointer text-sm`} onClick={() => setActiveFilters({ ...activeFilters, gender: "all" })}>
                    {activeFilters.gender === "boy" ? "Boys" : "Girls"} ✕
                  </Badge>
                )}
                {activeFilters.letter !== "all" && (
                  <Badge className={`${theme.badge} cursor-pointer text-sm`} onClick={() => setActiveFilters({ ...activeFilters, letter: "all" })}>
                    Letter: {activeFilters.letter} ✕
                  </Badge>
                )}
                {searchTerm && (
                  <Badge className={`${theme.badge} cursor-pointer text-sm`} onClick={() => setSearchTerm("")}>
                    Search: {searchTerm} ✕
                  </Badge>
                )}
              </div>
            )}

            {isLoading ? (
              <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                {[...Array(20)].map((_, i) => (
                  <div key={i} className="h-full">
                    <NameCardSkeleton compact />
                  </div>
                ))}
              </div>
            ) : filteredNames.length > 0 ? (
              <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                {filteredNames.map((name, index) => (
                  <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`} id={`name-${name.name_en}`} className="h-full">
                    <NameCard
                      name={name}
                      isFavorite={favorites.includes(name.name_en)}
                      onToggleFavorite={() => toggleFavorite(name.name_en)}
                      onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                      colorTheme={colorTheme}
                      compact={true}
                      className="rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-200 bg-white focus:outline focus:ring-2 focus:ring-blue-400 p-3 sm:p-4"
                      nameTitleClass="text-base sm:text-lg md:text-xl font-bold text-gray-900"
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="text-5xl mb-2">😕</div>
                <p className="text-base font-semibold">No trending names found matching your criteria.</p>
                <p className="text-sm text-muted-foreground mt-2 mb-4">
                  Try adjusting your search or filters.
                </p>
                <Button onClick={clearAllFilters} variant="outline" className="mx-auto">
                  Clear All Filters
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="favorites" className="space-y-6">
            {getFavoriteNames().length > 0 ? (
              <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                {getFavoriteNames().map((name, index) => (
                  <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`} id={`name-${name.name_en}`} className="h-full">
                    <NameCard
                      name={name}
                      isFavorite={true}
                      onToggleFavorite={() => toggleFavorite(name.name_en)}
                      onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                      colorTheme={colorTheme}
                      compact={true}
                      className="rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-200 bg-white focus:outline focus:ring-2 focus:ring-pink-400 p-3 sm:p-4"
                      nameTitleClass="text-base sm:text-lg md:text-xl font-bold text-gray-900"
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="text-5xl mb-2">💖</div>
                <p className="text-base font-semibold">You haven't added any trending names to favorites yet.</p>
                <p className="text-sm text-muted-foreground mt-2 mb-4">
                  Click the heart icon on any name to add it to your favorites.
                </p>
                <Button onClick={clearAllFilters} variant="outline" className="mx-auto">
                  Explore Trending Names
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* SEO Content Section - Responsive */}
        {seoContent && (
          <section className="bg-white rounded-lg shadow p-4 md:p-6 mt-8">
            {seoContent}
          </section>
        )}

        {/* Additional Content Section */}
        {additionalContent && (
          <section className="mt-8">
            {additionalContent}
          </section>
        )}
      </div>
    </main>
  )
} 