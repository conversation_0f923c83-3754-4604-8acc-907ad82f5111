"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { navigationConfig } from "@/lib/navigation-config"
import { Globe, Sparkles, Star, TrendingUp } from "lucide-react"
import Link from "next/link"

interface TrendingNavigationProps {
  currentPage?: string
  showCountryLinks?: boolean
  showRelatedTrending?: boolean
  highlightedCountries?: string[]
}

export default function TrendingNavigation({
  currentPage,
  showCountryLinks = true,
  showRelatedTrending = true,
  highlightedCountries = ["usa", "uk", "canada", "australia"]
}: TrendingNavigationProps) {

  const trendingPages = navigationConfig.trending.filter(page => page.href !== currentPage)
  const highPriorityPages = trendingPages.filter(page => page.priority === "high")
  const countryLinks = navigationConfig.countries.filter(country =>
    highlightedCountries.includes(country.name.toLowerCase())
  )

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Related Trending Pages - Mobile Responsive */}
      {showRelatedTrending && trendingPages.length > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardHeader className="px-4 md:px-6 py-3 md:py-6">
            <CardTitle className="flex items-center text-blue-800 text-lg md:text-xl">
              <TrendingUp className="mr-2 h-4 w-4 md:h-5 md:w-5" />
              <span className="hidden sm:inline">Explore More Trending Names</span>
              <span className="sm:hidden">More Trending</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="px-4 md:px-6 pb-4 md:pb-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3">
              {highPriorityPages.map((page) => (
                <Link key={page.href} href={page.href}>
                  <Button
                    variant="outline"
                    className="w-full text-left h-auto p-3 md:p-4 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                  >
                    <div className="flex flex-col min-w-0 w-full">
                      <div className="flex items-center justify-between mb-1 min-w-0 w-full gap-2">
                        <span className="font-semibold text-blue-700 text-sm md:text-base min-w-0 whitespace-normal break-words flex-1">
                          {page.name}
                        </span>
                        {page.priority === "high" && (
                          <Badge className="bg-blue-100 text-blue-800 border-blue-300 text-xs shrink-0 flex items-center">
                            <Star className="w-3 h-3 mr-1" />
                            <span className="hidden sm:inline">Popular</span>
                            <span className="sm:hidden">Hot</span>
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs md:text-sm text-blue-600 text-left line-clamp-2 min-w-0 whitespace-normal break-words">
                        {page.description}
                      </p>
                    </div>
                  </Button>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Country-Specific Links - Mobile Responsive */}
      {showCountryLinks && (
        <Card className="bg-gradient-to-r from-green-50 to-teal-50 border-green-200">
          <CardHeader className="px-4 md:px-6 py-3 md:py-6">
            <CardTitle className="flex items-center text-green-800 text-lg md:text-xl">
              <Globe className="mr-2 h-4 w-4 md:h-5 md:w-5" />
              <span className="hidden sm:inline">Discover Names by Country</span>
              <span className="sm:hidden">By Country</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="px-4 md:px-6 pb-4 md:pb-6">
            <p className="text-green-700 mb-3 md:mb-4 text-xs md:text-sm">
              <span className="hidden sm:inline">Explore trending names from specific countries and cultures:</span>
              <span className="sm:hidden">Explore names by country:</span>
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 md:gap-3">
              {countryLinks.map((country) => (
                <Link key={country.name} href={country.languages[0]?.boyHref || "#"}>
                  <Button
                    variant="outline"
                    className="w-full h-auto py-3 md:py-4 hover:bg-green-50 border-green-200 hover:border-green-300 touch-manipulation"
                  >
                    <div className="text-center">
                      <div className="text-lg md:text-xl mb-1">{country.flag}</div>
                      <div className="text-xs md:text-sm font-medium text-green-700 leading-tight">
                        {country.name === "United Kingdom" ? "UK" :
                          country.name === "United States" ? "USA" :
                            country.name.split(" ")[0]}
                      </div>
                    </div>
                  </Button>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Access Links - Mobile Responsive */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardHeader className="px-4 md:px-6 py-3 md:py-6">
          <CardTitle className="flex items-center text-purple-800 text-lg md:text-xl">
            <Sparkles className="mr-2 h-4 w-4 md:h-5 md:w-5" />
            <span className="hidden sm:inline">Popular Categories</span>
            <span className="sm:hidden">Categories</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 md:px-6 pb-4 md:pb-6">
          <div className="grid grid-cols-2 lg:grid-cols-3 gap-2 md:gap-3">
            <Link href="/usa/english-boy-names/">
              <Button
                variant="outline"
                className="w-full h-auto py-3 hover:bg-purple-50 border-purple-200 touch-manipulation"
              >
                <div className="text-center">
                  <div className="text-sm md:text-base">🇺🇸</div>
                  <div className="text-xs md:text-sm font-medium">
                    <span className="hidden sm:inline">American Boys</span>
                    <span className="sm:hidden">US Boys</span>
                  </div>
                </div>
              </Button>
            </Link>
            <Link href="/usa/english-girl-names/">
              <Button
                variant="outline"
                className="w-full h-auto py-3 hover:bg-pink-50 border-pink-200 touch-manipulation"
              >
                <div className="text-center">
                  <div className="text-sm md:text-base">🇺🇸</div>
                  <div className="text-xs md:text-sm font-medium">
                    <span className="hidden sm:inline">American Girls</span>
                    <span className="sm:hidden">US Girls</span>
                  </div>
                </div>
              </Button>
            </Link>
            <Link href="/uk/english-boy-names/">
              <Button
                variant="outline"
                className="w-full h-auto py-3 hover:bg-purple-50 border-purple-200 touch-manipulation"
              >
                <div className="text-center">
                  <div className="text-sm md:text-base">🇬🇧</div>
                  <div className="text-xs md:text-sm font-medium">
                    <span className="hidden sm:inline">British Boys</span>
                    <span className="sm:hidden">UK Boys</span>
                  </div>
                </div>
              </Button>
            </Link>
            <Link href="/uk/english-girl-names/">
              <Button
                variant="outline"
                className="w-full h-auto py-3 hover:bg-pink-50 border-pink-200 touch-manipulation"
              >
                <div className="text-center">
                  <div className="text-sm md:text-base">🇬🇧</div>
                  <div className="text-xs md:text-sm font-medium">
                    <span className="hidden sm:inline">British Girls</span>
                    <span className="sm:hidden">UK Girls</span>
                  </div>
                </div>
              </Button>
            </Link>
            <Link href="/india/hindi-boy-names/">
              <Button
                variant="outline"
                className="w-full h-auto py-3 hover:bg-orange-50 border-orange-200 touch-manipulation"
              >
                <div className="text-center">
                  <div className="text-sm md:text-base">🇮🇳</div>
                  <div className="text-xs md:text-sm font-medium">
                    <span className="hidden sm:inline">Indian Boys</span>
                    <span className="sm:hidden">IN Boys</span>
                  </div>
                </div>
              </Button>
            </Link>
            <Link href="/india/hindi-girl-names/">
              <Button
                variant="outline"
                className="w-full h-auto py-3 hover:bg-orange-50 border-orange-200 touch-manipulation"
              >
                <div className="text-center">
                  <div className="text-sm md:text-base">🇮🇳</div>
                  <div className="text-xs md:text-sm font-medium">
                    <span className="hidden sm:inline">Indian Girls</span>
                    <span className="sm:hidden">IN Girls</span>
                  </div>
                </div>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 