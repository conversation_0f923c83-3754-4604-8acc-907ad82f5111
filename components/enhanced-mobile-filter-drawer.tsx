"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer"
import { <PERSON>rollArea, ScrollBar } from "@/components/ui/scroll-area"
import { getDisplayText, rashiData } from "@/lib/astrological-data"
import { cn } from "@/lib/utils"
import { Filter, Globe, Heart, Star, Users, X } from "lucide-react"
import { useEffect, useState } from "react"

interface FilterOption {
  value: string
  label: string
}

interface EnhancedMobileFilterDrawerProps {
  // Filter options
  languages?: FilterOption[]
  religions?: FilterOption[]
  genders?: FilterOption[]
  startingLetters?: string[]

  // Selected values
  selectedLanguage?: string | null
  selectedReligion?: string | null
  selectedGender?: string | null
  selectedStartingLetter?: string | null
  selectedRashi?: string | null

  // Callbacks
  onLanguageChange?: (language: string | null) => void
  onReligionChange?: (religion: string | null) => void
  onGenderChange?: (gender: string | null) => void
  onStartingLetterChange?: (letter: string | null) => void
  onRashiChange?: (rashi: string | null) => void
  onClearAll?: () => void

  // Show/hide options
  showLanguageFilter?: boolean
  showReligionFilter?: boolean
  showGenderFilter?: boolean
  showAlphabetFilter?: boolean
  showRashiFilter?: boolean

  // Context
  currentLanguage?: string
  currentReligion?: string
  hideLanguageFilters?: boolean

  // Styling
  buttonClassName?: string
  colorTheme?: string
}

const defaultLanguages = [
  { value: "all", label: "All Languages" },
  { value: "Gujarati", label: "Gujarati" },
  { value: "Hindi", label: "Hindi" },
  { value: "Tamil", label: "Tamil" },
  { value: "Urdu", label: "Urdu" },
  { value: "Punjabi", label: "Punjabi" },
  { value: "Bengali", label: "Bengali" },
  { value: "Marathi", label: "Marathi" },
]

const defaultReligions = [
  { value: "all", label: "All Religions" },
  { value: "Hindu", label: "Hindu" },
  { value: "Muslim", label: "Muslim" },
  { value: "Sikh", label: "Sikh" },
  { value: "Christian", label: "Christian" },
]

const defaultGenders = [
  { value: "all", label: "All Genders" },
  { value: "male", label: "Boy Names" },
  { value: "female", label: "Girl Names" },
  { value: "unisex", label: "Unisex Names" },
]

const defaultStartingLetters = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"]

export function EnhancedMobileFilterDrawer({
  languages = defaultLanguages,
  religions = defaultReligions,
  genders = defaultGenders,
  startingLetters = defaultStartingLetters,
  selectedLanguage,
  selectedReligion,
  selectedGender,
  selectedStartingLetter,
  selectedRashi,
  onLanguageChange,
  onReligionChange,
  onGenderChange,
  onStartingLetterChange,
  onRashiChange,
  onClearAll,
  showLanguageFilter = true,
  showReligionFilter = true,
  showGenderFilter = true,
  showAlphabetFilter = true,
  showRashiFilter = true,
  currentLanguage,
  currentReligion,
  hideLanguageFilters = false,
  buttonClassName = "",
  colorTheme = "blue",
}: EnhancedMobileFilterDrawerProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Prevent body scroll when mobile drawer is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const activeFiltersCount = [
    selectedLanguage,
    selectedReligion,
    selectedGender,
    selectedStartingLetter,
    selectedRashi,
  ].filter(Boolean).length

  const isLanguageSpecific = currentLanguage && selectedLanguage
  const isHinduReligion = currentReligion === "Hindu" || selectedReligion === "Hindu"

  // Color theme classes
  const colorMap: Record<string, any> = {
    orange: {
      button: "bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700",
      active: "bg-orange-100 text-orange-700",
    },
    red: {
      button: "bg-red-50 hover:bg-red-100 border-red-200 text-red-700",
      active: "bg-red-100 text-red-700",
    },
    blue: {
      button: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
      active: "bg-blue-100 text-blue-700",
    },
    green: {
      button: "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
      active: "bg-green-100 text-green-700",
    },
    yellow: {
      button: "bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-700",
      active: "bg-yellow-100 text-yellow-700",
    },
    purple: {
      button: "bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700",
      active: "bg-purple-100 text-purple-700",
    },
  }
  const theme = colorMap[colorTheme] || colorMap.blue

  const handleFilterChange = (filterType: string, value: string | null) => {
    switch (filterType) {
      case "language":
        onLanguageChange?.(value)
        break
      case "religion":
        onReligionChange?.(value)
        break
      case "gender":
        onGenderChange?.(value)
        break
      case "letter":
        onStartingLetterChange?.(value)
        break
      case "rashi":
        onRashiChange?.(value)
        break
    }
    setIsOpen(false)
  }

  return (
    <div className="lg:hidden">
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full flex items-center justify-center gap-2",
              theme.button,
              buttonClassName
            )}
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </DrawerTrigger>
        <DrawerContent className="max-h-[80vh]">
          <DrawerHeader className="border-b">
            <div className="flex items-center justify-between">
              <DrawerTitle>Filters</DrawerTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DrawerHeader>
          <ScrollArea className="overflow-y-auto max-h-[calc(80vh-80px)]">
            <div className="p-4 space-y-6">

              {/* Language Filter */}
              {showLanguageFilter && !hideLanguageFilters && onLanguageChange && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Globe className="h-4 w-4 text-blue-500" />
                    Language
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {languages.map((lang) => (
                      <Button
                        key={lang.value}
                        variant={selectedLanguage === lang.value ? "default" : "outline"}
                        size="sm"
                        className="justify-start"
                        onClick={() => handleFilterChange("language", lang.value)}
                      >
                        {lang.label}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Religion Filter */}
              {showReligionFilter && religions.length > 0 && onReligionChange && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Heart className="h-4 w-4 text-red-500" />
                    Religion
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {religions.map((religion) => (
                      <Button
                        key={religion.value}
                        variant={selectedReligion === religion.value ? "default" : "outline"}
                        size="sm"
                        className="justify-start"
                        onClick={() => handleFilterChange("religion", religion.value)}
                      >
                        {religion.label}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Gender Filter */}
              {showGenderFilter && onGenderChange && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Users className="h-4 w-4 text-green-500" />
                    Gender
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {genders.map((gender) => (
                      <Button
                        key={gender.value}
                        variant={selectedGender === gender.value ? "default" : "outline"}
                        size="sm"
                        className="justify-start"
                        onClick={() => handleFilterChange("gender", gender.value)}
                      >
                        {gender.label}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Astrological Filters - Only show for Hindu religion */}
              {isHinduReligion && (showRashiFilter) && (
                <>
                  {/* Rashi Filter */}
                  {showRashiFilter && onRashiChange && (
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <Star className="h-4 w-4 text-orange-500" />
                        Rashi (Zodiac Sign)
                      </h4>
                      <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                        <Button
                          variant={selectedRashi === "all" ? "default" : "outline"}
                          size="sm"
                          className="justify-start"
                          onClick={() => handleFilterChange("rashi", "all")}
                        >
                          All Rashis
                        </Button>
                        {rashiData.map((rashi) => (
                          <Button
                            key={rashi.value}
                            variant={selectedRashi === rashi.value ? "default" : "outline"}
                            size="sm"
                            className="justify-start text-sm"
                            onClick={() => handleFilterChange("rashi", rashi.value)}
                          >
                            {isLanguageSpecific
                              ? getDisplayText(rashiData, rashi.value, currentLanguage || "")
                              : rashi.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Clear All Button */}
              {activeFiltersCount > 0 && (
                <div className="pt-4 border-t">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      onClearAll?.()
                      setIsOpen(false)
                    }}
                  >
                    Clear All Filters
                  </Button>
                </div>
              )}
            </div>
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        </DrawerContent>
      </Drawer>
    </div>
  )
}

export default EnhancedMobileFilterDrawer; 