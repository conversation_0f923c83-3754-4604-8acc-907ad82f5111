"use client"

import type { NameData } from '@/types/name-data'
import { useEffect, useState } from 'react'

interface TrendChartProps {
    data: Array<{
        year: number
        count: number
        displayYear: string
    }>
    nameData: NameData
}

// Loading component
const ChartLoading = () => (
    <div className="h-full w-full bg-gray-50 rounded-lg animate-pulse flex items-center justify-center">
        <div className="text-gray-400 text-xs sm:text-sm">Loading chart...</div>
    </div>
)

// Fallback chart component when Recha<PERSON> fails to load
const FallbackChart = ({ data, nameData }: TrendChartProps) => {
    const maxCount = Math.max(...data.map(d => d.count))
    const minYear = Math.min(...data.map(d => d.year))
    const maxYear = Math.max(...data.map(d => d.year))

    return (
        <div className="h-full w-full p-2 sm:p-4">
            <div className="text-center mb-3 sm:mb-4">
                <h4 className="font-semibold text-gray-800 text-sm sm:text-base">Popularity Trend: {minYear} - {maxYear}</h4>
                <p className="text-xs sm:text-sm text-gray-600">Peak: {maxCount.toLocaleString()} babies named {nameData.name_en}</p>
            </div>
            <div className="space-y-1.5 sm:space-y-2">
                {data.slice(-5).map((item) => (
                    <div key={item.year} className="flex items-center justify-between p-1.5 sm:p-2 bg-gray-50 rounded">
                        <span className="font-medium text-xs sm:text-sm">{item.year}</span>
                        <div className="flex items-center gap-1.5 sm:gap-2">
                            <div
                                className="h-1.5 sm:h-2 bg-blue-500 rounded"
                                style={{ width: `${(item.count / maxCount) * 100}px` }}
                            />
                            <span className="text-xs sm:text-sm text-gray-600">{item.count.toLocaleString()}</span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default function TrendChart({ data, nameData }: TrendChartProps) {
    const [isClient, setIsClient] = useState(false)
    const [RechartsComponents, setRechartsComponents] = useState<any>(null)
    const [loadError, setLoadError] = useState(false)

    useEffect(() => {
        setIsClient(true)

        // Dynamically import Recharts only on client side
        const loadRecharts = async () => {
            try {
                const recharts = await import('recharts')
                setRechartsComponents(recharts)
            } catch (error) {
                console.error('Failed to load Recharts:', error)
                setLoadError(true)
            }
        }

        loadRecharts()
    }, [])

    // Don't render anything on server side
    if (!isClient) {
        return <ChartLoading />
    }

    // Show fallback if Recharts failed to load
    if (loadError || !RechartsComponents) {
        return <FallbackChart data={data} nameData={nameData} />
    }

    const { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip } = RechartsComponents

    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            const data = payload[0]
            return (
                <div className="bg-white p-2 sm:p-3 border border-gray-200 rounded-lg shadow-lg">
                    <p className="font-semibold text-gray-800 text-xs sm:text-sm">{`Year: ${label}`}</p>
                    <p className="text-xs sm:text-sm text-gray-600">
                        <span className="inline-block w-2.5 sm:w-3 h-2.5 sm:h-3 rounded-full mr-1.5 sm:mr-2"
                            style={{ backgroundColor: data.color }}></span>
                        {`Babies Named: ${data.value?.toLocaleString() || 0}`}
                    </p>
                </div>
            )
        }
        return null
    }

    return (
        <ResponsiveContainer width="100%" height="100%">
            <LineChart
                data={data}
                margin={{
                    top: 5,
                    right: 10,
                    left: 0,
                    bottom: 25,
                }}
            >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                    dataKey="displayYear"
                    tick={{ fontSize: 10, fill: '#6b7280' }}
                    tickLine={{ stroke: '#d1d5db' }}
                    axisLine={{ stroke: '#d1d5db' }}
                    interval="preserveStartEnd"
                    minTickGap={10}
                />
                <YAxis
                    tick={{ fontSize: 10, fill: '#6b7280' }}
                    tickLine={{ stroke: '#d1d5db' }}
                    axisLine={{ stroke: '#d1d5db' }}
                    tickFormatter={(value: number) => value.toLocaleString()}
                />
                <Tooltip content={<CustomTooltip />} />
                <Line
                    type="monotone"
                    dataKey="count"
                    stroke={nameData.gender === 'girl' ? '#ec4899' : '#3b82f6'}
                    strokeWidth={2.5}
                    dot={{
                        fill: nameData.gender === 'girl' ? '#ec4899' : '#3b82f6',
                        strokeWidth: 2,
                        stroke: '#ffffff',
                        r: 3
                    }}
                    activeDot={{
                        r: 5,
                        stroke: nameData.gender === 'girl' ? '#ec4899' : '#3b82f6',
                        strokeWidth: 2.5,
                        fill: '#ffffff'
                    }}
                    connectNulls
                />
            </LineChart>
        </ResponsiveContainer>
    )
} 