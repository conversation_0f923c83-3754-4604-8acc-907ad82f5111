"use client"

import LanguageNamesPage from "@/components/language-names-page"

interface LanguageNamesPageWrapperProps {
  language: string
  religions?: string[]
  showReligionFilter?: boolean
  showGenderFilter?: boolean
  defaultGender?: string
  colorTheme: "orange" | "red" | "blue" | "green" | "yellow" | "purple" | "pink" | "indigo"
  apiEndpoint: string
  headerLabels: {
    title: string
    subtitle: string
    description: string
  }
  showRashiFilter?: boolean
  showAlphabetFilter?: boolean
  seoContent?: React.ReactNode
  additionalContent?: React.ReactNode
}

export default function LanguageNamesPageWrapper(props: LanguageNamesPageWrapperProps) {
  return <LanguageNamesPage {...props} />
} 