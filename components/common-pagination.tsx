"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
// Temporarily comment out the problematic Select import
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { NameData } from "@/types/name-data"
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"

interface PaginationMetadata {
  total: number
  limit: number
  offset: number
  hasMore: boolean
}

interface CommonPaginationProps {
  apiEndpoint: string
  searchParams?: Record<string, string>
  itemsPerPage?: number
  onDataChange: (data: NameData[], metadata: PaginationMetadata) => void
  onLoadingChange: (loading: boolean) => void
  className?: string
  showPageSizeOptions?: boolean
  pageSizeOptions?: number[]
}

export function CommonPagination({
  apiEndpoint,
  searchParams = {},
  itemsPerPage = 20,
  onDataChange,
  onLoadingChange,
  className = "",
  showPageSizeOptions = true,
  pageSizeOptions = [10, 20, 50, 100]
}: CommonPaginationProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [totalItems, setTotalItems] = useState(0)
  const [loading, setLoading] = useState(false)
  const [currentItemsPerPage, setCurrentItemsPerPage] = useState(itemsPerPage)
  const [isMobile, setIsMobile] = useState(false)
  const hasMounted = useRef(false)
  const currentSearchParams = useRef(searchParams)
  const currentApiEndpoint = useRef(apiEndpoint)
  const currentItemsPerPageRef = useRef(itemsPerPage)

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Update refs when props change
  useEffect(() => {
    currentSearchParams.current = searchParams
    currentApiEndpoint.current = apiEndpoint
  }, [searchParams, apiEndpoint])

  // Update items per page when prop changes
  useEffect(() => {
    setCurrentItemsPerPage(itemsPerPage)
    currentItemsPerPageRef.current = itemsPerPage
  }, [itemsPerPage])

  // Memoize search params to prevent unnecessary re-renders
  const searchParamsString = useMemo(() => JSON.stringify(searchParams), [searchParams])

  // Fetch data for current page - stable callback
  const fetchPageData = useCallback(async (page: number, pageSize?: number) => {
    if (loading) return

    const actualPageSize = pageSize || currentItemsPerPageRef.current
    setLoading(true)
    onLoadingChange(true)
    const offset = (page - 1) * actualPageSize

    try {
      const url = new URL(currentApiEndpoint.current, window.location.origin)

      // Add search params
      Object.entries(currentSearchParams.current).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          url.searchParams.set(key, value.toString())
        }
      })

      // Add pagination params
      url.searchParams.set('limit', actualPageSize.toString())
      url.searchParams.set('offset', offset.toString())

      const response = await fetch(url.toString())

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.data) {
        onDataChange(result.data, {
          total: result.metadata?.total || 0,
          limit: actualPageSize,
          offset: result.metadata?.offset || 0,
          hasMore: result.metadata?.hasMore || false
        })
        setTotalItems(result.metadata?.total || 0)
        setTotalPages(Math.ceil((result.metadata?.total || 0) / actualPageSize))
      } else {
        throw new Error('Invalid response format')
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      onDataChange([], {
        total: 0,
        limit: actualPageSize,
        offset: 0,
        hasMore: false
      })
      setTotalItems(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
      onLoadingChange(false)
    }
  }, [loading, onDataChange, onLoadingChange]) // Removed currentItemsPerPage from dependencies

  // Refetch when search params change (but not on initial mount)
  useEffect(() => {
    setCurrentPage(1)
    fetchPageData(1)
    // eslint-disable-next-line
  }, [searchParamsString])

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    if (page < 1 || page > totalPages || page === currentPage || loading) return

    setCurrentPage(page)
    fetchPageData(page)

    // Only scroll when user explicitly clicks on pagination buttons
    // Don't scroll on API calls or automatic changes
  }, [currentPage, totalPages, loading, fetchPageData])

  // Handle page size change
  const handlePageSizeChange = useCallback((newPageSize: string) => {
    const size = parseInt(newPageSize)
    setCurrentItemsPerPage(size)
    currentItemsPerPageRef.current = size
    setCurrentPage(1)
    fetchPageData(1, size)
  }, [fetchPageData])

  // Handle first page
  const handleFirstPage = () => {
    handlePageChange(1)
  }

  // Handle previous page
  const handlePrevPage = () => {
    handlePageChange(currentPage - 1)
  }

  // Handle next page
  const handleNextPage = () => {
    handlePageChange(currentPage + 1)
  }

  // Handle last page
  const handleLastPage = () => {
    handlePageChange(totalPages)
  }

  // Handle direct page number click
  const handlePageNumberClick = (page: number) => {
    handlePageChange(page)
  }

  // Generate page numbers to show with ellipsis
  const getPageNumbers = useMemo(() => {
    const pages = []
    const maxVisiblePages = isMobile ? 5 : 7
    const halfVisible = Math.floor(maxVisiblePages / 2)

    let startPage = Math.max(1, currentPage - halfVisible)
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    // Adjust start page if we're near the end
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pages.push(1)
      if (startPage > 2) {
        pages.push('...')
      }
    }

    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }

    // Add last page and ellipsis if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('...')
      }
      pages.push(totalPages)
    }

    return pages
  }, [currentPage, totalPages, isMobile])

  if (totalPages <= 1) return null

  return (
    <div className={`flex flex-col gap-6 ${className}`}>
      {/* Results info and page size selector */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-muted-foreground text-center sm:text-left">
          <span className="font-medium text-foreground">
            Showing {((currentPage - 1) * currentItemsPerPage) + 1} to{" "}
            {Math.min(currentPage * currentItemsPerPage, totalItems)} of {totalItems.toLocaleString()} results
          </span>
        </div>

        {/* Page size selector */}
        {showPageSizeOptions && (
          <div className="flex items-center gap-2">
            <label htmlFor="page-size-select" className="text-sm text-muted-foreground">
              Show per page:
            </label>
            <select
              id="page-size-select"
              value={currentItemsPerPage.toString()}
              onChange={(e) => handlePageSizeChange(e.target.value)}
              aria-label="Select number of items per page"
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size.toString()}>
                  {size}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center justify-center">
        {/* Mobile: Enhanced pagination with page numbers */}
        {isMobile ? (
          <div className="flex flex-col gap-4 w-full max-w-md">
            {/* Page info */}
            <div className="text-center">
              <div className="text-sm text-muted-foreground">
                Page <span className="font-semibold text-foreground">{currentPage}</span> of <span className="font-semibold text-foreground">{totalPages}</span>
              </div>
            </div>

            {/* Navigation buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevPage}
                disabled={currentPage === 1 || loading}
                className="flex-1 h-10 transition-all duration-200 hover:shadow-sm"
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleNextPage}
                disabled={currentPage === totalPages || loading}
                className="flex-1 h-10 transition-all duration-200 hover:shadow-sm"
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>

            {/* Page numbers for mobile */}
            {totalPages <= 7 ? (
              <div className="flex items-center justify-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={page === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageNumberClick(page)}
                    disabled={loading}
                    className={`w-8 h-8 p-0 text-xs font-medium transition-all duration-200 ${page === currentPage
                      ? "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm"
                      : "hover:bg-gray-50 hover:shadow-sm"
                      }`}
                  >
                    {page}
                  </Button>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center gap-1">
                {getPageNumbers.map((page, index) => (
                  <div key={index}>
                    {page === '...' ? (
                      <span className="px-2 text-muted-foreground font-medium">...</span>
                    ) : (
                      <Button
                        variant={page === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageNumberClick(page as number)}
                        disabled={loading}
                        className={`w-8 h-8 p-0 text-xs font-medium transition-all duration-200 ${page === currentPage
                          ? "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm"
                          : "hover:bg-gray-50 hover:shadow-sm"
                          }`}
                      >
                        {page}
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Loading indicator for mobile */}
            {loading && (
              <div className="text-center">
                <div className="inline-flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                  Loading...
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Desktop: Enhanced pagination controls */
          <div className="flex flex-col items-center gap-4">
            {/* Main pagination controls */}
            <div className="flex items-center space-x-2">
              {/* First page */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleFirstPage}
                disabled={currentPage === 1 || loading}
                title="Go to first page"
                className="h-10 w-10 p-0 hover:bg-gray-50 transition-all duration-200 hover:shadow-sm"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>

              {/* Previous page */}
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevPage}
                disabled={currentPage === 1 || loading}
                title="Go to previous page"
                className="h-10 w-10 p-0 hover:bg-gray-50 transition-all duration-200 hover:shadow-sm"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* Page numbers */}
              <div className="flex items-center space-x-1 mx-2">
                {getPageNumbers.map((page, index) => (
                  <div key={index}>
                    {page === '...' ? (
                      <span className="px-3 text-muted-foreground font-medium">...</span>
                    ) : (
                      <Button
                        variant={page === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageNumberClick(page as number)}
                        disabled={loading}
                        className={`w-12 h-10 p-0 text-sm font-medium transition-all duration-200 ${page === currentPage
                          ? "bg-primary text-primary-foreground hover:bg-primary/90 shadow-md"
                          : "hover:bg-gray-50 hover:shadow-sm border-gray-200"
                          }`}
                        title={`Go to page ${page}`}
                      >
                        {page}
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              {/* Next page */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextPage}
                disabled={currentPage === totalPages || loading}
                title="Go to next page"
                className="h-10 w-10 p-0 hover:bg-gray-50 transition-all duration-200 hover:shadow-sm"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>

              {/* Last page */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleLastPage}
                disabled={currentPage === totalPages || loading}
                title="Go to last page"
                className="h-10 w-10 p-0 hover:bg-gray-50 transition-all duration-200 hover:shadow-sm"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Page info and loading indicator */}
            <div className="text-center">
              <div className="text-sm text-muted-foreground">
                Page <span className="font-semibold text-foreground">{currentPage}</span> of <span className="font-semibold text-foreground">{totalPages}</span> • {totalPages} total pages
              </div>
              {loading && (
                <div className="mt-2 inline-flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                  Loading...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CommonPagination; 