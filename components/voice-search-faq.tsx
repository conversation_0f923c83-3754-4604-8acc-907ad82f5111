import React from 'react'

interface FAQItem {
  question: string
  answer: string
  category?: string
}

interface VoiceSearchFAQProps {
  faqs: FAQItem[]
  title?: string
  className?: string
}

export default function VoiceSearchFAQ({ faqs, title = "Frequently Asked Questions", className = "" }: VoiceSearchFAQProps) {
  // Generate FAQ schema for voice search optimization
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }

  return (
    <>
      {/* FAQ Schema for Voice Search */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema, null, 2) }}
      />
      
      <div className={`bg-gray-50 rounded-xl p-8 ${className}`}>
        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          ❓ {title}
        </h2>
        <div className="grid md:grid-cols-2 gap-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {faq.question}
              </h3>
              <p className="text-gray-700 leading-relaxed">
                {faq.answer}
              </p>
              {faq.category && (
                <span className="inline-block mt-3 px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  {faq.category}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  )
}

// Pre-defined FAQ sets for common voice search queries
export const VOICE_SEARCH_FAQS = {
  POPULAR_NAMES: [
    {
      question: "What are the most popular baby names in America 2025?",
      answer: "According to official Social Security Administration data, Liam is the #1 boy name and Olivia is the #1 girl name in America for 2025. These names have maintained their top positions for six consecutive years.",
      category: "Popular Names"
    },
    {
      question: "What is the number one baby name in the US?",
      answer: "Liam is the number one baby name for boys, and Olivia is the number one baby name for girls in the United States for 2025, based on official SSA statistics.",
      category: "Popular Names"
    },
    {
      question: "How many babies were named Liam in 2024?",
      answer: "Over 22,000 babies were named Liam in 2024, making it the most popular boy name in America for the sixth year running.",
      category: "Statistics"
    },
    {
      question: "What are the top 5 baby names in America?",
      answer: "The top 5 boy names are Liam, Noah, Oliver, Elijah, and James. The top 5 girl names are Olivia, Emma, Charlotte, Amelia, and Sophia.",
      category: "Popular Names"
    }
  ],
  
  NAMING_ADVICE: [
    {
      question: "How do I choose a baby name?",
      answer: "Consider the name's meaning, pronunciation, how it sounds with your last name, potential nicknames, and cultural significance. Also think about how the name might age with your child.",
      category: "Advice"
    },
    {
      question: "Should I choose a unique baby name?",
      answer: "Unique names can be special, but consider practicality. Make sure it's easy to pronounce and spell, and won't cause difficulties for your child throughout their life.",
      category: "Advice"
    },
    {
      question: "When should I decide on a baby name?",
      answer: "Many parents choose names during the second trimester, but there's no rush. Some parents wait until they meet their baby to make the final decision.",
      category: "Advice"
    },
    {
      question: "How do I test if a baby name sounds good?",
      answer: "Say the full name out loud, try different nicknames, consider how it sounds when called across a playground, and test how it flows with your surname.",
      category: "Advice"
    }
  ],
  
  TRENDS: [
    {
      question: "What baby name trends are popular in 2025?",
      answer: "Current trends include nature names, vintage names making a comeback, gender-neutral options, and names with strong meanings. Short, simple names are also very popular.",
      category: "Trends"
    },
    {
      question: "Are traditional baby names coming back?",
      answer: "Yes, many traditional names like James, William, Elizabeth, and Charlotte are experiencing a revival as parents seek timeless, classic options.",
      category: "Trends"
    },
    {
      question: "What are the fastest rising baby names?",
      answer: "Names like Luna, Aria, and Kai for girls, and names like Ezra, Asher, and Leo for boys are among the fastest rising in popularity.",
      category: "Trends"
    }
  ],
  
  OFFICIAL_DATA: [
    {
      question: "Where does baby name data come from?",
      answer: "Official baby name data comes from the Social Security Administration, which compiles statistics from Social Security card applications for all births in the United States.",
      category: "Data Source"
    },
    {
      question: "How often are baby name rankings updated?",
      answer: "The Social Security Administration releases new baby name rankings every May, providing the most current official statistics for the previous year.",
      category: "Data Source"
    },
    {
      question: "How far back does baby name data go?",
      answer: "Official SSA baby name data goes back to 1880, providing over 145 years of historical naming trends and patterns in the United States.",
      category: "Data Source"
    }
  ]
}

// Helper function to combine FAQ sets
export function combineFAQs(...faqSets: FAQItem[][]): FAQItem[] {
  return faqSets.flat()
}
