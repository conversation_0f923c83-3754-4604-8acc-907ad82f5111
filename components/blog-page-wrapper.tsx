"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON>Conte<PERSON>, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { getAllBlogPosts } from "@/lib/blog-content"
import { navigationConfig } from "@/lib/navigation-config"
import type { BlogPost } from "@/types/blog-post"
import { ArrowRight, Globe, TrendingUp } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

interface BlogPageWrapperProps {
  children: React.ReactNode
  showRelatedLinks?: boolean
  currentCategory?: string
}

export default function BlogPageWrapper({
  children,
  showRelatedLinks = true,
  currentCategory
}: BlogPageWrapperProps) {
  const router = useRouter()
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        // Load real blog content from our content management system
        const posts = getAllBlogPosts()
        setBlogPosts(posts)
        setIsLoading(false)
      } catch (error) {
        console.error("Error loading blog posts:", error)
        setIsLoading(false)
      }
    }

    fetchBlogPosts()
  }, [])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date)
  }

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      "Trending Names": "bg-blue-100 text-blue-800",
      "Religious Names": "bg-green-100 text-green-800",
      "Unique Names": "bg-purple-100 text-purple-800",
      "Cultural Names": "bg-orange-100 text-orange-800",
      "Celebrity Names": "bg-pink-100 text-pink-800",
      "Nature Names": "bg-emerald-100 text-emerald-800",
      "Alphabetical Guide": "bg-yellow-100 text-yellow-800",
      "Name Length": "bg-indigo-100 text-indigo-800",
      "Royal Names": "bg-red-100 text-red-800",
      "Country Focus": "bg-teal-100 text-teal-800",
      "Vintage Names": "bg-gray-100 text-gray-800",
    }
    return colors[category] || "bg-gray-100 text-gray-800"
  }

  // Get trending pages for related links
  const trendingPages = navigationConfig.trending.filter(page => page.priority === "high")

  // Get popular country pages
  const popularCountries = navigationConfig.countries.slice(0, 4)

  return (
    <main className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Baby Names Blog</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Discover the perfect name for your little one with our comprehensive guides,
          trending lists, and meaningful name collections from around the world.
        </p>
      </div>

      {/* Category Filter Buttons */}
      <div className="flex flex-wrap gap-2 mb-8 justify-center">
        <Button variant="outline" size="sm">All Categories</Button>
        <Button variant="outline" size="sm">Trending Names</Button>
        <Button variant="outline" size="sm">Religious Names</Button>
        <Button variant="outline" size="sm">Unique Names</Button>
        <Button variant="outline" size="sm">Cultural Names</Button>
      </div>

      {/* Main Blog Content */}
      <div className="prose dark:prose-invert max-w-none">
        {children}
      </div>

      {/* Related Links Section */}
      {showRelatedLinks && (
        <div className="space-y-6">
          {/* Trending Names Section */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-800">
                <TrendingUp className="mr-2 h-5 w-5" />
                Explore Trending Names
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-700 mb-4">
                Discover the latest trending baby names and naming trends:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {trendingPages.map((page) => (
                  <Link key={page.href} href={page.href}>
                    <Button
                      variant="outline"
                      className="w-full text-left h-auto p-3 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                    >
                      <div className="flex flex-col min-w-0 w-full">
                        <span className="font-semibold text-blue-700 text-sm mb-1">
                          {page.name}
                        </span>
                        <span className="text-xs text-blue-600 line-clamp-2">
                          {page.description}
                        </span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-blue-500 ml-2 shrink-0" />
                    </Button>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Popular Countries Section */}
          <Card className="bg-gradient-to-r from-green-50 to-teal-50 border-green-200">
            <CardHeader>
              <CardTitle className="flex items-center text-green-800">
                <Globe className="mr-2 h-5 w-5" />
                Popular Name Collections
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-green-700 mb-4">
                Browse names from popular countries and cultures:
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {popularCountries.map((country) => (
                  <Link key={country.name} href={country.languages[0]?.boyHref || "#"}>
                    <Button
                      variant="outline"
                      className="w-full h-auto py-3 hover:bg-green-50 border-green-200 hover:border-green-300"
                    >
                      <div className="text-center">
                        <div className="text-lg mb-1">{country.flag}</div>
                        <div className="text-xs font-medium text-green-700 leading-tight">
                          {country.name === "United Kingdom" ? "UK" :
                            country.name === "United States" ? "USA" :
                              country.name.split(" ")[0]}
                        </div>
                      </div>
                    </Button>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Religious Names Section */}
          <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center text-purple-800">
                <span className="mr-2">🕉️</span>
                Religious Names
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-purple-700 mb-4">
                Explore names with spiritual and religious significance:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {navigationConfig.religions.map((religion) => (
                  <Link key={religion.href} href={religion.href}>
                    <Button
                      variant="outline"
                      className="w-full h-auto p-3 hover:bg-purple-50 border-purple-200 hover:border-purple-300"
                    >
                      <div className="text-center">
                        <div className="font-medium text-purple-700 text-sm">
                          {religion.name}
                        </div>
                        {religion.description && (
                          <div className="text-xs text-purple-600 mt-1">
                            {religion.description}
                          </div>
                        )}
                      </div>
                    </Button>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-10">Loading...</div>
      ) : (
        <div className="space-y-8">
          {blogPosts.map((post, index) => (
            <>
              <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className={getCategoryColor(post.category)}>
                      {post.category}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(post.date)}
                    </span>
                    {post.readTime && (
                      <span className="text-sm text-muted-foreground">
                        • {post.readTime}
                      </span>
                    )}
                  </div>
                  <CardTitle className="text-2xl hover:text-blue-600 transition-colors">
                    {post.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">{post.excerpt}</p>
                  {post.tags && (
                    <div className="flex flex-wrap gap-1 mt-3">
                      {post.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    variant="default"
                    onClick={() => router.push(`/blog/${post.slug}`)}
                    className="w-full sm:w-auto"
                  >
                    Read Full Article →
                  </Button>
                </CardFooter>
              </Card>
            </>
          ))}
        </div>
      )}

      {/* Popular Categories Section */}
      <div className="mt-12 p-6 bg-muted/50 rounded-lg">
        <h2 className="text-2xl font-bold mb-4 text-center">Popular Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button variant="ghost" className="h-auto p-4 flex-col" onClick={() => router.push('/usa/english-boy-names')}>
            <span className="font-semibold">Boy Names</span>
            <span className="text-sm text-muted-foreground">1,200+ names</span>
          </Button>
          <Button variant="ghost" className="h-auto p-4 flex-col" onClick={() => router.push('/usa/english-girl-names')}>
            <span className="font-semibold">Girl Names</span>
            <span className="text-sm text-muted-foreground">1,400+ names</span>
          </Button>
          <Button variant="ghost" className="h-auto p-4 flex-col" onClick={() => router.push('/blog/unique-baby-names-2025-rare-beautiful')}>
            <span className="font-semibold">Unique Names</span>
            <span className="text-sm text-muted-foreground">500+ names</span>
          </Button>
          <Button variant="ghost" className="h-auto p-4 flex-col" onClick={() => router.push('/blog/most-popular-baby-names-2025')}>
            <span className="font-semibold">Trending 2025</span>
            <span className="text-sm text-muted-foreground">New picks</span>
          </Button>
        </div>
      </div>
    </main>
  )
} 