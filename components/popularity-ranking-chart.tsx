"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import type { NameData } from '@/types/name-data'
import { Award, BarChart3, ExternalLink, Medal, Trophy } from 'lucide-react'

interface PopularityRankingChartProps {
    nameData: NameData
    className?: string
}

export default function PopularityRankingChart({ nameData, className }: PopularityRankingChartProps) {
    // Only show chart for USA names with popularity rank
    if (!nameData.popularity_rank) {
        return null
    }

    const currentRank = nameData.popularity_rank

    const getRankIcon = (rank: number) => {
        if (rank <= 10) return <Trophy className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-500" />
        if (rank <= 50) return <Medal className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
        if (rank <= 100) return <Award className="h-3 w-3 sm:h-4 sm:w-4 text-amber-600" />
        return <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
    }

    const getRankCategory = (rank: number) => {
        if (rank <= 10) return { text: 'Top 10', color: 'text-yellow-600', bg: 'bg-yellow-50', border: 'border-yellow-200' }
        if (rank <= 50) return { text: 'Top 50', color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' }
        if (rank <= 100) return { text: 'Top 100', color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' }
        if (rank <= 500) return { text: 'Top 500', color: 'text-purple-600', bg: 'bg-purple-50', border: 'border-purple-200' }
        return { text: 'Popular', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' }
    }

    const currentCategory = getRankCategory(currentRank)

    // Get additional stats from nameData
    const totalOccurrences = nameData.total_occurrences || 0
    const peakYear = nameData.peak_year || 'Unknown'
    const peakCount = nameData.peak_count || 0

    return (
        <Card className={`w-full ${className || ''}`}>
            <CardHeader className="pb-3 sm:pb-4">
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-0">
                    <div className="flex-1">
                        <CardTitle className="text-base sm:text-lg lg:text-xl font-bold text-gray-800 flex items-center gap-2">
                            <Trophy className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-600" />
                            Popularity Ranking & Statistics
                        </CardTitle>
                        <p className="text-xs sm:text-sm text-gray-600 mt-1">
                            Current ranking and historical statistics
                        </p>
                    </div>
                    <div className={`flex items-center gap-2 px-2 sm:px-3 py-1 rounded-full border ${currentCategory.bg} ${currentCategory.border}`}>
                        {getRankIcon(currentRank)}
                        <span className={`font-medium text-xs sm:text-sm ${currentCategory.color}`}>
                            #{currentRank} - {currentCategory.text}
                        </span>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="pb-4 sm:pb-6 px-1 sm:px-2 lg:px-4">
                {/* Stats Grid - Mobile optimized */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 lg:gap-4 mb-4 sm:mb-6">
                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-2 sm:p-3 lg:p-4 border border-yellow-200">
                        <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <Trophy className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-600" />
                            <span className="text-xs sm:text-sm font-medium text-yellow-700">Current Rank</span>
                        </div>
                        <span className="text-lg sm:text-xl lg:text-2xl font-bold text-yellow-800">#{currentRank}</span>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-2 sm:p-3 lg:p-4 border border-blue-200">
                        <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
                            <span className="text-xs sm:text-sm font-medium text-blue-700">Total Births</span>
                        </div>
                        <span className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-800">{totalOccurrences.toLocaleString()}</span>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-2 sm:p-3 lg:p-4 border border-green-200">
                        <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <Award className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
                            <span className="text-xs sm:text-sm font-medium text-green-700">Peak Year</span>
                        </div>
                        <span className="text-lg sm:text-xl lg:text-2xl font-bold text-green-800">{peakYear}</span>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-2 sm:p-3 lg:p-4 border border-purple-200">
                        <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <Medal className="h-3 w-3 sm:h-4 sm:w-4 text-purple-600" />
                            <span className="text-xs sm:text-sm font-medium text-purple-700">Peak Count</span>
                        </div>
                        <span className="text-lg sm:text-xl lg:text-2xl font-bold text-purple-800">{peakCount.toLocaleString()}</span>
                    </div>
                </div>

                {/* Ranking Insights */}
                <div className={`rounded-lg p-3 sm:p-4 border ${currentCategory.bg} ${currentCategory.border}`}>
                    <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2 text-sm sm:text-base">
                        {getRankIcon(currentRank)}
                        Ranking Insights
                    </h4>
                    <p className="text-xs sm:text-sm text-gray-700">
                        {currentRank <= 10 && `${nameData.name_en} is in the top 10 most popular ${nameData.gender} names! This name is extremely popular and widely chosen by parents.`}
                        {currentRank > 10 && currentRank <= 50 && `${nameData.name_en} ranks in the top 50 ${nameData.gender} names, making it a very popular choice among parents.`}
                        {currentRank > 50 && currentRank <= 100 && `${nameData.name_en} is in the top 100 ${nameData.gender} names, indicating it's a well-liked and recognized name.`}
                        {currentRank > 100 && currentRank <= 500 && `${nameData.name_en} ranks in the top 500 ${nameData.gender} names, showing it's a moderately popular choice.`}
                        {currentRank > 500 && `${nameData.name_en} is a unique choice that stands out from the most common names.`}
                    </p>
                </div>

                {/* Source Attribution */}
                <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100 space-y-2 sm:space-y-3">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 sm:p-3">
                        <p className="text-xs text-blue-800">
                            <strong>Note:</strong> Rankings and statistics are based on Social Security Administration birth records.
                            Data reflects registered births in the United States.
                        </p>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                            <span>Source: </span>
                            <a
                                href="https://www.ssa.gov/oact/babynames"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline flex items-center gap-1"
                            >
                                Social Security Administration
                                <ExternalLink className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                            </a>
                        </div>
                        <div className="text-left sm:text-right">
                            <span>Based on {new Date().getFullYear()} data</span>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
} 