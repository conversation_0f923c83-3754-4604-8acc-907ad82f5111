"use client"

import Link from "next/link"

interface QueryLink {
    text: string
    href: string
}

interface TopQueryLinksProps {
    links?: QueryLink[]
    max?: number
}

const defaultLinks: QueryLink[] = [
    { text: "Muslim Girl Names", href: "/religions/muslim-girl-names/" },
    { text: "Swedish Boy Names", href: "/sweden/swedish-boy-names/" },
    { text: "Swedish Girl Names", href: "/sweden/swedish-girl-names/" },
    { text: "Canadian Girl Names", href: "/canada/english-girl-names/" },
    { text: "Austrian Boy Names", href: "/austria/german-boy-names/" },
]

export default function TopQueryLinks({ links = defaultLinks, max = 6 }: TopQueryLinksProps) {
    const topLinks = links.slice(0, max)

    return (
        <div>
            <h3 className="font-semibold text-lg mb-4">Popular Searches</h3>
            <ul className="space-y-2">
                {topLinks.map((link) => (
                    <li key={link.href}>
                        <Link href={link.href} className="text-sm text-muted-foreground hover:text-pink-500 transition-colors">
                            {link.text}
                        </Link>
                    </li>
                ))}
            </ul>
        </div>
    )
} 