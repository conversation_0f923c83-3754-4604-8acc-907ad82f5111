"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useTranslation } from "@/hooks/use-translation"
import { useRouter } from "next/navigation"
import type { NameData } from "@/types/name-data"

interface FilterPanelProps {
  activeFilters: {
    gender: string
    religion: string
    language: string
    letter: string
  }
  setActiveFilters: (filters: any) => void
  names: NameData[]
  hideReligionFilter?: boolean
  updateUrl?: boolean
}

export default function FilterPanel({
  activeFilters,
  setActiveFilters,
  names,
  hideReligionFilter = false,
  updateUrl = false,
}: FilterPanelProps) {
  const router = useRouter()
  const { t } = useTranslation()
  const [religions, setReligions] = useState<string[]>([])
  const [languages, setLanguages] = useState<string[]>([])
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")

  // Extract unique religions and languages
  useEffect(() => {
    if (names.length > 0) {
      const uniqueReligions = Array.from(new Set(names.map((name) => name.religion)))
      const uniqueLanguages = Array.from(new Set(names.map((name) => name.language)))

      setReligions(uniqueReligions)
      setLanguages(uniqueLanguages)
    }
  }, [names])

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = {
      ...activeFilters,
      [key]: value,
    }

    setActiveFilters(newFilters)

    // Update URL if enabled
    if (updateUrl) {
      const params = new URLSearchParams()
      if (newFilters.gender !== "all") params.set("gender", newFilters.gender)
      if (newFilters.language !== "all") params.set("language", newFilters.language)
      if (newFilters.letter !== "all") params.set("letter", newFilters.letter)

      router.push(`/?${params.toString()}`)
    }
  }

  const resetFilters = () => {
    const defaultFilters = {
      gender: "all",
      language: "all",
      letter: "all",
    }

    // Keep the religion filter if it's set and we're hiding the religion filter
    if (hideReligionFilter && activeFilters.religion !== "all") {
      setActiveFilters({
        ...defaultFilters,
        religion: activeFilters.religion,
      })
    } else {
      setActiveFilters({
        ...defaultFilters,
        religion: "all",
      })
    }

    // Update URL if enabled
    if (updateUrl) {
      router.push("/")
    }
  }

  // Determine color theme based on religion
  const getColorTheme = () => {
    switch (activeFilters.religion) {
      case "Hindu":
        return {
          border: "border-orange-200 dark:border-orange-800/30",
          ring: "focus:ring-orange-500",
          button: "border-orange-200 hover:bg-orange-50 dark:hover:bg-orange-900/20",
        }
      case "Muslim":
        return {
          border: "border-green-200 dark:border-green-800/30",
          ring: "focus:ring-green-500",
          button: "border-green-200 hover:bg-green-50 dark:hover:bg-green-900/20",
        }
      default:
        return {
          border: "border-pink-200 dark:border-pink-800/30",
          ring: "focus:ring-pink-500",
          button: "border-pink-200 hover:bg-pink-50 dark:hover:bg-pink-900/20",
        }
    }
  }

  const colorTheme = getColorTheme()

  return (
    <Card className={colorTheme.border}>
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Gender Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t("gender")}</label>
            <Select value={activeFilters.gender} onValueChange={(value) => handleFilterChange("gender", value)}>
              <SelectTrigger className={`${colorTheme.border} ${colorTheme.ring}`}>
                <SelectValue placeholder={t("selectGender")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allGenders")}</SelectItem>
                <SelectItem value="male">{t("male")}</SelectItem>
                <SelectItem value="female">{t("female")}</SelectItem>
                <SelectItem value="unisex">{t("unisex")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Religion Filter - Only show if not hidden */}
          {!hideReligionFilter && (
            <div className="space-y-2">
              <label className="text-sm font-medium">{t("religion")}</label>
              <Select value={activeFilters.religion} onValueChange={(value) => handleFilterChange("religion", value)}>
                <SelectTrigger className={`${colorTheme.border} ${colorTheme.ring}`}>
                  <SelectValue placeholder={t("selectReligion")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("allReligions")}</SelectItem>
                  {religions.map((religion) => (
                    <SelectItem key={religion} value={religion}>
                      {religion}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Language Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t("language")}</label>
            <Select value={activeFilters.language} onValueChange={(value) => handleFilterChange("language", value)}>
              <SelectTrigger className={`${colorTheme.border} ${colorTheme.ring}`}>
                <SelectValue placeholder={t("selectLanguage")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allLanguages")}</SelectItem>
                {languages.map((language) => (
                  <SelectItem key={language} value={language}>
                    {language}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Starting Letter Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t("startingLetter")}</label>
            <Select value={activeFilters.letter} onValueChange={(value) => handleFilterChange("letter", value)}>
              <SelectTrigger className={`${colorTheme.border} ${colorTheme.ring}`}>
                <SelectValue placeholder={t("selectLetter")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allLetters")}</SelectItem>
                {letters.map((letter) => (
                  <SelectItem key={letter} value={letter}>
                    {letter}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={resetFilters} className={colorTheme.button}>
            {t("resetFilters")}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
