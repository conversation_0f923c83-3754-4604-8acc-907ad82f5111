"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import { navigationConfig } from "@/lib/navigation-config"
import { cn } from "@/lib/utils"
import { BookOpen, ChevronDown, Globe, Menu, Sparkles, TrendingUp, X } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import HeaderLogo from "./header-logo"

export default function Header() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [expandedCountry, setExpandedCountry] = useState<string | null>(null)
  const [expandedReligion, setExpandedReligion] = useState<string | null>(null)
  const [expandedOther, setExpandedOther] = useState<string | null>(null)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
    setActiveDropdown(null)
  }

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown)
  }

  const isActiveLink = (href: string) => {
    return pathname === href || pathname.startsWith(href + "/")
  }

  const handleCountryToggle = (countryName: string) => {
    setExpandedCountry(expandedCountry === countryName ? null : countryName)
  }

  const handleReligionToggle = (religionName: string) => {
    setExpandedReligion(expandedReligion === religionName ? null : religionName)
  }

  const handleOtherToggle = (otherName: string) => {
    setExpandedOther(expandedOther === otherName ? null : otherName)
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-pink-100 dark:border-pink-900/20">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/" className="flex items-center space-x-2">
            <HeaderLogo />
          </Link>
        </div>

        {/* Desktop navigation */}
        <nav className="hidden lg:flex items-center space-x-1">
          {/* Baby Name Generator - High Priority */}
          <Link
            href="/baby-name-generator"
            className={cn(
              "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 focus:outline-none",
              isActiveLink("/baby-name-generator") ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
            )}
          >
            <Sparkles className="h-4 w-4" />
            <span>Name Generator</span>
          </Link>

          {/* Countries Dropdown */}
          <div className="relative">
            <button
              className={cn(
                "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 focus:outline-none",
                activeDropdown === "countries" ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
              )}
              onClick={() => toggleDropdown("countries")}
            >
              <Globe className="h-4 w-4" />
              <span>Countries</span>
              <ChevronDown className={cn("h-4 w-4 transition-transform", activeDropdown === "countries" && "rotate-180")} />
            </button>
            {activeDropdown === "countries" && (
              <div className="absolute left-0 mt-2 w-[400px] max-h-[70vh] overflow-y-auto bg-white dark:bg-zinc-900 border border-pink-100 dark:border-pink-900/20 rounded-xl shadow-2xl z-50 p-3 flex flex-col gap-2">
                <h3 className="text-sm font-bold text-pink-500 mb-1">Browse by Country</h3>
                <div className="flex flex-col gap-2">
                  {navigationConfig.countries.map((country) => (
                    <div
                      key={country.name}
                      className="bg-pink-50/60 dark:bg-pink-900/10 rounded-lg p-2 border border-pink-100 dark:border-pink-900/20 transition-shadow"
                    >
                      <button
                        className="flex items-center gap-2 w-full text-left focus:outline-none"
                        onClick={() => handleCountryToggle(country.name)}
                        aria-expanded={expandedCountry === country.name}
                        aria-controls={`country-${country.name}`}
                      >
                        <span className="text-lg">{country.flag}</span>
                        <span className="font-bold text-xs text-foreground">{country.name}</span>
                        {country.description && (
                          <span className="ml-2 text-[10px] text-muted-foreground italic">{country.description}</span>
                        )}
                        <ChevronDown className={`ml-auto h-4 w-4 transition-transform ${expandedCountry === country.name ? 'rotate-180' : ''}`} />
                      </button>
                      {expandedCountry === country.name && (
                        <div id={`country-${country.name}`} className="flex flex-col gap-1 mt-1 ml-7">
                          {/* Country Landing Page Link */}
                          {country.landingHref && (
                            <div className="flex items-center gap-2 group py-0.5 px-1 rounded hover:bg-pink-100/60 dark:hover:bg-pink-900/30 transition-colors">
                              <Link
                                href={country.landingHref}
                                className={cn(
                                  "font-medium text-xs text-foreground group-hover:text-pink-600 transition-colors",
                                  isActiveLink(country.landingHref) && "text-pink-500"
                                )}
                                onClick={() => setActiveDropdown(null)}
                              >
                                Overview
                              </Link>
                            </div>
                          )}
                          {/* Language Links */}
                          {country.languages.map((language) => (
                            <div key={language.href} className="flex items-center gap-2 group py-0.5 px-1 rounded hover:bg-pink-100/60 dark:hover:bg-pink-900/30 transition-colors">
                              <Link
                                href={language.href}
                                className={cn(
                                  "font-medium text-xs text-foreground group-hover:text-pink-600 transition-colors",
                                  isActiveLink(language.href) && "text-pink-500"
                                )}
                                onClick={() => setActiveDropdown(null)}
                              >
                                {language.name} Names
                              </Link>
                              <div className="flex gap-1 ml-1">
                                <Link
                                  href={language.boyHref}
                                  className={cn(
                                    "px-2 py-0.5 rounded-full text-[10px] font-semibold border border-pink-200 dark:border-pink-900/40 bg-white dark:bg-zinc-800 text-pink-600 hover:bg-pink-100 dark:hover:bg-pink-900/30 transition-colors",
                                    isActiveLink(language.boyHref) && "bg-pink-500 text-white border-pink-500"
                                  )}
                                  onClick={() => setActiveDropdown(null)}
                                >
                                  Boys
                                </Link>
                                <Link
                                  href={language.girlHref}
                                  className={cn(
                                    "px-2 py-0.5 rounded-full text-[10px] font-semibold border border-pink-200 dark:border-pink-900/40 bg-white dark:bg-zinc-800 text-pink-600 hover:bg-pink-100 dark:hover:bg-pink-900/30 transition-colors",
                                    isActiveLink(language.girlHref) && "bg-pink-500 text-white border-pink-500"
                                  )}
                                  onClick={() => setActiveDropdown(null)}
                                >
                                  Girls
                                </Link>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Religions Dropdown */}
          <div className="relative">
            <button
              className={cn(
                "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 focus:outline-none",
                activeDropdown === "religions" ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
              )}
              onClick={() => toggleDropdown("religions")}
            >
              <BookOpen className="h-4 w-4" />
              <span>Religions</span>
              <ChevronDown className={cn("h-4 w-4 transition-transform", activeDropdown === "religions" && "rotate-180")} />
            </button>
            {activeDropdown === "religions" && (
              <div className="absolute left-0 mt-2 w-[320px] max-h-[60vh] overflow-y-auto bg-white dark:bg-zinc-900 border border-pink-100 dark:border-pink-900/20 rounded-xl shadow-2xl z-50 p-3 flex flex-col gap-2">
                <h3 className="text-sm font-bold text-pink-500 mb-1">Religious Baby Names</h3>
                <div className="flex flex-col gap-2">
                  {navigationConfig.religions.map((religion) => (
                    <div
                      key={religion.name}
                      className="bg-pink-50/60 dark:bg-pink-900/10 rounded-lg p-2 border border-pink-100 dark:border-pink-900/20 transition-shadow"
                    >
                      <button
                        className="flex items-center gap-2 w-full text-left focus:outline-none"
                        onClick={() => handleReligionToggle(religion.name)}
                        aria-expanded={expandedReligion === religion.name}
                        aria-controls={`religion-${religion.name}`}
                      >
                        <span className="font-bold text-xs text-foreground">{religion.name}</span>
                        {religion.description && (
                          <span className="ml-2 text-[10px] text-muted-foreground italic">{religion.description}</span>
                        )}
                        <ChevronDown className={`ml-auto h-4 w-4 transition-transform ${expandedReligion === religion.name ? 'rotate-180' : ''}`} />
                      </button>
                      {expandedReligion === religion.name && (
                        <div id={`religion-${religion.name}`} className="flex flex-col gap-1 mt-1 ml-7">
                          <div className="flex items-center gap-2 group py-0.5 px-1 rounded hover:bg-pink-100/60 dark:hover:bg-pink-900/30 transition-colors">
                            <Link
                              href={religion.href}
                              className={cn(
                                "font-medium text-xs text-foreground group-hover:text-pink-600 transition-colors",
                                isActiveLink(religion.href) && "text-pink-500"
                              )}
                              onClick={() => setActiveDropdown(null)}
                            >
                              All Names
                            </Link>
                            <div className="flex gap-1 ml-1">
                              <Link
                                href={religion.boyHref}
                                className={cn(
                                  "px-2 py-0.5 rounded-full text-[10px] font-semibold border border-pink-200 dark:border-pink-900/40 bg-white dark:bg-zinc-800 text-pink-600 hover:bg-pink-100 dark:hover:bg-pink-900/30 transition-colors",
                                  isActiveLink(religion.boyHref) && "bg-pink-500 text-white border-pink-500"
                                )}
                                onClick={() => setActiveDropdown(null)}
                              >
                                Boys
                              </Link>
                              <Link
                                href={religion.girlHref}
                                className={cn(
                                  "px-2 py-0.5 rounded-full text-[10px] font-semibold border border-pink-200 dark:border-pink-900/40 bg-white dark:bg-zinc-800 text-pink-600 hover:bg-pink-100 dark:hover:bg-pink-900/30 transition-colors",
                                  isActiveLink(religion.girlHref) && "bg-pink-500 text-white border-pink-500"
                                )}
                                onClick={() => setActiveDropdown(null)}
                              >
                                Girls
                              </Link>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Other Links Dropdown (if any) */}
          {navigationConfig.other.length > 0 && (
            <div className="relative">
              <button
                className={cn(
                  "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 focus:outline-none",
                  activeDropdown === "other" ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
                )}
                onClick={() => toggleDropdown("other")}
              >
                <span>More</span>
                <ChevronDown className={cn("h-4 w-4 transition-transform", activeDropdown === "other" && "rotate-180")} />
              </button>
              {activeDropdown === "other" && (
                <div className="absolute left-0 mt-2 w-[200px] bg-white dark:bg-zinc-900 border border-pink-100 dark:border-pink-900/20 rounded-xl shadow-2xl z-50 p-2">
                  <div className="flex flex-col gap-1">
                    {navigationConfig.other.map((other) => (
                      <Link
                        key={other.name}
                        href={other.href}
                        className={cn(
                          "block px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20",
                          isActiveLink(other.href) ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
                        )}
                        onClick={() => setActiveDropdown(null)}
                      >
                        {other.name}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Trending Dropdown */}
          <div className="relative">
            <button
              className={cn(
                "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 focus:outline-none",
                activeDropdown === "trending" ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
              )}
              onClick={() => toggleDropdown("trending")}
            >
              <TrendingUp className="h-4 w-4" />
              <span>Trending</span>
              <ChevronDown className={cn("h-4 w-4 transition-transform", activeDropdown === "trending" && "rotate-180")} />
            </button>

            {activeDropdown === "trending" && (
              <div className="absolute left-0 mt-2 w-[380px] max-h-[70vh] overflow-y-auto bg-white dark:bg-zinc-900 border border-pink-100 dark:border-pink-900/20 rounded-xl shadow-2xl z-50 p-3 flex flex-col gap-2">
                <h3 className="text-sm font-bold text-pink-500 mb-1">Trending Baby Names 2025</h3>
                <div className="flex flex-col gap-2">
                  {navigationConfig.trending.filter(trending => trending.priority === "high").map((trending) => (
                    <div key={trending.href} className="bg-pink-50/60 dark:bg-pink-900/10 rounded-lg p-2 border border-pink-100 dark:border-pink-900/20 transition-shadow">
                      <Link
                        href={trending.href}
                        className={cn(
                          "block text-sm font-medium transition-colors hover:text-pink-600",
                          isActiveLink(trending.href) ? "text-pink-500" : "text-foreground"
                        )}
                        onClick={() => setActiveDropdown(null)}
                      >
                        {trending.name}
                      </Link>
                      <p className="text-xs text-muted-foreground mt-1">{trending.description}</p>
                    </div>
                  ))}
                </div>

                <div className="border-t border-pink-100 dark:border-pink-900/20 mt-2 pt-2">
                  <h4 className="text-xs font-medium text-pink-600 mb-1">More Options</h4>
                  <div className="flex flex-col gap-1">
                    {navigationConfig.trending.filter(trending => trending.priority === "medium").map((trending) => (
                      <Link
                        key={trending.href}
                        href={trending.href}
                        className="block p-2 rounded-md text-xs text-muted-foreground hover:text-foreground hover:bg-pink-50 dark:hover:bg-pink-900/20 transition-colors"
                        onClick={() => setActiveDropdown(null)}
                      >
                        {trending.name}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Blog Link */}
          <Link
            href="/blog"
            className={cn(
              "px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20",
              isActiveLink("/blog") ? "text-pink-500 bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground hover:text-pink-500"
            )}
          >
            Blog
          </Link>
        </nav>

        <div className="flex items-center space-x-2">
          {/* Mobile menu button */}
          <Drawer open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <DrawerTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                aria-label="Toggle menu"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="max-h-[85vh]">
              <DrawerHeader className="border-b">
                <div className="flex items-center justify-between">
                  <DrawerTitle>Navigation</DrawerTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={closeMenu}
                    aria-label="Close menu"
                    className="h-8 w-8"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </DrawerHeader>
              <ScrollArea className="flex-1 overflow-y-auto">
                <div className="p-4 space-y-6">
                  {/* Countries Section */}
                  <div>
                    <div className="flex items-center space-x-2 text-sm font-semibold text-pink-500 mb-4">
                      <Globe className="h-4 w-4" />
                      <span>Countries</span>
                    </div>
                    <div className="space-y-4">
                      {navigationConfig.countries.map((country) => (
                        <div key={country.name} className="space-y-3">
                          <div className="flex items-center space-x-2 p-2 bg-pink-50/60 dark:bg-pink-900/10 rounded-lg">
                            <span className="text-lg">{country.flag}</span>
                            <span className="text-sm font-medium text-foreground">{country.name}</span>
                            {country.description && (
                              <span className="text-xs text-muted-foreground italic ml-2">{country.description}</span>
                            )}
                          </div>
                          <div className="ml-4 space-y-2">
                            {/* Country Landing Page Link */}
                            {country.landingHref && (
                              <Link
                                href={country.landingHref}
                                className={cn(
                                  "block text-sm px-3 py-2 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20",
                                  isActiveLink(country.landingHref) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                                )}
                                onClick={closeMenu}
                              >
                                {country.name} Overview
                              </Link>
                            )}
                            {country.languages.map((language) => (
                              <div key={language.href} className="space-y-2">
                                <Link
                                  href={language.href}
                                  className={cn(
                                    "block text-sm px-3 py-2 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20",
                                    isActiveLink(language.href) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                                  )}
                                  onClick={closeMenu}
                                >
                                  {language.name} Names
                                </Link>
                                <div className="flex space-x-2 ml-3">
                                  <Link
                                    href={language.boyHref}
                                    className={cn(
                                      "text-xs px-3 py-1.5 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 border border-pink-200 dark:border-pink-900/40",
                                      isActiveLink(language.boyHref) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                                    )}
                                    onClick={closeMenu}
                                  >
                                    Boys
                                  </Link>
                                  <Link
                                    href={language.girlHref}
                                    className={cn(
                                      "text-xs px-3 py-1.5 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 border border-pink-200 dark:border-pink-900/40",
                                      isActiveLink(language.girlHref) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                                    )}
                                    onClick={closeMenu}
                                  >
                                    Girls
                                  </Link>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Religions Section */}
                  <div>
                    <div className="flex items-center space-x-2 text-sm font-semibold text-pink-500 mb-4">
                      <BookOpen className="h-4 w-4" />
                      <span>Religions</span>
                    </div>
                    <div className="space-y-3">
                      {navigationConfig.religions.map((religion) => (
                        <div key={religion.href} className="space-y-2">
                          <div className="p-2 bg-pink-50/60 dark:bg-pink-900/10 rounded-lg">
                            <Link
                              href={religion.href}
                              className={cn(
                                "block text-sm font-medium transition-colors hover:text-pink-600",
                                isActiveLink(religion.href) ? "text-pink-500" : "text-foreground"
                              )}
                              onClick={closeMenu}
                            >
                              {religion.name}
                            </Link>
                            {religion.description && (
                              <p className="text-xs text-muted-foreground mt-1">{religion.description}</p>
                            )}
                          </div>
                          <div className="flex space-x-2 ml-3">
                            <Link
                              href={religion.boyHref}
                              className={cn(
                                "text-xs px-3 py-1.5 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 border border-pink-200 dark:border-pink-900/40",
                                isActiveLink(religion.boyHref) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                              )}
                              onClick={closeMenu}
                            >
                              Boys
                            </Link>
                            <Link
                              href={religion.girlHref}
                              className={cn(
                                "text-xs px-3 py-1.5 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20 border border-pink-200 dark:border-pink-900/40",
                                isActiveLink(religion.girlHref) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                              )}
                              onClick={closeMenu}
                            >
                              Girls
                            </Link>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Trending Section */}
                  <div>
                    <div className="flex items-center space-x-2 text-sm font-semibold text-pink-500 mb-4">
                      <TrendingUp className="h-4 w-4" />
                      <span>Trending Names</span>
                    </div>
                    <div className="space-y-2">
                      {/* Baby Name Generator - High Priority */}
                      <Link
                        href="/baby-name-generator"
                        className={cn(
                          "flex items-center space-x-2 text-sm font-medium transition-colors hover:text-pink-600",
                          isActiveLink("/baby-name-generator") ? "text-pink-500" : "text-foreground"
                        )}
                        onClick={closeMenu}
                      >
                        <Sparkles className="h-4 w-4" />
                        <span>AI Name Generator</span>
                      </Link>
                      {navigationConfig.trending.filter(trending => trending.priority === "high").map((trending) => (
                        <Link
                          key={trending.href}
                          href={trending.href}
                          className={cn(
                            "block text-sm font-medium transition-colors hover:text-pink-600",
                            isActiveLink(trending.href) ? "text-pink-500" : "text-foreground"
                          )}
                          onClick={closeMenu}
                        >
                          {trending.name}
                        </Link>
                      ))}
                    </div>
                  </div>

                  {/* Blog Link */}
                  <div>
                    <Link
                      href="/blog"
                      className={cn(
                        "block text-sm px-3 py-2 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20",
                        isActiveLink("/blog") ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                      )}
                      onClick={closeMenu}
                    >
                      Blog
                    </Link>
                  </div>

                  {/* About & Contact Section */}
                  <div>
                    <div className="flex items-center space-x-2 text-sm font-semibold text-pink-500 mb-4">
                      <span>Info</span>
                    </div>
                    <div className="space-y-2">
                      {navigationConfig.other.map((other) => (
                        <Link
                          key={other.href}
                          href={other.href}
                          className={cn(
                            "block text-sm px-3 py-2 rounded-md transition-colors hover:bg-pink-50 dark:hover:bg-pink-900/20",
                            isActiveLink(other.href) ? "text-pink-500 font-medium bg-pink-50 dark:bg-pink-900/20" : "text-muted-foreground"
                          )}
                          onClick={closeMenu}
                        >
                          {other.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
                <ScrollBar orientation="vertical" />
              </ScrollArea>
            </DrawerContent>
          </Drawer>
        </div>
      </div>

      {/* Backdrop for dropdowns */}
      {activeDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setActiveDropdown(null)}
        />
      )}
    </header>
  )
}
