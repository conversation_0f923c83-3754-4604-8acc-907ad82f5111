"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, ExternalLink } from "lucide-react"
import { useTranslation } from "@/hooks/use-translation"
import type { NameData } from "@/types/name-data"

interface NameOfTheDayProps {
  name: NameData
  isFavorite: boolean
  onToggleFavorite: () => void
  onViewDetails: () => void
}

export default function NameOfTheDay({ name, isFavorite, onToggleFavorite, onViewDetails }: NameOfTheDayProps) {
  const { t, currentLanguage } = useTranslation()

  // Determine gradient based on religion
  const getGradient = () => {
    switch (name.religion) {
      case "Hindu":
        return "from-orange-100 to-red-100 dark:from-orange-900/20 dark:to-red-900/20"
      case "Muslim":
        return "from-green-100 to-teal-100 dark:from-green-900/20 dark:to-teal-900/20"
      case "Christian":
        return "from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20"
      case "Sikh":
        return "from-yellow-100 to-amber-100 dark:from-yellow-900/20 dark:to-amber-900/20"
      default:
        return "from-pink-100 to-purple-100 dark:from-pink-900/20 dark:to-purple-900/20"
    }
  }

  // Determine button color based on religion
  const getButtonClass = () => {
    switch (name.religion) {
      case "Hindu":
        return "bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
      case "Muslim":
        return "bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600"
      case "Christian":
        return "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
      case "Sikh":
        return "bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600"
      default:
        return "bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
    }
  }

  // Determine heart color based on religion
  const getHeartClass = () => {
    if (!isFavorite) return "text-muted-foreground"

    switch (name.religion) {
      case "Hindu":
        return "fill-orange-500 text-orange-500"
      case "Muslim":
        return "fill-green-500 text-green-500"
      case "Christian":
        return "fill-blue-500 text-blue-500"
      case "Sikh":
        return "fill-yellow-500 text-yellow-500"
      default:
        return "fill-pink-500 text-pink-500"
    }
  }

  return (
    <Card className={`bg-gradient-to-r ${getGradient()} overflow-hidden border-none shadow-md`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-2xl font-bold">
          {name.name_en}
          <span className="text-sm font-normal text-muted-foreground ml-2">
            ({t(name.gender as "male" | "female" | "unisex")})
          </span>
        </CardTitle>
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleFavorite}
          aria-label={isFavorite ? t("removeFromFavorites") : t("addToFavorites")}
        >
          <Heart className={`h-5 w-5 ${getHeartClass()}`} />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <div className="text-3xl font-medium">{name.name_native}</div>

          <div className="space-y-2">
            <div className="text-lg">
              <span className="font-medium">{t("meaning")}:</span>{" "}
              {currentLanguage === "en" ? name.meaning_en : name.meaning_native}
            </div>

            <div className="grid grid-cols-2 gap-4 mt-2">
              <div>
                <div className="text-sm text-muted-foreground">{t("religion")}</div>
                <div className="font-medium">{name.religion}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">{t("language")}</div>
                <div className="font-medium">{name.language}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={onViewDetails} className={`w-full text-white ${getButtonClass()}`}>
          {t("viewDetails")} <ExternalLink className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}
