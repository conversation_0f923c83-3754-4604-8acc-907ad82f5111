"use client"

import Link from "next/link"
import { cn } from "@/lib/utils"

interface CategoryLinksProps {
  activeReligion?: string
}

export default function CategoryLinks({ activeReligion }: CategoryLinksProps) {
  const categories = [
    {
      name: "Muslim",
      boyPath: "/religions/muslim-boy-names",
      girlPath: "/religions/muslim-girl-names",
      color: "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
      activeColor: "bg-green-100 border-green-300 text-green-800",
    },
    {
      name: "Sikh",
      boyPath: "/religions/sikh-boy-names",
      girlPath: "/religions/sikh-girl-names",
      color: "bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-700",
      activeColor: "bg-yellow-100 border-yellow-300 text-yellow-800",
    },
    {
      name: "Christian",
      boyPath: "/religions/christian-boy-names",
      girlPath: "/religions/christian-girl-names",
      color: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
      activeColor: "bg-blue-100 border-blue-300 text-blue-800",
    },
  ]

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {categories.map((category) => (
        <div key={category.name} className="flex-1 min-w-[100px] flex flex-col gap-1">
          <Link href={category.boyPath}>
            <div
              className={cn(
                "py-2 px-3 rounded-md border text-center font-medium transition-colors text-sm",
                activeReligion === category.name ? category.activeColor : category.color,
              )}
            >
              {category.name} Boy Names
            </div>
          </Link>
          <Link href={category.girlPath}>
            <div
              className={cn(
                "py-2 px-3 rounded-md border text-center font-medium transition-colors text-sm",
                activeReligion === category.name ? category.activeColor : category.color,
              )}
            >
              {category.name} Girl Names
            </div>
          </Link>
        </div>
      ))}
    </div>
  )
}
