"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface AlphabetFilterProps {
  activeLetter: string
  onLetterClick: (letter: string) => void
}

export default function AlphabetFilter({ activeLetter, onLetterClick }: AlphabetFilterProps) {
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")

  return (
    <div className="bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-900/20 dark:to-purple-900/20 p-4 rounded-lg shadow-sm border border-pink-100 dark:border-pink-900/30">
      <h3 className="text-sm font-medium mb-3 text-pink-700 dark:text-pink-300">Filter by first letter:</h3>
      <div className="flex flex-wrap gap-1.5">
        {alphabet.map((letter) => (
          <Button
            key={letter}
            variant="ghost"
            size="sm"
            className={cn(
              "min-w-[36px] h-9 p-0 font-medium rounded-md",
              activeLetter === letter
                ? "bg-pink-200 text-pink-800 hover:bg-pink-300 dark:bg-pink-700 dark:text-pink-100"
                : "hover:bg-pink-100 text-pink-700 dark:hover:bg-pink-800 dark:text-pink-300",
            )}
            onClick={() => onLetterClick(letter)}
          >
            {letter}
          </Button>
        ))}
        {activeLetter !== "all" && (
          <Button
            variant="ghost"
            size="sm"
            className="text-red-600 hover:bg-red-100 dark:text-red-400 dark:hover:bg-red-900/20 font-medium"
            onClick={() => onLetterClick("all")}
          >
            Clear
          </Button>
        )}
      </div>
    </div>
  )
}
