"use client"

import TopQueryLinks from "@/components/top-query-links"
import Link from "next/link"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-950/30 dark:to-purple-950/30 py-8 border-t border-pink-100 dark:border-pink-900/20 relative mt-20">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-semibold text-lg mb-4 bg-gradient-to-r from-pink-500 to-purple-600 text-transparent bg-clip-text">
              Baby Names
            </h3>
            <p className="text-sm text-muted-foreground">
              Discover beautiful baby names with meanings in English and native languages.
            </p>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-sm text-muted-foreground hover:text-pink-500 transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link
                  href="/baby-name-generator"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  AI Name Generator
                </Link>
              </li>
              <li>
                <Link
                  href="/religions/muslim-boy-names"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  Muslim Names
                </Link>
              </li>
              <li>
                <Link
                  href="/religions/sikh-boy-names"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  Sikh Names
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-sm text-muted-foreground hover:text-pink-500 transition-colors">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-4">About</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms-of-service"
                  className="text-sm text-muted-foreground hover:text-pink-500 transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>

          {/* Popular search links based on Search Console data */}
          <TopQueryLinks />
        </div>

        <div className="mt-8 pt-4 border-t border-pink-100 dark:border-pink-900/20 text-center text-sm text-muted-foreground">
          &copy; {currentYear} BabyNames. All Rights Reserved.
        </div>
      </div>
    </footer>
  )
}
