"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowRight, CheckCircle, Heart, Mic, Star, Volume2, X, XCircle, Zap } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"

interface CompatibilityResult {
    id: string
    name: string
    surname: string
    fullName: string
    flowScore: number
    pronunciationScore: number
    initialsScore: number
    overallScore: number
    flowAnalysis: string
    pronunciationAnalysis: string
    initialsAnalysis: string
    suggestions: string[]
    warnings: string[]
    timestamp: number
}

interface NameCompatibilityModalProps {
    babyName: string
    isOpen: boolean
    onClose: () => void
}

export default function NameCompatibilityModal({ babyName, isOpen, onClose }: NameCompatibilityModalProps) {
    const router = useRouter()
    const [surname, setSurname] = useState("")
    const [results, setResults] = useState<CompatibilityResult[]>([])
    const [isAnalyzing, setIsAnalyzing] = useState(false)

    // Sample surnames for quick testing
    const sampleSurnames = [
        "<PERSON>", "<PERSON>", "Williams", "<PERSON>", "Jones", "<PERSON>", "<PERSON>", "Davis",
        "<PERSON>", "Martinez", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    ]

    const analyzeCompatibility = async () => {
        if (!baby<PERSON>ame.trim() || !surname.trim()) return

        setIsAnalyzing(true)

        // Simulate API call - same timing as original
        await new Promise(resolve => setTimeout(resolve, 2000))

        const fullName = `${babyName} ${surname}`
        const initials = `${babyName.charAt(0)}${surname.charAt(0)}`.toUpperCase()

        // Analyze flow (syllable rhythm) - exact same logic as original
        const firstNameSyllables = countSyllables(babyName)
        const surnameSyllables = countSyllables(surname)
        const flowScore = calculateFlowScore(firstNameSyllables, surnameSyllables, babyName, surname)

        // Analyze pronunciation - exact same logic as original
        const pronunciationScore = calculatePronunciationScore(babyName, surname)

        // Analyze initials - exact same logic as original
        const initialsScore = calculateInitialsScore(initials)

        const overallScore = Math.round((flowScore + pronunciationScore + initialsScore) / 3)

        const result: CompatibilityResult = {
            id: Date.now().toString(), // Unique ID
            name: babyName,
            surname: surname,
            fullName: fullName,
            flowScore,
            pronunciationScore,
            initialsScore,
            overallScore,
            flowAnalysis: getFlowAnalysis(firstNameSyllables, surnameSyllables, flowScore),
            pronunciationAnalysis: getPronunciationAnalysis(babyName, surname, pronunciationScore),
            initialsAnalysis: getInitialsAnalysis(initials, initialsScore),
            suggestions: getSuggestions(babyName, surname, overallScore),
            warnings: getWarnings(babyName, surname, initials),
            timestamp: Date.now() // Timestamp for sorting
        }

        setResults(prevResults => [result, ...prevResults.slice(0, 4)]) // Keep last 5 results - same as original
        setIsAnalyzing(false)
    }

    const countSyllables = (word: string): number => {
        word = word.toLowerCase()
        word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '')
        word = word.replace(/^y/, '')
        const syllables = word.match(/[aeiouy]{1,2}/g)
        return syllables ? syllables.length : 1
    }

    const calculateFlowScore = (firstNameSyllables: number, surnameSyllables: number, firstName: string, surname: string): number => {
        let score = 70 // Base score

        // Syllable balance
        const totalSyllables = firstNameSyllables + surnameSyllables
        if (totalSyllables >= 4 && totalSyllables <= 6) score += 15
        else if (totalSyllables < 3) score -= 10
        else if (totalSyllables > 7) score -= 15

        // Ending/beginning sound harmony
        const firstNameEnd = firstName.toLowerCase().slice(-1)
        const surnameStart = surname.toLowerCase().charAt(0)
        if (firstNameEnd === surnameStart) score += 10
        else if (['a', 'e', 'i', 'o', 'u'].includes(firstNameEnd) && ['a', 'e', 'i', 'o', 'u'].includes(surnameStart)) score += 5

        // Rhythm pattern
        if (firstNameSyllables === surnameSyllables) score += 10
        else if (Math.abs(firstNameSyllables - surnameSyllables) === 1) score += 5

        return Math.min(100, Math.max(0, score))
    }

    const calculatePronunciationScore = (firstName: string, surname: string): number => {
        let score = 80 // Base score

        const fullName = `${firstName} ${surname}`.toLowerCase()

        // Check for difficult combinations
        if (fullName.includes('thth') || fullName.includes('shsh')) score -= 20
        if (fullName.includes('ngng') || fullName.includes('chch')) score -= 15

        // Check for smooth transitions
        const vowels = ['a', 'e', 'i', 'o', 'u']
        const firstNameEnd = firstName.toLowerCase().slice(-1)
        const surnameStart = surname.toLowerCase().charAt(0)

        if (vowels.includes(firstNameEnd) && vowels.includes(surnameStart)) score += 10
        if (firstNameEnd === surnameStart) score += 5

        return Math.min(100, Math.max(0, score))
    }

    const calculateInitialsScore = (initials: string): number => {
        let score = 90 // Base score

        // Check for problematic initials
        const problematicCombos = ['BS', 'FU', 'KK', 'SS', 'TT', 'UU', 'VV', 'WW', 'XX', 'YY', 'ZZ']
        if (problematicCombos.includes(initials)) score -= 50

        // Check for common words
        const wordCombos = ['AS', 'AT', 'BE', 'BY', 'DO', 'GO', 'HE', 'IF', 'IN', 'IT', 'ME', 'NO', 'OF', 'ON', 'OR', 'SO', 'TO', 'UP', 'US', 'WE']
        if (wordCombos.includes(initials)) score -= 20

        // Check for repeated letters
        if (initials.charAt(0) === initials.charAt(1)) score -= 15

        return Math.min(100, Math.max(0, score))
    }

    const getFlowAnalysis = (firstNameSyllables: number, surnameSyllables: number, flowScore: number): string => {
        if (flowScore >= 90) return "Excellent flow! The name and surname create a perfect rhythm."
        if (flowScore >= 80) return "Great flow! The combination sounds natural and harmonious."
        if (flowScore >= 70) return "Good flow! The name flows well together."
        if (flowScore >= 60) return "Fair flow. The combination works but could be smoother."
        return "Poor flow. Consider a different name or surname combination."
    }

    const getPronunciationAnalysis = (firstName: string, surname: string, pronunciationScore: number): string => {
        if (pronunciationScore >= 90) return "Very easy to pronounce! Clear and distinct sounds."
        if (pronunciationScore >= 80) return "Easy to pronounce! Most people will say it correctly."
        if (pronunciationScore >= 70) return "Generally easy to pronounce with minor challenges."
        if (pronunciationScore >= 60) return "Moderate pronunciation difficulty. May need clarification."
        return "Difficult to pronounce. Expect frequent mispronunciations."
    }

    const getInitialsAnalysis = (initials: string, initialsScore: number): string => {
        if (initialsScore >= 90) return "Great initials! Clean and professional."
        if (initialsScore >= 80) return "Good initials! No major concerns."
        if (initialsScore >= 70) return "Acceptable initials. Some minor considerations."
        if (initialsScore >= 60) return "Problematic initials. May cause issues."
        return "Poor initials. Strongly consider alternatives."
    }

    const getSuggestions = (firstName: string, surname: string, overallScore: number): string[] => {
        const suggestions = []

        if (overallScore < 70) {
            suggestions.push("Try a different first name with similar meaning")
            suggestions.push("Consider using a middle name to improve flow")
            suggestions.push("Test with a nickname or shortened version")
        }

        if (firstName.length < 3) {
            suggestions.push("Consider a longer first name for better balance")
        }

        if (surname.length > 8) {
            suggestions.push("A shorter first name might work better")
        }

        return suggestions
    }

    const getWarnings = (firstName: string, surname: string, initials: string): string[] => {
        const warnings = []

        if (initials.length === 2 && initials.charAt(0) === initials.charAt(1)) {
            warnings.push("Repeated initials may look unusual")
        }

        if (firstName.toLowerCase().endsWith(surname.toLowerCase().charAt(0))) {
            warnings.push("Name ends with same sound as surname starts")
        }

        if (firstName.length + surname.length > 15) {
            warnings.push("Very long full name may be cumbersome")
        }

        return warnings
    }

    const getScoreColor = (score: number): string => {
        if (score >= 90) return "text-green-600"
        if (score >= 80) return "text-blue-600"
        if (score >= 70) return "text-yellow-600"
        if (score >= 60) return "text-orange-600"
        return "text-red-600"
    }

    const getScoreIcon = (score: number) => {
        if (score >= 90) return <Star className="h-4 w-4 text-green-600" />
        if (score >= 80) return <CheckCircle className="h-4 w-4 text-blue-600" />
        if (score >= 70) return <CheckCircle className="h-4 w-4 text-yellow-600" />
        if (score >= 60) return <CheckCircle className="h-4 w-4 text-orange-600" />
        return <XCircle className="h-4 w-4 text-red-600" />
    }

    const getScoreBg = (score: number): string => {
        if (score >= 90) return "bg-green-50 border-green-200"
        if (score >= 80) return "bg-blue-50 border-blue-200"
        if (score >= 70) return "bg-yellow-50 border-yellow-200"
        if (score >= 60) return "bg-orange-50 border-orange-200"
        return "bg-red-50 border-red-200"
    }

    const handleFullCheckerRedirect = () => {
        onClose()
        router.push('/name-compatibility-checker')
    }

    const clearResults = () => {
        setResults([])
    }

    const speakFullName = (fullName: string) => {
        if (!fullName.trim()) return

        // Stop any current speech
        window.speechSynthesis.cancel()

        // Create utterance with slower, clearer speech
        const utterance = new SpeechSynthesisUtterance(fullName)
        utterance.rate = 0.8 // Slightly slower for clarity
        utterance.pitch = 1.0
        utterance.volume = 1.0

        // Try to use a good voice if available
        const voices = window.speechSynthesis.getVoices()
        const preferredVoice = voices.find(voice =>
            voice.lang.includes('en') && voice.name.includes('Female')
        ) || voices.find(voice => voice.lang.includes('en'))

        if (preferredVoice) {
            utterance.voice = preferredVoice
        }

        window.speechSynthesis.speak(utterance)
    }

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="sticky top-0 bg-white border-b border-gray-200 p-4 rounded-t-2xl">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="bg-gradient-to-r from-pink-500 to-purple-500 rounded-full p-2">
                                <Heart className="h-5 w-5 text-white" />
                            </div>
                            <div>
                                <h2 className="text-lg font-bold text-gray-900">Name Compatibility</h2>
                                <p className="text-sm text-gray-600">Test with your surname</p>
                            </div>
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            className="h-8 w-8 p-0"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-4 space-y-4">
                    {/* Input Section */}
                    <Card className="border-0 shadow-sm">
                        <CardContent className="p-4 space-y-4">
                            <div>
                                <Label className="text-sm font-medium text-gray-700">Baby Name</Label>
                                <div className="mt-1 p-3 bg-gray-50 rounded-lg border">
                                    <span className="font-semibold text-gray-900">{babyName}</span>
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="surname" className="text-sm font-medium text-gray-700">
                                    Your Surname
                                </Label>
                                <Input
                                    id="surname"
                                    placeholder="Enter your surname..."
                                    value={surname}
                                    onChange={(e) => setSurname(e.target.value)}
                                    className="mt-1"
                                />
                            </div>

                            <Button
                                onClick={analyzeCompatibility}
                                disabled={!surname.trim() || isAnalyzing}
                                className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white"
                            >
                                {isAnalyzing ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Analyzing...
                                    </>
                                ) : (
                                    <>
                                        <Zap className="mr-2 h-4 w-4" />
                                        Check Compatibility
                                    </>
                                )}
                            </Button>

                            <p className="text-xs text-gray-500 text-center">
                                💡 After testing, tap the 🔊 icon to hear how the names sound together
                            </p>
                        </CardContent>
                    </Card>

                    {/* Results */}
                    {results.length > 0 && (
                        <div className="space-y-3">
                            <div className="flex items-center justify-between">
                                <h3 className="font-semibold text-gray-900">Results ({results.length})</h3>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={clearResults}
                                    className="text-xs h-7 px-2"
                                >
                                    Clear All
                                </Button>
                            </div>

                            {results.map((result, index) => (
                                <Card key={result.id} className="border-0 shadow-sm">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <div>
                                                    <CardTitle className="text-base font-bold">
                                                        {result.fullName}
                                                    </CardTitle>
                                                    <div className="flex items-center gap-2 text-xs text-gray-600">
                                                        <span>Initials: {result.name.charAt(0)}{result.surname.charAt(0)}</span>
                                                        <span>•</span>
                                                        <span>Test #{index + 1}</span>
                                                    </div>
                                                </div>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => speakFullName(result.fullName)}
                                                    className="h-8 w-8 p-0 bg-gray-50 hover:bg-gray-100"
                                                    title="Hear pronunciation"
                                                >
                                                    <Volume2 className="h-4 w-4 text-gray-600" />
                                                </Button>
                                            </div>
                                            <div className={`flex items-center gap-2 px-3 py-1 rounded-full border ${getScoreBg(result.overallScore)}`}>
                                                <span className={`text-lg font-bold ${getScoreColor(result.overallScore)}`}>
                                                    {result.overallScore}%
                                                </span>
                                                {getScoreIcon(result.overallScore)}
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        {/* Score Breakdown */}
                                        <div className="grid grid-cols-3 gap-2">
                                            <div className="text-center p-2 bg-green-50 rounded-lg">
                                                <div className="flex items-center justify-center mb-1">
                                                    <Mic className="h-3 w-3 text-green-600 mr-1" />
                                                    <span className="text-xs font-medium">Flow</span>
                                                </div>
                                                <div className={`text-sm font-bold ${getScoreColor(result.flowScore)}`}>
                                                    {result.flowScore}%
                                                </div>
                                            </div>

                                            <div className="text-center p-2 bg-blue-50 rounded-lg">
                                                <div className="flex items-center justify-center mb-1">
                                                    <Mic className="h-3 w-3 text-blue-600 mr-1" />
                                                    <span className="text-xs font-medium">Pron.</span>
                                                </div>
                                                <div className={`text-sm font-bold ${getScoreColor(result.pronunciationScore)}`}>
                                                    {result.pronunciationScore}%
                                                </div>
                                            </div>

                                            <div className="text-center p-2 bg-purple-50 rounded-lg">
                                                <div className="flex items-center justify-center mb-1">
                                                    <Mic className="h-3 w-3 text-purple-600 mr-1" />
                                                    <span className="text-xs font-medium">Init.</span>
                                                </div>
                                                <div className={`text-sm font-bold ${getScoreColor(result.initialsScore)}`}>
                                                    {result.initialsScore}%
                                                </div>
                                            </div>
                                        </div>

                                        {/* Analysis */}
                                        <div className="space-y-2">
                                            <p className="text-xs text-gray-700">{result.flowAnalysis}</p>
                                            <p className="text-xs text-gray-700">{result.pronunciationAnalysis}</p>
                                            <p className="text-xs text-gray-700">{result.initialsAnalysis}</p>
                                        </div>

                                        {/* Warnings */}
                                        {result.warnings.length > 0 && (
                                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                                                <h4 className="font-semibold text-yellow-800 text-xs mb-1">⚠️ Considerations</h4>
                                                <ul className="space-y-0.5">
                                                    {result.warnings.map((warning, idx) => (
                                                        <li key={idx} className="text-xs text-yellow-700">• {warning}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}

                                        {/* Suggestions */}
                                        {result.suggestions.length > 0 && (
                                            <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                                                <h4 className="font-semibold text-green-800 text-xs mb-1">💡 Suggestions</h4>
                                                <ul className="space-y-0.5">
                                                    {result.suggestions.map((suggestion, idx) => (
                                                        <li key={idx} className="text-xs text-green-700">• {suggestion}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}

                    {/* Call to Action */}
                    <div className="text-center pt-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleFullCheckerRedirect}
                            className="text-xs"
                        >
                            Try Full Compatibility Checker
                            <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
} 