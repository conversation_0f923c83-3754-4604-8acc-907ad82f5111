"use client"

import { countInternalLinks, trackInternalLink, trackSEOPerformance } from "@/lib/analytics"
import { useEffect, useRef } from "react"

interface EnhancedSEOProps {
  pageType: 'home' | 'country' | 'language' | 'name_detail' | 'trending' | 'blog' | 'religion'
  pageTitle: string
  pageDescription: string
  canonicalUrl?: string
  structuredData?: any
  relatedPages?: Array<{
    title: string
    url: string
    description: string
  }>
  breadcrumbs?: Array<{
    name: string
    url: string
  }>
  children?: React.ReactNode
}

export default function EnhancedSEO({
  pageType,
  pageTitle,
  pageDescription,
  canonicalUrl,
  structuredData,
  relatedPages = [],
  breadcrumbs = [],
  children
}: EnhancedSEOProps) {
  const pageRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Track page load performance
    const trackPerformance = () => {
      const { internal, external } = countInternalLinks()

      trackSEOPerformance({
        action: 'page_load',
        category: 'SEO',
        page_type: pageType,
        seo_metrics: {
          title_length: pageTitle.length,
          meta_description_length: pageDescription.length,
          internal_links_count: internal,
          external_links_count: external,
          heading_structure: Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => h.tagName.toLowerCase())
        }
      })
    }

    // Track after page is fully loaded
    if (document.readyState === 'complete') {
      trackPerformance()
    } else {
      window.addEventListener('load', trackPerformance)
      return () => window.removeEventListener('load', trackPerformance)
    }
  }, [pageType, pageTitle, pageDescription])

  useEffect(() => {
    // Track internal link clicks
    const handleInternalLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const link = target.closest('a')

      if (link && (link.href.startsWith(window.location.origin) || link.href.startsWith('/'))) {
        const sourcePage = window.location.pathname
        const destinationPage = link.href.replace(window.location.origin, '')

        // Determine link type and position
        let linkType: 'navigation' | 'content' | 'related' | 'breadcrumb' = 'content'
        let linkPosition: 'header' | 'footer' | 'sidebar' | 'content' | 'mobile_menu' = 'content'

        if (link.closest('header')) {
          linkPosition = 'header'
          linkType = 'navigation'
        } else if (link.closest('footer')) {
          linkPosition = 'footer'
          linkType = 'navigation'
        } else if (link.closest('[data-breadcrumb]')) {
          linkPosition = 'content'
          linkType = 'breadcrumb'
        } else if (link.closest('[data-related-links]')) {
          linkPosition = 'content'
          linkType = 'related'
        }

        trackInternalLink({
          action: 'internal_link_click',
          category: 'SEO',
          source_page: sourcePage,
          destination_page: destinationPage,
          link_type: linkType,
          link_position: linkPosition
        })
      }
    }

    document.addEventListener('click', handleInternalLinkClick)
    return () => document.removeEventListener('click', handleInternalLinkClick)
  }, [])

  // Generate structured data
  const generateStructuredData = () => {
    const baseStructuredData = {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": pageTitle,
      "description": pageDescription,
      "url": canonicalUrl || window.location.href,
      "mainEntity": {
        "@type": "WebSite",
        "name": "Baby Names",
        "url": "https://babynames.com"
      }
    }

    // Add breadcrumbs structured data
    if (breadcrumbs.length > 0) {
      const breadcrumbList = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": breadcrumbs.map((crumb, index) => ({
          "@type": "ListItem",
          "position": index + 1,
          "name": crumb.name,
          "item": `${window.location.origin}${crumb.url}`
        }))
      }

      return [baseStructuredData, breadcrumbList]
    }

    return [baseStructuredData]
  }

  // Generate related links structured data
  const generateRelatedLinksData = () => {
    if (relatedPages.length === 0) return null

    return {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "name": "Related Pages",
      "description": "Related baby name pages",
      "itemListElement": relatedPages.map((page, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "WebPage",
          "name": page.title,
          "description": page.description,
          "url": page.url
        }
      }))
    }
  }

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateStructuredData())
        }}
      />

      {relatedPages.length > 0 && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(generateRelatedLinksData())
          }}
        />
      )}

      {/* Canonical URL */}
      {canonicalUrl && (
        <link rel="canonical" href={canonicalUrl} />
      )}

      {/* Meta tags for social sharing */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={canonicalUrl || window.location.href} />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />

      {/* Page content with SEO tracking */}
      <div ref={pageRef} data-page-type={pageType}>
        {children}
      </div>

      {/* SEO Performance Monitor */}
      <div className="hidden">
        <div data-seo-metrics>
          <span data-internal-links-count="0"></span>
          <span data-external-links-count="0"></span>
          <span data-title-length={pageTitle.length}></span>
          <span data-description-length={pageDescription.length}></span>
        </div>
      </div>
    </>
  )
}

// SEO Link component for tracking
interface SEOLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  linkType?: 'navigation' | 'content' | 'related' | 'breadcrumb'
  linkPosition?: 'header' | 'footer' | 'sidebar' | 'content' | 'mobile_menu'
  onClick?: () => void
}

export function SEOLink({
  href,
  children,
  className = "",
  linkType = 'content',
  linkPosition = 'content',
  onClick,
  ...props
}: SEOLinkProps) {
  const handleClick = (e: React.MouseEvent) => {
    const sourcePage = window.location.pathname
    const destinationPage = href.startsWith('/') ? href : `/${href}`

    trackInternalLink({
      action: 'internal_link_click',
      category: 'SEO',
      source_page: sourcePage,
      destination_page: destinationPage,
      link_type: linkType,
      link_position: linkPosition
    })

    if (onClick) onClick()
  }

  return (
    <a
      href={href}
      className={className}
      onClick={handleClick}
      {...props}
    >
      {children}
    </a>
  )
}

// SEO Section component for better structure
interface SEOSectionProps {
  title: string
  children: React.ReactNode
  className?: string
  headingLevel?: 1 | 2 | 3 | 4 | 5 | 6
}

export function SEOSection({
  title,
  children,
  className = "",
  headingLevel = 2
}: SEOSectionProps) {
  const renderHeading = () => {
    switch (headingLevel) {
      case 1:
        return <h1>{title}</h1>
      case 2:
        return <h2>{title}</h2>
      case 3:
        return <h3>{title}</h3>
      case 4:
        return <h4>{title}</h4>
      case 5:
        return <h5>{title}</h5>
      case 6:
        return <h6>{title}</h6>
      default:
        return <h2>{title}</h2>
    }
  }

  return (
    <section className={className}>
      {renderHeading()}
      {children}
    </section>
  )
} 