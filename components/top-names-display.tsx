'use client'

import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useTopNames } from "@/hooks/use-top-names"
import { Loader2, Star, TrendingDown, TrendingUp } from "lucide-react"

interface TopNamesDisplayProps {
  country?: string
  limit?: number
  showTrending?: boolean
  className?: string
  /** Show boys list (default true) */
  showBoys?: boolean
  /** Show girls list (default true) */
  showGirls?: boolean
}

export default function TopNamesDisplay({
  country = 'usa',
  limit = 10,
  showTrending = true,
  className = "",
  showBoys = true,
  showGirls = true,
}: TopNamesDisplayProps) {
  const { data, loading, error } = useTopNames({
    country,
    limit,
    autoFetch: true
  })

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            Loading Top Names...
          </CardTitle>
        </CardHeader>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Names</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  if (!data?.data) {
    return null
  }

  const { boys, girls, trending } = data.data

  // Determine how many primary cards will be displayed
  const visibleCardCount = (showBoys ? 1 : 0) + (showGirls && girls.length > 0 ? 1 : 0)

  // Dynamic wrapper classes: single column by default; 2-col grid on md+ only if >1 card
  const cardsWrapperClass =
    visibleCardCount > 1
      ? "grid md:grid-cols-2 gap-6 mb-8"
      : "space-y-6 max-w-xl mx-auto mb-8"

  return (
    <div className={className}>
      <div className={cardsWrapperClass}>
        {/* Boys Names */}
        {showBoys && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-blue-600" />
                Top {boys.length} Boys Names - {country.toUpperCase()}
              </CardTitle>
              <CardDescription>
                Most popular baby boy names in {data.metadata.last_updated}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {boys.map((name, index) => (
                  <div key={name.name_en} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <div>
                        <div className="font-semibold text-gray-900">{name.name_en}</div>
                        <div className="text-sm text-gray-600">{name.meaning_en}</div>
                        {name.popularity_change && (
                          <div className="flex items-center gap-1 text-xs">
                            {name.popularity_change.startsWith('+') ? (
                              <TrendingUp className="h-3 w-3 text-green-600" />
                            ) : (
                              <TrendingDown className="h-3 w-3 text-red-600" />
                            )}
                            <span className={name.popularity_change.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
                              {name.popularity_change}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-700">#{name.popularity_rank}</div>
                      {name.search_volume && (
                        <div className="text-xs text-gray-500">{name.search_volume.toLocaleString()} searches</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Girls Names (when available) */}
        {showGirls && girls.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-pink-600" />
                Top {girls.length} Girls Names - {country.toUpperCase()}
              </CardTitle>
              <CardDescription>
                Most popular baby girl names in {data.metadata.last_updated}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {girls.map((name, index) => (
                  <div key={name.name_en} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-pink-50 transition-colors">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <div>
                        <div className="font-semibold text-gray-900">{name.name_en}</div>
                        <div className="text-sm text-gray-600">{name.meaning_en}</div>
                        {name.popularity_change && (
                          <div className="flex items-center gap-1 text-xs">
                            {name.popularity_change.startsWith('+') ? (
                              <TrendingUp className="h-3 w-3 text-green-600" />
                            ) : (
                              <TrendingDown className="h-3 w-3 text-red-600" />
                            )}
                            <span className={name.popularity_change.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
                              {name.popularity_change}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-700">#{name.popularity_rank}</div>
                      {name.search_volume && (
                        <div className="text-xs text-gray-500">{name.search_volume.toLocaleString()} searches</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Trending Names Section */}
      {showTrending && (trending.rising.length > 0 || trending.new.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Trending Names - {country.toUpperCase()}
            </CardTitle>
            <CardDescription>
              Names that are gaining popularity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 gap-6">
              {trending.rising.length > 0 && (
                <div>
                  <h4 className="font-semibold text-green-600 mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Rising Stars
                  </h4>
                  <div className="space-y-2">
                    {trending.rising.slice(0, 5).map((name) => (
                      <div key={name.name_en} className="flex items-center justify-between p-2 rounded bg-green-50">
                        <div>
                          <div className="font-medium">{name.name_en}</div>
                          <div className="text-xs text-gray-600">{name.meaning_en}</div>
                        </div>
                        <Badge className="bg-green-100 text-green-800">
                          {name.popularity_change}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {trending.new.length > 0 && (
                <div>
                  <h4 className="font-semibold text-blue-600 mb-3 flex items-center gap-2">
                    <Star className="h-4 w-4" />
                    New Entries
                  </h4>
                  <div className="space-y-2">
                    {trending.new.slice(0, 5).map((name) => (
                      <div key={name.name_en} className="flex items-center justify-between p-2 rounded bg-blue-50">
                        <div>
                          <div className="font-medium">{name.name_en}</div>
                          <div className="text-xs text-gray-600">{name.meaning_en}</div>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800">
                          {name.popularity_change}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Source Footer */}
      <div className="mt-6 text-center">
        <div className="text-sm text-gray-500">
          Data source: {data.metadata.data_source} | Last updated: {data.metadata.last_updated}
        </div>
        <div className="text-xs text-gray-400 mt-1">
          Total names in database: {data.metadata.total_boys} boys, {data.metadata.total_girls} girls
        </div>
      </div>
    </div>
  )
} 