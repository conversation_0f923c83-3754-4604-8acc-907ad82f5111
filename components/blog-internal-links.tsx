"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { navigationConfig } from "@/lib/navigation-config"
import { ArrowRight, Globe, TrendingUp } from "lucide-react"
import Link from "next/link"

interface BlogInternalLinksProps {
    showTrending?: boolean
    showCountries?: boolean
    showReligions?: boolean
    className?: string
}

export default function BlogInternalLinks({
    showTrending = true,
    showCountries = true,
    showReligions = true,
    className = ""
}: BlogInternalLinksProps) {
    // Get trending pages for related links
    const trendingPages = navigationConfig.trending.filter(page => page.priority === "high")

    // Get popular country pages
    const popularCountries = navigationConfig.countries.slice(0, 4)

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Trending Names Section */}
            {showTrending && (
                <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                    <CardHeader>
                        <CardTitle className="flex items-center text-blue-800">
                            <TrendingUp className="mr-2 h-5 w-5" />
                            Explore Trending Names
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-blue-700 mb-4">
                            Discover the latest trending baby names and naming trends:
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {trendingPages.map((page) => (
                                <Link key={page.href} href={page.href}>
                                    <Button
                                        variant="outline"
                                        className="w-full text-left h-auto p-3 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                                    >
                                        <div className="flex flex-col min-w-0 w-full">
                                            <span className="font-semibold text-blue-700 text-sm mb-1">
                                                {page.name}
                                            </span>
                                            <span className="text-xs text-blue-600 line-clamp-2">
                                                {page.description}
                                            </span>
                                        </div>
                                        <ArrowRight className="h-4 w-4 text-blue-500 ml-2 shrink-0" />
                                    </Button>
                                </Link>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Popular Countries Section */}
            {showCountries && (
                <Card className="bg-gradient-to-r from-green-50 to-teal-50 border-green-200">
                    <CardHeader>
                        <CardTitle className="flex items-center text-green-800">
                            <Globe className="mr-2 h-5 w-5" />
                            Popular Name Collections
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-green-700 mb-4">
                            Browse names from popular countries and cultures:
                        </p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                            {popularCountries.map((country) => (
                                <Link key={country.name} href={country.languages[0]?.boyHref || "#"}>
                                    <Button
                                        variant="outline"
                                        className="w-full h-auto py-3 hover:bg-green-50 border-green-200 hover:border-green-300"
                                    >
                                        <div className="text-center">
                                            <div className="text-lg mb-1">{country.flag}</div>
                                            <div className="text-xs font-medium text-green-700 leading-tight">
                                                {country.name === "United Kingdom" ? "UK" :
                                                    country.name === "United States" ? "USA" :
                                                        country.name.split(" ")[0]}
                                            </div>
                                        </div>
                                    </Button>
                                </Link>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Religious Names Section */}
            {showReligions && (
                <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                    <CardHeader>
                        <CardTitle className="flex items-center text-purple-800">
                            <span className="mr-2">🕉️</span>
                            Religious Names
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-purple-700 mb-4">
                            Explore names with spiritual and religious significance:
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            {navigationConfig.religions.map((religion) => (
                                <Link key={religion.href} href={religion.href}>
                                    <Button
                                        variant="outline"
                                        className="w-full h-auto p-3 hover:bg-purple-50 border-purple-200 hover:border-purple-300"
                                    >
                                        <div className="text-center">
                                            <div className="font-medium text-purple-700 text-sm">
                                                {religion.name}
                                            </div>
                                            {religion.description && (
                                                <div className="text-xs text-purple-600 mt-1">
                                                    {religion.description}
                                                </div>
                                            )}
                                        </div>
                                    </Button>
                                </Link>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    )
} 