export interface LanguageConfig {
  name: string
  href: string
  boyHref: string
  girlHref: string
  flag?: string
}

export interface CountryConfig {
  name: string
  flag: string
  languages: LanguageConfig[]
  description?: string
  landingHref?: string
}

export interface ReligionConfig {
  name: string
  href: string
  boyHref: string
  girlHref: string
  description?: string
}

export interface OtherLinkConfig {
  name: string
  href: string
  icon: any // Lucide icon component
}

export interface TrendingLinkConfig {
  name: string
  href: string
  description: string
  category: 'global' | 'regional' | 'specific'
  priority: 'high' | 'medium' | 'low'
}

export interface NavigationConfig {
  countries: CountryConfig[]
  religions: ReligionConfig[]
  trending: TrendingLinkConfig[]
  other: OtherLinkConfig[]
}

// Centralized navigation configuration - ONLY IMPLEMENTED COUNTRIES
export const navigationConfig: NavigationConfig = {
  countries: [
    {
      name: "USA",
      flag: "🇺🇸",
      description: "Melting pot of cultures and traditions",
      languages: [
        { name: "English", href: "/usa/english-boy-names/", boyHref: "/usa/english-boy-names/", girlHref: "/usa/english-girl-names/" },
      ]
    },
    {
      name: "United Kingdom",
      flag: "🇬🇧",
      description: "Historic names with royal traditions",
      languages: [
        { name: "English", href: "/uk/english-boy-names/", boyHref: "/uk/english-boy-names/", girlHref: "/uk/english-girl-names/" },
      ]
    },
    {
      name: "Canada",
      flag: "🇨🇦",
      description: "Bilingual nation with rich cultural diversity",
      languages: [
        { name: "English", href: "/canada/english-boy-names/", boyHref: "/canada/english-boy-names/", girlHref: "/canada/english-girl-names/" },
        { name: "French", href: "/canada/french-boy-names/", boyHref: "/canada/french-boy-names/", girlHref: "/canada/french-girl-names/" },
      ]
    },
    {
      name: "Australia",
      flag: "🇦🇺",
      description: "Modern names with Aboriginal influences",
      languages: [
        { name: "English", href: "/australia/english-boy-names/", boyHref: "/australia/english-boy-names/", girlHref: "/australia/english-girl-names/" },
      ]
    },
    {
      name: "Germany",
      flag: "🇩🇪",
      description: "Strong Germanic names with deep meanings",
      languages: [
        { name: "German", href: "/germany/german-boy-names/", boyHref: "/germany/german-boy-names/", girlHref: "/germany/german-girl-names/" },
      ]
    },
    {
      name: "France",
      flag: "🇫🇷",
      description: "Elegant French names with romantic charm",
      languages: [
        { name: "French", href: "/france/french-boy-names/", boyHref: "/france/french-boy-names/", girlHref: "/france/french-girl-names/" },
      ]
    },
    {
      name: "Netherlands",
      flag: "🇳🇱",
      description: "Dutch names with traditional values",
      languages: [
        { name: "Dutch", href: "/netherlands/dutch-boy-names/", boyHref: "/netherlands/dutch-boy-names/", girlHref: "/netherlands/dutch-girl-names/" },
      ]
    },
    {
      name: "Sweden",
      flag: "🇸🇪",
      description: "Nordic names with natural beauty",
      languages: [
        { name: "Swedish", href: "/sweden/swedish-boy-names/", boyHref: "/sweden/swedish-boy-names/", girlHref: "/sweden/swedish-girl-names/" },
      ]
    },
    {
      name: "Switzerland",
      flag: "🇨🇭",
      description: "Swiss names with multiple languages",
      languages: [
        { name: "German", href: "/switzerland/german-boy-names", boyHref: "/switzerland/german-boy-names", girlHref: "/switzerland/german-girl-names" },
        { name: "French", href: "/switzerland/french-boy-names", boyHref: "/switzerland/french-boy-names", girlHref: "/switzerland/french-girl-names" },
      ]
    },
    {
      name: "Austria",
      flag: "🇦🇹",
      description: "Austrian names with Alpine heritage",
      languages: [
        { name: "German", href: "/austria/german-boy-names", boyHref: "/austria/german-boy-names", girlHref: "/austria/german-girl-names" },
      ]
    },
    {
      name: "Belgium",
      flag: "🇧🇪",
      description: "Belgian names with dual language heritage",
      languages: [
        { name: "Dutch", href: "/belgium/dutch-boy-names", boyHref: "/belgium/dutch-boy-names", girlHref: "/belgium/dutch-girl-names" },
        { name: "French", href: "/belgium/french-boy-names", boyHref: "/belgium/french-boy-names", girlHref: "/belgium/french-girl-names" },
      ]
    },
    {
      name: "India",
      flag: "🇮🇳",
      description: "Diverse cultural heritage with multiple languages",
      languages: [
        { name: "Hindi", href: "/india/hindi-boy-names", boyHref: "/india/hindi-boy-names", girlHref: "/india/hindi-girl-names" },
        { name: "Gujarati", href: "/india/gujarati-boy-names", boyHref: "/india/gujarati-boy-names", girlHref: "/india/gujarati-girl-names" },
        { name: "Tamil", href: "/india/tamil-boy-names", boyHref: "/india/tamil-boy-names", girlHref: "/india/tamil-girl-names" },
        { name: "Urdu", href: "/india/urdu-boy-names", boyHref: "/india/urdu-boy-names", girlHref: "/india/urdu-girl-names" },
        { name: "Punjabi", href: "/india/punjabi-boy-names", boyHref: "/india/punjabi-boy-names", girlHref: "/india/punjabi-girl-names" },
        { name: "Bengali", href: "/india/bengali-boy-names", boyHref: "/india/bengali-boy-names", girlHref: "/india/bengali-girl-names" },
        { name: "Marathi", href: "/india/marathi-boy-names", boyHref: "/india/marathi-boy-names", girlHref: "/india/marathi-girl-names" },
      ]
    },
  ],
  religions: [
    {
      name: "Muslim Names",
      href: "/religions/muslim-boy-names",
      boyHref: "/religions/muslim-boy-names",
      girlHref: "/religions/muslim-girl-names",
      description: "Islamic names with spiritual significance"
    },
    {
      name: "Sikh Names",
      href: "/religions/sikh-boy-names",
      boyHref: "/religions/sikh-boy-names",
      girlHref: "/religions/sikh-girl-names",
      description: "Sikh names reflecting spiritual values"
    },
    {
      name: "Christian Names",
      href: "/religions/christian-boy-names",
      boyHref: "/religions/christian-boy-names",
      girlHref: "/religions/christian-girl-names",
      description: "Christian names with biblical origins"
    }
  ],
  trending: [
    {
      name: "Global Trending Names",
      href: "/trending-names",
      description: "Trending baby names across 12 countries worldwide",
      category: "global",
      priority: "high"
    },
    {
      name: "Popular Names 2025",
      href: "/popular-names-2025",
      description: "Most popular baby names worldwide in 2025",
      category: "global",
      priority: "high"
    },
    {
      name: "Trending Baby Names 2025",
      href: "/trending-baby-names-2025",
      description: "Complete guide to trending baby names with future predictions",
      category: "global",
      priority: "high"
    },
    {
      name: "Unique Baby Names 2025",
      href: "/unique-baby-names-2025",
      description: "Rare and distinctive names from multiple countries",
      category: "global",
      priority: "medium"
    },
    {
      name: "Popular American Names 2025",
      href: "/popular-american-names-2025",
      description: "Top USA baby names and regional trends",
      category: "specific",
      priority: "medium"
    }
  ],
  other: [
    {
      name: "AI Name Generator",
      href: "/baby-name-generator",
      icon: "Sparkles"
    },
    {
      name: "About Us",
      href: "/about",
      icon: "Info"
    },
    {
      name: "Contact",
      href: "/contact",
      icon: "Mail"
    }
  ]
}

// Helper functions
export function addCountry(country: CountryConfig) {
  navigationConfig.countries.push(country)
}

export function addReligion(religion: ReligionConfig) {
  navigationConfig.religions.push(religion)
}

export function addOtherLink(link: OtherLinkConfig) {
  navigationConfig.other.push(link)
}

export function getAllLanguages(): LanguageConfig[] {
  return navigationConfig.countries.flatMap(country => country.languages)
}

export function getLanguagesByCountry(countryName: string): LanguageConfig[] {
  const country = navigationConfig.countries.find(c => c.name === countryName)
  return country ? country.languages : []
}

export function getCountryByLanguage(languageName: string): CountryConfig | undefined {
  return navigationConfig.countries.find(country =>
    country.languages.some(lang => lang.name === languageName)
  )
}

export function getCountriesWithLanguageCounts() {
  return navigationConfig.countries.map(country => ({
    ...country,
    languageCount: country.languages.length
  }))
}

export function searchCountriesAndLanguages(query: string) {
  const normalizedQuery = query.toLowerCase()
  return navigationConfig.countries.filter(country =>
    country.name.toLowerCase().includes(normalizedQuery) ||
    country.languages.some(lang => lang.name.toLowerCase().includes(normalizedQuery))
  )
}

export function getCountriesByRegion() {
  return {
    "English-Speaking": navigationConfig.countries.filter(c =>
      ["USA", "United Kingdom", "Canada", "Australia"].includes(c.name)
    ),
    "European": navigationConfig.countries.filter(c =>
      ["Germany", "France", "Netherlands", "Sweden", "Switzerland", "Austria", "Belgium"].includes(c.name)
    ),
    "Asian": navigationConfig.countries.filter(c =>
      ["India"].includes(c.name)
    )
  }
}

export function getCountriesInRegion(region: string) {
  const regions = getCountriesByRegion()
  return regions[region as keyof typeof regions] || []
} 