// Google Analytics 4 configuration
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID || ''

// Track page views
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_location: url,
    })
  }
}

// Track events
export const event = ({
  action,
  category,
  label,
  value,
}: {
  action: string
  category: string
  label?: string
  value?: number
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

// Track name searches
export const trackNameSearch = (searchTerm: string, country: string, language: string) => {
  event({
    action: 'search',
    category: 'Name Search',
    label: `${country}-${language}: ${searchTerm}`,
  })
}

// Track name views
export const trackNameView = (nameName: string, country: string, language: string) => {
  event({
    action: 'view_name',
    category: 'Name Details',
    label: `${country}-${language}: ${nameName}`,
  })
}

// Track trending page views
export const trackTrendingView = (pageType: string, countries: string[]) => {
  event({
    action: 'view_trending',
    category: 'Trending Names',
    label: `${pageType}: ${countries.join(',')}`,
  })
}

// Track user engagement
export const trackEngagement = (action: string, section: string) => {
  event({
    action: action,
    category: 'User Engagement',
    label: section,
  })
}

// Initialize Google Analytics
export const initGA = () => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    // Load gtag script
    const script = document.createElement('script')
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`
    script.async = true
    document.head.appendChild(script)

    // Initialize gtag
    window.dataLayer = window.dataLayer || []
    window.gtag = function () {
      window.dataLayer.push(arguments)
    }
    window.gtag('js', new Date())
    window.gtag('config', GA_TRACKING_ID, {
      page_title: document.title,
      page_location: window.location.href,
    })
  }
}

// Type declarations for global gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

// Enhanced Analytics for SEO and Internal Linking Performance

export interface AnalyticsEvent {
  action: string
  category: string
  label?: string
  value?: number
  custom_parameters?: Record<string, any>
}

export interface InternalLinkEvent extends AnalyticsEvent {
  source_page: string
  destination_page: string
  link_type: 'navigation' | 'content' | 'related' | 'breadcrumb'
  link_position: 'header' | 'footer' | 'sidebar' | 'content' | 'mobile_menu'
}

export interface SEOPerformanceEvent extends AnalyticsEvent {
  page_type: string
  load_time?: number
  core_web_vitals?: {
    lcp?: number
    fid?: number
    cls?: number
  }
  seo_metrics?: {
    title_length: number
    meta_description_length: number
    heading_structure: string[]
    internal_links_count: number
    external_links_count: number
  }
}

// Track internal link clicks for SEO analysis
export function trackInternalLink(event: InternalLinkEvent) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'internal_link_click', {
      event_category: 'SEO',
      event_label: `${event.source_page} → ${event.destination_page}`,
      custom_map: {
        'source_page': event.source_page,
        'destination_page': event.destination_page,
        'link_type': event.link_type,
        'link_position': event.link_position
      }
    })
  }

  // Also track as custom event for detailed analysis
  trackCustomEvent({
    action: 'internal_link_click',
    category: 'SEO',
    label: `${event.source_page} → ${event.destination_page}`,
    custom_parameters: {
      source_page: event.source_page,
      destination_page: event.destination_page,
      link_type: event.link_type,
      link_position: event.link_position,
      timestamp: new Date().toISOString()
    }
  })
}

// Track SEO performance metrics
export function trackSEOPerformance(event: SEOPerformanceEvent) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'seo_performance', {
      event_category: 'SEO',
      event_label: event.page_type,
      value: event.load_time,
      custom_map: {
        'page_type': event.page_type,
        'load_time': event.load_time,
        'internal_links_count': event.seo_metrics?.internal_links_count,
        'external_links_count': event.seo_metrics?.external_links_count
      }
    })
  }
}

// Track page view with enhanced SEO data
export function trackPageView(page_path: string, page_title?: string, seo_data?: Partial<SEOPerformanceEvent['seo_metrics']>) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_ID!, {
      page_path,
      page_title,
      custom_map: {
        'internal_links_count': seo_data?.internal_links_count || 0,
        'external_links_count': seo_data?.external_links_count || 0,
        'title_length': seo_data?.title_length || 0,
        'meta_description_length': seo_data?.meta_description_length || 0
      }
    })
  }
}

// Track custom events
export function trackCustomEvent(event: AnalyticsEvent) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters
    })
  }
}

// Track navigation performance
export function trackNavigationPerformance(
  from_page: string,
  to_page: string,
  navigation_type: 'header' | 'footer' | 'mobile' | 'breadcrumb' | 'related',
  load_time?: number
) {
  trackCustomEvent({
    action: 'navigation_performance',
    category: 'User Experience',
    label: `${from_page} → ${to_page}`,
    value: load_time,
    custom_parameters: {
      from_page,
      to_page,
      navigation_type,
      load_time,
      timestamp: new Date().toISOString()
    }
  })
}

// Track search performance
export function trackSearchPerformance(
  search_term: string,
  results_count: number,
  search_type: 'name' | 'country' | 'religion' | 'trending',
  load_time?: number
) {
  trackCustomEvent({
    action: 'search_performance',
    category: 'Search',
    label: search_term,
    value: results_count,
    custom_parameters: {
      search_term,
      results_count,
      search_type,
      load_time,
      timestamp: new Date().toISOString()
    }
  })
}

// Track content engagement
export function trackContentEngagement(
  content_type: 'name_detail' | 'country_page' | 'trending_page' | 'blog_post',
  content_id: string,
  engagement_type: 'view' | 'favorite' | 'share' | 'time_spent',
  value?: number
) {
  trackCustomEvent({
    action: 'content_engagement',
    category: 'Content',
    label: `${content_type}: ${content_id}`,
    value,
    custom_parameters: {
      content_type,
      content_id,
      engagement_type,
      value,
      timestamp: new Date().toISOString()
    }
  })
}

// Track mobile vs desktop usage
export function trackDeviceUsage(device_type: 'mobile' | 'desktop' | 'tablet') {
  trackCustomEvent({
    action: 'device_usage',
    category: 'User Experience',
    label: device_type,
    custom_parameters: {
      device_type,
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      timestamp: new Date().toISOString()
    }
  })
}

// Track error events
export function trackError(error_type: string, error_message: string, page_path?: string) {
  trackCustomEvent({
    action: 'error',
    category: 'Technical',
    label: error_type,
    custom_parameters: {
      error_type,
      error_message,
      page_path: page_path || window.location.pathname,
      timestamp: new Date().toISOString()
    }
  })
}

// Initialize enhanced analytics
export function initializeAnalytics() {
  if (typeof window !== 'undefined') {
    // Track initial page load
    trackPageView(window.location.pathname, document.title)

    // Track device type
    const deviceType = window.innerWidth < 768 ? 'mobile' :
      window.innerWidth < 1024 ? 'tablet' : 'desktop'
    trackDeviceUsage(deviceType)

    // Track core web vitals if available
    if ('web-vital' in window) {
      // This would integrate with web-vitals library
      console.log('Web Vitals tracking available')
    }
  }
}

// Utility function to count internal links on a page
export function countInternalLinks(): { internal: number; external: number } {
  if (typeof window === 'undefined') return { internal: 0, external: 0 }

  const links = document.querySelectorAll('a[href]')
  let internal = 0
  let external = 0

  links.forEach(link => {
    const href = (link as HTMLAnchorElement).href
    if (href.startsWith(window.location.origin) || href.startsWith('/')) {
      internal++
    } else {
      external++
    }
  })

  return { internal, external }
}

// Track page load performance
export function trackPageLoadPerformance() {
  if (typeof window === 'undefined') return

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  if (navigation) {
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart

    trackSEOPerformance({
      action: 'page_load',
      category: 'Performance',
      page_type: window.location.pathname.split('/')[1] || 'home',
      load_time: Math.round(loadTime),
      seo_metrics: {
        internal_links_count: countInternalLinks().internal,
        external_links_count: countInternalLinks().external,
        title_length: document.title.length,
        meta_description_length: document.querySelector('meta[name="description"]')?.getAttribute('content')?.length || 0,
        heading_structure: Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => h.tagName.toLowerCase())
      }
    })
  }
} 