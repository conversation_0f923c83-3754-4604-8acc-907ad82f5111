
import { NameData } from "@/types/name-data";

export async function fetchNames(params: {
    country: string;
    gender: "boy" | "girl";
    limit: number;
}): Promise<NameData[] | null> {
    const { country, gender, limit } = params;

    // This is a placeholder for your actual data fetching logic.
    // Replace this with a call to your database or API.
    try {
        const response = await fetch(
            `http://localhost:3000/api/names/top?country=${country}&gender=${gender}&limit=${limit}`
        );

        if (!response.ok) {
            return null;
        }

        const result = await response.json();

        if (!result.success) {
            return null;
        }

        return result.data as NameData[];
    } catch (error) {
        console.error("Failed to fetch names:", error);
        return null;
    }
} 