import { navigationConfig } from './navigation-config'

export interface BackNavigationContext {
    language: string
    gender: string
    religion: string
    name: string
}

export interface BackNavigationResult {
    backLink: string
    backLabel: string
    browseMoreHref: string
    browseMoreLabel: string
    countryContext?: string
}

/**
 * Intelligently determines the best back navigation based on language and religion
 */
export function getSmartBackNavigation(context: BackNavigationContext): BackNavigationResult {
    const { language, gender, religion } = context

    // Define language-to-country mapping with priorities
    const languageCountryMap: Record<string, string[]> = {
        'english': ['usa', 'uk', 'canada', 'australia'],
        'hindi': ['india'],
        'gujarati': ['india'],
        'tamil': ['india'],
        'urdu': ['india'],
        'punjabi': ['india'],
        'bengali': ['india'],
        'marathi': ['india'],
        'french': ['canada', 'france', 'belgium', 'switzerland'],
        'german': ['germany', 'austria', 'switzerland'],
        'dutch': ['netherlands', 'belgium'],
        'swedish': ['sweden'],
        'arabic': ['religions'] // Will use religion pages
    }

    // Define religion-to-country preferences
    const religionCountryMap: Record<string, string[]> = {
        'muslim': ['religions', 'india'], // Prefer religion pages for Muslim names
        'sikh': ['religions', 'india'],
        'christian': ['usa', 'uk', 'canada', 'australia', 'religions'],
        'hindu': ['india'],
        'buddhist': ['india'],
        'jain': ['india']
    }

    // Get potential countries based on language
    const languageCountries = languageCountryMap[language.toLowerCase()] || []

    // Get potential countries based on religion
    const religionCountries = religionCountryMap[religion.toLowerCase()] || []

    // Combine and prioritize
    const prioritizedCountries = [
        ...languageCountries.filter(c => religionCountries.includes(c)), // Intersection first
        ...languageCountries.filter(c => !religionCountries.includes(c)), // Language matches
        ...religionCountries.filter(c => !languageCountries.includes(c)) // Religion matches
    ]

    // Try to find a matching navigation config
    for (const countryKey of prioritizedCountries) {
        if (countryKey === 'religions') {
            // Use religion-based navigation
            const religionConfig = navigationConfig.religions.find(r =>
                r.name.toLowerCase().includes(religion.toLowerCase())
            )

            if (religionConfig) {
                const backLink = gender.toLowerCase() === 'boy' ? religionConfig.boyHref : religionConfig.girlHref
                const browseMoreHref = gender.toLowerCase() === 'boy' ? religionConfig.girlHref : religionConfig.boyHref
                const browseMoreLabel = gender.toLowerCase() === 'boy'
                    ? `Browse ${religion} Girl Names`
                    : `Browse ${religion} Boy Names`

                return {
                    backLink,
                    backLabel: `Back to ${religion} ${gender} Names`,
                    browseMoreHref,
                    browseMoreLabel,
                    countryContext: 'religion'
                }
            }
        } else {
            // Use country-based navigation
            const country = navigationConfig.countries.find(c =>
                c.name.toLowerCase().includes(countryKey) ||
                countryKey.includes(c.name.toLowerCase())
            )

            if (country) {
                const languageConfig = country.languages.find(l =>
                    l.name.toLowerCase() === language.toLowerCase()
                )

                if (languageConfig) {
                    const backLink = gender.toLowerCase() === 'boy' ? languageConfig.boyHref : languageConfig.girlHref
                    const browseMoreHref = gender.toLowerCase() === 'boy' ? languageConfig.girlHref : languageConfig.boyHref
                    const browseMoreLabel = gender.toLowerCase() === 'boy'
                        ? `Browse ${country.name} ${language} Girl Names`
                        : `Browse ${country.name} ${language} Boy Names`

                    return {
                        backLink,
                        backLabel: `Back to ${country.name} ${language} ${gender} Names`,
                        browseMoreHref,
                        browseMoreLabel,
                        countryContext: country.name
                    }
                }
            }
        }
    }

    // Fallback: Generic navigation
    return getGenericBackNavigation(context)
}

/**
 * Fallback navigation when no specific country/religion match is found
 */
function getGenericBackNavigation(context: BackNavigationContext): BackNavigationResult {
    const { language, gender, religion } = context

    // Try to find any matching language configuration
    for (const country of navigationConfig.countries) {
        const languageConfig = country.languages.find(l =>
            l.name.toLowerCase() === language.toLowerCase()
        )

        if (languageConfig) {
            const backLink = gender.toLowerCase() === 'boy' ? languageConfig.boyHref : languageConfig.girlHref
            const browseMoreHref = gender.toLowerCase() === 'boy' ? languageConfig.girlHref : languageConfig.boyHref
            const browseMoreLabel = gender.toLowerCase() === 'boy'
                ? `Browse ${language} Girl Names`
                : `Browse ${language} Boy Names`

            return {
                backLink,
                backLabel: `Back to ${language} ${gender} Names`,
                browseMoreHref,
                browseMoreLabel,
                countryContext: country.name
            }
        }
    }

    // Ultimate fallback: Home page
    return {
        backLink: '/',
        backLabel: 'Back to Home',
        browseMoreHref: '/trending-names',
        browseMoreLabel: 'Browse Trending Names',
        countryContext: 'home'
    }
}

/**
 * Get breadcrumb navigation for individual name pages (simple two-level)
 */
export function getNameBreadcrumbs(context: BackNavigationContext): Array<{ name: string, href: string }> {
    const { language, gender, religion, name } = context
    const navigation = getSmartBackNavigation(context)

    const breadcrumbs = [
        { name: 'Home', href: '/' }
    ]

    // Add only the specific language-gender page (two-level breadcrumbs)
    if (navigation.countryContext && navigation.countryContext !== 'home') {
        if (navigation.countryContext === 'religion') {
            breadcrumbs.push({
                name: `${religion} ${gender} Names`,
                href: navigation.backLink
            })
        } else {
            // Country context - create descriptive label but link directly to the page
            breadcrumbs.push({
                name: `${navigation.countryContext} ${language} ${gender} Names`,
                href: navigation.backLink
            })
        }
    }

    // Add current name
    breadcrumbs.push({
        name: name,
        href: '' // Current page
    })

    return breadcrumbs
}

/**
 * Get related navigation suggestions
 */
export function getRelatedNavigation(context: BackNavigationContext): Array<{ name: string, href: string, description: string }> {
    const { language, gender, religion } = context
    const suggestions = []

    // Same language, different gender
    const oppositeGender = gender.toLowerCase() === 'boy' ? 'girl' : 'boy'
    const navigation = getSmartBackNavigation({ ...context, gender: oppositeGender })

    suggestions.push({
        name: `${language} ${oppositeGender} Names`,
        href: navigation.backLink,
        description: `Explore ${language} names for ${oppositeGender}s`
    })

    // Same religion, different language (for Indian names)
    if (language.toLowerCase() !== 'hindi' && religion.toLowerCase() === 'hindu') {
        const hindiNavigation = getSmartBackNavigation({ ...context, language: 'hindi' })
        suggestions.push({
            name: `Hindi ${gender} Names`,
            href: hindiNavigation.backLink,
            description: `Discover Hindi names for ${gender}s`
        })
    }

    // Trending names
    suggestions.push({
        name: 'Trending Names 2025',
        href: '/trending-names',
        description: 'Explore the most popular names this year'
    })

    // Religion-specific if not already there
    if (navigation.countryContext !== 'religion') {
        const religionConfig = navigationConfig.religions.find(r =>
            r.name.toLowerCase().includes(religion.toLowerCase())
        )
        if (religionConfig) {
            const religionHref = gender.toLowerCase() === 'boy' ? religionConfig.boyHref : religionConfig.girlHref
            suggestions.push({
                name: `${religion} ${gender} Names`,
                href: religionHref,
                description: `Browse ${religion} names for ${gender}s`
            })
        }
    }

    return suggestions.slice(0, 4) // Limit to 4 suggestions
} 