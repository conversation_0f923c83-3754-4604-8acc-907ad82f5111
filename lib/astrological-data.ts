// Astrological data for rashi and nakshatra filters
export const rashiData = [
  {
    value: "<PERSON><PERSON> (Aries)",
    label: "<PERSON><PERSON> (Aries)",
    native: {
      gujarati: "મેષ",
      hindi: "मेष",
      marathi: "मेष",
      bengali: "মেষ",
      tamil: "மேஷம்",
      telugu: "మేషం",
      kannada: "ಮೇಷ",
      malayalam: "മേഷം",
      punjabi: "ਮੇਸ਼"
    }
  },
  {
    value: "<PERSON><PERSON><PERSON><PERSON> (Taurus)",
    label: "<PERSON><PERSON><PERSON><PERSON> (Taurus)",
    native: {
      gujarati: "વૃષભ",
      hindi: "वृषभ",
      marathi: "वृषभ",
      bengali: "বৃষভ",
      tamil: "ரிஷபம்",
      telugu: "వృషభం",
      kannada: "ವೃಷಭ",
      malayalam: "വൃഷഭം",
      punjabi: "ਵ੍ਰਿਸ਼ਭ"
    }
  },
  {
    value: "<PERSON><PERSON><PERSON> (Gemini)",
    label: "<PERSON><PERSON><PERSON> (Gemini)",
    native: {
      gujarati: "મિથુન",
      hindi: "मिथुन",
      marathi: "मिथुन",
      bengali: "মিথুন",
      tamil: "மிதுனம்",
      telugu: "మిథునం",
      kannada: "ಮಿಥುನ",
      malayalam: "മിഥുനം",
      punjabi: "ਮਿਥੁਨ"
    }
  },
  {
    value: "Karka (Cancer)",
    label: "Karka (Cancer)",
    native: {
      gujarati: "કર્ક",
      hindi: "कर्क",
      marathi: "कर्क",
      bengali: "কর্কট",
      tamil: "கடகம்",
      telugu: "కర్కాటకం",
      kannada: "ಕರ್ಕಾಟಕ",
      malayalam: "കര്കടകം",
      punjabi: "ਕਰਕ"
    }
  },
  {
    value: "Simha (Leo)",
    label: "Simha (Leo)",
    native: {
      gujarati: "સિંહ",
      hindi: "सिंह",
      marathi: "सिंह",
      bengali: "সিংহ",
      tamil: "சிம்மம்",
      telugu: "సింహం",
      kannada: "ಸಿಂಹ",
      malayalam: "സിംഹം",
      punjabi: "ਸਿੰਘ"
    }
  },
  {
    value: "Kanya (Virgo)",
    label: "Kanya (Virgo)",
    native: {
      gujarati: "કન્યા",
      hindi: "कन्या",
      marathi: "कन्या",
      bengali: "কন্যা",
      tamil: "கன்னி",
      telugu: "కన్య",
      kannada: "ಕನ್ಯಾ",
      malayalam: "കന്യ",
      punjabi: "ਕੰਨਿਆ"
    }
  },
  {
    value: "Tula (Libra)",
    label: "Tula (Libra)",
    native: {
      gujarati: "તુલા",
      hindi: "तुला",
      marathi: "तुला",
      bengali: "তুলা",
      tamil: "துலாம்",
      telugu: "తుల",
      kannada: "ತುಲಾ",
      malayalam: "തുല",
      punjabi: "ਤੁਲਾ"
    }
  },
  {
    value: "Vrishchika (Scorpio)",
    label: "Vrishchika (Scorpio)",
    native: {
      gujarati: "વૃશ્ચિક",
      hindi: "वृश्चिक",
      marathi: "वृश्चिक",
      bengali: "বৃশ্চিক",
      tamil: "விருச்சிகம்",
      telugu: "వృశ్చికం",
      kannada: "ವೃಶ್ಚిక",
      malayalam: "വൃശ്ചികം",
      punjabi: "ਵ੍ਰਿਸ਼ਚਿਕ"
    }
  },
  {
    value: "Dhanu (Sagittarius)",
    label: "Dhanu (Sagittarius)",
    native: {
      gujarati: "ધનુ",
      hindi: "धनु",
      marathi: "धनु",
      bengali: "ধনু",
      tamil: "தனுசு",
      telugu: "ధనుస్సు",
      kannada: "ಧನು",
      malayalam: "ധനു",
      punjabi: "ਧਨੁ"
    }
  },
  {
    value: "Makar (Capricorn)",
    label: "Makar (Capricorn)",
    native: {
      gujarati: "મકર",
      hindi: "मकर",
      marathi: "मकर",
      bengali: "মকর",
      tamil: "மகரம்",
      telugu: "మకరం",
      kannada: "ಮಕರ",
      malayalam: "മകരം",
      punjabi: "ਮਕਰ"
    }
  },
  {
    value: "Kumbha (Aquarius)",
    label: "Kumbha (Aquarius)",
    native: {
      gujarati: "કુંભ",
      hindi: "कुंभ",
      marathi: "कुंभ",
      bengali: "কুম্ভ",
      tamil: "கும்பம்",
      telugu: "కుంభం",
      kannada: "ಕುಂಭ",
      malayalam: "കുംഭം",
      punjabi: "ਕੁੰਭ"
    }
  },
  {
    value: "Meena (Pisces)",
    label: "Meena (Pisces)",
    native: {
      gujarati: "મીન",
      hindi: "मीन",
      marathi: "मीन",
      bengali: "মীন",
      tamil: "மீனம்",
      telugu: "మీనం",
      kannada: "ಮೀನ",
      malayalam: "മീനം",
      punjabi: "ਮੀਨ"
    }
  }
]

export const nakshatraData = [
  {
    value: "Ashwini",
    label: "Ashwini",
    native: {
      gujarati: "અશ્વિની",
      hindi: "अश्विनी",
      marathi: "अश्विनी",
      bengali: "অশ্বিনী",
      tamil: "அஸ்வினி",
      telugu: "అశ్విని",
      kannada: "ಅಶ್ವಿನಿ",
      malayalam: "അശ്വിനി",
      punjabi: "ਅਸ਼ਵਿਨੀ"
    }
  },
  {
    value: "Bharani",
    label: "Bharani",
    native: {
      gujarati: "ભરણી",
      hindi: "भरणी",
      marathi: "भरणी",
      bengali: "ভরণী",
      tamil: "பரணி",
      telugu: "భరణి",
      kannada: "ಭರಣಿ",
      malayalam: "ഭരണി",
      punjabi: "ਭਰਣੀ"
    }
  },
  {
    value: "Krittika",
    label: "Krittika",
    native: {
      gujarati: "કૃત્તિકા",
      hindi: "कृत्तिका",
      marathi: "कृत्तिका",
      bengali: "কৃত্তিকা",
      tamil: "கார்த்திகை",
      telugu: "కృత్తిక",
      kannada: "ಕೃತ್ತಿಕಾ",
      malayalam: "കൃത്തിക",
      punjabi: "ਕ੍ਰਿਤਿਕਾ"
    }
  },
  {
    value: "Rohini",
    label: "Rohini",
    native: {
      gujarati: "રોહિણી",
      hindi: "रोहिणी",
      marathi: "रोहिणी",
      bengali: "রোহিণী",
      tamil: "ரோகிணி",
      telugu: "రోహిణి",
      kannada: "ರೋಹಿಣಿ",
      malayalam: "രോഹിണി",
      punjabi: "ਰੋਹਿਣੀ"
    }
  },
  {
    value: "Mrigashira",
    label: "Mrigashira",
    native: {
      gujarati: "મૃગશિરા",
      hindi: "मृगशिरा",
      marathi: "मृगशिरा",
      bengali: "মৃগশিরা",
      tamil: "மிருகசீரிடம்",
      telugu: "మృగశిర",
      kannada: "ಮೃಗಶಿರ",
      malayalam: "മൃഗശിര",
      punjabi: "ਮ੍ਰਿਗਸ਼ਿਰਾ"
    }
  },
  {
    value: "Ardra",
    label: "Ardra",
    native: {
      gujarati: "આર્દ્રા",
      hindi: "आर्द्रा",
      marathi: "आर्द्रा",
      bengali: "আর্দ্রা",
      tamil: "திருவாதிரை",
      telugu: "ఆర్ద్ర",
      kannada: "ಆರ್ದ್ರಾ",
      malayalam: "ആര്ദ്ര",
      punjabi: "ਆਰਦਰਾ"
    }
  },
  {
    value: "Punarvasu",
    label: "Punarvasu",
    native: {
      gujarati: "પુનર્વસુ",
      hindi: "पुनर्वसु",
      marathi: "पुनर्वसु",
      bengali: "পুনর্বসু",
      tamil: "புனர்பூசம்",
      telugu: "పునర్వసు",
      kannada: "ಪುನರ್ವಸು",
      malayalam: "പുനര്വസു",
      punjabi: "ਪੁਨਰਵਸੁ"
    }
  },
  {
    value: "Pushya",
    label: "Pushya",
    native: {
      gujarati: "પુષ્ય",
      hindi: "पुष्य",
      marathi: "पुष्य",
      bengali: "পুষ্য",
      tamil: "பூசம்",
      telugu: "పుష్య",
      kannada: "ಪುಷ್ಯ",
      malayalam: "പുഷ്യ",
      punjabi: "ਪੁਸ਼"
    }
  },
  {
    value: "Ashlesha",
    label: "Ashlesha",
    native: {
      gujarati: "આશ્લેષા",
      hindi: "आश्लेषा",
      marathi: "आश्लेषा",
      bengali: "আশ্লেষা",
      tamil: "ஆயில்யம்",
      telugu: "ఆశ్లేష",
      kannada: "ಆಶ್ಲೇಷಾ",
      malayalam: "ആശ്ലേഷ",
      punjabi: "ਆਸ਼ਲੇਸ਼ਾ"
    }
  },
  {
    value: "Magha",
    label: "Magha",
    native: {
      gujarati: "મઘા",
      hindi: "मघा",
      marathi: "मघा",
      bengali: "মঘা",
      tamil: "மகம்",
      telugu: "మఘ",
      kannada: "ಮಘಾ",
      malayalam: "മഘ",
      punjabi: "ਮਘਾ"
    }
  },
  {
    value: "Purva Phalguni",
    label: "Purva Phalguni",
    native: {
      gujarati: "પૂર્વ ફાલ્ગુની",
      hindi: "पूर्व फाल्गुनी",
      marathi: "पूर्व फाल्गुनी",
      bengali: "পূর্ব ফাল্গুনী",
      tamil: "பூரம்",
      telugu: "పూర్వ ఫాల్గుణి",
      kannada: "ಪೂರ್ವ ಫಾಲ್ಗುನಿ",
      malayalam: "പൂര്വ ഫാല്ഗുനി",
      punjabi: "ਪੂਰਵ ਫਾਲਗੁਨੀ"
    }
  },
  {
    value: "Uttara Phalguni",
    label: "Uttara Phalguni",
    native: {
      gujarati: "ઉત્તર ફાલ્ગુની",
      hindi: "उत्तर फाल्गुनी",
      marathi: "उत्तर फाल्गुनी",
      bengali: "উত্তর ফাল্গুনী",
      tamil: "உத்திரம்",
      telugu: "ఉత్తర ఫాల్గుణి",
      kannada: "ಉತ್ತರ ಫಾಲ್ಗುನಿ",
      malayalam: "ഉത്തര ഫാല്ഗുനി",
      punjabi: "ਉੱਤਰ ਫਾਲਗੁਨੀ"
    }
  },
  {
    value: "Hasta",
    label: "Hasta",
    native: {
      gujarati: "હસ્ત",
      hindi: "हस्त",
      marathi: "हस्त",
      bengali: "হস্ত",
      tamil: "ஹஸ்தம்",
      telugu: "హస్త",
      kannada: "ಹಸ್ತ",
      malayalam: "ഹസ്ത",
      punjabi: "ਹਸਤ"
    }
  },
  {
    value: "Chitra",
    label: "Chitra",
    native: {
      gujarati: "ચિત્રા",
      hindi: "चित्रा",
      marathi: "चित्रा",
      bengali: "চিত্রা",
      tamil: "சித்திரை",
      telugu: "చిత్ర",
      kannada: "ಚಿತ್ರಾ",
      malayalam: "ചിത്ര",
      punjabi: "ਚਿਤਰਾ"
    }
  },
  {
    value: "Swati",
    label: "Swati",
    native: {
      gujarati: "સ્વાતિ",
      hindi: "स्वाति",
      marathi: "स्वाति",
      bengali: "স্বাতি",
      tamil: "சுவாதி",
      telugu: "స్వాతి",
      kannada: "ಸ್ವಾತಿ",
      malayalam: "സ്വാതി",
      punjabi: "ਸਵਾਤੀ"
    }
  },
  {
    value: "Vishakha",
    label: "Vishakha",
    native: {
      gujarati: "વિશાખા",
      hindi: "विशाखा",
      marathi: "विशाखा",
      bengali: "বিশাখা",
      tamil: "விசாகம்",
      telugu: "విశాఖ",
      kannada: "ವಿಶಾಖಾ",
      malayalam: "വിശാഖ",
      punjabi: "ਵਿਸ਼ਾਖਾ"
    }
  },
  {
    value: "Anuradha",
    label: "Anuradha",
    native: {
      gujarati: "અનુરાધા",
      hindi: "अनुराधा",
      marathi: "अनुराधा",
      bengali: "অনুরাধা",
      tamil: "அனுஷம்",
      telugu: "అనూరాధ",
      kannada: "ಅನುರಾಧಾ",
      malayalam: "അനൂരാധ",
      punjabi: "ਅਨੁਰਾਧਾ"
    }
  },
  {
    value: "Jyeshtha",
    label: "Jyeshtha",
    native: {
      gujarati: "જ્યેષ્ઠા",
      hindi: "ज्येष्ठा",
      marathi: "ज्येष्ठा",
      bengali: "জ্যেষ্ঠা",
      tamil: "கேட்டை",
      telugu: "జ్యేష్ఠ",
      kannada: "ಜ್ಯೇಷ್ಠಾ",
      malayalam: "ജ്യേഷ്ഠ",
      punjabi: "ਜਿਸ਼ਟਾ"
    }
  },
  {
    value: "Mula",
    label: "Mula",
    native: {
      gujarati: "મૂળ",
      hindi: "मूल",
      marathi: "मूल",
      bengali: "মূল",
      tamil: "மூலம்",
      telugu: "మూల",
      kannada: "ಮೂಲ",
      malayalam: "മൂല",
      punjabi: "ਮੂਲ"
    }
  },
  {
    value: "Purva Ashadha",
    label: "Purva Ashadha",
    native: {
      gujarati: "પૂર્વ આષાઢા",
      hindi: "पूर्वाषाढा",
      marathi: "पूर्वाषाढा",
      bengali: "পূর্বাষাঢ়া",
      tamil: "பூராடம்",
      telugu: "పూర్వాషాఢ",
      kannada: "ಪೂರ್ವಾಷಾಢ",
      malayalam: "പൂര്വാഷാഢ",
      punjabi: "ਪੂਰਵ ਅਸ਼ਾਢਾ"
    }
  },
  {
    value: "Uttara Ashadha",
    label: "Uttara Ashadha",
    native: {
      gujarati: "ઉત્તર આષાઢા",
      hindi: "उत्तराषाढा",
      marathi: "उत्तराषाढा",
      bengali: "উত্তরাষাঢ়া",
      tamil: "உத்திராடம்",
      telugu: "ఉత్తరాషాఢ",
      kannada: "ಉತ್ತರಾಷಾಢ",
      malayalam: "ഉത്തരാഷാഢ",
      punjabi: "ਉੱਤਰ ਅਸ਼ਾਢਾ"
    }
  },
  {
    value: "Shravana",
    label: "Shravana",
    native: {
      gujarati: "શ્રાવણ",
      hindi: "श्रवण",
      marathi: "श्रवण",
      bengali: "শ্রবণা",
      tamil: "திருவோணம்",
      telugu: "శ్రవణ",
      kannada: "ಶ್ರವಣ",
      malayalam: "ശ്രവണ",
      punjabi: "ਸ਼ਰਵਣ"
    }
  },
  {
    value: "Dhanishta",
    label: "Dhanishta",
    native: {
      gujarati: "ધનિષ્ઠા",
      hindi: "धनिष्ठा",
      marathi: "धनिष्ठा",
      bengali: "ধনিষ্ঠা",
      tamil: "அவிட்டம்",
      telugu: "ధనిష్ఠ",
      kannada: "ಧನಿಷ್ಠಾ",
      malayalam: "ധനിഷ്ഠ",
      punjabi: "ਧਨਿਸ਼ਟਾ"
    }
  },
  {
    value: "Shatabhisha",
    label: "Shatabhisha",
    native: {
      gujarati: "શતભિષા",
      hindi: "शतभिषा",
      marathi: "शतभिषा",
      bengali: "শতভিষা",
      tamil: "சதயம்",
      telugu: "శతభిష",
      kannada: "ಶತಭಿಷಾ",
      malayalam: "ശതഭിഷ",
      punjabi: "ਸ਼ਤਭਿਸ਼ਾ"
    }
  },
  {
    value: "Purva Bhadrapada",
    label: "Purva Bhadrapada",
    native: {
      gujarati: "પૂર્વ ભાદ્રપદ",
      hindi: "पूर्वभाद्रपद",
      marathi: "पूर्वभाद्रपद",
      bengali: "পূর্বভাদ্রপদ",
      tamil: "பூரட்டாதி",
      telugu: "పూర్వభాద్రపద",
      kannada: "ಪೂರ್ವಭಾದ್ರಪದ",
      malayalam: "പൂര്വഭാദ്രപദ",
      punjabi: "ਪੂਰਵ ਭਾਦਰਪਦ"
    }
  },
  {
    value: "Uttara Bhadrapada",
    label: "Uttara Bhadrapada",
    native: {
      gujarati: "ઉત્તર ભાદ્રપદ",
      hindi: "उत्तरभाद्रपद",
      marathi: "उत्तरभाद्रपद",
      bengali: "উত্তরভাদ্রপদ",
      tamil: "உத்திரட்டாதி",
      telugu: "ఉత్తరభాద్రపద",
      kannada: "ಉತ್ತರಭಾದ್ರಪದ",
      malayalam: "ഉത്തരഭാദ്രപദ",
      punjabi: "ਉੱਤਰ ਭਾਦਰਪਦ"
    }
  },
  {
    value: "Revati",
    label: "Revati",
    native: {
      gujarati: "રેવતી",
      hindi: "रेवती",
      marathi: "रेवती",
      bengali: "রেবতী",
      tamil: "ரேவதி",
      telugu: "రేవతి",
      kannada: "ರೇವತಿ",
      malayalam: "രേവതി",
      punjabi: "ਰੇਵਤੀ"
    }
  }
]

// Helper function to get native text for a language
export const getNativeText = (data: any[], value: string, language: string) => {
  const item = data.find(d => d.value === value)
  if (!item || !item.native[language.toLowerCase()]) {
    return value
  }
  return `${item.native[language.toLowerCase()]} (${item.label})`
}

// Helper function to get display text for filters
export const getDisplayText = (data: any[], value: string, language: string) => {
  const item = data.find(d => d.value === value)
  if (!item) return value
  
  const nativeText = item.native[language.toLowerCase()]
  if (nativeText) {
    return `${nativeText} (${item.label})`
  }
  return item.label
} 