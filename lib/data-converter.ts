import type { NameData } from "@/types/name-data"

// Utility to convert existing name data to new format
export class DataConverter {
  
  // Convert existing name data to new format
  static convertToNewFormat(existingName: any): NameData {
    const baseData = {
      name_en: existingName.name_en,
      name_native: existingName.name_native,
      gender: existingName.gender,
      religion: existingName.religion,
      language: existingName.language,
      meaning_en: existingName.meaning_en,
      meaning_native: existingName.meaning_native,
      starting_letter: existingName.starting_letter,
      pronunciation: this.generatePronunciation(existingName.name_en),
      origin: this.determineOrigin(existingName.language, existingName.religion)
    }

    // Add Hindu-specific fields only for Hindu names
    if (existingName.religion === "Hindu") {
      return {
        ...baseData,
        rashi: this.getRashi(existingName.name_en),
        rashi_native: this.getRashiNative(existingName.language, existingName.name_en),
        nakshatra: this.getNakshatra(existingName.name_en),
        nakshatra_native: this.getNakshatraNative(existingName.language, existingName.name_en)
      }
    }

    return baseData
  }

  // Generate pronunciation guide
  private static generatePronunciation(nameEn: string): string {
    const name = nameEn.toLowerCase()
    
    // Simple pronunciation rules
    const pronunciationMap: { [key: string]: string } = {
      'aa': 'AA',
      'ee': 'EE', 
      'oo': 'OO',
      'ai': 'AI',
      'au': 'AU',
      'ch': 'CH',
      'sh': 'SH',
      'th': 'TH',
      'ph': 'F',
      'kh': 'KH',
      'gh': 'GH',
      'dh': 'DH',
      'bh': 'BH',
      'ng': 'NG'
    }

    let pronunciation = nameEn
    Object.entries(pronunciationMap).forEach(([pattern, replacement]) => {
      pronunciation = pronunciation.replace(new RegExp(pattern, 'gi'), replacement)
    })

    return pronunciation
  }

  // Determine origin based on language and religion
  private static determineOrigin(language: string, religion: string): string {
    const originMap: { [key: string]: string } = {
      'Gujarati': 'Sanskrit',
      'Hindi': 'Sanskrit', 
      'Marathi': 'Sanskrit',
      'Bengali': 'Sanskrit',
      'Tamil': 'Tamil',
      'Telugu': 'Sanskrit',
      'Kannada': 'Sanskrit',
      'Malayalam': 'Sanskrit',
      'Punjabi': 'Punjabi',
      'Urdu': 'Arabic'
    }

    if (religion === "Muslim") return "Arabic"
    if (religion === "Christian") return "Hebrew"
    if (religion === "Sikh") return "Punjabi"
    
    return originMap[language] || "Sanskrit"
  }

  // Get Rashi (Zodiac sign) for Hindu names
  private static getRashi(nameEn: string): string {
    const firstLetter = nameEn.charAt(0).toLowerCase()
    
    const rashiMap: { [key: string]: string } = {
      'a': 'Mesha (Aries)',
      'b': 'Vrishabha (Taurus)',
      'c': 'Mithuna (Gemini)',
      'd': 'Karka (Cancer)',
      'e': 'Simha (Leo)',
      'f': 'Kanya (Virgo)',
      'g': 'Tula (Libra)',
      'h': 'Vrishchika (Scorpio)',
      'i': 'Dhanu (Sagittarius)',
      'j': 'Makar (Capricorn)',
      'k': 'Kumbha (Aquarius)',
      'l': 'Meena (Pisces)',
      'm': 'Mesha (Aries)',
      'n': 'Vrishabha (Taurus)',
      'o': 'Mithuna (Gemini)',
      'p': 'Karka (Cancer)',
      'q': 'Simha (Leo)',
      'r': 'Kanya (Virgo)',
      's': 'Tula (Libra)',
      't': 'Vrishchika (Scorpio)',
      'u': 'Dhanu (Sagittarius)',
      'v': 'Makar (Capricorn)',
      'w': 'Kumbha (Aquarius)',
      'x': 'Meena (Pisces)',
      'y': 'Mesha (Aries)',
      'z': 'Vrishabha (Taurus)'
    }

    return rashiMap[firstLetter] || 'Mesha (Aries)'
  }

  // Get Rashi in native script for all languages
  private static getRashiNative(language: string, nameEn: string): string {
    const rashi = this.getRashi(nameEn)
    
    const nativeRashiMap: { [key: string]: { [key: string]: string } } = {
      'Gujarati': {
        'Mesha (Aries)': 'મેષ',
        'Vrishabha (Taurus)': 'વૃષભ',
        'Mithuna (Gemini)': 'મિથુન',
        'Karka (Cancer)': 'કર્ક',
        'Simha (Leo)': 'સિંહ',
        'Kanya (Virgo)': 'કન્યા',
        'Tula (Libra)': 'તુલા',
        'Vrishchika (Scorpio)': 'વૃશ્ચિક',
        'Dhanu (Sagittarius)': 'ધનુ',
        'Makar (Capricorn)': 'મકર',
        'Kumbha (Aquarius)': 'કુંભ',
        'Meena (Pisces)': 'મીન'
      },
      'Hindi': {
        'Mesha (Aries)': 'मेष',
        'Vrishabha (Taurus)': 'वृषभ',
        'Mithuna (Gemini)': 'मिथुन',
        'Karka (Cancer)': 'कर्क',
        'Simha (Leo)': 'सिंह',
        'Kanya (Virgo)': 'कन्या',
        'Tula (Libra)': 'तुला',
        'Vrishchika (Scorpio)': 'वृश्चिक',
        'Dhanu (Sagittarius)': 'धनु',
        'Makar (Capricorn)': 'मकर',
        'Kumbha (Aquarius)': 'कुम्भ',
        'Meena (Pisces)': 'मीन'
      },
      'Marathi': {
        'Mesha (Aries)': 'मेष',
        'Vrishabha (Taurus)': 'वृषभ',
        'Mithuna (Gemini)': 'मिथुन',
        'Karka (Cancer)': 'कर्क',
        'Simha (Leo)': 'सिंह',
        'Kanya (Virgo)': 'कन्या',
        'Tula (Libra)': 'तुला',
        'Vrishchika (Scorpio)': 'वृश्चिक',
        'Dhanu (Sagittarius)': 'धनु',
        'Makar (Capricorn)': 'मकर',
        'Kumbha (Aquarius)': 'कुम्भ',
        'Meena (Pisces)': 'मीन'
      },
      'Bengali': {
        'Mesha (Aries)': 'মেষ',
        'Vrishabha (Taurus)': 'বৃষভ',
        'Mithuna (Gemini)': 'মিথুন',
        'Karka (Cancer)': 'কর্ক',
        'Simha (Leo)': 'সিংহ',
        'Kanya (Virgo)': 'কন্যা',
        'Tula (Libra)': 'তুলা',
        'Vrishchika (Scorpio)': 'বৃশ্চিক',
        'Dhanu (Sagittarius)': 'ধনু',
        'Makar (Capricorn)': 'মকর',
        'Kumbha (Aquarius)': 'কুম্ভ',
        'Meena (Pisces)': 'মীন'
      },
      'Tamil': {
        'Mesha (Aries)': 'மேஷம்',
        'Vrishabha (Taurus)': 'ரிஷபம்',
        'Mithuna (Gemini)': 'மிதுனம்',
        'Karka (Cancer)': 'கர்க்கடகம்',
        'Simha (Leo)': 'சிம்மம்',
        'Kanya (Virgo)': 'கன்னி',
        'Tula (Libra)': 'துலாம்',
        'Vrishchika (Scorpio)': 'விருச்சிகம்',
        'Dhanu (Sagittarius)': 'தனுசு',
        'Makar (Capricorn)': 'மகரம்',
        'Kumbha (Aquarius)': 'கும்பம்',
        'Meena (Pisces)': 'மீனம்'
      },
      'Telugu': {
        'Mesha (Aries)': 'మేషం',
        'Vrishabha (Taurus)': 'వృషభం',
        'Mithuna (Gemini)': 'మిథునం',
        'Karka (Cancer)': 'కర్కాటకం',
        'Simha (Leo)': 'సింహం',
        'Kanya (Virgo)': 'కన్య',
        'Tula (Libra)': 'తులా',
        'Vrishchika (Scorpio)': 'వృశ్చికం',
        'Dhanu (Sagittarius)': 'ధనుస్సు',
        'Makar (Capricorn)': 'మకరం',
        'Kumbha (Aquarius)': 'కుంభం',
        'Meena (Pisces)': 'మీనం'
      },
      'Kannada': {
        'Mesha (Aries)': 'ಮೇಷ',
        'Vrishabha (Taurus)': 'ವೃಷಭ',
        'Mithuna (Gemini)': 'ಮಿಥುನ',
        'Karka (Cancer)': 'ಕರ್ಕಾಟಕ',
        'Simha (Leo)': 'ಸಿಂಹ',
        'Kanya (Virgo)': 'ಕನ್ಯಾ',
        'Tula (Libra)': 'ತುಲಾ',
        'Vrishchika (Scorpio)': 'ವೃಶ್ಚಿಕ',
        'Dhanu (Sagittarius)': 'ಧನು',
        'Makar (Capricorn)': 'ಮಕರ',
        'Kumbha (Aquarius)': 'ಕುಂಭ',
        'Meena (Pisces)': 'ಮೀನ'
      },
      'Malayalam': {
        'Mesha (Aries)': 'മേഷം',
        'Vrishabha (Taurus)': 'വൃഷഭം',
        'Mithuna (Gemini)': 'മിഥുനം',
        'Karka (Cancer)': 'കര്ക്കടകം',
        'Simha (Leo)': 'സിംഹം',
        'Kanya (Virgo)': 'കന്യ',
        'Tula (Libra)': 'തുലാ',
        'Vrishchika (Scorpio)': 'വൃശ്ചികം',
        'Dhanu (Sagittarius)': 'ധനു',
        'Makar (Capricorn)': 'മകരം',
        'Kumbha (Aquarius)': 'കുംഭം',
        'Meena (Pisces)': 'മീനം'
      },
      'Punjabi': {
        'Mesha (Aries)': 'ਮੇਸ਼',
        'Vrishabha (Taurus)': 'ਵ੍ਰਿਸ਼ਭ',
        'Mithuna (Gemini)': 'ਮਿਥੁਨ',
        'Karka (Cancer)': 'ਕਰਕ',
        'Simha (Leo)': 'ਸਿੰਘ',
        'Kanya (Virgo)': 'ਕੰਨਿਆ',
        'Tula (Libra)': 'ਤੁਲਾ',
        'Vrishchika (Scorpio)': 'ਵ੍ਰਿਸ਼ਚਿਕ',
        'Dhanu (Sagittarius)': 'ਧਨੁ',
        'Makar (Capricorn)': 'ਮਕਰ',
        'Kumbha (Aquarius)': 'ਕੁੰਭ',
        'Meena (Pisces)': 'ਮੀਨ'
      }
    }

    return nativeRashiMap[language]?.[rashi] || rashi
  }

  // Get Nakshatra (Lunar mansion) for Hindu names
  private static getNakshatra(nameEn: string): string {
    const firstLetter = nameEn.charAt(0).toLowerCase()
    
    const nakshatraMap: { [key: string]: string } = {
      'a': 'Ashwini',
      'b': 'Bharani',
      'c': 'Krittika',
      'd': 'Rohini',
      'e': 'Mrigashira',
      'f': 'Ardra',
      'g': 'Punarvasu',
      'h': 'Pushya',
      'i': 'Ashlesha',
      'j': 'Magha',
      'k': 'Purva Phalguni',
      'l': 'Uttara Phalguni',
      'm': 'Hasta',
      'n': 'Chitra',
      'o': 'Swati',
      'p': 'Vishakha',
      'q': 'Anuradha',
      'r': 'Jyeshtha',
      's': 'Mula',
      't': 'Purva Ashadha',
      'u': 'Uttara Ashadha',
      'v': 'Shravana',
      'w': 'Dhanishta',
      'x': 'Shatabhisha',
      'y': 'Purva Bhadrapada',
      'z': 'Uttara Bhadrapada'
    }

    return nakshatraMap[firstLetter] || 'Ashwini'
  }

  // Get Nakshatra in native script for all languages
  private static getNakshatraNative(language: string, nameEn: string): string {
    const nakshatra = this.getNakshatra(nameEn)
    
    const nativeNakshatraMap: { [key: string]: { [key: string]: string } } = {
      'Gujarati': {
        'Ashwini': 'અશ્વિની',
        'Bharani': 'ભરણી',
        'Krittika': 'કૃત્તિકા',
        'Rohini': 'રોહિણી',
        'Mrigashira': 'મૃગશિરા',
        'Ardra': 'આર્દ્રા',
        'Punarvasu': 'પુનર્વસુ',
        'Pushya': 'પુષ્ય',
        'Ashlesha': 'આશ્લેષા',
        'Magha': 'મઘા',
        'Purva Phalguni': 'પૂર્વ ફાલ્ગુની',
        'Uttara Phalguni': 'ઉત્તર ફાલ્ગુની',
        'Hasta': 'હસ્ત',
        'Chitra': 'ચિત્રા',
        'Swati': 'સ્વાતિ',
        'Vishakha': 'વિશાખા',
        'Anuradha': 'અનુરાધા',
        'Jyeshtha': 'જ્યેષ્ઠા',
        'Mula': 'મૂળ',
        'Purva Ashadha': 'પૂર્વ આષાઢા',
        'Uttara Ashadha': 'ઉત્તર આષાઢા',
        'Shravana': 'શ્રાવણ',
        'Dhanishta': 'ધનિષ્ઠા',
        'Shatabhisha': 'શતભિષા',
        'Purva Bhadrapada': 'પૂર્વ ભાદ્રપદા',
        'Uttara Bhadrapada': 'ઉત્તર ભાદ્રપદા'
      },
      'Hindi': {
        'Ashwini': 'अश्विनी',
        'Bharani': 'भरणी',
        'Krittika': 'कृत्तिका',
        'Rohini': 'रोहिणी',
        'Mrigashira': 'मृगशिरा',
        'Ardra': 'आर्द्रा',
        'Punarvasu': 'पुनर्वसु',
        'Pushya': 'पुष्य',
        'Ashlesha': 'आश्लेषा',
        'Magha': 'मघा',
        'Purva Phalguni': 'पूर्व फाल्गुनी',
        'Uttara Phalguni': 'उत्तर फाल्गुनी',
        'Hasta': 'हस्त',
        'Chitra': 'चित्रा',
        'Swati': 'स्वाति',
        'Vishakha': 'विशाखा',
        'Anuradha': 'अनुराधा',
        'Jyeshtha': 'ज्येष्ठा',
        'Mula': 'मूल',
        'Purva Ashadha': 'पूर्व आषाढा',
        'Uttara Ashadha': 'उत्तर आषाढा',
        'Shravana': 'श्रवण',
        'Dhanishta': 'धनिष्ठा',
        'Shatabhisha': 'शतभिषा',
        'Purva Bhadrapada': 'पूर्व भाद्रपदा',
        'Uttara Bhadrapada': 'उत्तर भाद्रपदा'
      },
      'Marathi': {
        'Ashwini': 'अश्विनी',
        'Bharani': 'भरणी',
        'Krittika': 'कृत्तिका',
        'Rohini': 'रोहिणी',
        'Mrigashira': 'मृगशिरा',
        'Ardra': 'आर्द्रा',
        'Punarvasu': 'पुनर्वसु',
        'Pushya': 'पुष्य',
        'Ashlesha': 'आश्लेषा',
        'Magha': 'मघा',
        'Purva Phalguni': 'पूर्व फाल्गुनी',
        'Uttara Phalguni': 'उत्तर फाल्गुनी',
        'Hasta': 'हस्त',
        'Chitra': 'चित्रा',
        'Swati': 'स्वाति',
        'Vishakha': 'विशाखा',
        'Anuradha': 'अनुराधा',
        'Jyeshtha': 'ज्येष्ठा',
        'Mula': 'मूल',
        'Purva Ashadha': 'पूर्व आषाढा',
        'Uttara Ashadha': 'उत्तर आषाढा',
        'Shravana': 'श्रवण',
        'Dhanishta': 'धनिष्ठा',
        'Shatabhisha': 'शतभिषा',
        'Purva Bhadrapada': 'पूर्व भाद्रपदा',
        'Uttara Bhadrapada': 'उत्तर भाद्रपदा'
      },
      'Bengali': {
        'Ashwini': 'অশ্বিনী',
        'Bharani': 'ভরণী',
        'Krittika': 'কৃত্তিকা',
        'Rohini': 'রোহিণী',
        'Mrigashira': 'মৃগশিরা',
        'Ardra': 'আর্দ্রা',
        'Punarvasu': 'পুনর্বসু',
        'Pushya': 'পুষ্য',
        'Ashlesha': 'আশ্লেষা',
        'Magha': 'মঘা',
        'Purva Phalguni': 'পূর্ব ফাল্গুনী',
        'Uttara Phalguni': 'উত্তর ফাল্গুনী',
        'Hasta': 'হস্ত',
        'Chitra': 'চিত্রা',
        'Swati': 'স্বাতি',
        'Vishakha': 'বিশাখা',
        'Anuradha': 'অনুরাধা',
        'Jyeshtha': 'জ্যেষ্ঠা',
        'Mula': 'মূল',
        'Purva Ashadha': 'পূর্ব আষাঢ়া',
        'Uttara Ashadha': 'উত্তর আষাঢ়া',
        'Shravana': 'শ্রাবণ',
        'Dhanishta': 'ধনিষ্ঠা',
        'Shatabhisha': 'শতভিষা',
        'Purva Bhadrapada': 'পূর্ব ভাদ্রপদা',
        'Uttara Bhadrapada': 'উত্তর ভাদ্রপদা'
      },
      'Tamil': {
        'Ashwini': 'அஸ்வினி',
        'Bharani': 'பரணி',
        'Krittika': 'கிருத்திகை',
        'Rohini': 'ரோகிணி',
        'Mrigashira': 'மிருகசீரிடம்',
        'Ardra': 'ஆர்த்திரை',
        'Punarvasu': 'புனர்பூசம்',
        'Pushya': 'பூசம்',
        'Ashlesha': 'ஆயில்யம்',
        'Magha': 'மகம்',
        'Purva Phalguni': 'பூரம்',
        'Uttara Phalguni': 'உத்திரம்',
        'Hasta': 'அஸ்தம்',
        'Chitra': 'சித்திரை',
        'Swati': 'சுவாதி',
        'Vishakha': 'விசாகம்',
        'Anuradha': 'அனுஷம்',
        'Jyeshtha': 'கேட்டை',
        'Mula': 'மூலம்',
        'Purva Ashadha': 'பூராடம்',
        'Uttara Ashadha': 'உத்திராடம்',
        'Shravana': 'திருவோணம்',
        'Dhanishta': 'அவிட்டம்',
        'Shatabhisha': 'சதயம்',
        'Purva Bhadrapada': 'பூரட்டாதி',
        'Uttara Bhadrapada': 'உத்திரட்டாதி'
      },
      'Telugu': {
        'Ashwini': 'అశ్విని',
        'Bharani': 'భరణి',
        'Krittika': 'కృత్తిక',
        'Rohini': 'రోహిణి',
        'Mrigashira': 'మృగశిర',
        'Ardra': 'ఆర్ద్ర',
        'Punarvasu': 'పునర్వసు',
        'Pushya': 'పుష్య',
        'Ashlesha': 'ఆశ్లేష',
        'Magha': 'మఘ',
        'Purva Phalguni': 'పూర్వ ఫాల్గుణి',
        'Uttara Phalguni': 'ఉత్తర ఫాల్గుణి',
        'Hasta': 'హస్త',
        'Chitra': 'చిత్ర',
        'Swati': 'స్వాతి',
        'Vishakha': 'విశాఖ',
        'Anuradha': 'అనురాధ',
        'Jyeshtha': 'జ్యేష్ఠ',
        'Mula': 'మూల',
        'Purva Ashadha': 'పూర్వాషాఢ',
        'Uttara Ashadha': 'ఉత్తరాషాఢ',
        'Shravana': 'శ్రవణ',
        'Dhanishta': 'ధనిష్ఠ',
        'Shatabhisha': 'శతభిష',
        'Purva Bhadrapada': 'పూర్వ భాద్రపద',
        'Uttara Bhadrapada': 'ఉత్తర భాద్రపద'
      },
      'Kannada': {
        'Ashwini': 'ಅಶ್ವಿನಿ',
        'Bharani': 'ಭರಣಿ',
        'Krittika': 'ಕೃತ್ತಿಕಾ',
        'Rohini': 'ರೋಹಿಣಿ',
        'Mrigashira': 'ಮೃಗಶಿರ',
        'Ardra': 'ಆರ್ದ್ರ',
        'Punarvasu': 'ಪುನರ್ವಸು',
        'Pushya': 'ಪುಷ್ಯ',
        'Ashlesha': 'ಆಶ್ಲೇಷ',
        'Magha': 'ಮಘ',
        'Purva Phalguni': 'ಪೂರ್ವ ಫಾಲ್ಗುಣಿ',
        'Uttara Phalguni': 'ಉತ್ತರ ಫಾಲ್ಗುಣಿ',
        'Hasta': 'ಹಸ್ತ',
        'Chitra': 'ಚಿತ್ರಾ',
        'Swati': 'ಸ್ವಾತಿ',
        'Vishakha': 'ವಿಶಾಖ',
        'Anuradha': 'ಅನುರಾಧ',
        'Jyeshtha': 'ಜ್ಯೇಷ್ಠ',
        'Mula': 'ಮೂಲ',
        'Purva Ashadha': 'ಪೂರ್ವಾಷಾಢ',
        'Uttara Ashadha': 'ಉತ್ತರಾಷಾಢ',
        'Shravana': 'ಶ್ರವಣ',
        'Dhanishta': 'ಧನಿಷ್ಠ',
        'Shatabhisha': 'ಶತಭಿಷ',
        'Purva Bhadrapada': 'ಪೂರ್ವ ಭಾದ್ರಪದ',
        'Uttara Bhadrapada': 'ಉತ್ತರ ಭಾದ್ರಪದ'
      },
      'Malayalam': {
        'Ashwini': 'അശ്വിനി',
        'Bharani': 'ഭരണി',
        'Krittika': 'കൃത്തിക',
        'Rohini': 'രോഹിണി',
        'Mrigashira': 'മൃഗശിര',
        'Ardra': 'ആർദ്ര',
        'Punarvasu': 'പുനർവസു',
        'Pushya': 'പുഷ്യ',
        'Ashlesha': 'ആശ്ലേഷ',
        'Magha': 'മഘ',
        'Purva Phalguni': 'പൂർവ ഫാൽഗുണി',
        'Uttara Phalguni': 'ഉത്തര ഫാൽഗുണി',
        'Hasta': 'ഹസ്ത',
        'Chitra': 'ചിത്ര',
        'Swati': 'സ്വാതി',
        'Vishakha': 'വിശാഖ',
        'Anuradha': 'അനുരാധ',
        'Jyeshtha': 'ജ്യേഷ്ഠ',
        'Mula': 'മൂല',
        'Purva Ashadha': 'പൂർവാഷാഢ',
        'Uttara Ashadha': 'ഉത്തരാഷാഢ',
        'Shravana': 'ശ്രവണ',
        'Dhanishta': 'ധനിഷ്ഠ',
        'Shatabhisha': 'ശതഭിഷ',
        'Purva Bhadrapada': 'പൂർവ ഭാദ്രപദ',
        'Uttara Bhadrapada': 'ഉത്തര ഭാദ്രപദ'
      },
      'Punjabi': {
        'Ashwini': 'ਅਸ਼ਵਿਨੀ',
        'Bharani': 'ਭਰਣੀ',
        'Krittika': 'ਕ੍ਰਿਤਿਕਾ',
        'Rohini': 'ਰੋਹਿਣੀ',
        'Mrigashira': 'ਮ੍ਰਿਗਸ਼ਿਰਾ',
        'Ardra': 'ਆਰਦਰਾ',
        'Punarvasu': 'ਪੁਨਰਵਸੁ',
        'Pushya': 'ਪੁਸ਼ਯ',
        'Ashlesha': 'ਆਸ਼ਲੇਸ਼ਾ',
        'Magha': 'ਮਘਾ',
        'Purva Phalguni': 'ਪੂਰਵ ਫਾਲਗੁਨੀ',
        'Uttara Phalguni': 'ਉੱਤਰ ਫਾਲਗੁਨੀ',
        'Hasta': 'ਹਸਤ',
        'Chitra': 'ਚਿਤਰਾ',
        'Swati': 'ਸਵਾਤੀ',
        'Vishakha': 'ਵਿਸ਼ਾਖਾ',
        'Anuradha': 'ਅਨੁਰਾਧਾ',
        'Jyeshtha': 'ਜਿਸ਼ਟਾ',
        'Mula': 'ਮੂਲ',
        'Purva Ashadha': 'ਪੂਰਵ ਆਸ਼ਾਢਾ',
        'Uttara Ashadha': 'ਉੱਤਰ ਆਸ਼ਾਢਾ',
        'Shravana': 'ਸ਼੍ਰਾਵਣ',
        'Dhanishta': 'ਧਨਿਸ਼ਟਾ',
        'Shatabhisha': 'ਸ਼ਤਭਿਸ਼ਾ',
        'Purva Bhadrapada': 'ਪੂਰਵ ਭਾਦਰਪਦਾ',
        'Uttara Bhadrapada': 'ਉੱਤਰ ਭਾਦਰਪਦਾ'
      }
    }

    return nativeNakshatraMap[language]?.[nakshatra] || nakshatra
  }

  // Convert entire array of names
  static convertArray(existingNames: any[]): NameData[] {
    return existingNames.map(name => this.convertToNewFormat(name))
  }

  // Validate converted data
  static validateNameData(nameData: NameData): boolean {
    const requiredFields = ['name_en', 'name_native', 'gender', 'religion', 'language', 'meaning_en', 'meaning_native', 'starting_letter', 'pronunciation', 'origin']
    
    for (const field of requiredFields) {
      if (!nameData[field as keyof NameData]) {
        console.error(`Missing required field: ${field}`)
        return false
      }
    }

    // Check Hindu-specific fields
    if (nameData.religion === "Hindu") {
      const hinduFields = ['rashi', 'rashi_native', 'nakshatra', 'nakshatra_native']
      for (const field of hinduFields) {
        if (!nameData[field as keyof NameData]) {
          console.error(`Missing Hindu field: ${field}`)
          return false
        }
      }
    }

    return true
  }
} 