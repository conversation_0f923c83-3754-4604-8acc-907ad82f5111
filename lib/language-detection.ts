// Language detection patterns
const languagePatterns = {
  gujarati: {
    script: /[\u0A80-\u0AFF]/,
    keywords: ['ગુજરાતી', 'ગુજરાત', 'ગુજરાતી નામ'],
    commonLetters: ['અ', 'આ', 'ઇ', 'ઈ', 'ઉ', 'ઊ', 'એ', 'ઐ', 'ઓ', 'ઔ', 'ક', 'ખ', 'ગ', 'ઘ', 'ઙ', 'ચ', 'છ', 'જ', 'ઝ', 'ઞ', 'ટ', 'ઠ', 'ડ', 'ઢ', 'ણ', 'ત', 'થ', 'દ', 'ધ', 'ન', 'પ', 'ફ', 'બ', 'ભ', 'મ', 'ય', 'ર', 'લ', 'વ', 'શ', 'ષ', 'સ', 'હ', 'ળ', 'ક્ષ', 'જ્ઞ']
  },
  hindi: {
    script: /[\u0900-\u097F]/,
    keywords: ['हिंदी', 'हिन्दी', 'हिंदी नाम', 'हिन्दी नाम'],
    commonLetters: ['अ', 'आ', 'इ', 'ई', 'उ', 'ऊ', 'ए', 'ऐ', 'ओ', 'औ', 'क', 'ख', 'ग', 'घ', 'ङ', 'च', 'छ', 'ज', 'झ', 'ञ', 'ट', 'ठ', 'ड', 'ढ', 'ण', 'त', 'थ', 'द', 'ध', 'न', 'प', 'फ', 'ब', 'भ', 'म', 'य', 'र', 'ल', 'व', 'श', 'ष', 'स', 'ह', 'ळ', 'क्ष', 'ज्ञ']
  },
  punjabi: {
    script: /[\u0A00-\u0A7F]/,
    keywords: ['ਪੰਜਾਬੀ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬੀ ਨਾਮ'],
    commonLetters: ['ਅ', 'ਆ', 'ਇ', 'ਈ', 'ਉ', 'ਊ', 'ਏ', 'ਐ', 'ਓ', 'ਔ', 'ਕ', 'ਖ', 'ਗ', 'ਘ', 'ਙ', 'ਚ', 'ਛ', 'ਜ', 'ਝ', 'ਞ', 'ਟ', 'ਠ', 'ਡ', 'ਢ', 'ਣ', 'ਤ', 'ਥ', 'ਦ', 'ਧ', 'ਨ', 'ਪ', 'ਫ', 'ਬ', 'ਭ', 'ਮ', 'ਯ', 'ਰ', 'ਲ', 'ਵ', 'ਸ', 'ਹ', 'ੜ', 'ਖ਼', 'ਗ਼', 'ਜ਼', 'ਫ਼', 'ਲ਼']
  },
  tamil: {
    script: /[\u0B80-\u0BFF]/,
    keywords: ['தமிழ்', 'தமிழ் பெயர்கள்'],
    commonLetters: ['அ', 'ஆ', 'இ', 'ஈ', 'உ', 'ஊ', 'எ', 'ஏ', 'ஐ', 'ஒ', 'ஓ', 'ஔ', 'க', 'ங', 'ச', 'ஜ', 'ஞ', 'ட', 'ண', 'த', 'ந', 'ன', 'ப', 'ம', 'ய', 'ர', 'ற', 'ல', 'ள', 'ழ', 'வ', 'ஶ', 'ஷ', 'ஸ', 'ஹ', 'க்ஷ']
  },
  urdu: {
    script: /[\u0600-\u06FF]/,
    keywords: ['اردو', 'اردو نام'],
    commonLetters: ['ا', 'ب', 'پ', 'ت', 'ٹ', 'ث', 'ج', 'چ', 'ح', 'خ', 'د', 'ڈ', 'ذ', 'ر', 'ڑ', 'ز', 'ژ', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ک', 'گ', 'ل', 'م', 'ن', 'و', 'ہ', 'ھ', 'ی', 'ے']
  },
  bengali: {
    script: /[\u0980-\u09FF]/,
    keywords: ['বাংলা', 'বাংলা নাম'],
    commonLetters: ['অ', 'আ', 'ই', 'ঈ', 'উ', 'ঊ', 'ঋ', 'এ', 'ঐ', 'ও', 'ঔ', 'ক', 'খ', 'গ', 'ঘ', 'ঙ', 'চ', 'ছ', 'জ', 'ঝ', 'ঞ', 'ট', 'ঠ', 'ড', 'ঢ', 'ণ', 'ত', 'থ', 'দ', 'ধ', 'ন', 'প', 'ফ', 'ব', 'ভ', 'ম', 'য', 'র', 'ল', 'শ', 'ষ', 'স', 'হ', 'ড়', 'ঢ়', 'য়', 'ৎ']
  },
  marathi: {
    script: /[\u0900-\u097F]/,
    keywords: ['मराठी', 'मराठी नावे'],
    commonLetters: ['अ', 'आ', 'इ', 'ई', 'उ', 'ऊ', 'ए', 'ऐ', 'ओ', 'औ', 'क', 'ख', 'ग', 'घ', 'ङ', 'च', 'छ', 'ज', 'झ', 'ञ', 'ट', 'ठ', 'ड', 'ढ', 'ण', 'त', 'थ', 'द', 'ध', 'न', 'प', 'फ', 'ब', 'भ', 'म', 'य', 'र', 'ल', 'व', 'श', 'ष', 'स', 'ह', 'ळ', 'क्ष', 'ज्ञ']
  }
}

// Gender detection patterns
const genderPatterns = {
  male: {
    keywords: ['boy', 'male', 'son', 'lad', 'guy', 'man', 'masculine'],
    suffixes: ['kumar', 'singh', 'raj', 'dev', 'esh', 'an', 'ar', 'av', 'it', 'esh', 'endra', 'nath', 'pal', 'deep', 'jeet', 'preet', 'inder', 'bir', 'jit', 'vir', 'veer', 'sukh', 'gur', 'har', 'man', 'jit', 'preet', 'inder', 'bir', 'jit', 'vir', 'veer', 'sukh', 'gur', 'har', 'man']
  },
  female: {
    keywords: ['girl', 'female', 'daughter', 'lady', 'woman', 'feminine'],
    suffixes: ['a', 'i', 'ee', 'ya', 'ka', 'na', 'ta', 'ra', 'la', 'va', 'sa', 'ma', 'pa', 'da', 'ga', 'ja', 'cha', 'sha', 'tha', 'dha', 'bha', 'pha', 'kha', 'gha', 'jha', 'cha', 'tha', 'dha', 'bha', 'pha', 'kha', 'gha', 'jha']
  }
}

export interface LanguageDetectionResult {
  language: string | null
  confidence: number
  script: string | null
}

export interface GenderDetectionResult {
  gender: string | null
  confidence: number
}

export function detectLanguage(text: string): LanguageDetectionResult {
  if (!text || text.trim().length === 0) {
    return { language: null, confidence: 0, script: null }
  }

  const normalizedText = text.toLowerCase().trim()
  let bestMatch = { language: null as string | null, confidence: 0, script: null as string | null }

  for (const [language, patterns] of Object.entries(languagePatterns)) {
    let confidence = 0
    let script = null

    // Check for script characters
    if (patterns.script.test(text)) {
      confidence += 50
      script = language
    }

    // Check for keywords
    for (const keyword of patterns.keywords) {
      if (normalizedText.includes(keyword.toLowerCase())) {
        confidence += 30
        break
      }
    }

    // Check for common letters
    const letterMatches = patterns.commonLetters.filter(letter => 
      text.includes(letter)
    ).length
    confidence += (letterMatches / patterns.commonLetters.length) * 20

    if (confidence > bestMatch.confidence) {
      bestMatch = { language, confidence, script }
    }
  }

  return bestMatch
}

export function detectGender(text: string): GenderDetectionResult {
  if (!text || text.trim().length === 0) {
    return { gender: null, confidence: 0 }
  }

  const normalizedText = text.toLowerCase().trim()
  let bestMatch = { gender: null as string | null, confidence: 0 }

  for (const [gender, patterns] of Object.entries(genderPatterns)) {
    let confidence = 0

    // Check for keywords
    for (const keyword of patterns.keywords) {
      if (normalizedText.includes(keyword)) {
        confidence += 40
        break
      }
    }

    // Check for suffixes
    for (const suffix of patterns.suffixes) {
      if (normalizedText.endsWith(suffix)) {
        confidence += 20
        break
      }
    }

    if (confidence > bestMatch.confidence) {
      bestMatch = { gender, confidence }
    }
  }

  return bestMatch
}

export function getRelevantFilters(searchQuery: string) {
  const languageResult = detectLanguage(searchQuery)
  const genderResult = detectGender(searchQuery)

  return {
    detectedLanguage: languageResult.language,
    detectedGender: genderResult.gender,
    languageConfidence: languageResult.confidence,
    genderConfidence: genderResult.confidence,
    shouldHideLanguageFilters: languageResult.confidence > 30,
    shouldHideGenderFilters: genderResult.confidence > 30,
  }
}

// Language display names
export const languageDisplayNames: Record<string, string> = {
  gujarati: "ગુજરાતી",
  hindi: "हिंदी",
  punjabi: "ਪੰਜਾਬੀ",
  tamil: "தமிழ்",
  urdu: "اردو",
  bengali: "বাংলা",
  marathi: "मराठी",
  english: "English",
  arabic: "العربية"
}

// Religion display names
export const religionDisplayNames: Record<string, string> = {
  hindu: "Hindu",
  muslim: "Muslim",
  sikh: "Sikh",
  christian: "Christian"
} 