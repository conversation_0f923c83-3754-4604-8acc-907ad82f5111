/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable trailing slash for better SEO  
  trailingSlash: true,

  // Image optimization for better Core Web Vitals
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Add minimum cache time for better performance
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },

  // Compression for better performance
  compress: true,

  // SEO-friendly headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' *.googletagmanager.com *.google-analytics.com *.vercel-scripts.com; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: *.google-analytics.com; connect-src 'self' *.google-analytics.com *.analytics.google.com *.googletagmanager.com *.vercel-scripts.com *.emailjs.com api.emailjs.com;",
          },
          // Add cache control for better performance
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, s-maxage=86400, stale-while-revalidate=43200',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/icons/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // Add specific caching for blog and content pages
      {
        source: '/blog/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
          },
        ],
      },
      {
        source: '/(usa|uk|canada|australia|germany|france|netherlands|sweden|switzerland|austria|belgium|india)/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=1800, s-maxage=43200, stale-while-revalidate=21600',
          },
        ],
      },
    ]
  },

  // SEO redirects for better URL structure and user experience
  async redirects() {
    return [
      // ===== URL STRUCTURE IMPROVEMENTS =====
      {
        source: '/names/:path*',
        destination: '/name/:path*',
        permanent: true,
      },

      // ===== MAIN CATEGORY REDIRECTS =====
      {
        source: '/baby-names',
        destination: '/',
        permanent: true,
      },

      // ===== COUNTRY-SPECIFIC REDIRECTS =====
      {
        source: '/indian-names',
        destination: '/india/hindi-boy-names',
        permanent: true,
      },
      {
        source: '/american-names',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/british-names',
        destination: '/uk/english-boy-names',
        permanent: true,
      },
      {
        source: '/usa-names',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/uk-names',
        destination: '/uk/english-boy-names',
        permanent: true,
      },
      {
        source: '/canada-names',
        destination: '/canada/english-boy-names',
        permanent: true,
      },
      {
        source: '/australian-names',
        destination: '/australia/english-boy-names',
        permanent: true,
      },
      {
        source: '/german-names',
        destination: '/germany/german-boy-names',
        permanent: true,
      },
      {
        source: '/french-names',
        destination: '/france/french-boy-names',
        permanent: true,
      },
      {
        source: '/dutch-names',
        destination: '/netherlands/dutch-boy-names',
        permanent: true,
      },
      {
        source: '/swedish-names',
        destination: '/sweden/swedish-boy-names',
        permanent: true,
      },
      {
        source: '/swiss-names',
        destination: '/switzerland/german-boy-names',
        permanent: true,
      },
      {
        source: '/austrian-names',
        destination: '/austria/german-boy-names',
        permanent: true,
      },
      {
        source: '/belgian-names',
        destination: '/belgium/dutch-boy-names',
        permanent: true,
      },

      // ===== GENDER-SPECIFIC REDIRECTS =====
      {
        source: '/boy-names',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/girl-names',
        destination: '/usa/english-girl-names',
        permanent: true,
      },
      {
        source: '/male-names',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/female-names',
        destination: '/usa/english-girl-names',
        permanent: true,
      },

      // ===== RELIGIOUS REDIRECTS =====
      {
        source: '/christian-names',
        destination: '/religions/christian-boy-names',
        permanent: true,
      },
      {
        source: '/muslim-names',
        destination: '/religions/muslim-boy-names',
        permanent: true,
      },
      {
        source: '/hindu-names',
        destination: '/india/hindi-boy-names',
        permanent: true,
      },
      {
        source: '/sikh-names',
        destination: '/religions/sikh-boy-names',
        permanent: true,
      },

      // ===== LANGUAGE-SPECIFIC REDIRECTS =====
      {
        source: '/english-names',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/hindi-names',
        destination: '/india/hindi-boy-names',
        permanent: true,
      },
      {
        source: '/gujarati-names',
        destination: '/india/gujarati-boy-names',
        permanent: true,
      },
      {
        source: '/tamil-names',
        destination: '/india/tamil-boy-names',
        permanent: true,
      },
      {
        source: '/bengali-names',
        destination: '/india/bengali-boy-names',
        permanent: true,
      },
      {
        source: '/marathi-names',
        destination: '/india/marathi-boy-names',
        permanent: true,
      },
      {
        source: '/punjabi-names',
        destination: '/india/punjabi-boy-names',
        permanent: true,
      },
      {
        source: '/urdu-names',
        destination: '/india/urdu-boy-names',
        permanent: true,
      },

      // ===== TRENDING & POPULAR REDIRECTS =====
      {
        source: '/trending',
        destination: '/trending-baby-names-2025',
        permanent: true,
      },
      {
        source: '/popular',
        destination: '/popular-american-names-2025',
        permanent: true,
      },
      {
        source: '/trending-names-2025',
        destination: '/trending-baby-names-2025',
        permanent: true,
      },
      {
        source: '/popular-names-2025',
        destination: '/popular-american-names-2025',
        permanent: true,
      },

      // ===== SEARCH INTENT REDIRECTS =====
      {
        source: '/baby-name-meanings',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/baby-name-finder',
        destination: '/trending-names',
        permanent: true,
      },
      {
        source: '/baby-name-search',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/name-meanings',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/name-finder',
        destination: '/trending-names',
        permanent: true,
      },
      {
        source: '/name-search',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/baby-names-with-meanings',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/names-with-meanings',
        destination: '/usa/english-boy-names',
        permanent: true,
      },

      // ===== COMMON MISSPELLINGS & VARIATIONS =====
      {
        source: '/babynames',
        destination: '/',
        permanent: true,
      },
      {
        source: '/babyname',
        destination: '/',
        permanent: true,
      },
      {
        source: '/babynames-2025',
        destination: '/',
        permanent: true,
      },
      {
        source: '/baby-names-2025',
        destination: '/',
        permanent: true,
      },
      {
        source: '/indian-baby-names',
        destination: '/india/hindi-boy-names',
        permanent: true,
      },
      {
        source: '/american-baby-names',
        destination: '/usa/english-boy-names',
        permanent: true,
      },
      {
        source: '/british-baby-names',
        destination: '/uk/english-boy-names',
        permanent: true,
      },
      {
        source: '/canadian-baby-names',
        destination: '/canada/english-boy-names',
        permanent: true,
      },
      {
        source: '/australian-baby-names',
        destination: '/australia/english-boy-names',
        permanent: true,
      },
      {
        source: '/german-baby-names',
        destination: '/germany/german-boy-names',
        permanent: true,
      },
      {
        source: '/french-baby-names',
        destination: '/france/french-boy-names',
        permanent: true,
      },
      {
        source: '/dutch-baby-names',
        destination: '/netherlands/dutch-boy-names',
        permanent: true,
      },
      {
        source: '/swedish-baby-names',
        destination: '/sweden/swedish-boy-names',
        permanent: true,
      },
      {
        source: '/swiss-baby-names',
        destination: '/switzerland/german-boy-names',
        permanent: true,
      },
      {
        source: '/austrian-baby-names',
        destination: '/austria/german-boy-names',
        permanent: true,
      },
      {
        source: '/belgian-baby-names',
        destination: '/belgium/dutch-boy-names',
        permanent: true,
      },
    ]
  },

  // Experimental features for better performance
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
    optimizeCss: true,
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // PoweredByHeader for cleaner headers
  poweredByHeader: false,

  // Webpack optimization for better performance
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Optimize bundle for better Core Web Vitals
    config.optimization.splitChunks = {
      chunks: 'all',
      minSize: 10000,
      maxSize: 150000,
      cacheGroups: {
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: -10,
          chunks: 'all',
        },
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          priority: 20,
          chunks: 'all',
        },
        // Add specific chunk for UI components
        ui: {
          test: /[\\/]components[\\/]ui[\\/]/,
          name: 'ui-components',
          priority: 15,
          chunks: 'all',
        },
        // Add specific chunk for Lucide icons
        lucide: {
          test: /[\\/]node_modules[\\/]lucide-react[\\/]/,
          name: 'lucide-icons',
          priority: 10,
          chunks: 'all',
        },
        // Add specific chunk for Radix UI
        radix: {
          test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
          name: 'radix-ui',
          priority: 5,
          chunks: 'all',
        },
        // Add specific chunk for analytics
        analytics: {
          test: /[\\/]node_modules[\\/](@vercel|va)[\\/]/,
          name: 'analytics',
          priority: 5,
          chunks: 'all',
        },
      },
    }

    // Enable tree shaking (removed usedExports due to cacheUnaffected conflict)
    config.optimization.sideEffects = false

    // Add bundle analyzer in development
    if (dev) {
      config.plugins.push(
        new webpack.DefinePlugin({
          'process.env.NODE_ENV': JSON.stringify('development'),
        })
      )
    }

    return config
  },

}

export default nextConfig
