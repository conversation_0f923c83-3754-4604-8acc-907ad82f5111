// Simplified Service Worker for Baby Names - Optimized for Performance

const CACHE_NAME = 'baby-names-v2'
const STATIC_CACHE = 'baby-names-static-v2'

// Essential assets to cache
const STATIC_ASSETS = [
  '/',
  '/manifest.webmanifest',
  '/icons/logo_full_hd.png',
  '/favicon-32x32.png',
  '/favicon-16x16.png',
  '/favicon-192x192.png',
]

// Install event - cache essential assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => cache.addAll(STATIC_ASSETS))
      .then(() => self.skipWaiting())
  )
})

// Activate event - clean old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames
            .filter(cacheName =>
              cacheName !== STATIC_CACHE &&
              cacheName.startsWith('baby-names-')
            )
            .map(cacheName => caches.delete(cacheName))
        )
      })
      .then(() => self.clients.claim())
  )
})

// Fetch event - simplified caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests and external requests
  if (request.method !== 'GET' || url.origin !== location.origin) {
    return
  }

  // Cache static assets (CSS, JS, images)
  if (/\.(css|js|png|jpg|jpeg|svg|ico|woff|woff2)$/.test(url.pathname)) {
    event.respondWith(cacheFirst(request))
    return
  }

  // Network first for API calls
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(networkFirst(request))
    return
  }

  // Stale-while-revalidate for pages
  event.respondWith(staleWhileRevalidate(request))
})

// Cache-first strategy for static assets
async function cacheFirst(request) {
  const cached = await caches.match(request)
  if (cached) {
    return cached
  }

  try {
    const response = await fetch(request)
    if (response.ok) {
      const cache = await caches.open(STATIC_CACHE)
      cache.put(request, response.clone())
    }
    return response
  } catch (error) {
    return new Response('Offline', { status: 503 })
  }
}

// Network-first strategy for API calls
async function networkFirst(request) {
  try {
    const response = await fetch(request)
    return response
  } catch (error) {
    const cached = await caches.match(request)
    return cached || new Response('Offline', { status: 503 })
  }
}

// Stale-while-revalidate strategy for pages
async function staleWhileRevalidate(request) {
  const cached = await caches.match(request)

  const fetchPromise = fetch(request).then(response => {
    if (response.ok) {
      const cache = caches.open(STATIC_CACHE)
      cache.then(c => c.put(request, response.clone()))
    }
    return response
  })

  if (cached) {
    return cached
  }

  return await fetchPromise
} 