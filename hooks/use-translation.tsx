"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

// Define translations
const translations = {
  en: {
    babyNames: "Baby Names",
    male: "Male",
    female: "Female",
    unisex: "Unisex",
    meaning: "Meaning",
    gender: "Gender",
    religion: "Religion",
    language: "Language",
    startingLetter: "Starting Letter",
    allGenders: "All Genders",
    allReligions: "All Religions",
    allLanguages: "All Languages",
    allLetters: "All Letters",
    selectGender: "Select gender",
    selectReligion: "Select religion",
    selectLanguage: "Select language",
    selectLetter: "Select letter",
    resetFilters: "Reset Filters",
    filters: "Filters",
    allNames: "All Names",
    favorites: "Favorites",
    loadingNames: "Loading names...",
    noNamesFound: "No names found matching your criteria.",
    noFavorites: "You haven't added any favorites yet.",
    clickHeartToAdd: "Click the heart icon on any name to add it to your favorites.",
    namesFound: "names found",
    searchPlaceholder: "Search names or meanings...",
    search: "Search",
    viewDetails: "View Details",
    backToAllNames: "Back to all names",
    backToHome: "Back to home",
    loading: "Loading...",
    nameNotFound: "Name not found",
    shareName: "Share name",
    sharePost: "Share post",
    addToFavorites: "Add to favorites",
    removeFromFavorites: "Remove from favorites",
    pronounceName: "Pronounce name",
    similarNames: "Similar Names",
    nameOfTheDay: "Name of the Day",
    randomName: "Random Name",
    newRandom: "New Random",
    blog: "Blog",
    readMore: "Read More",
    backToBlog: "Back to Blog",
    postNotFound: "Post not found",
    previous: "Previous",
    next: "Next",
    is: "is a",
    name: "name",
    checkOutName: "Check out this beautiful name",
    babyNameMeaning: "baby name meaning",
    quickLinks: "Quick Links",
    home: "Home",
    legal: "Legal",
    privacyPolicy: "Privacy Policy",
    termsOfService: "Terms of Service",
    allRightsReserved: "All Rights Reserved",
    boysNames: "Boys Names",
    girlsNames: "Girls Names",
    selectLanguageText: "Select Language",
  },
  hi: {
    babyNames: "बच्चों के नाम",
    male: "लड़का",
    female: "लड़की",
    unisex: "उभयलिंगी",
    meaning: "अर्थ",
    gender: "लिंग",
    religion: "धर्म",
    language: "भाषा",
    startingLetter: "प्रारंभिक अक्षर",
    allGenders: "सभी लिंग",
    allReligions: "सभी धर्म",
    allLanguages: "सभी भाषाएँ",
    allLetters: "सभी अक्षर",
    selectGender: "लिंग चुनें",
    selectReligion: "धर्म चुनें",
    selectLanguage: "भाषा चुनें",
    selectLetter: "अक्षर चुनें",
    resetFilters: "फ़िल्टर रीसेट करें",
    filters: "फ़िल्टर",
    allNames: "सभी नाम",
    favorites: "पसंदीदा",
    loadingNames: "नाम लोड हो रहे हैं...",
    noNamesFound: "आपके मापदंडों से मेल खाने वाला कोई नाम नहीं मिला।",
    noFavorites: "आपने अभी तक कोई पसंदीदा नहीं जोड़ा है।",
    clickHeartToAdd: "किसी भी नाम पर दिल के आइकन पर क्लिक करके उसे अपने पसंदीदा में जोड़ें।",
    namesFound: "नाम मिले",
    searchPlaceholder: "नाम या अर्थ खोजें...",
    search: "खोज",
    viewDetails: "विवरण देखें",
    backToAllNames: "सभी नामों पर वापस जाएं",
    backToHome: "होम पर वापस जाएं",
    loading: "लोड हो रहा है...",
    nameNotFound: "नाम नहीं मिला",
    shareName: "नाम साझा करें",
    sharePost: "पोस्ट साझा करें",
    addToFavorites: "पसंदीदा में जोड़ें",
    removeFromFavorites: "पसंदीदा से हटाएं",
    pronounceName: "नाम का उच्चारण करें",
    similarNames: "समान नाम",
    nameOfTheDay: "आज का नाम",
    randomName: "रैंडम नाम",
    newRandom: "नया रैंडम",
    blog: "ब्लॉग",
    readMore: "और पढ़ें",
    backToBlog: "ब्लॉग पर वापस जाएं",
    postNotFound: "पोस्ट नहीं मिली",
    previous: "पिछला",
    next: "अगला",
    is: "एक",
    name: "नाम है",
    checkOutName: "इस सुंदर नाम को देखें",
    babyNameMeaning: "बच्चे के नाम का अर्थ",
    quickLinks: "त्वरित लिंक",
    home: "होम",
    legal: "कानूनी",
    privacyPolicy: "गोपनीयता नीति",
    termsOfService: "सेवा की शर्तें",
    allRightsReserved: "सर्वाधिकार सुरक्षित",
    boysNames: "लड़कों के नाम",
    girlsNames: "लड़कियों के नाम",
    selectLanguageText: "भाषा चुनें",
  },
}

type TranslationContextType = {
  t: (key: string) => string
  currentLanguage: string
  setLanguage: (lang: string) => void
}

const TranslationContext = createContext<TranslationContextType>({
  t: (key: string) => key,
  currentLanguage: "en",
  setLanguage: () => {},
})

export const TranslationProvider = ({ children }: { children: ReactNode }) => {
  const [currentLanguage, setCurrentLanguage] = useState("en")

  useEffect(() => {
    const savedLanguage = localStorage.getItem("preferredLanguage")
    if (savedLanguage && (savedLanguage === "en" || savedLanguage === "hi")) {
      setCurrentLanguage(savedLanguage)
    }
  }, [])

  const setLanguage = (lang: string) => {
    setCurrentLanguage(lang)
    localStorage.setItem("preferredLanguage", lang)
  }

  const t = (key: string) => {
    return translations[currentLanguage as "en" | "hi"][key as keyof (typeof translations)["en"]] || key
  }

  return (
    <TranslationContext.Provider value={{ t, currentLanguage, setLanguage }}>{children}</TranslationContext.Provider>
  )
}

export const useTranslation = () => useContext(TranslationContext)
