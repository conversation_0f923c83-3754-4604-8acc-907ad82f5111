import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
    const { pathname, hostname } = request.nextUrl

    // Redirect all non-www requests to www for consistent domain usage
    // This prevents duplicate content issues and ensures SEO consistency
    if (hostname === 'babynamediaries.com') {
        const url = request.nextUrl.clone()
        url.hostname = 'www.babynamediaries.com'
        return NextResponse.redirect(url, 301)
    }

    return NextResponse.next()
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!api|_next/static|_next/image|favicon.ico).*)',
    ],
} 