import { FAQSection } from "@/components/faq-section"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { BookOpen, Globe, Heart, Shield, Star, Users } from "lucide-react"
import Link from "next/link"

export const metadata = generatePageMetadata({
  path: "/about",
  title: "About Us - Baby Names",
  description: "Learn about Baby Names - your trusted source for baby name meanings from 12 countries. Discover our mission to help parents find the perfect name.",
  keywords: [
    "about baby names",
    "baby name website",
    "name meanings",
    "global baby names",
    "parenting resources",
  ],
})

export default function AboutPage() {
  const faqs = [
    {
      question: "What makes BabyNamesDiary different from other baby name websites?",
      answer: "BabyNamesDiary stands out with our comprehensive global database of 25,000+ names from 12 countries, AI-powered name generator, verified cultural meanings, and real-time trending data. We combine traditional naming wisdom with modern technology to help parents make informed decisions."
    },
    {
      question: "How accurate are the name meanings and cultural information?",
      answer: "All name meanings and cultural information are verified by linguistic experts and cultural consultants from each region. We source information from authoritative dictionaries, historical texts, and native language experts to ensure accuracy and cultural sensitivity."
    },
    {
      question: "Do you offer names from specific religions or cultures?",
      answer: "Yes! We offer names from Christian, Muslim, Sikh, Hindu, and other religious traditions, as well as names from various cultural backgrounds including European, Asian, American, and global naming traditions. You can filter by religion, country, and cultural origin."
    },
    {
      question: "How often do you update your name database?",
      answer: "We update our database monthly with new names, trending data, and cultural information. Our team continuously researches emerging naming trends and adds verified names from different cultures and regions."
    },
    {
      question: "Can I save and organize my favorite names?",
      answer: "Yes! You can save your favorite names by clicking the heart icon on any name card. Your favorites are stored locally on your device and will persist between sessions. You can also create custom lists and share them with family members."
    },
    {
      question: "Is BabyNamesDiary free to use?",
      answer: "Yes, BabyNamesDiary is completely free to use. All our name search features, AI generator, trending data, and cultural information are available at no cost. We believe that finding the perfect baby name should be accessible to all parents."
    },
    {
      question: "How do you ensure privacy when using the website?",
      answer: "We are committed to protecting your privacy. We don't collect personal information for name searches, and your favorites are stored locally on your device. We use secure HTTPS encryption and comply with GDPR and other privacy regulations."
    },
    {
      question: "Can I contribute names or suggest improvements?",
      answer: "Absolutely! We welcome contributions from our community. If you have names to suggest, cultural information to share, or ideas for improvement, please contact us through our contact page. We value input from parents and cultural experts."
    }
  ]

  return (
    <main className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">About Baby Names</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Your trusted global resource for discovering meaningful baby names from cultures around the world.
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-pink-500" />
            Our Mission
          </CardTitle>
        </CardHeader>
        <CardContent className="prose dark:prose-invert max-w-none">
          <p className="text-lg">
            Baby Names is dedicated to helping parents and expecting families find the perfect name for their children.
            We believe that a name is more than just a word—it's a gift that carries meaning, heritage, and hope for the future.
          </p>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-8 mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-blue-500" />
              Global Coverage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">We provide comprehensive name information from 12 countries:</p>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline">🇺🇸 USA</Badge>
              <Badge variant="outline">🇬🇧 UK</Badge>
              <Badge variant="outline">🇨🇦 Canada</Badge>
              <Badge variant="outline">🇦🇺 Australia</Badge>
              <Badge variant="outline">🇩🇪 Germany</Badge>
              <Badge variant="outline">🇫🇷 France</Badge>
              <Badge variant="outline">🇳🇱 Netherlands</Badge>
              <Badge variant="outline">🇸🇪 Sweden</Badge>
              <Badge variant="outline">🇨🇭 Switzerland</Badge>
              <Badge variant="outline">🇦🇹 Austria</Badge>
              <Badge variant="outline">🇧🇪 Belgium</Badge>
              <Badge variant="outline">🇮🇳 India</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-500" />
              What We Offer
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>• 25,000+ carefully curated baby names</li>
              <li>• Names in 15+ languages with native scripts</li>
              <li>• Cultural meanings and origins</li>
              <li>• Trending name analysis</li>
              <li>• Religious and traditional name collections</li>
              <li>• Mobile-friendly search experience</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-purple-500" />
            Our Approach
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Cultural Authenticity</h3>
            <p className="text-muted-foreground">
              We research name meanings from authentic cultural and linguistic sources, ensuring accuracy and respect for traditions.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">Global Perspective</h3>
            <p className="text-muted-foreground">
              Our international coverage helps families explore names from their heritage or discover beautiful options from other cultures.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">Modern Technology</h3>
            <p className="text-muted-foreground">
              We use advanced search and filtering tools to help you find names that match your preferences, meanings, and cultural background.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-8 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              Key Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>• Advanced search by meaning, origin, religion</li>
              <li>• Trending names across multiple countries</li>
              <li>• Names by alphabet and starting letter</li>
              <li>• Gender-specific collections</li>
              <li>• Cultural and religious categorization</li>
              <li>• Pronunciation guides</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-500" />
              Privacy & Trust
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">We are committed to protecting your privacy and providing a safe browsing experience:</p>
            <ul className="space-y-2">
              <li>• No personal data collection for name searches</li>
              <li>• Transparent privacy practices</li>
              <li>• Secure HTTPS encryption</li>
              <li>• GDPR and privacy law compliance</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Our Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">25,000+</div>
              <div className="text-sm text-muted-foreground">Baby Names</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">12</div>
              <div className="text-sm text-muted-foreground">Countries</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">15+</div>
              <div className="text-sm text-muted-foreground">Languages</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-pink-600">2025</div>
              <div className="text-sm text-muted-foreground">Updated Data</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Contact & Support</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            We're here to help you on your naming journey. Whether you have questions, suggestions,
            or need assistance, our team is ready to support you.
          </p>
          <div className="flex flex-wrap gap-4">
            <Link href="/contact" className="text-blue-600 hover:underline">
              Contact Us
            </Link>
            <Link href="/privacy-policy" className="text-blue-600 hover:underline">
              Privacy Policy
            </Link>
            <Link href="/terms-of-service" className="text-blue-600 hover:underline">
              Terms of Service
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <div className="mt-12">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
          Frequently Asked Questions
        </h2>
        <FAQSection faqs={faqs} />
      </div>
    </main>
  )
} 