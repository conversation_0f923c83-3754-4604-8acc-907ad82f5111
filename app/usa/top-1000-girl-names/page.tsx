"use client"

import NameC<PERSON> from "@/components/name-card";
import SEOStructuredData from "@/components/seo-structured-data";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { USAGirlNames } from "@/data/countries/usa";
import type { NameData } from "@/types/name-data";
import {
    Crown,
    Download,
    Filter,
    Grid3X3,
    Heart,
    List,
    Search,
    Share2,
    Star,
    TrendingUp,
    Trophy
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";

export default function Top1000UsaGirlNamesPage() {
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState("");
    const [favorites, setFavorites] = useState<string[]>([]);
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [sortBy, setSortBy] = useState<'popularity' | 'alphabetical' | 'favorites'>('popularity');
    const [filterBy, setFilterBy] = useState<'all' | 'favorites' | 'trending'>('all');
    const [showFilters, setShowFilters] = useState(false);

    // Get top 1000 names - data is already sorted by popularity_rank
    const names = USAGirlNames.slice(0, 1000);

    // Filter and sort names based on all criteria
    const filteredNames = useMemo(() => {
        let result = [...names];

        // Apply search filter
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase();
            result = result.filter(name =>
                name.name_en.toLowerCase().includes(query) ||
                name.meaning_en.toLowerCase().includes(query) ||
                name.origin?.toLowerCase().includes(query)
            );
        }

        // Apply category filter
        if (filterBy === 'favorites') {
            result = result.filter(name => favorites.includes(name.name_en));
        } else if (filterBy === 'trending') {
            // Show top 100 as "trending"
            result = result.slice(0, 100);
        }

        // Apply sorting
        if (sortBy === 'alphabetical') {
            result.sort((a, b) => a.name_en.localeCompare(b.name_en));
        } else if (sortBy === 'favorites') {
            result.sort((a, b) => {
                const aFav = favorites.includes(a.name_en);
                const bFav = favorites.includes(b.name_en);
                if (aFav && !bFav) return -1;
                if (!aFav && bFav) return 1;
                return 0;
            });
        }
        // 'popularity' is already sorted by default

        return result;
    }, [names, searchQuery, filterBy, sortBy, favorites]);

    const toggleFavorite = (nameEn: string) => {
        setFavorites(prev =>
            prev.includes(nameEn)
                ? prev.filter(n => n !== nameEn)
                : [...prev, nameEn]
        );
    };

    const handleNameClick = (name: NameData) => {
        router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`);
    };

    return (
        <>
            <SEOStructuredData
                names={names.slice(0, 20)}
                pageType="name-list"
                title="Top 1000 USA Girl Names 2025"
                description="Complete list of the top 1000 most popular girl names in America for 2025, ranked by official Social Security Administration data."
                url="/usa/top-1000-girl-names"
                country="USA"
                language="English"
                gender="girl"
            />

            <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-rose-50">
                <div className="container mx-auto px-4 py-8">
                    {/* Hero Header */}
                    <div className="text-center mb-12 relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-pink-600/10 to-rose-600/10 rounded-3xl blur-3xl"></div>
                        <div className="relative">
                            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-600 to-rose-600 text-white px-6 py-3 rounded-full text-sm font-semibold mb-6 shadow-lg">
                                <Crown className="h-4 w-4" />
                                🇺🇸 Official USA Rankings
                            </div>
                            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent mb-6">
                                Top 1000 USA Girl Names 2025
                            </h1>
                            <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8 leading-relaxed">
                                Discover the complete ranking of America's most beloved girl names for 2025.
                                Based on official <strong>Social Security Administration</strong> data with
                                detailed meanings, origins, and popularity trends.
                            </p>
                            <div className="flex flex-wrap justify-center gap-6 text-sm">
                                <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm">
                                    <Trophy className="h-4 w-4 text-yellow-500" />
                                    <strong>Official SSA Data</strong>
                                </div>
                                <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm">
                                    <TrendingUp className="h-4 w-4 text-green-500" />
                                    <strong>1000 Most Popular</strong>
                                </div>
                                <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm">
                                    <Star className="h-4 w-4 text-pink-500" />
                                    <strong>2025 Rankings</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Search & Filters */}
                    <div className="mb-8 space-y-6">
                        {/* Search Bar */}
                        <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                            <CardContent className="p-6">
                                <div className="flex flex-col lg:flex-row gap-4 items-center">
                                    <div className="relative flex-1 max-w-2xl">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                                        <Input
                                            placeholder="Search by name, meaning, or origin..."
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            className="pl-10 h-12 text-lg border-2 border-gray-200 focus:border-pink-500 rounded-xl"
                                        />
                                    </div>
                                    <div className="flex gap-2">
                                        <Button
                                            variant={showFilters ? "default" : "outline"}
                                            onClick={() => setShowFilters(!showFilters)}
                                            className="h-12 px-6"
                                        >
                                            <Filter className="h-4 w-4 mr-2" />
                                            Filters
                                        </Button>
                                        <Button
                                            variant={viewMode === 'grid' ? "default" : "outline"}
                                            onClick={() => setViewMode('grid')}
                                            size="icon"
                                            className="h-12 w-12"
                                        >
                                            <Grid3X3 className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant={viewMode === 'list' ? "default" : "outline"}
                                            onClick={() => setViewMode('list')}
                                            size="icon"
                                            className="h-12 w-12"
                                        >
                                            <List className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Filter Options */}
                        {showFilters && (
                            <Card className="border-0 shadow-lg bg-gradient-to-r from-pink-50 to-rose-50">
                                <CardContent className="p-6">
                                    <div className="grid md:grid-cols-3 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-700 mb-2 block">Sort By</label>
                                            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                                                <SelectTrigger className="h-10">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="popularity">Popularity Rank</SelectItem>
                                                    <SelectItem value="alphabetical">Alphabetical</SelectItem>
                                                    <SelectItem value="favorites">Favorites First</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-700 mb-2 block">Filter By</label>
                                            <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
                                                <SelectTrigger className="h-10">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Names</SelectItem>
                                                    <SelectItem value="favorites">My Favorites</SelectItem>
                                                    <SelectItem value="trending">Top 100 Trending</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div className="flex items-end">
                                            <Button
                                                variant="outline"
                                                onClick={() => {
                                                    setSortBy('popularity');
                                                    setFilterBy('all');
                                                    setSearchQuery('');
                                                }}
                                                className="h-10 w-full"
                                            >
                                                Reset Filters
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Enhanced Stats */}
                    <div className="grid md:grid-cols-4 gap-4 mb-8">
                        <Card className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-orange-50">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-3 bg-yellow-100 rounded-full">
                                        <Trophy className="h-6 w-6 text-yellow-600" />
                                    </div>
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">#1</div>
                                        <div className="text-sm text-gray-600 font-medium">{names[0]?.name_en}</div>
                                        <div className="text-xs text-gray-500">Most Popular</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="border-0 shadow-lg bg-gradient-to-br from-pink-50 to-rose-50">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-3 bg-pink-100 rounded-full">
                                        <Star className="h-6 w-6 text-pink-600" />
                                    </div>
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">{filteredNames.length.toLocaleString()}</div>
                                        <div className="text-sm text-gray-600 font-medium">Names Shown</div>
                                        <div className="text-xs text-gray-500">Filtered Results</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-3 bg-green-100 rounded-full">
                                        <TrendingUp className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">SSA</div>
                                        <div className="text-sm text-gray-600 font-medium">Official Data</div>
                                        <div className="text-xs text-gray-500">Government Source</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-pink-50">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-3 bg-purple-100 rounded-full">
                                        <Heart className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">{favorites.length}</div>
                                        <div className="text-sm text-gray-600 font-medium">Favorites</div>
                                        <div className="text-xs text-gray-500">Your Collection</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Names Display */}
                    <div className="space-y-6">
                        {/* Results Header */}
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900">
                                    {searchQuery ? `Search Results for "${searchQuery}"` : 'All Names'}
                                </h2>
                                <p className="text-gray-600">
                                    Showing {filteredNames.length.toLocaleString()} of {names.length.toLocaleString()} names
                                </p>
                            </div>
                            <div className="flex gap-2">
                                <Button variant="outline" size="sm">
                                    <Download className="h-4 w-4 mr-2" />
                                    Export
                                </Button>
                                <Button variant="outline" size="sm">
                                    <Share2 className="h-4 w-4 mr-2" />
                                    Share
                                </Button>
                            </div>
                        </div>

                        {/* Names Grid/List */}
                        {viewMode === 'grid' ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                {filteredNames.map((name, index) => (
                                    <div key={name.name_en} className="relative group">
                                        {/* Enhanced Ranking Badge */}
                                        <Badge
                                            variant="secondary"
                                            className="absolute -top-3 -left-3 z-10 bg-gradient-to-r from-pink-600 to-rose-600 text-white shadow-lg group-hover:scale-110 transition-transform"
                                        >
                                            #{name.popularity_rank || index + 1}
                                        </Badge>
                                        <div className="transform transition-all duration-300 group-hover:scale-105 group-hover:shadow-xl">
                                            <NameCard
                                                name={name}
                                                isFavorite={favorites.includes(name.name_en)}
                                                onToggleFavorite={() => toggleFavorite(name.name_en)}
                                                onClick={() => handleNameClick(name)}
                                                colorTheme="pink"
                                                compact={false}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="space-y-3">
                                {filteredNames.map((name, index) => (
                                    <Card
                                        key={name.name_en}
                                        className="hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 border-l-pink-500"
                                        onClick={() => handleNameClick(name)}
                                    >
                                        <CardContent className="p-4">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-4">
                                                    <Badge
                                                        variant="secondary"
                                                        className="bg-pink-100 text-pink-800 font-bold"
                                                    >
                                                        #{name.popularity_rank || index + 1}
                                                    </Badge>
                                                    <div>
                                                        <h3 className="text-lg font-semibold text-gray-900">{name.name_en}</h3>
                                                        <p className="text-sm text-gray-600">{name.meaning_en}</p>
                                                        {name.origin && (
                                                            <p className="text-xs text-gray-500">Origin: {name.origin}</p>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            toggleFavorite(name.name_en);
                                                        }}
                                                        className={favorites.includes(name.name_en) ? "text-red-500" : "text-gray-400"}
                                                    >
                                                        <Heart className={`h-4 w-4 ${favorites.includes(name.name_en) ? 'fill-current' : ''}`} />
                                                    </Button>
                                                    {name.count && (
                                                        <div className="text-right">
                                                            <div className="text-sm font-medium text-pink-600">
                                                                {name.count.toLocaleString()}
                                                            </div>
                                                            <div className="text-xs text-gray-500">babies</div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        )}

                        {/* No Results */}
                        {filteredNames.length === 0 && searchQuery && (
                            <div className="text-center py-12">
                                <div className="text-gray-500 mb-4">
                                    No names found matching "{searchQuery}"
                                </div>
                                <button
                                    onClick={() => setSearchQuery("")}
                                    className="text-pink-600 hover:text-pink-800"
                                >
                                    Clear search
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}
