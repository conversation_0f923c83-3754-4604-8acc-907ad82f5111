import { generatePageMetadata } from "@/lib/metadata-utils";

export const metadata = generatePageMetadata({
    title: "Top 1000 USA Girl Names 2025 | Most Popular American Girl Names",
    description: "Browse the top 1000 most popular girl names in the USA for 2025. Official SSA data with rankings, meanings, and popularity trends.",
    keywords: [
        "top 1000 USA girl names",
        "most popular American girl names",
        "USA girl names 2025",
        "popular girl names America",
        "top girl names United States",
        "American girl names list",
        "USA baby girl names ranking",
        "most popular girl names 2025",
        "top 1000 girl names America",
        "USA girl names with meanings"
    ],
});

export default function Top1000GirlNamesLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return children;
}
