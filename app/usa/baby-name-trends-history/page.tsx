import { generatePageMetadata } from "@/lib/metadata"
import { USABoyNames, USAGirlNames } from "@/data/countries/usa"
import type { NameData } from "@/types/name-data"
import SEOStructuredData from "@/components/seo-structured-data"

export const metadata = generatePageMetadata({
    path: "/usa/baby-name-trends-history",
    title: "Baby Name Trends Over Decades: Complete USA History 1880-2025 | Historical Analysis",
    description: "Explore 145+ years of American baby name trends from 1880-2025. Discover how popular names changed through decades with official SSA data, historical charts, and trend analysis.",
    keywords: [
        "baby name trends over decades",
        "baby name history USA",
        "most popular baby names by decade",
        "American baby name trends",
        "baby name popularity history",
        "historical baby names America",
        "baby name trends 1880-2025",
        "decade baby name analysis",
        "USA baby name statistics history",
        "Social Security baby name trends",
        "historical name popularity",
        "baby name evolution America",
        "century of baby names",
        "American naming trends history",
        "baby name patterns over time",
        "vintage baby names comeback",
        "traditional vs modern baby names",
        "baby name cycles history",
        "generational baby name trends",
        "classic baby names revival"
    ],
    lastModified: new Date().toISOString(),
});

// Helper function to get decade data
function getDecadeData(names: NameData[], decade: string) {
    return names
        .filter(name => name.historical_data && name.historical_data[decade])
        .sort((a, b) => (b.historical_data?.[decade] || 0) - (a.historical_data?.[decade] || 0))
        .slice(0, 10)
}

// Get trending names for different decades
const decades = ['1920', '1940', '1960', '1980', '2000', '2020']

export default function BabyNameTrendsHistory() {
    // Get current top names
    const currentTopBoys = USABoyNames.slice(0, 5)
    const currentTopGirls = USAGirlNames.slice(0, 5)
    
    return (
        <>
            <SEOStructuredData
                names={[...currentTopBoys, ...currentTopGirls]}
                pageType="blog-post"
                title="Baby Name Trends Over Decades: Complete USA History 1880-2025"
                description="Comprehensive analysis of 145+ years of American baby name trends with historical data and decade-by-decade breakdowns."
                url="/usa/baby-name-trends-history"
                country="USA"
                language="English"
                datePublished={new Date().toISOString()}
                dateModified={new Date().toISOString()}
            />
            
            <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50">
                <div className="container mx-auto px-4 py-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <div className="inline-flex items-center gap-2 bg-amber-100 text-amber-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                            📊 Historical Analysis
                        </div>
                        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                            Baby Name Trends Over Decades
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
                            Explore <strong>145+ years</strong> of American baby name evolution from 1880 to 2025. 
                            Discover how cultural shifts, historical events, and generational changes shaped naming trends 
                            across more than a century of American history.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                                📅 <strong>1880-2025 Data</strong>
                            </span>
                            <span className="flex items-center gap-1">
                                📈 <strong>Decade Analysis</strong>
                            </span>
                            <span className="flex items-center gap-1">
                                🎯 <strong>Trend Predictions</strong>
                            </span>
                        </div>
                    </div>

                    {/* Key Insights */}
                    <div className="bg-white rounded-xl shadow-lg p-8 mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-6">
                            🔍 Key Historical Insights
                        </h2>
                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div className="bg-blue-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-blue-800 mb-3">Victorian Era (1880-1900)</h3>
                                <ul className="text-blue-700 space-y-2 text-sm">
                                    <li>• Biblical names dominated (Mary, John, William)</li>
                                    <li>• Formal, traditional naming patterns</li>
                                    <li>• Family honor names were common</li>
                                    <li>• Limited name variety compared to today</li>
                                </ul>
                            </div>
                            <div className="bg-green-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-green-800 mb-3">Mid-Century (1940-1960)</h3>
                                <ul className="text-green-700 space-y-2 text-sm">
                                    <li>• Post-war optimism influenced choices</li>
                                    <li>• Hollywood glamour names emerged</li>
                                    <li>• Suburban family ideals reflected</li>
                                    <li>• Gender-specific naming peaked</li>
                                </ul>
                            </div>
                            <div className="bg-purple-50 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-purple-800 mb-3">Modern Era (2000-2025)</h3>
                                <ul className="text-purple-700 space-y-2 text-sm">
                                    <li>• Unique spellings and creativity</li>
                                    <li>• Cultural diversity in naming</li>
                                    <li>• Celebrity and pop culture influence</li>
                                    <li>• Gender-neutral options growing</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Decade Breakdown */}
                    <div className="mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                            📅 Decade-by-Decade Analysis
                        </h2>
                        <div className="grid lg:grid-cols-2 gap-8">
                            {decades.map((decade, index) => {
                                const boyTrends = getDecadeData(USABoyNames, decade)
                                const girlTrends = getDecadeData(USAGirlNames, decade)
                                const decadeEnd = parseInt(decade) + 10
                                
                                return (
                                    <div key={decade} className="bg-white rounded-xl shadow-lg p-6">
                                        <h3 className="text-2xl font-bold text-gray-800 mb-4">
                                            {decade}s - {decadeEnd}s
                                        </h3>
                                        <div className="grid md:grid-cols-2 gap-6">
                                            <div>
                                                <h4 className="text-lg font-semibold text-blue-600 mb-3">Top Boy Names</h4>
                                                <div className="space-y-2">
                                                    {boyTrends.slice(0, 5).map((name, idx) => (
                                                        <div key={name.name_en} className="flex justify-between items-center">
                                                            <span className="font-medium">{idx + 1}. {name.name_en}</span>
                                                            <span className="text-sm text-gray-500">
                                                                {name.historical_data?.[decade]?.toLocaleString()}
                                                            </span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                            <div>
                                                <h4 className="text-lg font-semibold text-pink-600 mb-3">Top Girl Names</h4>
                                                <div className="space-y-2">
                                                    {girlTrends.slice(0, 5).map((name, idx) => (
                                                        <div key={name.name_en} className="flex justify-between items-center">
                                                            <span className="font-medium">{idx + 1}. {name.name_en}</span>
                                                            <span className="text-sm text-gray-500">
                                                                {name.historical_data?.[decade]?.toLocaleString()}
                                                            </span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </div>

                    {/* Current vs Historical */}
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-8 mb-12">
                        <h2 className="text-3xl font-bold mb-6 text-center">
                            🔄 Then vs Now: Name Evolution
                        </h2>
                        <div className="grid md:grid-cols-2 gap-8">
                            <div>
                                <h3 className="text-xl font-semibold mb-4">1920s Most Popular</h3>
                                <div className="bg-white/10 rounded-lg p-4">
                                    <p className="text-blue-100 mb-2"><strong>Boys:</strong> Robert, John, James, William, Charles</p>
                                    <p className="text-pink-100"><strong>Girls:</strong> Mary, Dorothy, Helen, Betty, Margaret</p>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold mb-4">2025 Most Popular</h3>
                                <div className="bg-white/10 rounded-lg p-4">
                                    <p className="text-blue-100 mb-2"><strong>Boys:</strong> Liam, Noah, Oliver, Elijah, James</p>
                                    <p className="text-pink-100"><strong>Girls:</strong> Olivia, Emma, Charlotte, Amelia, Sophia</p>
                                </div>
                            </div>
                        </div>
                        <div className="text-center mt-6">
                            <p className="text-blue-100">
                                Notice how <strong>James</strong> remains popular across a century, while names like 
                                <strong> Liam</strong> and <strong>Olivia</strong> represent modern preferences!
                            </p>
                        </div>
                    </div>

                    {/* FAQ Section */}
                    <div className="bg-gray-50 rounded-xl p-8">
                        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                            ❓ Historical Baby Name FAQs
                        </h2>
                        <div className="grid md:grid-cols-2 gap-6">
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        What was the most popular baby name in the 1950s?
                                    </h3>
                                    <p className="text-gray-700">
                                        <strong>James</strong> was the top boy name and <strong>Mary</strong> was the top girl name 
                                        in the 1950s, reflecting the traditional naming patterns of the post-war era.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        How have baby name trends changed over 100 years?
                                    </h3>
                                    <p className="text-gray-700">
                                        Names have evolved from formal biblical choices to diverse, creative options. 
                                        Modern parents value uniqueness and cultural representation more than previous generations.
                                    </p>
                                </div>
                            </div>
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        Which names have stayed popular across decades?
                                    </h3>
                                    <p className="text-gray-700">
                                        Classic names like <strong>James, William, Elizabeth,</strong> and <strong>Mary</strong> 
                                        have maintained popularity across multiple decades, showing their timeless appeal.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        What influences baby name trends?
                                    </h3>
                                    <p className="text-gray-700">
                                        Historical events, popular culture, celebrity choices, immigration patterns, 
                                        and generational values all significantly influence naming trends over time.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
