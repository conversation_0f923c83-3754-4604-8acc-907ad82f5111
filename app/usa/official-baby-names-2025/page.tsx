import SEOStructuredData from "@/components/seo-structured-data";
import { USABoyNames, USAGirlNames } from "@/data/countries/usa";
import { generatePageMetadata } from "@/lib/metadata-utils";
export const metadata = generatePageMetadata({
    path: "/usa/official-baby-names-2025",
    title: "Official USA Baby Names 2025 | Social Security Administration Data - Most Popular American Names",
    description: "Official 2025 USA baby names from Social Security Administration data. Discover the most popular American boy and girl names with historical trends, meanings, and rankings. Updated with latest SSA statistics.",
    keywords: [
        "official USA baby names 2025",
        "Social Security Administration baby names",
        "SSA baby names 2025",
        "most popular American baby names",
        "official American baby names",
        "USA government baby names data",
        "Social Security baby names statistics",
        "official US baby names ranking",
        "American baby names 2025 list",
        "SSA most popular names",
        "official baby names America",
        "government baby names data",
        "Social Security name statistics",
        "USA baby names official list",
        "American names SSA data",
        "official US baby names 2025",
        "Social Security Administration names",
        "USA baby names government data",
        "official American names list",
        "SSA baby names ranking 2025"
    ],
    lastModified: new Date().toISOString(),
});

export default function OfficialUSABabyNames2025() {
    // Get top 20 names for display
    const topBoyNames = USABoyNames.slice(0, 20)
    const topGirlNames = USAGirlNames.slice(0, 20)

    return (
        <>
            <SEOStructuredData
                names={[...topBoyNames, ...topGirlNames]}
                pageType="name-list"
                title="Official USA Baby Names 2025 | SSA Data"
                description="Official 2025 USA baby names from Social Security Administration data with historical trends and popularity rankings."
                url="/usa/official-baby-names-2025"
                country="USA"
                language="English"
            />

            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-red-50">
                <div className="container mx-auto px-4 py-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                            🇺🇸 Official SSA Data
                        </div>
                        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                            Official USA Baby Names 2025
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
                            The most comprehensive collection of official American baby names from the
                            <strong> Social Security Administration</strong>. Discover trending names,
                            historical data, and popularity rankings for 2025.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                                📊 <strong>Official SSA Data</strong>
                            </span>
                            <span className="flex items-center gap-1">
                                📈 <strong>Historical Trends Since 1880</strong>
                            </span>
                            <span className="flex items-center gap-1">
                                🎯 <strong>Real-time Rankings</strong>
                            </span>
                        </div>
                    </div>

                    {/* Statistics Section */}
                    <div className="grid md:grid-cols-3 gap-6 mb-12">
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100">
                            <div className="text-3xl font-bold text-blue-600 mb-2">
                                {USABoyNames.length.toLocaleString()}+
                            </div>
                            <div className="text-gray-600">Official Boy Names</div>
                            <div className="text-sm text-gray-500 mt-1">From SSA Database</div>
                        </div>
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-pink-100">
                            <div className="text-3xl font-bold text-pink-600 mb-2">
                                {USAGirlNames.length.toLocaleString()}+
                            </div>
                            <div className="text-gray-600">Official Girl Names</div>
                            <div className="text-sm text-gray-500 mt-1">From SSA Database</div>
                        </div>
                        <div className="bg-white rounded-xl p-6 shadow-lg border border-green-100">
                            <div className="text-3xl font-bold text-green-600 mb-2">145+</div>
                            <div className="text-gray-600">Years of Data</div>
                            <div className="text-sm text-gray-500 mt-1">Since 1880</div>
                        </div>
                    </div>

                    {/* Top Names Grid */}
                    <div className="grid lg:grid-cols-2 gap-8 mb-12">
                        {/* Top Boy Names */}
                        <div className="bg-white rounded-xl shadow-lg p-6">
                            <h2 className="text-2xl font-bold text-blue-600 mb-6 flex items-center gap-2">
                                👦 Top USA Boy Names 2025
                            </h2>
                            <div className="space-y-3">
                                {topBoyNames.map((name, index) => (
                                    <div key={name.name_en} className="flex items-center justify-between p-3 rounded-lg hover:bg-blue-50 transition-colors">
                                        <div className="flex items-center gap-3">
                                            <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold text-sm">
                                                {index + 1}
                                            </div>
                                            <div>
                                                <div className="font-semibold text-gray-900">{name.name_en}</div>
                                                <div className="text-sm text-gray-500">{name.meaning_en}</div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-sm font-medium text-blue-600">
                                                {name.count?.toLocaleString()} babies
                                            </div>
                                            <div className="text-xs text-gray-500">2024 births</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Top Girl Names */}
                        <div className="bg-white rounded-xl shadow-lg p-6">
                            <h2 className="text-2xl font-bold text-pink-600 mb-6 flex items-center gap-2">
                                👧 Top USA Girl Names 2025
                            </h2>
                            <div className="space-y-3">
                                {topGirlNames.map((name, index) => (
                                    <div key={name.name_en} className="flex items-center justify-between p-3 rounded-lg hover:bg-pink-50 transition-colors">
                                        <div className="flex items-center gap-3">
                                            <div className="w-8 h-8 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center font-bold text-sm">
                                                {index + 1}
                                            </div>
                                            <div>
                                                <div className="font-semibold text-gray-900">{name.name_en}</div>
                                                <div className="text-sm text-gray-500">{name.meaning_en}</div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-sm font-medium text-pink-600">
                                                {name.count?.toLocaleString()} babies
                                            </div>
                                            <div className="text-xs text-gray-500">2024 births</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Key Insights Section */}
                    <div className="bg-white rounded-xl shadow-lg p-8 mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-6">
                            📊 Key Insights from SSA Data 2025
                        </h2>
                        <div className="grid md:grid-cols-2 gap-8">
                            <div>
                                <h3 className="text-xl font-semibold text-blue-600 mb-4">Trending Patterns</h3>
                                <ul className="space-y-3 text-gray-700">
                                    <li className="flex items-start gap-2">
                                        <span className="text-green-500 mt-1">↗️</span>
                                        <span><strong>Liam</strong> maintains #1 position for boys (6th consecutive year)</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-green-500 mt-1">↗️</span>
                                        <span><strong>Olivia</strong> continues dominance for girls (6th consecutive year)</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-blue-500 mt-1">📈</span>
                                        <span>Short, strong names gaining popularity across both genders</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-purple-500 mt-1">🌟</span>
                                        <span>Traditional names making a comeback in 2025</span>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold text-pink-600 mb-4">Historical Context</h3>
                                <ul className="space-y-3 text-gray-700">
                                    <li className="flex items-start gap-2">
                                        <span className="text-orange-500 mt-1">📅</span>
                                        <span>Data spans <strong>145+ years</strong> of American naming trends</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-red-500 mt-1">🎯</span>
                                        <span>Over <strong>4 million births</strong> recorded annually</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-indigo-500 mt-1">📊</span>
                                        <span>Names must appear <strong>5+ times</strong> to be included in SSA data</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-teal-500 mt-1">🔄</span>
                                        <span>Rankings updated annually every May by SSA</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* FAQ Section for Voice Search */}
                    <div className="bg-gray-50 rounded-xl p-8 mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                            ❓ Frequently Asked Questions
                        </h2>
                        <div className="grid md:grid-cols-2 gap-6">
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        What are the most popular baby names in America 2025?
                                    </h3>
                                    <p className="text-gray-700">
                                        According to official SSA data, <strong>Liam</strong> is the #1 boy name and
                                        <strong> Olivia</strong> is the #1 girl name in America for 2025, maintaining
                                        their positions for the 6th consecutive year.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        How does the Social Security Administration collect baby name data?
                                    </h3>
                                    <p className="text-gray-700">
                                        The SSA compiles data from Social Security card applications. Names must appear
                                        at least 5 times in a given year to be included in the official statistics.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        When are new baby name rankings released?
                                    </h3>
                                    <p className="text-gray-700">
                                        The SSA typically releases the previous year's baby name data every May,
                                        providing the most current and official statistics available.
                                    </p>
                                </div>
                            </div>
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        How far back does SSA baby name data go?
                                    </h3>
                                    <p className="text-gray-700">
                                        SSA baby name data goes back to <strong>1880</strong>, providing over 145 years
                                        of historical naming trends and patterns in the United States.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        Are these names ranked by state or nationally?
                                    </h3>
                                    <p className="text-gray-700">
                                        The main rankings are national, but the SSA also provides state-by-state
                                        breakdowns showing regional preferences and variations across America.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        What makes a name "officially" popular?
                                    </h3>
                                    <p className="text-gray-700">
                                        Official popularity is determined by the actual number of Social Security
                                        cards issued, making this the most accurate measure of real naming trends.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* CTA Section */}
                    <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-8">
                        <h2 className="text-2xl font-bold mb-4">
                            Explore More Official USA Baby Names
                        </h2>
                        <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                            Discover thousands more official American baby names with detailed meanings,
                            pronunciations, and historical trends from our comprehensive SSA database.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4">
                            <a
                                href="/usa/english-boy-names"
                                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
                            >
                                Browse All Boy Names
                            </a>
                            <a
                                href="/usa/english-girl-names"
                                className="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors"
                            >
                                Browse All Girl Names
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
