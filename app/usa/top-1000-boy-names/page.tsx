
import NameCard from "@/components/name-card";
import SEOStructuredData from "@/components/seo-structured-data";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { USABoyNames } from "@/data/countries/usa";
import { generatePageMetadata } from "@/lib/metadata-utils";
import type { NameData } from "@/types/name-data";
import { Search, Trophy } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";

export const metadata = generatePageMetadata({
    title: "Top 1000 USA Boy Names 2025 | Most Popular American Boy Names",
    description: "Browse the top 1000 most popular boy names in the USA for 2025. Official SSA data with rankings, meanings, and popularity trends.",
    keywords: [
        "top 1000 USA boy names",
        "most popular American boy names",
        "USA boy names 2025",
        "popular boy names America",
        "top boy names United States",
        "American boy names list",
        "USA baby boy names ranking",
        "most popular boy names 2025",
        "top 1000 boy names America",
        "USA boy names with meanings"
    ],
});

export default function Top1000UsaBoyNamesPage() {
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState("");
    const [favorites, setFavorites] = useState<string[]>([]);

    // Get top 1000 names - data is already sorted by popularity_rank
    const names = USABoyNames.slice(0, 1000);

    // Filter names based on search query
    const filteredNames = useMemo(() => {
        if (!searchQuery.trim()) return names;

        const query = searchQuery.toLowerCase();
        return names.filter(name =>
            name.name_en.toLowerCase().includes(query) ||
            name.meaning_en.toLowerCase().includes(query) ||
            name.origin?.toLowerCase().includes(query)
        );
    }, [names, searchQuery]);

    const toggleFavorite = (nameEn: string) => {
        setFavorites(prev =>
            prev.includes(nameEn)
                ? prev.filter(n => n !== nameEn)
                : [...prev, nameEn]
        );
    };

    const handleNameClick = (name: NameData) => {
        router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`);
    };

    return (
        <>
            <SEOStructuredData
                names={names.slice(0, 20)}
                pageType="name-list"
                title="Top 1000 USA Boy Names 2025"
                description="Complete list of the top 1000 most popular boy names in America for 2025, ranked by official Social Security Administration data."
                url="/usa/top-1000-boy-names"
                country="USA"
                language="English"
                gender="boy"
            />

            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                            🇺🇸 Official USA Rankings
                        </div>
                        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                            Top 1000 USA Boy Names 2025
                        </h1>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
                            The complete ranking of the most popular boy names in America for 2025,
                            based on official <strong>Social Security Administration</strong> data.
                            Each name includes popularity rank, meaning, and origin.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                                📊 <strong>Official SSA Rankings</strong>
                            </span>
                            <span className="flex items-center gap-1">
                                🎯 <strong>1000 Most Popular Names</strong>
                            </span>
                            <span className="flex items-center gap-1">
                                📈 <strong>2025 Data</strong>
                            </span>
                        </div>
                    </div>

                    {/* Search */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Search Names
                            </CardTitle>
                            <CardDescription>
                                Search by name, meaning, or origin
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Input
                                placeholder="Search for a name..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="max-w-md"
                            />
                        </CardContent>
                    </Card>

                    {/* Stats */}
                    <div className="grid md:grid-cols-3 gap-6 mb-8">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <Trophy className="h-8 w-8 text-yellow-500" />
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">#1</div>
                                        <div className="text-sm text-gray-600">Most Popular: {names[0]?.name_en}</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span className="text-blue-600 font-bold">1K</span>
                                    </div>
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">{filteredNames.length}</div>
                                        <div className="text-sm text-gray-600">Names Shown</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <span className="text-green-600 font-bold">📊</span>
                                    </div>
                                    <div>
                                        <div className="text-2xl font-bold text-gray-900">SSA</div>
                                        <div className="text-sm text-gray-600">Official Data</div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Names Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        {filteredNames.map((name, index) => (
                            <div key={name.name_en} className="relative">
                                {/* Ranking Badge */}
                                <Badge
                                    variant="secondary"
                                    className="absolute -top-2 -left-2 z-10 bg-blue-600 text-white"
                                >
                                    #{name.popularity_rank || index + 1}
                                </Badge>
                                <NameCard
                                    name={name}
                                    isFavorite={favorites.includes(name.name_en)}
                                    onToggleFavorite={() => toggleFavorite(name.name_en)}
                                    onClick={() => handleNameClick(name)}
                                    colorTheme="blue"
                                    compact={true}
                                />
                            </div>
                        ))}
                    </div>

                    {/* No Results */}
                    {filteredNames.length === 0 && searchQuery && (
                        <div className="text-center py-12">
                            <div className="text-gray-500 mb-4">
                                No names found matching "{searchQuery}"
                            </div>
                            <button
                                onClick={() => setSearchQuery("")}
                                className="text-blue-600 hover:text-blue-800"
                            >
                                Clear search
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}