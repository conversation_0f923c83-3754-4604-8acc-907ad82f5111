
import LanguageNamesPage from "@/components/language-names-page";
import { fetchNames } from "@/lib/data-fetching";
import { generatePageMetadata } from "@/lib/metadata-utils";
import { notFound } from "next/navigation";

export async function generateMetadata() {
    return generatePageMetadata({
        title: "Top 1000 USA Boy Names",
        description: "Browse the top 1000 most popular boy names in the USA.",
    });
}

export default async function Top1000UsaBoyNamesPage() {
    const names = await fetchNames({
        country: "usa",
        gender: "boy",
        limit: 1000,
    });

    if (!names) {
        notFound();
    }

    return (
        <LanguageNamesPage
            title="Top 1000 USA Boy Names"
            names={names}
        />
    );
} 