import { generatePageMetadata } from "@/lib/metadata-utils";

export const metadata = generatePageMetadata({
    title: "Top 1000 USA Boy Names 2025 | Most Popular American Boy Names",
    description: "Browse the top 1000 most popular boy names in the USA for 2025. Official SSA data with rankings, meanings, and popularity trends.",
    keywords: [
        "top 1000 USA boy names",
        "most popular American boy names",
        "USA boy names 2025",
        "popular boy names America",
        "top boy names United States",
        "American boy names list",
        "USA baby boy names ranking",
        "most popular boy names 2025",
        "top 1000 boy names America",
        "USA boy names with meanings"
    ],
});

export default function Top1000BoyNamesLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return children;
}
