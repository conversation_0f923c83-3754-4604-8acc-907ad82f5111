import { FAQSection } from "@/components/faq-section"
import TopNamesDisplay from "@/components/top-names-display"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
    path: "/usa/top-100-girl-names",
    title: "Top 100 American Girl Names 2025 – Popular USA Baby Names",
    description: "Discover the Top-100 list of American baby girl names for 2025 with meanings and popularity trends, updated monthly.",
    keywords: [
        "top 100 american girl names",
        "popular usa girl names 2025",
        "us baby girl names list",
        "american girl names with meanings"
    ]
})

export default function Top100USAGirlNamesPage() {
    const faqs = [
        { question: "What is the #1 American girl name in 2025?", answer: "<PERSON> remains the most popular girl name in the United States for 2025." },
        { question: "Where does this ranking data come from?", answer: "We combine U.S. Social Security Administration birth data with BabyNameDiaries search statistics." },
        { question: "Will the rankings change during the year?", answer: "Yes—our list updates every month to reflect new data and trends." },
    ]

    return (
        <main className="container mx-auto px-4 py-8 max-w-5xl">
            <h1 className="text-3xl md:text-4xl font-bold text-center mb-6">Top 100 American Girl Names 2025</h1>
            <p className="text-center text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
                Browse the definitive Top-100 ranking of popular U.S. baby girl names with origins, meanings, and trend data.
            </p>

            <TopNamesDisplay country="usa" limit={100} showTrending={false} showBoys={false} className="mb-12" />

            <FAQSection faqs={faqs} />
        </main>
    )
} 