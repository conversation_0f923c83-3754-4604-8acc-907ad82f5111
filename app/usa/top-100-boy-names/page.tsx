import { FAQSection } from "@/components/faq-section"
import TopNamesDisplay from "@/components/top-names-display"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
    path: "/usa/top-100-boy-names",
    title: "Top 100 American Boy Names 2025 – Popular USA Baby Names",
    description: "See the definitive Top-100 list of American baby boy names for 2025. Meanings, origins, popularity trends and more – updated monthly from official SSA data.",
    keywords: [
        "top 100 american boy names",
        "popular usa boy names 2025",
        "us baby boy names list",
        "american boy names with meanings"
    ]
})

export default function Top100USABoyNamesPage() {
    const faqs = [
        { question: "What is the #1 American boy name in 2025?", answer: "According to SSA data, <PERSON> remains the most popular boy name in the United States for 2025." },
        { question: "How is this Top-100 list compiled?", answer: "We combine official U.S. Social Security Administration birth records with BabyNameDiaries search data to rank names by actual usage and interest." },
        { question: "How often is the list updated?", answer: "Monthly – we refresh rankings as soon as new SSA or search-volume data becomes available." },
    ]

    return (
        <main className="container mx-auto px-4 py-8 max-w-5xl">
            <h1 className="text-3xl md:text-4xl font-bold text-center mb-6">Top 100 American Boy Names 2025</h1>
            <p className="text-center text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
                Explore the most popular baby boy names in the United States. Our list combines official birth statistics with real-time search trends to give you the definitive Top-100 ranking.
            </p>

            <TopNamesDisplay country="usa" limit={100} showTrending={false} showGirls={false} className="mb-12" />

            <FAQSection faqs={faqs} />
        </main>
    )
} 