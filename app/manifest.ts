import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'BabyNamesDiary - 25,000+ Beautiful Baby Names 2025',
    short_name: 'BabyNamesDiary',
    description: 'Discover 25,000+ stunning baby names for 2025! Find popular American, British, Indian & global names with beautiful meanings, cultural significance & pronunciation guides.',
    start_url: '/',
    scope: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    orientation: 'portrait',
    lang: 'en',
    categories: ['parenting', 'baby-names', 'family', 'education'],
    icons: [
      {
        src: '/favicon-16x16.png',
        sizes: '16x16',
        type: 'image/png',
      },
      {
        src: '/favicon-32x32.png',
        sizes: '32x32',
        type: 'image/png',
      },
      {
        src: '/favicon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/favicon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
    // Uncomment and add screenshots if available for richer search/PWA appearance
    // screenshots: [
    //   {
    //     src: '/icons/screenshot1.png',
    //     sizes: '540x720',
    //     type: 'image/png',
    //     label: 'Homepage',
    //   },
    // ],
  }
}
