import SEOStructuredData from "@/components/seo-structured-data";
import { generatePageMetadata } from "@/lib/metadata-utils";
import type { Metadata } from "next";
import HomePageClient from "./home-page-client";

// SEO-optimized metadata following keyword best practices
export const metadata: Metadata = generatePageMetadata({
  path: "/",
  title: "Baby Names 2025 - 25,000+ Beautiful Names with Meanings & Origins",
  description: "Discover 25,000+ beautiful baby names for 2025 with meanings, origins, and cultural significance. Start your baby naming journey now—explore, search, and find the perfect name today!",
  keywords: [
    "baby names 2025",
    "baby names with meanings",
    "popular baby names",
    "beautiful baby names",
    "American baby names",
    "British baby names",
    "Indian baby names",
    "unique baby names",
    "boy names",
    "girl names",
    "baby name meanings",
    "international baby names",
    "trending baby names",
    "baby name finder",
    "baby name generator",
    "AI baby name generator",
    "personalized baby names",
    "cultural baby names",
    "religious baby names",
    "baby name search",
    "name pronunciations",
    "baby naming guide",
    "global baby names",
    "traditional baby names",
    "modern baby names",
    "European baby names",
    "Asian baby names",
    "baby name origins",
    "multilingual baby names",
    "baby name trends 2025",
    "popular names worldwide",
    "cross-cultural names",
    "baby name inspiration"
  ]
})

export const dynamic = "force-static";

export default function HomePage() {
  return (
    <>
      <SEOStructuredData
        names={[]}
        pageType="name-list"
        title="Baby Names 2025 - 25,000+ Beautiful Names with Meanings & Origins"
        description="Discover 25,000+ beautiful baby names for 2025 with meanings, origins, and cultural significance from 12 countries worldwide."
        url="/"
      />
      <HomePageClient />
    </>
  )
}