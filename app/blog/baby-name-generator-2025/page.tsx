import { FAQSection } from "@/components/faq-section"
import SEOStructuredData from "@/components/seo-structured-data"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { BookOpen, Globe, Heart, Sparkles, Star, Users, Zap } from "lucide-react"
import Link from "next/link"

export const metadata = generatePageMetadata({
    path: "/blog/baby-name-generator-2025",
    title: "Baby Name Generator 2025 - AI-Powered Name Suggestions",
    description: "Discover the perfect baby name with our AI-powered baby name generator. Get personalized name suggestions based on meaning, origin, style, and cultural preferences.",
    keywords: [
        "baby name generator",
        "name generator",
        "baby name finder",
        "name suggestions",
        "personalized baby names",
        "AI baby names",
        "name matching tool",
        "baby name quiz",
        "name compatibility",
        "unique name generator"
    ]
})

export default function BabyNameGeneratorPage() {
    const faqs = [
        {
            question: "How does the baby name generator work?",
            answer: "Our AI-powered baby name generator analyzes your preferences including style, meaning, origin, and cultural background to suggest personalized names. It considers factors like pronunciation, popularity trends, and compatibility with your surname."
        },
        {
            question: "Can I get names from specific cultures or religions?",
            answer: "Yes! Our generator can filter names by cultural background, religious tradition, and geographic origin. Whether you want Hindu names, Christian names, Muslim names, or names from specific countries, we can customize suggestions."
        },
        {
            question: "Are the generated names unique or popular?",
            answer: "You can choose! Our generator offers options ranging from trending popular names to unique, rare names. You can specify your preference for common names, unique names, or a mix of both."
        },
        {
            question: "Can I save my favorite generated names?",
            answer: "Absolutely! Create a free account to save your favorite generated names, create lists, and compare options. You can also share your lists with family members for collaborative decision-making."
        },
        {
            question: "How accurate are the name meanings and origins?",
            answer: "All name meanings and origins are verified by linguistic experts and cultural consultants. We source information from authoritative dictionaries, historical texts, and native language experts."
        },
        {
            question: "Can the generator suggest names based on my last name?",
            answer: "Yes! Our advanced algorithm considers how names sound with your surname, avoiding awkward combinations and ensuring the full name flows well together."
        }
    ]

    return (
        <>
            <SEOStructuredData
                names={[]}
                pageType="blog-post"
                title="Baby Name Generator 2025 - AI-Powered Name Suggestions"
                description="Discover the perfect baby name with our AI-powered baby name generator. Get personalized name suggestions based on meaning, origin, style, and cultural preferences."
                url="/blog/baby-name-generator-2025"
            />

            <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
                {/* Hero Section */}
                <section className="bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 text-white py-16 md:py-20">
                    <div className="container mx-auto px-4 text-center">
                        <div className="flex items-center justify-center mb-4">
                            <Sparkles className="h-8 w-8 mr-3" />
                            <h1 className="text-4xl md:text-6xl font-bold">
                                AI Baby Name Generator 2025
                            </h1>
                        </div>
                        <p className="text-xl md:text-2xl mb-8 text-purple-100 max-w-3xl mx-auto">
                            Discover the perfect name for your baby with our intelligent name generator.
                            Get personalized suggestions based on meaning, style, and cultural preferences.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4 mb-8">
                            <Badge variant="secondary" className="text-purple-600 bg-white">
                                AI-Powered
                            </Badge>
                            <Badge variant="secondary" className="text-purple-600 bg-white">
                                25,000+ Names
                            </Badge>
                            <Badge variant="secondary" className="text-purple-600 bg-white">
                                Personalized
                            </Badge>
                            <Badge variant="secondary" className="text-purple-600 bg-white">
                                Free to Use
                            </Badge>
                        </div>
                        <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 text-lg px-8 py-4">
                            <Zap className="mr-2 h-5 w-5" />
                            Start Generating Names
                        </Button>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-16 bg-white">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                            Why Choose Our Baby Name Generator?
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center mb-4">
                                        <Sparkles className="h-8 w-8 text-purple-600 mr-3" />
                                        <CardTitle className="text-xl">AI-Powered Intelligence</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Our advanced algorithm learns your preferences and suggests names that match your style,
                                        cultural background, and personal taste.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center mb-4">
                                        <Globe className="h-8 w-8 text-blue-600 mr-3" />
                                        <CardTitle className="text-xl">Global Name Database</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Access 25,000+ names from 12 countries with verified meanings, origins, and cultural significance.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center mb-4">
                                        <Heart className="h-8 w-8 text-pink-600 mr-3" />
                                        <CardTitle className="text-xl">Personalized Results</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Get suggestions tailored to your preferences including style, meaning, popularity, and cultural background.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center mb-4">
                                        <BookOpen className="h-8 w-8 text-green-600 mr-3" />
                                        <CardTitle className="text-xl">Detailed Information</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Each suggestion includes meaning, origin, pronunciation, popularity trends, and cultural context.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center mb-4">
                                        <Users className="h-8 w-8 text-orange-600 mr-3" />
                                        <CardTitle className="text-xl">Family Collaboration</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Share your favorite names with family members and get their input on your selections.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center mb-4">
                                        <Star className="h-8 w-8 text-yellow-600 mr-3" />
                                        <CardTitle className="text-xl">Save & Compare</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Save your favorite names, create lists, and compare options side-by-side to make the best decision.
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* How It Works Section */}
                <section className="py-16 bg-gradient-to-r from-purple-50 to-pink-50">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                            How Our Baby Name Generator Works
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="text-center">
                                <div className="bg-purple-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                                    1
                                </div>
                                <h3 className="text-xl font-semibold mb-3 text-gray-900">Tell Us Your Preferences</h3>
                                <p className="text-gray-600">
                                    Share your style preferences, cultural background, desired meaning, and any specific requirements.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="bg-pink-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                                    2
                                </div>
                                <h3 className="text-xl font-semibold mb-3 text-gray-900">AI Analyzes & Suggests</h3>
                                <p className="text-gray-600">
                                    Our AI algorithm processes your preferences and suggests names that match your criteria.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="bg-orange-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                                    3
                                </div>
                                <h3 className="text-xl font-semibold mb-3 text-gray-900">Explore & Choose</h3>
                                <p className="text-gray-600">
                                    Browse suggestions, save favorites, and get detailed information to make your final choice.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6">
                            Ready to Find Your Perfect Baby Name?
                        </h2>
                        <p className="text-xl mb-8 text-purple-100 max-w-2xl mx-auto">
                            Join thousands of parents who have found their ideal baby name using our AI-powered generator.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 text-lg px-8 py-4">
                                <Zap className="mr-2 h-5 w-5" />
                                Start Generating Now
                            </Button>
                            <Link href="/trending-names">
                                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                                    <Star className="mr-2 h-5 w-5" />
                                    View Trending Names
                                </Button>
                            </Link>
                        </div>
                    </div>
                </section>

                {/* FAQ Section */}
                <section className="py-16 bg-white">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                            Frequently Asked Questions
                        </h2>
                        <FAQSection faqs={faqs} />
                    </div>
                </section>
            </div>
        </>
    )
} 