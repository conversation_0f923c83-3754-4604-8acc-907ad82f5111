import { <PERSON><PERSON> } from "@/components/ui/button"
import { getBlogPost } from "@/lib/blog-content"
import { ArrowLeft, Share2 } from "lucide-react"
import { Metada<PERSON> } from "next"
import Link from "next/link"

type Props = {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params
  const post = getBlogPost(slug)
  if (!post) {
    return {
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
    }
  }
  return {
    title: `${post.title} | Baby Names Blog`,
    description: post.seoDescription || post.excerpt,
    keywords: post.tags?.join(", ") || "baby names, blog, parenting",
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.date,
      authors: [post.author || "Baby Names Expert"],
      tags: post.tags,
      images: [
        {
          url: "/icons/logo_full_hd.png",
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: ["/icons/logo_full_hd.png"],
    },
    alternates: {
      canonical: `https://www.babynamediaries.com/blog/${post.slug}/`,
    },
  }
}

export default async function BlogPostPage({ params }: Props) {
  const { slug } = await params
  const post = getBlogPost(slug)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date)
  }

  if (!post) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="text-center py-10">
          <h2 className="text-2xl font-bold mb-4">Post not found</h2>
          <Link href="/blog">
            <Button>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  // Add structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    headline: post.title,
    description: post.excerpt,
    datePublished: post.date,
    articleSection: post.category,
    author: {
      "@type": "Person",
      name: post.author || "Baby Names Expert"
    },
    wordCount: post.content.replace(/<[^>]*>/g, '').split(' ').length,
    timeRequired: post.readTime || "5 min read"
  }

  return (
    <main className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Add structured data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData).replace(/</g, "\\u003c"),
        }}
      />

      <div className="flex justify-between items-center mb-6">
        <Link href="/blog">
          <Button variant="ghost">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Blog
          </Button>
        </Link>

        <Button variant="outline" size="icon" aria-label="Share post">
          <Share2 className="h-5 w-5" />
        </Button>
      </div>

      <article className="prose dark:prose-invert max-w-none">
        <div className="mb-6">
          <h1 className="text-4xl font-bold mb-2">{post.title}</h1>
          <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
            <span>{formatDate(post.date)}</span>
            <span>•</span>
            <span>{post.category}</span>
            {post.readTime && (
              <>
                <span>•</span>
                <span>{post.readTime}</span>
              </>
            )}
            {post.author && (
              <>
                <span>•</span>
                <span>By {post.author}</span>
              </>
            )}
          </div>

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-6">
              {post.tags.map((tag) => (
                <span key={tag} className="px-2 py-1 bg-muted rounded-md text-xs">
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Blog content */}
        <div
          className="prose-lg max-w-none"
          dangerouslySetInnerHTML={{ __html: post.content || "" }}
        />

        {/* Related Articles Section */}
        <div className="mt-12 p-6 bg-muted/30 rounded-lg">
          <h3 className="text-xl font-bold mb-4">Related Articles</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/blog/most-popular-baby-names-2025">
              <Button variant="outline" className="h-auto p-4 text-left justify-start w-full">
                <div>
                  <div className="font-semibold">Most Popular Baby Names 2025</div>
                  <div className="text-sm text-muted-foreground">Top 100 trending names</div>
                </div>
              </Button>
            </Link>
            <Link href="/usa/english-boy-names">
              <Button variant="outline" className="h-auto p-4 text-left justify-start w-full">
                <div>
                  <div className="font-semibold">American Baby Boy Names</div>
                  <div className="text-sm text-muted-foreground">Browse our full collection</div>
                </div>
              </Button>
            </Link>
          </div>
        </div>
      </article>
    </main>
  )
}
