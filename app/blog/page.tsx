import { FAQSection } from "@/components/faq-section"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getAllBlogPosts } from "@/lib/blog-content"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { ArrowRight, Calendar, Clock, User } from "lucide-react"
import Link from "next/link"

// Categories for filtering
const categories = [
  "All",
  "Baby Naming Guide",
  "Trending Names",
  "Religious Names",
  "Unique Names",
  "Alphabetical Guide",
  "Celebrity Names"
]

export const metadata = generatePageMetadata({
  path: "/blog",
  title: "Baby Names Blog - Expert Tips, Trends & Naming Guides",
  description:
    "Discover expert baby naming advice, trending names, cultural insights, and comprehensive guides. Learn how to choose the perfect name for your baby with our expert tips and latest trends.",
  keywords: [
    "baby names blog",
    "baby naming tips",
    "baby name trends 2025",
    "how to choose baby name",
    "baby name meanings",
    "cultural baby names",
    "baby naming guide",
    "popular baby names",
    "unique baby names",
    "baby name inspiration",
  ],
})

export default function BlogPage() {
  // Get blog posts from the content library
  const blogPosts = getAllBlogPosts()

  // Mark first two posts as featured
  const featuredPosts = blogPosts.slice(0, 2)
  const allPosts = blogPosts

  const faqs = [
    {
      question: "How often do you publish new articles on the blog?",
      answer: "We publish new articles weekly, covering the latest baby name trends, cultural insights, and expert advice. Our team of naming experts and cultural consultants ensures all content is accurate, up-to-date, and helpful for parents."
    },
    {
      question: "What types of articles can I find on your blog?",
      answer: "Our blog features trending name analysis, cultural naming traditions, religious name guides, unique name spotlights, alphabetical guides, celebrity baby name trends, and expert tips for choosing the perfect baby name."
    },
    {
      question: "Are the baby naming tips and advice from experts?",
      answer: "Yes! All our articles are written by or reviewed by naming experts, linguists, and cultural consultants. We ensure accuracy and provide practical, research-based advice to help parents make informed decisions."
    },
    {
      question: "Can I request a specific topic for a blog article?",
      answer: "Absolutely! We welcome topic suggestions from our readers. If you'd like to see an article about a specific culture, naming trend, or baby naming challenge, please contact us through our contact page."
    },
    {
      question: "Do you cover names from all the countries in your database?",
      answer: "Yes! Our blog covers naming traditions and trends from all 12 countries in our database, including USA, UK, Canada, Australia, Germany, France, Netherlands, Sweden, Switzerland, Austria, Belgium, and India."
    },
    {
      question: "How can I stay updated with new blog posts?",
      answer: "You can bookmark our blog page and check back regularly, or follow us on social media for updates. We also feature our latest articles on our homepage and in our newsletter."
    },
    {
      question: "Are the trending name articles based on real data?",
      answer: "Yes! Our trending name articles are based on official birth records, search data, and social media analysis. We combine multiple data sources to provide accurate, up-to-date information about name popularity."
    },
    {
      question: "Can I share blog articles with friends and family?",
      answer: "Yes! All our blog articles are free to read and share. We encourage sharing our content to help other parents in their baby naming journey. Just make sure to credit BabyNamesDiary when sharing."
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white py-16 md:py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Baby Names Blog
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Expert tips, trending names, cultural insights, and comprehensive guides to help you choose the perfect name for your baby.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Badge variant="secondary" className="text-blue-600 bg-white">
              25,000+ Names
            </Badge>
            <Badge variant="secondary" className="text-blue-600 bg-white">
              12 Countries
            </Badge>
            <Badge variant="secondary" className="text-blue-600 bg-white">
              Expert Advice
            </Badge>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-12 md:py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
            Featured Articles
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {featuredPosts.map((post) => (
              <Card key={post.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="text-xs">
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  <CardTitle className="text-xl md:text-2xl group-hover:text-blue-600 transition-colors">
                    <Link href={`/blog/${post.slug}`} className="hover:underline">
                      {post.title}
                    </Link>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-1" />
                      {post.author}
                      <Calendar className="h-4 w-4 ml-3 mr-1" />
                      {new Date(post.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>
                    <Link
                      href={`/blog/${post.slug}`}
                      className="flex items-center text-blue-600 hover:text-blue-800 font-medium group"
                    >
                      Read More
                      <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* All Posts */}
      <section className="py-12 md:py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
            All Articles
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {allPosts.map((post) => (
              <Card key={post.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="text-xs">
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  <CardTitle className="text-lg md:text-xl group-hover:text-blue-600 transition-colors">
                    <Link href={`/blog/${post.slug}`} className="hover:underline">
                      {post.title}
                    </Link>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-1" />
                      {post.author}
                    </div>
                    <Link
                      href={`/blog/${post.slug}`}
                      className="flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm group"
                    >
                      Read More
                      <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
            Frequently Asked Questions
          </h2>
          <FAQSection faqs={faqs} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">
            Ready to Find Your Perfect Baby Name?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Explore our comprehensive database of 25,000+ baby names from 12 countries with meanings, origins, and cultural significance.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/usa/english-boy-names/">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                Explore Boy Names
              </button>
            </Link>
            <Link href="/usa/english-girl-names/">
              <button className="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors font-medium">
                Explore Girl Names
              </button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
