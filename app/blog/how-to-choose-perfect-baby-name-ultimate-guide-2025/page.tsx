import { FAQSection } from "@/components/faq-section"
import SEOStructuredData from "@/components/seo-structured-data"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { BookOpen, CheckCircle, Globe, Heart, Lightbulb, Star, Zap } from "lucide-react"
import Link from "next/link"

export const metadata = generatePageMetadata({
    path: "/blog/how-to-choose-perfect-baby-name-ultimate-guide-2025",
    title: "How to Choose the Perfect Baby Name: Ultimate Guide 2025 | Expert Tips & 25,000+ Names",
    description: "Master the art of choosing the perfect baby name with our comprehensive 10-step guide. Learn expert tips, avoid common mistakes, and discover 25,000+ names with meanings. Free baby naming advice for parents.",
    keywords: [
        "how to choose baby name",
        "baby naming tips",
        "choosing baby name",
        "baby name guide",
        "perfect baby name",
        "baby naming advice",
        "baby name selection",
        "baby name meaning",
        "baby name trends 2025",
        "baby name inspiration",
        "baby naming mistakes",
        "baby name checklist",
        "baby name considerations",
        "baby name psychology",
        "baby name popularity",
        "baby name generator",
        "baby name finder",
        "baby naming guide 2025",
        "how to pick baby name",
        "baby name decision",
        "baby name help",
        "baby naming process",
        "baby name research",
        "baby name meaning importance",
        "baby name cultural significance"
    ],
    type: "article",
    lastModified: "2025-01-20T00:00:00.000Z"
})

export default function HowToChoosePerfectBabyNamePage() {
    const faqs = [
        {
            question: "When should I start thinking about baby names?",
            answer: "It's never too early to start! Many parents begin brainstorming names as soon as they find out they're expecting. Starting early gives you time to research meanings, test pronunciations, and discuss options with your partner and family."
        },
        {
            question: "How important is the meaning of a baby name?",
            answer: "Name meanings can be very important as they often reflect your values and hopes for your child. However, the sound, flow, and personal connection to the name are equally important. Choose a name that feels right for your family."
        },
        {
            question: "Should I consider how the name will sound with my last name?",
            answer: "Absolutely! The full name should flow well together. Consider the rhythm, avoid awkward combinations, and think about how it will sound when called out loud. Test the full name by saying it repeatedly."
        },
        {
            question: "What are the most common baby naming mistakes to avoid?",
            answer: "Common mistakes include not considering initials, ignoring cultural significance, choosing names that are too trendy, not thinking about nicknames, and not considering how the name will age with your child."
        },
        {
            question: "How do I handle family pressure when choosing a baby name?",
            answer: "Be respectful but firm about your decision. You can explain your reasoning, but remember that this is ultimately your choice. Consider family suggestions, but don't feel obligated to use them if they don't feel right."
        },
        {
            question: "Should I tell people the name before the baby is born?",
            answer: "This is a personal choice. Some parents prefer to keep it private to avoid unwanted opinions, while others enjoy sharing and getting feedback. Consider your comfort level and how you might handle negative reactions."
        },
        {
            question: "What if my partner and I can't agree on a name?",
            answer: "Try creating separate lists of names you love, then look for common themes or styles. Use our baby name generator to discover new options, and be willing to compromise. Sometimes the perfect name emerges from combining elements you both like."
        },
        {
            question: "How do I know if a name is too popular or too unique?",
            answer: "Check current popularity rankings, but remember that what matters most is how you feel about the name. Popular names are popular for good reasons, while unique names can be special. Consider your child's future comfort level."
        }
    ]

    return (
        <>
            <SEOStructuredData
                names={[]}
                pageType="blog-post"
                title="How to Choose the Perfect Baby Name: Ultimate Guide 2025"
                description="Master the art of choosing the perfect baby name with our comprehensive guide. Learn expert tips, avoid common mistakes, and discover 25,000+ names with meanings."
                url="/blog/how-to-choose-perfect-baby-name-ultimate-guide-2025"
            />

            {/* Additional Structured Data for Blog Post */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Article",
                        "headline": "How to Choose the Perfect Baby Name: Ultimate Guide 2025",
                        "description": "Master the art of choosing the perfect baby name with our comprehensive 10-step guide. Learn expert tips, avoid common mistakes, and discover 25,000+ names with meanings.",
                        "image": "https://www.babynamediaries.com/icons/logo_full_hd.png",
                        "author": {
                            "@type": "Person",
                            "name": "Baby Names Expert",
                            "url": "https://www.babynamediaries.com"
                        },
                        "publisher": {
                            "@type": "Organization",
                            "name": "BabyNamesDiary",
                            "logo": {
                                "@type": "ImageObject",
                                "url": "https://www.babynamediaries.com/icons/logo_full_hd.png"
                            }
                        },
                        "datePublished": "2025-01-20T00:00:00.000Z",
                        "dateModified": "2025-01-20T00:00:00.000Z",
                        "mainEntityOfPage": {
                            "@type": "WebPage",
                            "@id": "https://www.babynamediaries.com/blog/how-to-choose-perfect-baby-name-ultimate-guide-2025"
                        },
                        "articleSection": "Baby Naming Guide",
                        "keywords": "how to choose baby name, baby naming tips, choosing baby name, baby name guide, perfect baby name, baby naming advice",
                        "wordCount": 3500,
                        "timeRequired": "PT15M",
                        "inLanguage": "en-US",
                        "isAccessibleForFree": true,
                        "mainEntity": {
                            "@type": "HowTo",
                            "name": "How to Choose the Perfect Baby Name",
                            "description": "A comprehensive 10-step guide to choosing the perfect baby name",
                            "step": [
                                {
                                    "@type": "HowToStep",
                                    "name": "Start Early and Research Thoroughly",
                                    "text": "Begin your baby naming journey early to give yourself time to explore, research, and reflect on your choices."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Consider the Meaning and Origin",
                                    "text": "Research the meaning and cultural significance of potential names to ensure they align with your values."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Think About Pronunciation and Spelling",
                                    "text": "Choose names that are easy to pronounce and spell to avoid constant corrections."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Test the Full Name",
                                    "text": "Say the complete name out loud and consider how it sounds in different contexts."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Consider Cultural and Family Significance",
                                    "text": "Honor your heritage and family traditions while creating your own path."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Think About Nicknames",
                                    "text": "Consider what shortened versions might emerge and whether you're comfortable with them."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Consider Popularity and Uniqueness",
                                    "text": "Decide whether you prefer classic, well-known names or something unique and distinctive."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Avoid Common Mistakes",
                                    "text": "Learn from others' experiences to avoid common pitfalls in the naming process."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Get Family Input (But Don't Be Pressured)",
                                    "text": "Be open to suggestions while staying true to your vision and values."
                                },
                                {
                                    "@type": "HowToStep",
                                    "name": "Trust Your Instincts",
                                    "text": "After thorough research and consideration, trust your gut feeling about the perfect name."
                                }
                            ]
                        }
                    })
                }}
            />

            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
                {/* Hero Section */}
                <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white py-8 sm:py-12 md:py-16 lg:py-20">
                    <div className="container mx-auto px-4 sm:px-6 text-center">
                        <div className="flex flex-col sm:flex-row items-center justify-center mb-4 sm:mb-6">
                            <Lightbulb className="h-6 w-6 sm:h-8 sm:w-8 mr-2 sm:mr-3 mb-2 sm:mb-0" />
                            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight">
                                How to Choose the Perfect Baby Name
                            </h1>
                        </div>
                        <p className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-blue-100 max-w-4xl mx-auto leading-relaxed px-2">
                            The ultimate guide to selecting the perfect name for your baby. Expert tips, common mistakes to avoid, and everything you need to know to make this important decision with confidence.
                        </p>
                        <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8 px-2">
                            <Badge variant="secondary" className="text-blue-600 bg-white text-xs sm:text-sm px-2 py-1 sm:px-3 sm:py-1">
                                Expert Guide
                            </Badge>
                            <Badge variant="secondary" className="text-blue-600 bg-white text-xs sm:text-sm px-2 py-1 sm:px-3 sm:py-1">
                                25,000+ Names
                            </Badge>
                            <Badge variant="secondary" className="text-blue-600 bg-white text-xs sm:text-sm px-2 py-1 sm:px-3 sm:py-1">
                                Step-by-Step
                            </Badge>
                            <Badge variant="secondary" className="text-blue-600 bg-white text-xs sm:text-sm px-2 py-1 sm:px-3 sm:py-1">
                                Free Resources
                            </Badge>
                        </div>
                        <Link href="/baby-name-generator">
                            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 w-full sm:w-auto">
                                <Zap className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                                Start Your Name Search
                            </Button>
                        </Link>
                    </div>
                </section>

                {/* Table of Contents */}
                <section className="py-6 sm:py-8 bg-white border-b">
                    <div className="container mx-auto px-4 sm:px-6">
                        <div className="bg-blue-50 rounded-lg p-4 sm:p-6">
                            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">Table of Contents</h2>
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <ul className="space-y-2 text-sm sm:text-base">
                                    <li><a href="#step-1" className="text-blue-600 hover:text-blue-800 transition-colors">1. Start Early and Research Thoroughly</a></li>
                                    <li><a href="#step-2" className="text-blue-600 hover:text-blue-800 transition-colors">2. Consider the Meaning and Origin</a></li>
                                    <li><a href="#step-3" className="text-blue-600 hover:text-blue-800 transition-colors">3. Think About Pronunciation and Spelling</a></li>
                                    <li><a href="#step-4" className="text-blue-600 hover:text-blue-800 transition-colors">4. Test the Full Name</a></li>
                                    <li><a href="#step-5" className="text-blue-600 hover:text-blue-800 transition-colors">5. Consider Cultural and Family Significance</a></li>
                                </ul>
                                <ul className="space-y-2 text-sm sm:text-base">
                                    <li><a href="#step-6" className="text-blue-600 hover:text-blue-800 transition-colors">6. Think About Nicknames</a></li>
                                    <li><a href="#step-7" className="text-blue-600 hover:text-blue-800 transition-colors">7. Consider Popularity and Uniqueness</a></li>
                                    <li><a href="#step-8" className="text-blue-600 hover:text-blue-800 transition-colors">8. Avoid Common Mistakes</a></li>
                                    <li><a href="#step-9" className="text-blue-600 hover:text-blue-800 transition-colors">9. Get Family Input (But Don't Be Pressured)</a></li>
                                    <li><a href="#step-10" className="text-blue-600 hover:text-blue-800 transition-colors">10. Trust Your Instincts</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Main Content */}
                <section className="py-8 sm:py-12 md:py-16">
                    <div className="container mx-auto px-4 sm:px-6 max-w-4xl">
                        <div className="prose prose-sm sm:prose-base lg:prose-lg max-w-none">
                            <h2 id="step-1" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">1</span>
                                <span>Start Early and Research Thoroughly</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                The journey to finding the perfect baby name begins long before your due date. Starting early gives you the luxury of time to explore, research, and reflect on your choices. Use our <Link href="/baby-name-generator" className="text-blue-600 hover:text-blue-800 underline font-medium">AI-powered baby name generator</Link> to discover personalized suggestions based on your preferences.
                            </p>
                            <div className="bg-blue-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Pro Tips for Early Planning:</h3>
                                <ul className="space-y-2 sm:space-y-3">
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Create separate lists for boy and girl names, even if you know the gender</span>
                                    </li>
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Use our comprehensive database of <Link href="/" className="text-blue-600 hover:text-blue-800 underline">25,000+ names with verified meanings</Link></span>
                                    </li>
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Research name origins and cultural significance</span>
                                    </li>
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Consider seasonal or time-based naming trends</span>
                                    </li>
                                </ul>
                            </div>

                            <h2 id="step-2" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">2</span>
                                <span>Consider the Meaning and Origin</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                A name's meaning can carry significant weight and often reflects your values, hopes, and dreams for your child. Understanding the origin and cultural context adds depth to your choice.
                            </p>
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="flex items-center text-base sm:text-lg">
                                            <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-blue-600 flex-shrink-0" />
                                            Meaning Matters
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                                            Names like <Link href="/name/english/girl/christian/grace" className="text-blue-600 hover:text-blue-800 underline">"Grace"</Link> (divine favor), <Link href="/name/english/boy/christian/felix" className="text-blue-600 hover:text-blue-800 underline">"Felix"</Link> (happy), or <Link href="/name/english/boy/christian/sage" className="text-blue-600 hover:text-blue-800 underline">"Sage"</Link> (wise) carry positive meanings that can inspire your child throughout their life.
                                        </p>
                                    </CardContent>
                                </Card>
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="flex items-center text-base sm:text-lg">
                                            <Globe className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600 flex-shrink-0" />
                                            Cultural Heritage
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                                            Consider names that honor your family's cultural background or heritage, connecting your child to their roots and traditions. Explore <Link href="/religions/christian-boy-names" className="text-blue-600 hover:text-blue-800 underline">Christian names</Link>, <Link href="/religions/muslim-boy-names" className="text-blue-600 hover:text-blue-800 underline">Muslim names</Link>, or <Link href="/india/hindi-boy-names" className="text-blue-600 hover:text-blue-800 underline">Indian names</Link> based on your heritage.
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>

                            <h2 id="step-3" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">3</span>
                                <span>Think About Pronunciation and Spelling</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                A name should be easy to pronounce and spell. Consider how often your child will need to correct people or spell their name out loud.
                            </p>
                            <div className="bg-yellow-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Pronunciation Checklist:</h3>
                                <ul className="space-y-2 text-sm sm:text-base">
                                    <li className="flex items-start">
                                        <span className="text-yellow-600 mr-2 mt-1">•</span>
                                        <span>Can most people pronounce it correctly on first sight?</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-yellow-600 mr-2 mt-1">•</span>
                                        <span>Is the spelling intuitive?</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-yellow-600 mr-2 mt-1">•</span>
                                        <span>Will your child constantly have to correct people?</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-yellow-600 mr-2 mt-1">•</span>
                                        <span>Does it work in different languages or cultures?</span>
                                    </li>
                                </ul>
                            </div>

                            <h2 id="step-4" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">4</span>
                                <span>Test the Full Name</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                Always test the complete name - first, middle, and last name together. Consider how it sounds when called out loud, written on official documents, or announced at graduation.
                            </p>
                            <div className="bg-purple-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Full Name Testing Tips:</h3>
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <h4 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Say it out loud:</h4>
                                        <p className="text-gray-600 text-sm sm:text-base">"Emma Grace Johnson" - How does it flow?</p>
                                    </div>
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <h4 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Check initials:</h4>
                                        <p className="text-gray-600 text-sm sm:text-base">"EGJ" - Avoid embarrassing combinations</p>
                                    </div>
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <h4 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Write it down:</h4>
                                        <p className="text-gray-600 text-sm sm:text-base">See how it looks on paper</p>
                                    </div>
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <h4 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Imagine scenarios:</h4>
                                        <p className="text-gray-600 text-sm sm:text-base">Job interviews, awards ceremonies, etc.</p>
                                    </div>
                                </div>
                            </div>

                            <h2 id="step-5" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">5</span>
                                <span>Consider Cultural and Family Significance</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                Names often carry deep cultural, religious, or family significance. Honoring traditions while creating your own path is a beautiful way to name your child.
                            </p>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-6 sm:mb-8">
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="text-base sm:text-lg">Family Names</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                                            Honor grandparents, parents, or other family members while considering modern variations.
                                        </p>
                                    </CardContent>
                                </Card>
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="text-base sm:text-lg">Religious Names</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                                            Choose names with spiritual significance that align with your faith and values.
                                        </p>
                                    </CardContent>
                                </Card>
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="text-base sm:text-lg">Cultural Names</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                                            Celebrate your heritage with names that reflect your cultural background and traditions.
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>

                            <h2 id="step-6" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">6</span>
                                <span>Think About Nicknames</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                Most names naturally develop nicknames. Consider what shortened versions might emerge and whether you're comfortable with them.
                            </p>
                            <div className="bg-green-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Nickname Considerations:</h3>
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <h4 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Common Nicknames:</h4>
                                        <ul className="text-gray-600 space-y-1 text-sm sm:text-base">
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Elizabeth → Liz, Beth, Ellie</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>William → Will, Bill, Liam</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Alexander → Alex, Xander</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Isabella → Bella, Izzy</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <h4 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Questions to Ask:</h4>
                                        <ul className="text-gray-600 space-y-1 text-sm sm:text-base">
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Do you like the potential nicknames?</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Can you control which nickname sticks?</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Does the name work well shortened?</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <h2 id="step-7" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">7</span>
                                <span>Consider Popularity and Uniqueness</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                There's no right answer when it comes to popularity. Some parents prefer classic, well-known names, while others want something unique and distinctive.
                            </p>
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="flex items-center text-base sm:text-lg">
                                            <Star className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-yellow-600 flex-shrink-0" />
                                            Popular Names
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 mb-2 sm:mb-3 text-sm sm:text-base font-medium">Pros:</p>
                                        <ul className="text-gray-600 text-xs sm:text-sm space-y-1 mb-3">
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Familiar and easy to pronounce</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Less likely to be misspelled</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Often have positive associations</span>
                                            </li>
                                        </ul>
                                        <p className="text-gray-600 mb-2 sm:mb-3 text-sm sm:text-base font-medium">Cons:</p>
                                        <ul className="text-gray-600 text-xs sm:text-sm space-y-1">
                                            <li className="flex items-start">
                                                <span className="text-red-600 mr-2 mt-1">•</span>
                                                <span>Multiple children with same name</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-red-600 mr-2 mt-1">•</span>
                                                <span>May feel less special</span>
                                            </li>
                                        </ul>
                                    </CardContent>
                                </Card>
                                <Card className="border-0 shadow-lg">
                                    <CardHeader className="pb-3 sm:pb-4">
                                        <CardTitle className="flex items-center text-base sm:text-lg">
                                            <Heart className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-pink-600 flex-shrink-0" />
                                            Unique Names
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-gray-600 mb-2 sm:mb-3 text-sm sm:text-base font-medium">Pros:</p>
                                        <ul className="text-gray-600 text-xs sm:text-sm space-y-1 mb-3">
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Stands out and memorable</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Reflects creativity</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-green-600 mr-2 mt-1">•</span>
                                                <span>Less confusion in groups</span>
                                            </li>
                                        </ul>
                                        <p className="text-gray-600 mb-2 sm:mb-3 text-sm sm:text-base font-medium">Cons:</p>
                                        <ul className="text-gray-600 text-xs sm:text-sm space-y-1">
                                            <li className="flex items-start">
                                                <span className="text-red-600 mr-2 mt-1">•</span>
                                                <span>May be difficult to pronounce</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-red-600 mr-2 mt-1">•</span>
                                                <span>Could lead to teasing</span>
                                            </li>
                                            <li className="flex items-start">
                                                <span className="text-red-600 mr-2 mt-1">•</span>
                                                <span>Might be misspelled often</span>
                                            </li>
                                        </ul>
                                    </CardContent>
                                </Card>
                            </div>

                            <h2 id="step-8" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">8</span>
                                <span>Avoid Common Mistakes</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                Learning from others' experiences can help you avoid common pitfalls in the naming process.
                            </p>
                            <div className="bg-red-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Common Naming Mistakes to Avoid:</h3>
                                <div className="space-y-3 sm:space-y-4">
                                    <div className="flex items-start bg-white rounded-lg p-3 sm:p-4">
                                        <span className="bg-red-600 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-xs sm:text-sm font-bold mr-3 mt-0.5 flex-shrink-0">✗</span>
                                        <div>
                                            <h4 className="font-semibold text-gray-800 text-sm sm:text-base mb-1">Ignoring Initials</h4>
                                            <p className="text-gray-600 text-xs sm:text-sm">Check that initials don't spell something embarrassing or inappropriate.</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start bg-white rounded-lg p-3 sm:p-4">
                                        <span className="bg-red-600 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-xs sm:text-sm font-bold mr-3 mt-0.5 flex-shrink-0">✗</span>
                                        <div>
                                            <h4 className="font-semibold text-gray-800 text-sm sm:text-base mb-1">Following Trends Blindly</h4>
                                            <p className="text-gray-600 text-xs sm:text-sm">Very trendy names can quickly become dated and associated with a specific era.</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start bg-white rounded-lg p-3 sm:p-4">
                                        <span className="bg-red-600 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-xs sm:text-sm font-bold mr-3 mt-0.5 flex-shrink-0">✗</span>
                                        <div>
                                            <h4 className="font-semibold text-gray-800 text-sm sm:text-base mb-1">Not Considering the Future</h4>
                                            <p className="text-gray-600 text-xs sm:text-sm">Think about how the name will work for a child, teenager, and adult.</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start bg-white rounded-lg p-3 sm:p-4">
                                        <span className="bg-red-600 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center text-xs sm:text-sm font-bold mr-3 mt-0.5 flex-shrink-0">✗</span>
                                        <div>
                                            <h4 className="font-semibold text-gray-800 text-sm sm:text-base mb-1">Ignoring Cultural Sensitivity</h4>
                                            <p className="text-gray-600 text-xs sm:text-sm">Be respectful of cultural names and avoid appropriation.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h2 id="step-9" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">9</span>
                                <span>Get Family Input (But Don't Be Pressured)</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                Family opinions can be valuable, but remember that this is ultimately your decision. Be open to suggestions while staying true to your vision.
                            </p>
                            <div className="bg-indigo-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Managing Family Input:</h3>
                                <ul className="space-y-2 sm:space-y-3">
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Listen to suggestions with an open mind</span>
                                    </li>
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Explain your reasoning respectfully</span>
                                    </li>
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Consider family names as middle names</span>
                                    </li>
                                    <li className="flex items-start">
                                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-sm sm:text-base">Remember: you're the parent, you make the final decision</span>
                                    </li>
                                </ul>
                            </div>

                            <h2 id="step-10" className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-0">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold sm:mr-3 flex-shrink-0">10</span>
                                <span>Trust Your Instincts</span>
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                                After all the research, consideration, and discussion, trust your gut feeling. The perfect name is the one that feels right to you and your partner.
                            </p>
                            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Final Checklist:</h3>
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                    <ul className="space-y-2">
                                        <li className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                            <span className="text-sm sm:text-base">Does it feel right when you say it?</span>
                                        </li>
                                        <li className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                            <span className="text-sm sm:text-base">Can you imagine calling your child this name?</span>
                                        </li>
                                        <li className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                            <span className="text-sm sm:text-base">Does it work with your last name?</span>
                                        </li>
                                    </ul>
                                    <ul className="space-y-2">
                                        <li className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                            <span className="text-sm sm:text-base">Are you both happy with the choice?</span>
                                        </li>
                                        <li className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                            <span className="text-sm sm:text-base">Does it reflect your values?</span>
                                        </li>
                                        <li className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                                            <span className="text-sm sm:text-base">Will it grow with your child?</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            {/* Call to Action */}
                            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 sm:p-8 text-white text-center">
                                <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">Ready to Find Your Perfect Baby Name?</h3>
                                <p className="text-blue-100 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
                                    Explore our comprehensive database of 25,000+ names with meanings, origins, and popularity data. Use our AI-powered name generator to discover personalized suggestions.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                                    <Link href="/baby-name-generator" className="w-full sm:w-auto">
                                        <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 w-full sm:w-auto text-sm sm:text-base px-4 sm:px-6 py-3 sm:py-4">
                                            <Zap className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                                            Try Our Name Generator
                                        </Button>
                                    </Link>
                                    <Link href="/" className="w-full sm:w-auto">
                                        <Button size="lg" variant="outline" className="border-white text-blue-600 hover:bg-white hover:text-blue-800 w-full sm:w-auto text-sm sm:text-base px-4 sm:px-6 py-3 sm:py-4">
                                            <BookOpen className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                                            Browse All Names
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* FAQ Section */}
                <section className="py-8 sm:py-12 md:py-16 bg-white">
                    <div className="container mx-auto px-4 sm:px-6">
                        <FAQSection faqs={faqs} />
                    </div>
                </section>

                {/* Related Resources */}
                <section className="py-8 sm:py-12 md:py-16 bg-gray-50">
                    <div className="container mx-auto px-4 sm:px-6">
                        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-8 sm:mb-12 text-gray-900">
                            Related Resources
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                            <Card className="group hover:shadow-lg transition-all duration-300">
                                <CardHeader className="pb-3 sm:pb-4">
                                    <CardTitle className="group-hover:text-blue-600 transition-colors text-base sm:text-lg">
                                        <Link href="/baby-name-generator" className="hover:underline">
                                            AI Baby Name Generator
                                        </Link>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base leading-relaxed">
                                        Get personalized name suggestions based on your preferences, style, and cultural background.
                                    </p>
                                    <Link href="/baby-name-generator" className="text-blue-600 hover:text-blue-800 font-medium text-sm sm:text-base">
                                        Try Generator →
                                    </Link>
                                </CardContent>
                            </Card>

                            <Card className="group hover:shadow-lg transition-all duration-300">
                                <CardHeader className="pb-3 sm:pb-4">
                                    <CardTitle className="group-hover:text-blue-600 transition-colors text-base sm:text-lg">
                                        <Link href="/popular-names-2025" className="hover:underline">
                                            Popular Names 2025
                                        </Link>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base leading-relaxed">
                                        Discover the most trending baby names for 2025 with popularity rankings and meanings.
                                    </p>
                                    <Link href="/popular-names-2025" className="text-blue-600 hover:text-blue-800 font-medium text-sm sm:text-base">
                                        View Trends →
                                    </Link>
                                </CardContent>
                            </Card>

                            <Card className="group hover:shadow-lg transition-all duration-300">
                                <CardHeader className="pb-3 sm:pb-4">
                                    <CardTitle className="group-hover:text-blue-600 transition-colors text-base sm:text-lg">
                                        <Link href="/unique-baby-names-2025" className="hover:underline">
                                            Unique Baby Names
                                        </Link>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base leading-relaxed">
                                        Find rare and distinctive names that will make your child stand out from the crowd.
                                    </p>
                                    <Link href="/unique-baby-names-2025" className="text-blue-600 hover:text-blue-800 font-medium text-sm sm:text-base">
                                        Explore Unique Names →
                                    </Link>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>
            </div>
        </>
    )
} 