"use client"

import { FAQSection } from "@/components/faq-section"
import SEOStructuredData from "@/components/seo-structured-data"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CheckCircle, Heart, Mic, Star, Volume2, XCircle, Zap } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface CompatibilityResult {
    name: string
    surname: string
    fullName: string
    flowScore: number
    pronunciationScore: number
    initialsScore: number
    overallScore: number
    flowAnalysis: string
    pronunciationAnalysis: string
    initialsAnalysis: string
    suggestions: string[]
    warnings: string[]
}

export default function NameCompatibilityCheckerPage() {
    const [firstName, setFirstName] = useState("")
    const [surname, setSurname] = useState("")
    const [results, setResults] = useState<CompatibilityResult[]>([])
    const [isAnalyzing, setIsAnalyzing] = useState(false)

    const analyzeCompatibility = async () => {
        if (!firstName.trim() || !surname.trim()) return

        setIsAnalyzing(true)

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000))

        const fullName = `${firstName} ${surname}`
        const initials = `${firstName.charAt(0)}${surname.charAt(0)}`.toUpperCase()

        // Analyze flow (syllable rhythm)
        const firstNameSyllables = countSyllables(firstName)
        const surnameSyllables = countSyllables(surname)
        const flowScore = calculateFlowScore(firstNameSyllables, surnameSyllables, firstName, surname)

        // Analyze pronunciation
        const pronunciationScore = calculatePronunciationScore(firstName, surname)

        // Analyze initials
        const initialsScore = calculateInitialsScore(initials)

        const overallScore = Math.round((flowScore + pronunciationScore + initialsScore) / 3)

        const result: CompatibilityResult = {
            name: firstName,
            surname: surname,
            fullName: fullName,
            flowScore,
            pronunciationScore,
            initialsScore,
            overallScore,
            flowAnalysis: getFlowAnalysis(firstNameSyllables, surnameSyllables, flowScore),
            pronunciationAnalysis: getPronunciationAnalysis(firstName, surname, pronunciationScore),
            initialsAnalysis: getInitialsAnalysis(initials, initialsScore),
            suggestions: getSuggestions(firstName, surname, overallScore),
            warnings: getWarnings(firstName, surname, initials)
        }

        setResults([result, ...results.slice(0, 4)]) // Keep last 5 results
        setIsAnalyzing(false)
    }

    const countSyllables = (word: string): number => {
        word = word.toLowerCase()
        word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '')
        word = word.replace(/^y/, '')
        const syllables = word.match(/[aeiouy]{1,2}/g)
        return syllables ? syllables.length : 1
    }

    const calculateFlowScore = (firstNameSyllables: number, surnameSyllables: number, firstName: string, surname: string): number => {
        let score = 70 // Base score

        // Syllable balance
        const totalSyllables = firstNameSyllables + surnameSyllables
        if (totalSyllables >= 4 && totalSyllables <= 6) score += 15
        else if (totalSyllables < 3) score -= 10
        else if (totalSyllables > 7) score -= 15

        // Ending/beginning sound harmony
        const firstNameEnd = firstName.toLowerCase().slice(-1)
        const surnameStart = surname.toLowerCase().charAt(0)
        if (firstNameEnd === surnameStart) score += 10
        else if (['a', 'e', 'i', 'o', 'u'].includes(firstNameEnd) && ['a', 'e', 'i', 'o', 'u'].includes(surnameStart)) score += 5

        // Rhythm pattern
        if (firstNameSyllables === surnameSyllables) score += 10
        else if (Math.abs(firstNameSyllables - surnameSyllables) === 1) score += 5

        return Math.min(100, Math.max(0, score))
    }

    const calculatePronunciationScore = (firstName: string, surname: string): number => {
        let score = 80 // Base score

        const fullName = `${firstName} ${surname}`.toLowerCase()

        // Check for difficult combinations
        if (fullName.includes('thth') || fullName.includes('shsh')) score -= 20
        if (fullName.includes('ngng') || fullName.includes('chch')) score -= 15

        // Check for smooth transitions
        const vowels = ['a', 'e', 'i', 'o', 'u']
        const firstNameEnd = firstName.toLowerCase().slice(-1)
        const surnameStart = surname.toLowerCase().charAt(0)

        if (vowels.includes(firstNameEnd) && vowels.includes(surnameStart)) score += 10
        if (firstNameEnd === surnameStart) score += 5

        return Math.min(100, Math.max(0, score))
    }

    const calculateInitialsScore = (initials: string): number => {
        let score = 90 // Base score

        // Check for problematic initials
        const problematicCombos = ['BS', 'FU', 'KK', 'SS', 'TT', 'UU', 'VV', 'WW', 'XX', 'YY', 'ZZ']
        if (problematicCombos.includes(initials)) score -= 50

        // Check for common words
        const wordCombos = ['AS', 'AT', 'BE', 'BY', 'DO', 'GO', 'HE', 'IF', 'IN', 'IT', 'ME', 'NO', 'OF', 'ON', 'OR', 'SO', 'TO', 'UP', 'US', 'WE']
        if (wordCombos.includes(initials)) score -= 20

        // Check for repeated letters
        if (initials.charAt(0) === initials.charAt(1)) score -= 15

        return Math.min(100, Math.max(0, score))
    }

    const getFlowAnalysis = (firstNameSyllables: number, surnameSyllables: number, flowScore: number): string => {
        if (flowScore >= 90) return "Excellent flow! The name and surname create a perfect rhythm."
        if (flowScore >= 80) return "Great flow! The combination sounds natural and harmonious."
        if (flowScore >= 70) return "Good flow! The name flows well together."
        if (flowScore >= 60) return "Fair flow. The combination works but could be smoother."
        return "Poor flow. Consider a different name or surname combination."
    }

    const getPronunciationAnalysis = (firstName: string, surname: string, pronunciationScore: number): string => {
        if (pronunciationScore >= 90) return "Very easy to pronounce! Clear and distinct sounds."
        if (pronunciationScore >= 80) return "Easy to pronounce! Most people will say it correctly."
        if (pronunciationScore >= 70) return "Generally easy to pronounce with minor challenges."
        if (pronunciationScore >= 60) return "Moderate pronunciation difficulty. May need clarification."
        return "Difficult to pronounce. Expect frequent mispronunciations."
    }

    const getInitialsAnalysis = (initials: string, initialsScore: number): string => {
        if (initialsScore >= 90) return "Great initials! Clean and professional."
        if (initialsScore >= 80) return "Good initials! No major concerns."
        if (initialsScore >= 70) return "Acceptable initials. Some minor considerations."
        if (initialsScore >= 60) return "Problematic initials. May cause issues."
        return "Poor initials. Strongly consider alternatives."
    }

    const getSuggestions = (firstName: string, surname: string, overallScore: number): string[] => {
        const suggestions = []

        if (overallScore < 70) {
            suggestions.push("Try a different first name with similar meaning")
            suggestions.push("Consider using a middle name to improve flow")
            suggestions.push("Test with a nickname or shortened version")
        }

        if (firstName.length < 3) {
            suggestions.push("Consider a longer first name for better balance")
        }

        if (surname.length > 8) {
            suggestions.push("A shorter first name might work better")
        }

        return suggestions
    }

    const getWarnings = (firstName: string, surname: string, initials: string): string[] => {
        const warnings = []

        if (initials.length === 2 && initials.charAt(0) === initials.charAt(1)) {
            warnings.push("Repeated initials may look unusual")
        }

        if (firstName.toLowerCase().endsWith(surname.toLowerCase().charAt(0))) {
            warnings.push("Name ends with same sound as surname starts")
        }

        if (firstName.length + surname.length > 15) {
            warnings.push("Very long full name may be cumbersome")
        }

        return warnings
    }

    const getScoreColor = (score: number): string => {
        if (score >= 90) return "text-green-600"
        if (score >= 80) return "text-blue-600"
        if (score >= 70) return "text-yellow-600"
        if (score >= 60) return "text-orange-600"
        return "text-red-600"
    }

    const getScoreIcon = (score: number) => {
        if (score >= 90) return <Star className="h-5 w-5 text-green-600" />
        if (score >= 80) return <CheckCircle className="h-5 w-5 text-blue-600" />
        if (score >= 70) return <CheckCircle className="h-5 w-5 text-yellow-600" />
        if (score >= 60) return <CheckCircle className="h-5 w-5 text-orange-600" />
        return <XCircle className="h-5 w-5 text-red-600" />
    }

    const speakFullName = (fullName: string) => {
        if (!fullName.trim()) return

        // Stop any current speech
        window.speechSynthesis.cancel()

        // Create utterance with slower, clearer speech
        const utterance = new SpeechSynthesisUtterance(fullName)
        utterance.rate = 0.8 // Slightly slower for clarity
        utterance.pitch = 1.0
        utterance.volume = 1.0

        // Try to use a good voice if available
        const voices = window.speechSynthesis.getVoices()
        const preferredVoice = voices.find(voice =>
            voice.lang.includes('en') && voice.name.includes('Female')
        ) || voices.find(voice => voice.lang.includes('en'))

        if (preferredVoice) {
            utterance.voice = preferredVoice
        }

        window.speechSynthesis.speak(utterance)
    }

    const faqs = [
        {
            question: "How does the name compatibility checker work?",
            answer: "Our checker analyzes three key factors: flow (how the name and surname sound together), pronunciation (ease of saying the full name), and initials (potential issues with abbreviated forms). It provides a comprehensive score and detailed analysis."
        },
        {
            question: "What makes a good name-surname combination?",
            answer: "Good combinations have balanced syllables, smooth sound transitions, and avoid awkward initial combinations. The ideal full name should be easy to pronounce, have a natural rhythm, and create professional-looking initials."
        },
        {
            question: "Should I worry about initials?",
            answer: "While not the most important factor, initials matter for professional contexts. Avoid combinations that spell words or create awkward abbreviations. Our checker identifies potential issues with initials."
        },
        {
            question: "What if my chosen name gets a low score?",
            answer: "Low scores don't mean you shouldn't use the name! Consider our suggestions for improvement, such as adding a middle name or trying variations. Personal preference and family significance often outweigh technical scores."
        },
        {
            question: "Can I test multiple name combinations?",
            answer: "Yes! You can test as many combinations as you want. The checker saves your recent results so you can compare different options and find the best combination for your baby."
        },
        {
            question: "How accurate is the pronunciation analysis?",
            answer: "Our analysis considers common pronunciation patterns and potential challenges. However, pronunciation can vary by region and accent. The checker helps identify obvious issues but can't predict all regional variations."
        }
    ]

    return (
        <>
            <SEOStructuredData
                names={[]}
                pageType="tool"
                title="Baby Name Compatibility Checker - Test How Names Sound with Your Surname"
                description="Check how baby names sound with your surname. Our unique compatibility analyzer tests pronunciation, flow, and initials to find the perfect name-surname combination."
                url="/name-compatibility-checker"
            />

            <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
                {/* Hero Section */}
                <section className="bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 text-white py-12 md:py-20">
                    <div className="container mx-auto px-4 text-center">
                        <div className="flex items-center justify-center mb-6">
                            <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
                                <Heart className="h-8 w-8 md:h-12 md:w-12" />
                            </div>
                        </div>
                        <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6">
                            Name Compatibility Checker
                        </h1>
                        <p className="text-lg md:text-xl lg:text-2xl mb-8 text-green-100 max-w-3xl mx-auto">
                            Test how baby names sound with your surname. Get instant analysis of flow, pronunciation, and initials.
                        </p>
                        <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 px-2">
                            <Badge variant="secondary" className="text-green-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                🎵 Flow Analysis
                            </Badge>
                            <Badge variant="secondary" className="text-green-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                🗣️ Pronunciation
                            </Badge>
                            <Badge variant="secondary" className="text-green-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                ✍️ Initials Check
                            </Badge>
                            <Badge variant="secondary" className="text-green-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                ⚡ Instant Results
                            </Badge>
                        </div>
                    </div>
                </section>

                {/* Main Checker Section */}
                <section className="py-8 sm:py-12 md:py-16">
                    <div className="container mx-auto px-4 max-w-4xl">
                        <Card className="shadow-xl border-0">
                            <CardHeader className="text-center pb-6">
                                <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900">
                                    Test Name Compatibility
                                </CardTitle>
                                <p className="text-gray-600 mt-2">
                                    Enter a baby name and your surname to see how they work together
                                </p>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                                            Baby Name
                                        </Label>
                                        <Input
                                            id="firstName"
                                            placeholder="Enter baby name..."
                                            value={firstName}
                                            onChange={(e) => setFirstName(e.target.value)}
                                            className="mt-1"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="surname" className="text-sm font-medium text-gray-700">
                                            Your Surname
                                        </Label>
                                        <Input
                                            id="surname"
                                            placeholder="Enter your surname..."
                                            value={surname}
                                            onChange={(e) => setSurname(e.target.value)}
                                            className="mt-1"
                                        />
                                    </div>
                                </div>

                                <Button
                                    onClick={analyzeCompatibility}
                                    disabled={!firstName.trim() || !surname.trim() || isAnalyzing}
                                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-3"
                                >
                                    {isAnalyzing ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Analyzing...
                                        </>
                                    ) : (
                                        <>
                                            <Zap className="mr-2 h-5 w-5" />
                                            Check Compatibility
                                        </>
                                    )}
                                </Button>
                            </CardContent>
                        </Card>

                        {/* Results */}
                        {results.length > 0 && (
                            <div className="mt-8 space-y-6">
                                <h2 className="text-2xl font-bold text-gray-900 text-center">Compatibility Results</h2>

                                {results.map((result, index) => (
                                    <Card key={index} className="shadow-lg border-0">
                                        <CardHeader>
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-3">
                                                    <CardTitle className="text-xl font-bold">
                                                        {result.fullName}
                                                    </CardTitle>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => speakFullName(result.fullName)}
                                                        className="h-8 w-8 p-0 bg-gray-50 hover:bg-gray-100"
                                                        title="Hear pronunciation"
                                                    >
                                                        <Volume2 className="h-4 w-4 text-gray-600" />
                                                    </Button>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <span className={`text-2xl font-bold ${getScoreColor(result.overallScore)}`}>
                                                        {result.overallScore}%
                                                    </span>
                                                    {getScoreIcon(result.overallScore)}
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            {/* Score Breakdown */}
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <div className="text-center p-4 bg-green-50 rounded-lg">
                                                    <div className="flex items-center justify-center mb-2">
                                                        <Mic className="h-5 w-5 text-green-600 mr-2" />
                                                        <span className="font-semibold">Flow</span>
                                                    </div>
                                                    <div className={`text-2xl font-bold ${getScoreColor(result.flowScore)}`}>
                                                        {result.flowScore}%
                                                    </div>
                                                    <p className="text-sm text-gray-600 mt-1">{result.flowAnalysis}</p>
                                                </div>

                                                <div className="text-center p-4 bg-blue-50 rounded-lg">
                                                    <div className="flex items-center justify-center mb-2">
                                                        <Mic className="h-5 w-5 text-blue-600 mr-2" />
                                                        <span className="font-semibold">Pronunciation</span>
                                                    </div>
                                                    <div className={`text-2xl font-bold ${getScoreColor(result.pronunciationScore)}`}>
                                                        {result.pronunciationScore}%
                                                    </div>
                                                    <p className="text-sm text-gray-600 mt-1">{result.pronunciationAnalysis}</p>
                                                </div>

                                                <div className="text-center p-4 bg-purple-50 rounded-lg">
                                                    <div className="flex items-center justify-center mb-2">
                                                        <Mic className="h-5 w-5 text-purple-600 mr-2" />
                                                        <span className="font-semibold">Initials</span>
                                                    </div>
                                                    <div className={`text-2xl font-bold ${getScoreColor(result.initialsScore)}`}>
                                                        {result.initialsScore}%
                                                    </div>
                                                    <p className="text-sm text-gray-600 mt-1">{result.initialsAnalysis}</p>
                                                </div>
                                            </div>

                                            {/* Warnings */}
                                            {result.warnings.length > 0 && (
                                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                                    <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Considerations</h4>
                                                    <ul className="space-y-1">
                                                        {result.warnings.map((warning, idx) => (
                                                            <li key={idx} className="text-sm text-yellow-700">• {warning}</li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}

                                            {/* Suggestions */}
                                            {result.suggestions.length > 0 && (
                                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                                    <h4 className="font-semibold text-green-800 mb-2">💡 Suggestions</h4>
                                                    <ul className="space-y-1">
                                                        {result.suggestions.map((suggestion, idx) => (
                                                            <li key={idx} className="text-sm text-green-700">• {suggestion}</li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        )}

                        {/* Call to Action */}
                        <div className="mt-12 text-center">
                            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
                                <h3 className="text-2xl font-bold mb-4">Need More Name Ideas?</h3>
                                <p className="text-blue-100 mb-6">
                                    Use our AI-powered name generator to discover perfect names that work well with your surname.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <Link href="/baby-name-generator">
                                        <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
                                            <Zap className="mr-2 h-5 w-5" />
                                            Try Name Generator
                                        </Button>
                                    </Link>
                                    <Link href="/">
                                        <Button size="lg" variant="outline" className="border-white text-blue-600 hover:bg-white hover:text-blue-800">
                                            Browse All Names
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* FAQ Section */}
                <section className="py-16 bg-white">
                    <div className="container mx-auto px-4">
                        <FAQSection faqs={faqs} />
                    </div>
                </section>
            </div>
        </>
    )
} 