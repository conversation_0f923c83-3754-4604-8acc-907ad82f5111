import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata, metadataConfigs } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata(metadataConfigs.bengaliGirl)

export default function IndiaBengaliGirlNamesPage() {
  return (
    <LanguageNamesPage
      language="Bengali"
      country="India"
      religions={["Hindu"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="female"
      colorTheme="pink"
      apiEndpoint="/api/names/countries/india/languages/bengali/girl-names"
      headerLabels={{
        title: "Bengali Hindu Girl Names with Meanings",
        subtitle: "Traditional Bengali Girl Names for 2025",
        description: "Explore beautiful Bengali Hindu girl names with deep meanings, reflecting Bengal's rich culture and spiritual heritage."
      }}
      showRashiFilter={true}
      showAlphabetFilter={true}
      seoContent={
        <>
          <h2>About Bengali Girl Names</h2>
          <p>
            Bengali girl names are known for their elegance, poetic beauty, and deep meanings. Many names are inspired by Bengali literature, mythology, and spiritual traditions, reflecting the grace and strength of Bengali women.
          </p>
          <h3>Popular Bengali Girl Names in 2025</h3>
          <p>
            Names like <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> are popular for their melodious sound and cultural significance. These names are chosen for their positive meanings and connection to Bengali heritage.
          </p>
          <h3>Choosing the Right Bengali Girl Name</h3>
          <ul>
            <li><strong>Meaning:</strong> Select names with auspicious, beautiful meanings</li>
            <li><strong>Pronunciation:</strong> Easy to pronounce in both Bengali and English</li>
            <li><strong>Family Tradition:</strong> Honor family customs and heritage</li>
            <li><strong>Modern Appeal:</strong> Balance tradition with contemporary style</li>
          </ul>
        </>
      }
    />
  )
} 