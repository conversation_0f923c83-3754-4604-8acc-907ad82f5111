import LanguageNamesPageWrapper from "@/components/language-names-page-wrapper"
import { generatePageMetadata, metadataConfigs } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata(metadataConfigs.urduBoy)

export default function IndiaUrduBoyNamesPage() {
  return (
    <LanguageNamesPageWrapper
      language="Urdu"
      religions={[]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="male"
      colorTheme="blue"
      apiEndpoint="/api/names/countries/india/languages/urdu/boy-names"
      headerLabels={{
        title: "Urdu Hindu Boy Names with Meanings",
        subtitle: "Traditional Urdu Boy Names for 2025",
        description: "Explore beautiful Urdu Hindu boy names with deep meanings, reflecting the rich culture and spiritual heritage of Urdu-speaking communities."
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
      seoContent={
        <>
          <h2>About Urdu Hindu Boy Names</h2>
          <p>
            Urdu Hindu boy names are inspired by Hindu tradition, Sanskrit literature, and the rich cultural heritage of Urdu-speaking Hindu communities. Many names are derived from Sanskrit and reflect virtues, spirituality, and cultural pride.
          </p>
          <h3>Popular Urdu Hindu Boy Names in 2025</h3>
          <p>
            Names like <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>aan are popular for their beautiful meanings and spiritual resonance. These names are cherished for their connection to Hindu values and Urdu literature.
          </p>
          <h3>Choosing the Right Urdu Hindu Boy Name</h3>
          <ul>
            <li><strong>Meaning:</strong> Select names with positive, spiritual meanings</li>
            <li><strong>Pronunciation:</strong> Easy to pronounce in both Urdu and English</li>
            <li><strong>Family Tradition:</strong> Honor family customs and Hindu heritage</li>
            <li><strong>Modern Appeal:</strong> Balance tradition with contemporary style</li>
          </ul>
        </>
      }
    />
  )
} 