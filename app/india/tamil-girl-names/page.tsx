import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata, metadataConfigs } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata(metadataConfigs.tamilGirl)

export default function IndiaTamilGirlNamesPage() {
  return (
    <LanguageNamesPage
      language="Tamil"
      country="India"
      religions={["Hindu"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="female"
      colorTheme="pink"
      apiEndpoint="/api/names/countries/india/languages/tamil/girl-names"
      headerLabels={{
        title: "Tamil Hindu Girl Names with Meanings",
        subtitle: "Traditional Tamil Girl Names for 2025",
        description: "Explore beautiful Tamil Hindu girl names with deep meanings, reflecting Tamil Nadu's rich culture and spiritual heritage."
      }}
      showRashiFilter={true}
      showAlphabetFilter={true}
      seoContent={
        <>
          <h2>About Tamil Girl Names</h2>
          <p>
            Tamil girl names are known for their elegance, cultural depth, and spiritual significance. Many names are inspired by Tamil literature, mythology, and the region's vibrant traditions, reflecting the grace and strength of Tamil women.
          </p>
          <h3>Popular Tamil Girl Names in 2025</h3>
          <p>
            Names like <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> are popular for their melodious sound and cultural significance. These names are chosen for their positive meanings and connection to Tamil heritage.
          </p>
          <h3>Choosing the Right Tamil Girl Name</h3>
          <ul>
            <li><strong>Meaning:</strong> Select names with auspicious, beautiful meanings</li>
            <li><strong>Pronunciation:</strong> Easy to pronounce in both Tamil and English</li>
            <li><strong>Family Tradition:</strong> Honor family customs and heritage</li>
            <li><strong>Modern Appeal:</strong> Balance tradition with contemporary style</li>
          </ul>
        </>
      }
    />
  )
} 