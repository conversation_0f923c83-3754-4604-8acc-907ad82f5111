import BottomNav from "@/components/bottom-nav"
import B<PERSON><PERSON><PERSON>b<PERSON><PERSON> from "@/components/breadcrumb-nav"
import Footer from "@/components/footer"
import Header from "@/components/header"
import { ThemeProvider } from "@/components/theme-provider"
import { TranslationProvider } from "@/hooks/use-translation"
import { SpeedInsights } from "@vercel/speed-insights/next"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import Script from "next/script"
import type React from "react"
import "./globals.css"

// Optimized font loading for better Core Web Vitals
const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'arial'],
  adjustFontFallback: true,
  variable: '--font-inter'
})

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'https://www.babynamediaries.com'),
  title: {
    default: "Baby Names 2025 - 25,000+ Beautiful Names with Meanings & Origins",
    template: "%s | BabyNamesDiary - Your Trusted Baby Naming Guide"
  },
  description:
    "🌟 Discover 25,000+ stunning baby names for 2025! Find popular American, British, Indian & global names with beautiful meanings, cultural significance & pronunciation guides. Your complete baby naming resource.",
  keywords: [
    "baby names 2025",
    "baby names with meanings",
    "popular baby names",
    "American baby names",
    "British baby names",
    "Indian baby names",
    "unique baby names",
    "trending baby names",
    "baby name meanings",
    "international baby names",
    "baby name finder",
    "cultural baby names",
    "baby name search",
    "name pronunciations",
    "baby naming guide",
    "beautiful baby names",
    "modern baby names",
    "traditional baby names",
    "baby name origins",
    "global baby names",
    "boy names",
    "girl names"
  ].join(", "),
  authors: [{ name: "Baby Names Expert Team", url: "https://www.babynamediaries.com" }],
  creator: "BabyNamesDiary",
  publisher: "BabyNamesDiary",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  applicationName: 'BabyNamesDiary',
  generator: 'Next.js',
  referrer: 'origin-when-cross-origin',
  category: 'parenting',
  classification: 'parenting resources',

  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://www.babynamediaries.com",
    siteName: "BabyNamesDiary - Your Baby Naming Guide",
    title: "Baby Names 2025 - 25,000+ Beautiful Names with Meanings & Origins",
    description: "🌟 Discover 25,000+ stunning baby names for 2025! Find popular American, British, Indian & global names with beautiful meanings & cultural significance.",
    images: [
      {
        url: "/icons/logo_full_hd.png",
        width: 1024,
        height: 1024,
        alt: "BabyNamesDiary - Find the Perfect Baby Name",
        type: "image/png",
      },
      {
        url: "/favicon-512x512.png",
        width: 512,
        height: 512,
        alt: "BabyNamesDiary Logo",
        type: "image/png",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Baby Names 2025 - 25,000+ Beautiful Names with Meanings & Origins",
    description: "🌟 Discover 25,000+ stunning baby names for 2025! Find popular American, British, Indian & global names with beautiful meanings & cultural significance.",
    images: ["/icons/logo_full_hd.png"],
  },
  icons: {
    icon: [
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/favicon-512x512.png", sizes: "512x512", type: "image/png" }
    ],
    apple: "/favicon-192x192.png",
    shortcut: "/favicon-32x32.png",
    other: {
      rel: "mask-icon",
      url: "/favicon-192x192.png",
    }
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    // DNS verification is already active, no HTML meta tags needed
  },
  alternates: {
    canonical: "https://www.babynamediaries.com",
  },
  other: {
    'apple-mobile-web-app-title': 'BabyNamesDiary',
    'application-name': 'BabyNamesDiary',
    'msapplication-TileColor': '#3b82f6',
    'theme-color': '#3b82f6',
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'format-detection': 'telephone=no, address=no, email=no',
    'HandheldFriendly': 'True',
    'MobileOptimized': '320',
    // Enhanced search intent signals for better Google ranking
    'search-intent': 'informational',
    'content-type': 'baby-names-database',
    'target-audience': 'expecting-parents',
    'content-freshness': '2025-updated',
    'geographic-coverage': '12-countries',
    'language-coverage': '8-languages',
    'name-count': '25000-plus',
    'update-frequency': 'weekly',
    'expertise-level': 'comprehensive',
    'user-engagement': 'high',
    'content-quality': 'expert-curated',
    'mobile-optimization': 'perfect',
    'core-web-vitals': 'optimized',
    'structured-data': 'comprehensive',
    'internal-linking': 'excellent',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
}

// Enhanced structured data for better Google ranking and rich snippets
const structuredData = {
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "name": "BabyNamesDiary",
      "description": "Your trusted baby naming guide with 25,000+ beautiful names from 12 countries",
      "url": "https://www.babynamediaries.com/",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.babynamediaries.com/icons/logo_full_hd.png",
        "width": 1024,
        "height": 1024
      },
      "foundingDate": "2024",
      "sameAs": [
        "https://www.babynamediaries.com/"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["English", "Hindi", "Gujarati", "Tamil", "Bengali", "Marathi", "Punjabi", "Urdu"]
      },
      "knowsAbout": [
        "Baby Names",
        "Name Meanings",
        "Cultural Names",
        "Religious Names",
        "International Names",
        "Name Origins",
        "Name Trends"
      ]
    },
    {
      "@type": "WebSite",
      "name": "BabyNamesDiary",
      "alternateName": ["Baby Names Diary", "babynamediaries.com"],
      "description": "Discover 25,000+ beautiful baby names with meanings from 12 countries worldwide",
      "url": "https://www.babynamediaries.com/",
      "mainEntity": {
        "@type": "ItemList",
        "name": "Baby Names Database 2025",
        "description": "Comprehensive collection of 25,000+ baby names from 12 countries with meanings and origins",
        "numberOfItems": 25000
      }
    },
    {
      "@type": "WebPage",
      "name": "Baby Names 2025 - 25,000+ Beautiful Names with Meanings & Origins",
      "description": "🌟 Discover 25,000+ stunning baby names for 2025! Find popular American, British, Indian & global names with beautiful meanings, cultural significance & pronunciation guides.",
      "url": "https://www.babynamediaries.com/",
      "isPartOf": {
        "@type": "WebSite",
        "name": "BabyNamesDiary"
      },
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://www.babynamediaries.com/"
          }
        ]
      },
      "mainEntity": {
        "@type": "ItemList",
        "name": "Featured Baby Names 2025",
        "description": "Top trending and popular baby names for 2025",
        "numberOfItems": 100
      }
    }
  ]
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning className={inter.variable}>
      <head>
        {/* Critical CSS for faster rendering */}
        <style dangerouslySetInnerHTML={{
          __html: `
            html { font-family: var(--font-inter), system-ui, sans-serif; }
            /* Critical above-the-fold styles */
            body { margin: 0; padding: 0; }
            .header-logo { display: flex; align-items: center; gap: 0.5rem; height: 40px; }
            .header-logo img { display: block; width: 32px; height: 32px; }
            .header-logo span { font-weight: 600; font-size: 1.12rem; color: #2563eb; }
            /* Prevent layout shift */
            .name-card { min-height: 120px; }
            .skeleton { background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; }
            @keyframes loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }
          `
        }} />

        {/* Preload critical resources */}
        <link rel="preload" href="/icons/logo_full_hd.png" as="image" type="image/png" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Preload critical API endpoints */}
        <link rel="preconnect" href="/api/names" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* PWA manifest */}
        <link rel="manifest" href="/manifest.webmanifest" />
        <meta name="theme-color" content="#3b82f6" />

        {/* Enhanced Favicon and icons for better SEO */}
        <link rel="icon" href="/favicon-32x32.png" sizes="32x32" type="image/png" />
        <link rel="icon" href="/favicon-16x16.png" sizes="16x16" type="image/png" />
        <link rel="apple-touch-icon" href="/favicon-192x192.png" />
        <link rel="apple-touch-icon" sizes="192x192" href="/favicon-192x192.png" />
        <link rel="apple-touch-icon" sizes="512x512" href="/favicon-512x512.png" />

        {/* Additional favicon formats for better browser support */}
        <link rel="icon" href="/favicon-32x32.png" type="image/png" />
        <link rel="shortcut icon" href="/favicon-32x32.png" type="image/png" />
        <meta name="msapplication-TileImage" content="/favicon-192x192.png" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* Enhanced meta tags for mobile SEO */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="BabyNamesDiary" />

        {/* SEO meta tags - Enhanced for better Google ranking */}
        {/* {process.env.GOOGLE_SITE_VERIFICATION && (
          <meta name="google-site-verification" content={process.env.GOOGLE_SITE_VERIFICATION} />
        )}
        {process.env.BING_SITE_VERIFICATION && (
          <meta name="msvalidate.01" content={process.env.BING_SITE_VERIFICATION} />
        )} */}

        {/* Enhanced SEO signals for Google ranking */}
        <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
        <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
        <meta name="bingbot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />

        {/* Content freshness and quality signals */}
        <meta name="article:published_time" content="2025-01-01T00:00:00Z" />
        <meta name="article:modified_time" content={new Date().toISOString()} />
        <meta name="content-type" content="baby-names-database" />
        <meta name="target-audience" content="expecting-parents" />
        <meta name="geographic-coverage" content="12-countries" />
        <meta name="language-coverage" content="8-languages" />
        <meta name="name-count" content="25000-plus" />
        <meta name="update-frequency" content="weekly" />
        <meta name="expertise-level" content="comprehensive" />

        {/* Consolidated Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      </head>
      <body className={inter.className}>
        {/* Google Analytics - Optimized loading */}
        {process.env.NEXT_PUBLIC_GA_ID && (
          <>
            <Script
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
              strategy="afterInteractive"
            />
            <Script id="google-analytics" strategy="afterInteractive">
              {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
              `}
            </Script>
          </>
        )}

        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <TranslationProvider>
            <Header />
            <main className="pt-16">
              <BreadcrumbNav />
              <div className="min-h-screen pb-20 md:pb-0">{children}</div>
            </main>
            <Footer />
            <BottomNav />
          </TranslationProvider>
        </ThemeProvider>

        {/* Simplified Service Worker Registration */}
        <Script id="sw-registration" strategy="lazyOnload">
          {`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('SW registered successfully');
                  })
                  .catch(function(error) {
                    console.log('SW registration failed:', error);
                  });
              });
            }
          `}
        </Script>

        {/* Vercel Speed Insights for performance monitoring */}
        <SpeedInsights />
      </body>
    </html>
  )
}
