import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/belgium/french-boy-names",
  title: "Belgian French Boy Names 2025 - Belgium Baby Names with Meanings",
  description: "Discover beautiful Belgian French boy names for 2025. Find traditional and modern French baby boy names popular in Belgium with meanings, origins, and cultural significance.",
  keywords: [
    "Belgian French boy names",
    "Belgium baby names",
    "French boy names Belgium",
    "Belgian baby boy names",
    "French names with meanings",
    "Belgian French names 2025",
    "Belgium boy names",
    "French baby names",
    "Belgian names for boys",
    "French Belgian names"
  ]
})

export default function BelgianFrenchBoyNamesPage() {
  return (
    <LanguageNamesPage
      language="French"
      country="Belgium"
      religions={["Christian"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="male"
      colorTheme="blue"
      apiEndpoint="/api/names/countries/belgium/languages/french/boy-names"
      headerLabels={{
        title: "Belgian French Boy Names",
        subtitle: "Traditional & Modern French Names in Belgium",
        description: "Discover authentic Belgian French boy names with Walloon heritage and modern Belgian culture"
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
    />
  )
}
