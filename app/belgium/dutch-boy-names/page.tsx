import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/belgium/dutch-boy-names",
  title: "Belgian Dutch Boy Names 2025 - Traditional & Modern Names with Meanings",
  description: "Discover beautiful Belgian Dutch boy names for 2025. Find traditional Flemish names and modern Dutch baby boy names popular in Belgium with meanings, origins, and cultural significance.",
  keywords: [
    // Primary target keywords
    "Belgian Dutch boy names",
    "Dutch boy names Belgium",
    "Flemish boy names",

    // Secondary with meanings/origins
    "Dutch names with meanings",
    "Belgian baby boy names",

    // Year-specific for freshness
    "Belgian Dutch names 2025",

    // Cultural/traditional aspect
    "traditional Flemish names",
    "Dutch Belgian baby names",

    // Broader variations
    "Belgium boy names",
    "Dutch names Belgium culture",

    // Long-tail for specific intent
    "Belgian Dutch male names meanings"
  ]
})

export default function BelgianDutchBoyNamesPage() {
  return (
    
      <LanguageNamesPage
        language="Dutch"
        country="Belgium"
        religions={["Christian"]}
        showReligionFilter={false}
        showGenderFilter={false}
        defaultGender="male"
        colorTheme="orange"
        apiEndpoint="/api/names/countries/belgium/languages/dutch/boy-names"
        headerLabels={{
          title: "Belgian Dutch Boy Names",
          subtitle: "Traditional & Modern Dutch Names in Belgium",
          description: "Discover authentic Belgian Dutch boy names with Flemish heritage and modern Belgian culture"
        }}
        showRashiFilter={false}
        showAlphabetFilter={true}
      />
    
  )
}
