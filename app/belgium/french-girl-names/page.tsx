import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/belgium/french-girl-names",
  title: "Belgian French Girl Names 2025 - Belgium Baby Names with Meanings",
  description: "Discover beautiful Belgian French girl names for 2025. Find traditional and modern French baby girl names popular in Belgium with meanings, origins, and cultural significance.",
  keywords: [
    "Belgian French girl names",
    "Belgium baby names",
    "French girl names Belgium",
    "Belgian baby girl names",
    "French names with meanings",
    "Belgian French names 2025",
    "Belgium girl names",
    "French baby names",
    "Belgian names for girls",
    "French Belgian names"
  ]
})

export default function BelgianFrenchGirlNamesPage() {
  return (
    <LanguageNamesPage
      language="French"
      country="Belgium"
      religions={["Christian"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="female"
      colorTheme="blue"
      apiEndpoint="/api/names/countries/belgium/languages/french/girl-names"
      headerLabels={{
        title: "Belgian French Girl Names",
        subtitle: "Traditional & Modern French Names in Belgium",
        description: "Discover authentic Belgian French girl names with Walloon heritage and modern Belgian culture"
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
    />
  )
}
