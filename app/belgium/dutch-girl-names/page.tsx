import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/belgium/dutch-girl-names",
  title: "Belgian Dutch Girl Names 2025 - Belgium Baby Names with Meanings",
  description: "Discover beautiful Belgian Dutch girl names for 2025. Find traditional and modern Dutch baby girl names popular in Belgium with meanings, origins, and cultural significance.",
  keywords: [
    "Belgian Dutch girl names",
    "Belgium baby names",
    "Dutch girl names Belgium",
    "Belgian baby girl names",
    "Dutch names with meanings",
    "Belgian Dutch names 2025",
    "Belgium girl names",
    "Dutch baby names",
    "Belgian names for girls",
    "Dutch Belgian names"
  ]
})

export default function BelgianDutchGirlNamesPage() {
  return (
    <LanguageNamesPage
      language="Dutch"
      country="Belgium"
      religions={["Christian"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="female"
      colorTheme="orange"
      apiEndpoint="/api/names/countries/belgium/languages/dutch/girl-names"
      headerLabels={{
        title: "Belgian Dutch Girl Names",
        subtitle: "Traditional & Modern Dutch Names in Belgium",
        description: "Discover authentic Belgian Dutch girl names with Flemish heritage and modern Belgian culture"
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
    />
  )
}
