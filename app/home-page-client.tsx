"use client"

import { generateFAQStructuredData } from '@/components/seo-structured-data';
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useTopNames, useTrendingSummary } from "@/hooks/use-top-names";
import { navigationConfig } from "@/lib/navigation-config";
import {
    ArrowRight,
    Baby,
    BookOpen,
    Crown,
    Globe,
    Heart,
    Loader2,
    Search,
    Sparkles,
    TrendingUp,
    Zap
} from "lucide-react";
import Link from "next/link";
import Script from 'next/script';

// Only implemented countries - based on actual data we have
const implementedCountries = [
    { name: "USA", flag: "🇺🇸", tier: "premium", cpm: "high", code: "usa" },
    { name: "United Kingdom", flag: "🇬🇧", tier: "premium", cpm: "high", code: "uk" },
    { name: "Canada", flag: "🇨🇦", tier: "premium", cpm: "high", code: "canada" },
    { name: "Australia", flag: "🇦🇺", tier: "premium", cpm: "high", code: "australia" },
    { name: "Germany", flag: "🇩🇪", tier: "standard", cpm: "medium-high", code: "germany" },
    { name: "France", flag: "🇫🇷", tier: "standard", cpm: "medium-high", code: "france" },
    { name: "Netherlands", flag: "🇳🇱", tier: "standard", cpm: "medium", code: "netherlands" },
    { name: "Sweden", flag: "🇸🇪", tier: "standard", cpm: "medium", code: "sweden" },
    { name: "Switzerland", flag: "🇨🇭", tier: "standard", cpm: "medium-high", code: "switzerland" },
    { name: "Austria", flag: "🇦🇹", tier: "standard", cpm: "medium", code: "austria" },
    { name: "Belgium", flag: "🇧🇪", tier: "standard", cpm: "medium", code: "belgium" },
    { name: "India", flag: "🇮🇳", tier: "volume", cpm: "low-volume", code: "india" }
]

// Helper function to get tier styling
const getTierStyling = (tier: string) => {
    switch (tier) {
        case 'premium':
            return {
                gradient: 'bg-gradient-to-br from-yellow-50 to-amber-100 border-yellow-200 hover:border-yellow-300',
                badge: 'bg-yellow-100 text-yellow-800 border-yellow-300',
                icon: '👑'
            }
        case 'standard':
            return {
                gradient: 'bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200 hover:border-blue-300',
                badge: 'bg-blue-100 text-blue-800 border-blue-300',
                icon: '⭐'
            }
        case 'volume':
            return {
                gradient: 'bg-gradient-to-br from-green-50 to-emerald-100 border-green-200 hover:border-green-300',
                badge: 'bg-green-100 text-green-800 border-green-300',
                icon: '📈'
            }
        default:
            return {
                gradient: 'bg-gradient-to-br from-gray-50 to-slate-100 border-gray-200 hover:border-gray-300',
                badge: 'bg-gray-100 text-gray-800 border-gray-300',
                icon: '🌍'
            }
    }
}

export default function HomePageClient() {
    // Fetch real data from API
    const { data: usaTopNames, loading: usaLoading } = useTopNames({
        country: 'usa',
        limit: 5
    })

    const { data: trendingSummary, loading: trendingLoading } = useTrendingSummary({
        countries: ['usa', 'uk', 'canada']
    })

    // Real-time trending data or fallback
    const getTrendingForCountry = (countryCode: string) => {
        const trendingItem = trendingSummary?.find(item =>
            item.country.toLowerCase() === countryCode.toLowerCase()
        )
        return trendingItem || null
    }

    // Fallback trending data for only implemented countries
    const trendingNamesByCountry = {
        "USA": {
            boy: getTrendingForCountry('usa')?.top_boy || "Liam",
            girl: getTrendingForCountry('usa')?.top_girl || "Emma",
            trend: getTrendingForCountry('usa')?.growth_percentage || "+15%"
        },
        "United Kingdom": {
            boy: getTrendingForCountry('uk')?.top_boy || "Noah",
            girl: getTrendingForCountry('uk')?.top_girl || "Olivia",
            trend: getTrendingForCountry('uk')?.growth_percentage || "+12%"
        },
        "Canada": {
            boy: getTrendingForCountry('canada')?.top_boy || "Oliver",
            girl: getTrendingForCountry('canada')?.top_girl || "Charlotte",
            trend: getTrendingForCountry('canada')?.growth_percentage || "+18%"
        },
        "Australia": { boy: "Henry", girl: "Isla", trend: "+10%" },
        "Germany": { boy: "Noah", girl: "Emilia", trend: "+8%" },
        "France": { boy: "Gabriel", girl: "Louise", trend: "+14%" },
        "Netherlands": { boy: "Noah", girl: "Julia", trend: "+9%" },
        "Sweden": { boy: "William", girl: "Alice", trend: "+13%" },
        "Switzerland": { boy: "Noah", girl: "Mia", trend: "+11%" },
        "Austria": { boy: "Noah", girl: "Emilia", trend: "+7%" },
        "Belgium": { boy: "Noah", girl: "Emma", trend: "+9%" },
        "India": { boy: "Arjun", girl: "Aadhya", trend: "+22%" }
    }

    // Homepage FAQ data - comprehensive SEO-optimized questions
    const homepageFAQs = [
        {
            question: "What are the most popular baby names in 2025?",
            answer: "The most popular baby names in 2025 include Liam, Oliver, and Noah for boys, and Emma, Olivia, and Charlotte for girls. These names are trending across the USA, UK, Canada, and Australia, with variations popular in other countries as well."
        },
        {
            question: "How many baby names are available on BabyNameDiaries?",
            answer: "BabyNameDiaries features over 25,000 carefully curated baby names from 12 countries including USA, UK, Canada, Australia, Germany, France, Netherlands, Sweden, Switzerland, Austria, Belgium, and India. Each name includes meanings, origins, and cultural significance."
        },
        {
            question: "Are the baby name meanings and origins accurate?",
            answer: "Yes, all name meanings and origins are researched and verified from authentic linguistic and cultural sources. We work with language experts and cultural consultants to ensure accuracy across all 12 countries in our database."
        },
        {
            question: "Can I find names from multiple cultures and religions?",
            answer: "Absolutely! Our database includes names from Christian, Muslim, Sikh, Hindu, and other religious traditions, as well as names from various cultural backgrounds including European, Asian, American, and global naming traditions."
        },
        {
            question: "How do I choose the perfect baby name?",
            answer: "Consider factors like pronunciation, meaning, cultural significance, family heritage, and how the name sounds with your last name. Our search and filter tools help you explore options by origin, religion, popularity, and starting letter."
        },
        {
            question: "Are there trending baby names for 2025?",
            answer: "Yes! Trending names for 2025 include nature-inspired names like Luna and River, celestial names like Nova and Atlas, and internationally-friendly names like Kai and Mila. We track trends across all 12 countries in our database."
        },
        {
            question: "Do you provide pronunciation guides for baby names?",
            answer: "Yes, we provide pronunciation guides for names across all languages and cultures in our database. This includes phonetic spellings and audio guidance for names from different linguistic backgrounds."
        },
        {
            question: "Can I save my favorite baby names?",
            answer: "Yes! You can add names to your favorites list by clicking the heart icon on any name card. This helps you create a personalized collection of names you love for easy comparison and decision-making."
        },
        {
            question: "What baby names are trending in different countries?",
            answer: "Trending names vary by country: USA favors Liam and Emma, UK prefers Oliver and Olivia, Canada loves Noah and Charlotte, while India trends toward Aarav and Aadhya. Our database tracks regional preferences and cultural influences that shape naming trends in each country."
        },
        {
            question: "How do I find baby names by meaning?",
            answer: "Use our search function to find names by meaning - search for qualities like 'strength,' 'wisdom,' 'peace,' or 'joy' to discover names that embody those characteristics. Our database includes detailed meanings for every name, helping you choose names that reflect your values and aspirations."
        },
        {
            question: "Are there baby names that work in multiple languages?",
            answer: "Yes! Many names in our database work beautifully across languages, such as Sofia (popular in USA, Europe, and Latin America), Noah (trending globally), and Aria (loved worldwide). We highlight names that have international appeal and are easy to pronounce in different languages."
        },
        {
            question: "What should I consider when naming my baby?",
            answer: "Consider pronunciation ease, name meaning significance, cultural heritage, family traditions, potential nicknames, professional appropriateness, and how the name pairs with your surname. Also think about initials, length, and whether the name ages well from childhood through adulthood."
        }
    ]

    // Generate FAQ structured data
    const faqStructuredData = generateFAQStructuredData(homepageFAQs)

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
            {/* Hero Section - Global with Country Rotation */}
            <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="relative container mx-auto px-2 sm:px-4 py-10 sm:py-16 md:py-20">
                    <div className="text-center mb-6 sm:mb-8">
                        <div className="flex justify-center mb-4 sm:mb-6">
                            <div className="bg-white/20 backdrop-blur-sm rounded-full p-3 sm:p-4">
                                <div className="flex items-center space-x-2">
                                    <Baby className="h-7 w-7 sm:h-8 sm:w-8 text-white" />
                                    <Globe className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                                </div>
                            </div>
                        </div>
                        <h1 className="text-2xl xs:text-3xl sm:text-4xl md:text-6xl font-bold mb-4 sm:mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                            Baby Names from 12 Countries
                        </h1>
                        <p className="text-base xs:text-lg sm:text-xl mb-6 sm:mb-8 text-blue-100 max-w-xl sm:max-w-3xl mx-auto">
                            Discover meaningful baby names from USA, UK, Canada, Australia, Germany, France, Netherlands, Sweden, Switzerland, Austria, Belgium, and India
                        </p>
                    </div>

                    {/* Quick Country Access - Mobile Optimized */}
                    <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3 mb-6 sm:mb-8">
                        {implementedCountries.slice(0, 6).map((country) => {
                            const countryConfig = navigationConfig.countries.find(c => c.name === country.name)
                            const firstLanguageHref = countryConfig?.languages[0]?.boyHref || `/${country.code}/english-boy-names`

                            return (
                                <Link
                                    key={country.name}
                                    href={firstLanguageHref}
                                    className="group"
                                >
                                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-2 sm:p-3 text-center hover:bg-white/20 transition-all duration-300">
                                        <div className="text-xl sm:text-2xl mb-0.5 sm:mb-1">{country.flag}</div>
                                        <div className="text-[11px] sm:text-xs font-medium text-white/90 group-hover:text-white truncate">
                                            {country.name === "United Kingdom" ? "UK" : country.name}
                                        </div>
                                    </div>
                                </Link>
                            )
                        })}
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                        <Link href="/baby-name-generator">
                            <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 w-full sm:w-auto shadow-lg">
                                <Sparkles className="mr-2 h-5 w-5" />
                                AI Name Generator
                            </Button>
                        </Link>
                        <Link href="/usa/english-boy-names/">
                            <Button size="lg" className="bg-white text-blue-800 hover:bg-blue-100 w-full sm:w-auto">
                                <Search className="mr-2 h-5 w-5" />
                                Start Exploring
                            </Button>
                        </Link>
                        <Link href="/blog/">
                            <Button size="lg" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 w-full sm:w-auto">
                                <BookOpen className="mr-2 h-5 w-5" />
                                Name Trends 2025
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Decorative elements - hide on mobile */}
                <div className="absolute top-4 left-4 opacity-20 hidden md:block">
                    <Sparkles className="h-7 w-7 md:h-8 md:w-8" />
                </div>
                <div className="absolute bottom-4 right-4 opacity-20 hidden md:block">
                    <Sparkles className="h-7 w-7 md:h-8 md:w-8" />
                </div>
            </section>

            {/* Baby Name Generator Section - High Revenue Focus */}
            <section className="py-8 sm:py-12 md:py-16 bg-gradient-to-r from-purple-50 to-pink-50">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="text-center mb-6 sm:mb-8 md:mb-12">
                        <div className="flex items-center justify-center mb-3 sm:mb-4">
                            <Sparkles className="h-7 w-7 sm:h-8 sm:w-8 text-purple-600 mr-2 sm:mr-3" />
                            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">AI Baby Name Generator</h2>
                        </div>
                        <p className="text-sm sm:text-base md:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto">
                            Get personalized baby name suggestions powered by artificial intelligence
                        </p>
                    </div>

                    <div className="max-w-4xl mx-auto">
                        <Card className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-none shadow-2xl">
                            <CardHeader className="text-center pb-6">
                                <div className="flex justify-center mb-4">
                                    <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
                                        <Sparkles className="h-8 w-8 md:h-12 md:w-12" />
                                    </div>
                                </div>
                                <CardTitle className="text-2xl md:text-3xl font-bold mb-4">
                                    Find Your Perfect Baby Name
                                </CardTitle>
                                <p className="text-purple-100 text-lg mb-6">
                                    Our AI analyzes your preferences to suggest names that match your style, culture, and values
                                </p>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                    <div className="bg-white/10 rounded-lg p-4 text-center">
                                        <div className="text-2xl font-bold">25,000+</div>
                                        <div className="text-purple-200">Names Available</div>
                                    </div>
                                    <div className="bg-white/10 rounded-lg p-4 text-center">
                                        <div className="text-2xl font-bold">12</div>
                                        <div className="text-purple-200">Countries</div>
                                    </div>
                                    <div className="bg-white/10 rounded-lg p-4 text-center">
                                        <div className="text-2xl font-bold">AI</div>
                                        <div className="text-purple-200">Powered</div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="text-center pb-6">
                                <Link href="/baby-name-generator">
                                    <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50 text-lg px-8 py-4 font-semibold shadow-lg">
                                        <Zap className="mr-2 h-5 w-5" />
                                        Start Generating Names
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Top 3 Premium Countries Section - High Revenue Focus */}
            <section className="py-8 sm:py-12 md:py-16 bg-white">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="text-center mb-6 sm:mb-8 md:mb-12">
                        <div className="flex items-center justify-center mb-3 sm:mb-4">
                            <Crown className="h-7 w-7 sm:h-8 sm:w-8 text-yellow-500 mr-2 sm:mr-3" />
                            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Premium Baby Name Collections</h2>
                        </div>
                        <p className="text-sm sm:text-base md:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto">
                            Explore top-rated baby names from our highest quality collections
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 md:gap-8 mb-6 sm:mb-8 md:mb-12">
                        {/* USA - Priority #1 */}
                        <div className="relative">
                            <Card className="h-full bg-gradient-to-br from-blue-500 to-purple-600 text-white border-none shadow-xl hover:shadow-2xl transition-all duration-300">
                                <CardHeader className="text-center pb-3 sm:pb-4">
                                    <div className="text-3xl sm:text-4xl mb-2 sm:mb-3">🇺🇸</div>
                                    <CardTitle className="text-lg sm:text-xl md:text-2xl font-bold">American Names</CardTitle>
                                    <Badge className="bg-yellow-400 text-yellow-900 mt-1 sm:mt-2 text-xs sm:text-sm">🔥 Most Popular</Badge>
                                </CardHeader>
                                <CardContent className="text-center">
                                    {usaLoading ? (
                                        <div className="flex justify-center mb-3 sm:mb-4">
                                            <Loader2 className="h-5 w-5 animate-spin" />
                                        </div>
                                    ) : (
                                        <p className="text-blue-100 mb-3 sm:mb-4 text-sm sm:text-base">
                                            Trending: {usaTopNames?.data?.boys?.[0]?.name_en || "Liam"} & Emma
                                        </p>
                                    )}
                                    <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
                                        <div className="bg-white/10 rounded-lg p-2">
                                            <div className="font-semibold">{usaTopNames?.metadata?.total_boys || "5000"}+</div>
                                            <div className="text-blue-200">Names</div>
                                        </div>
                                        <div className="bg-white/10 rounded-lg p-2">
                                            <div className="font-semibold text-green-300">
                                                {getTrendingForCountry('usa')?.growth_percentage || "+15%"}
                                            </div>
                                            <div className="text-blue-200">Growth</div>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter className="pt-2">
                                    <Link href="/usa/english-boy-names/" className="w-full">
                                        <Button className="w-full bg-white text-blue-800 hover:bg-blue-100 font-semibold text-xs sm:text-base">
                                            Explore American Names <ArrowRight className="ml-2 h-4 w-4" />
                                        </Button>
                                    </Link>
                                </CardFooter>
                            </Card>
                        </div>
                        {/* UK - Priority #2 */}
                        <div className="relative">
                            <Card className="h-full bg-gradient-to-br from-red-500 to-pink-600 text-white border-none shadow-xl hover:shadow-2xl transition-all duration-300">
                                <CardHeader className="text-center pb-3 sm:pb-4">
                                    <div className="text-3xl sm:text-4xl mb-2 sm:mb-3">🇬🇧</div>
                                    <CardTitle className="text-lg sm:text-xl md:text-2xl font-bold">British Names</CardTitle>
                                    <Badge className="bg-yellow-400 text-yellow-900 mt-1 sm:mt-2 text-xs sm:text-sm">👑 Royal Heritage</Badge>
                                </CardHeader>
                                <CardContent className="text-center">
                                    <p className="text-pink-100 mb-3 sm:mb-4 text-sm sm:text-base">
                                        Trending: {getTrendingForCountry('uk')?.top_boy || "Noah"} & {getTrendingForCountry('uk')?.top_girl || "Olivia"}
                                    </p>
                                    <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
                                        <div className="bg-white/10 rounded-lg p-2">
                                            <div className="font-semibold">3500+</div>
                                            <div className="text-pink-200">Names</div>
                                        </div>
                                        <div className="bg-white/10 rounded-lg p-2">
                                            <div className="font-semibold text-green-300">{getTrendingForCountry('uk')?.growth_percentage || "+10%"}</div>
                                            <div className="text-pink-200">Growth</div>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter className="pt-2">
                                    <Link href="/uk/english-boy-names/" className="w-full">
                                        <Button className="w-full bg-white text-red-600 hover:bg-red-50 font-semibold text-xs sm:text-base">
                                            Explore British Names <ArrowRight className="ml-2 h-4 w-4" />
                                        </Button>
                                    </Link>
                                </CardFooter>
                            </Card>
                        </div>
                        {/* Canada - Priority #3 */}
                        <div className="relative">
                            <Card className="h-full bg-gradient-to-br from-green-500 to-teal-600 text-white border-none shadow-xl hover:shadow-2xl transition-all duration-300">
                                <CardHeader className="text-center pb-3 sm:pb-4">
                                    <div className="text-3xl sm:text-4xl mb-2 sm:mb-3">🇨🇦</div>
                                    <CardTitle className="text-lg sm:text-xl md:text-2xl font-bold">Canadian Names</CardTitle>
                                    <Badge className="bg-yellow-400 text-yellow-900 mt-1 sm:mt-2 text-xs sm:text-sm">🍁 Bilingual</Badge>
                                </CardHeader>
                                <CardContent className="text-center">
                                    <p className="text-green-100 mb-3 sm:mb-4 text-sm sm:text-base">
                                        Trending: {getTrendingForCountry('canada')?.top_boy || "Oliver"} & {getTrendingForCountry('canada')?.top_girl || "Charlotte"}
                                    </p>
                                    <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
                                        <div className="bg-white/10 rounded-lg p-2">
                                            <div className="font-semibold">2800+</div>
                                            <div className="text-green-200">Names</div>
                                        </div>
                                        <div className="bg-white/10 rounded-lg p-2">
                                            <div className="font-semibold text-yellow-300">{getTrendingForCountry('canada')?.growth_percentage || "+18%"}</div>
                                            <div className="text-green-200">Growth</div>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter className="pt-2">
                                    <Link href="/canada/english-boy-names/" className="w-full">
                                        <Button className="w-full bg-white text-green-800 hover:bg-green-100 font-semibold text-xs sm:text-base">
                                            Explore Canadian Names <ArrowRight className="ml-2 h-4 w-4" />
                                        </Button>
                                    </Link>
                                </CardFooter>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>

            {/* All 12 Countries Grid - Mobile Optimized */}
            <section className="py-8 sm:py-12 md:py-16 bg-gradient-to-br from-gray-50 to-blue-50">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="text-center mb-6 sm:mb-8 md:mb-12">
                        <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">All Baby Name Collections</h2>
                        <p className="text-sm sm:text-base md:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto">
                            Browse baby names from 12 countries, each with unique cultural significance and meanings
                        </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                        {implementedCountries.map((country, index) => {
                            const countryConfig = navigationConfig.countries.find(c => c.name === country.name)
                            const styling = getTierStyling(country.tier)
                            const trending = trendingNamesByCountry[country.name as keyof typeof trendingNamesByCountry]

                            if (!countryConfig) return null

                            return (
                                <Card key={country.name} className={`h-full transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer border-2 ${styling.gradient}`}>
                                    <CardHeader className="pb-2 sm:pb-3">
                                        <div className="flex items-start justify-between mb-1 sm:mb-2 gap-2">
                                            <div className="text-2xl sm:text-3xl leading-none">{country.flag}</div>
                                            <Badge className={`text-[10px] sm:text-xs ${styling.badge} shrink-0`}>{styling.icon} {country.tier}</Badge>
                                        </div>
                                        <CardTitle className="text-base sm:text-lg leading-tight">{country.name}</CardTitle>
                                        <CardDescription className="text-xs sm:text-sm">{countryConfig.description}</CardDescription>
                                    </CardHeader>
                                    <CardContent className="py-1 sm:py-2">
                                        {trending && (
                                            <div className="space-y-1 sm:space-y-2">
                                                <div className="flex justify-between items-center text-[11px] sm:text-xs">
                                                    <span className="font-medium">Trending:</span>
                                                    <span className="text-green-600 font-semibold shrink-0">{trending.trend}</span>
                                                </div>
                                                <div className="text-xs sm:text-sm text-gray-600 leading-tight">
                                                    ♂️ {trending.boy} • ♀️ {trending.girl}
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                    <CardFooter className="pt-2 px-4 sm:px-6">
                                        <div className="grid grid-cols-2 gap-1 sm:gap-2 w-full">
                                            {countryConfig.languages[0] && (
                                                <>
                                                    <Link href={countryConfig.languages[0].boyHref}>
                                                        <Button size="sm" variant="outline" className="w-full text-[11px] sm:text-xs py-1.5 sm:py-2">
                                                            Boys
                                                        </Button>
                                                    </Link>
                                                    <Link href={countryConfig.languages[0].girlHref}>
                                                        <Button size="sm" variant="outline" className="w-full text-[11px] sm:text-xs py-1.5 sm:py-2">
                                                            Girls
                                                        </Button>
                                                    </Link>
                                                </>
                                            )}
                                        </div>
                                    </CardFooter>
                                </Card>
                            )
                        })}
                    </div>
                </div>
            </section>

            {/* Regional Trending Names - More Engagement */}
            <section className="py-8 sm:py-12 md:py-16 bg-white">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="grid lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
                        <div className="lg:col-span-3">
                            <div className="text-center mb-6 sm:mb-8 md:mb-12">
                                <div className="flex items-center justify-center mb-3 sm:mb-4">
                                    <TrendingUp className="h-7 w-7 sm:h-8 sm:w-8 text-blue-600 mr-2 sm:mr-3" />
                                    <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Global Name Trends 2025</h2>
                                </div>
                                <p className="text-sm sm:text-base md:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto">
                                    See what's trending in baby names across different regions
                                </p>
                            </div>

                            <Tabs defaultValue="english-speaking" className="w-full">
                                <TabsList className="grid w-full grid-cols-3 gap-0.5 sm:gap-1 md:gap-2 mb-4 sm:mb-6">
                                    <TabsTrigger value="english-speaking" className="text-xs sm:text-sm">English Speaking</TabsTrigger>
                                    <TabsTrigger value="european" className="text-xs sm:text-sm">European</TabsTrigger>
                                    <TabsTrigger value="asian" className="text-xs sm:text-sm">Asian</TabsTrigger>
                                </TabsList>

                                <TabsContent value="english-speaking">
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-6">
                                        {['USA', 'United Kingdom', 'Canada', 'Australia'].map((country) => {
                                            const trending = trendingNamesByCountry[country as keyof typeof trendingNamesByCountry]
                                            const flag = implementedCountries.find(c => c.name === country)?.flag
                                            return (
                                                <Card key={country} className="hover:shadow-md transition-shadow">
                                                    <CardContent className="p-3 sm:p-4">
                                                        <div className="flex items-center justify-between mb-2 sm:mb-3">
                                                            <div className="flex items-center space-x-2">
                                                                <span className="text-lg sm:text-xl">{flag}</span>
                                                                <span className="font-semibold text-gray-900 text-sm sm:text-base">{country}</span>
                                                            </div>
                                                            <Badge variant="outline" className="text-green-600 border-green-200 text-xs sm:text-sm">
                                                                {trending?.trend}
                                                            </Badge>
                                                        </div>
                                                        <div className="space-y-1 sm:space-y-2">
                                                            <div className="flex justify-between text-xs sm:text-sm">
                                                                <span className="text-gray-600">Top Boy:</span>
                                                                <span className="font-medium">{trending?.boy}</span>
                                                            </div>
                                                            <div className="flex justify-between text-xs sm:text-sm">
                                                                <span className="text-gray-600">Top Girl:</span>
                                                                <span className="font-medium">{trending?.girl}</span>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            )
                                        })}
                                    </div>
                                </TabsContent>

                                <TabsContent value="european">
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-6">
                                        {['Germany', 'France', 'Netherlands', 'Sweden', 'Switzerland', 'Austria', 'Belgium'].map((country) => {
                                            const trending = trendingNamesByCountry[country as keyof typeof trendingNamesByCountry]
                                            const flag = implementedCountries.find(c => c.name === country)?.flag
                                            return (
                                                <Card key={country} className="hover:shadow-md transition-shadow">
                                                    <CardContent className="p-3 sm:p-4">
                                                        <div className="flex items-center justify-between mb-2 sm:mb-3">
                                                            <div className="flex items-center space-x-2">
                                                                <span className="text-lg sm:text-xl">{flag}</span>
                                                                <span className="font-semibold text-gray-900 text-sm sm:text-base">{country}</span>
                                                            </div>
                                                            <Badge variant="outline" className="text-green-600 border-green-200 text-xs sm:text-sm">
                                                                {trending?.trend}
                                                            </Badge>
                                                        </div>
                                                        <div className="space-y-1 sm:space-y-2">
                                                            <div className="flex justify-between text-xs sm:text-sm">
                                                                <span className="text-gray-600">Top Boy:</span>
                                                                <span className="font-medium">{trending?.boy}</span>
                                                            </div>
                                                            <div className="flex justify-between text-xs sm:text-sm">
                                                                <span className="text-gray-600">Top Girl:</span>
                                                                <span className="font-medium">{trending?.girl}</span>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            )
                                        })}
                                    </div>
                                </TabsContent>

                                <TabsContent value="asian">
                                    <div className="grid grid-cols-1 gap-3 sm:gap-4 md:gap-6">
                                        {['India'].map((country) => {
                                            const trending = trendingNamesByCountry[country as keyof typeof trendingNamesByCountry]
                                            const flag = implementedCountries.find(c => c.name === country)?.flag
                                            return (
                                                <Card key={country} className="hover:shadow-md transition-shadow">
                                                    <CardContent className="p-3 sm:p-4">
                                                        <div className="flex items-center justify-between mb-2 sm:mb-3">
                                                            <div className="flex items-center space-x-2">
                                                                <span className="text-lg sm:text-xl">{flag}</span>
                                                                <span className="font-semibold text-gray-900 text-sm sm:text-base">{country}</span>
                                                            </div>
                                                            <Badge variant="outline" className="text-green-600 border-green-200 text-xs sm:text-sm">
                                                                {trending?.trend}
                                                            </Badge>
                                                        </div>
                                                        <div className="space-y-1 sm:space-y-2">
                                                            <div className="flex justify-between text-xs sm:text-sm">
                                                                <span className="text-gray-600">Top Boy:</span>
                                                                <span className="font-medium">{trending?.boy}</span>
                                                            </div>
                                                            <div className="flex justify-between text-xs sm:text-sm">
                                                                <span className="text-gray-600">Top Girl:</span>
                                                                <span className="font-medium">{trending?.girl}</span>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            )
                                        })}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </div>

                        {/* Sidebar with Quick Actions */}
                        <div className="lg:col-span-1 mt-6 lg:mt-0">
                            <div className="space-y-4 sm:space-y-6 sticky top-4">
                                {/* Quick Country Stats */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-base sm:text-lg">Global Stats</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-2 sm:space-y-3">
                                        <div className="flex justify-between items-center">
                                            <span className="text-xs sm:text-sm text-gray-600">Total Countries</span>
                                            <Badge variant="outline" className="text-blue-600 text-xs sm:text-sm">12</Badge>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-xs sm:text-sm text-gray-600">Total Names</span>
                                            <Badge variant="outline" className="text-green-600 text-xs sm:text-sm">25,000+</Badge>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-xs sm:text-sm text-gray-600">Languages</span>
                                            <Badge variant="outline" className="text-purple-600 text-xs sm:text-sm">15+</Badge>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-xs sm:text-sm text-gray-600">Updated</span>
                                            <Badge variant="outline" className="text-orange-600 text-xs sm:text-sm">2025</Badge>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Quick Links Card */}
                                <Card className="bg-gradient-to-br from-green-50 to-teal-50">
                                    <CardHeader>
                                        <CardTitle className="text-base sm:text-lg text-center">Quick Explore</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-2 sm:space-y-3">
                                        <Link href="/usa/english-boy-names/">
                                            <Button variant="outline" className="w-full text-blue-800 hover:bg-blue-100 text-xs sm:text-base">
                                                American Names
                                            </Button>
                                        </Link>
                                        <Link href="/uk/english-boy-names/">
                                            <Button variant="outline" className="w-full text-red-600 hover:bg-red-50 text-xs sm:text-base">
                                                British Names
                                            </Button>
                                        </Link>
                                        <Link href="/blog/">
                                            <Button variant="outline" className="w-full text-purple-600 hover:bg-purple-50 text-xs sm:text-base">
                                                📖 Name Trends Blog
                                            </Button>
                                        </Link>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-8 sm:py-12 md:py-16 bg-gray-50">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="text-center mb-6 sm:mb-8 md:mb-12">
                        <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Why Choose Our Global Collection?</h2>
                        <p className="text-sm sm:text-base md:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto">
                            The world's most comprehensive baby name database with cultural insights
                        </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 md:gap-8">
                        <div className="text-center">
                            <div className="bg-blue-100 rounded-full p-3 sm:p-4 w-14 sm:w-16 h-14 sm:h-16 mx-auto mb-3 sm:mb-4 flex items-center justify-center">
                                <Globe className="h-7 w-7 sm:h-8 sm:w-8 text-blue-600" />
                            </div>
                            <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-1 sm:mb-2">12 Countries</h3>
                            <p className="text-gray-600 text-xs sm:text-sm">Names from USA, UK, Canada, Australia, and 8 more countries</p>
                        </div>

                        <div className="text-center">
                            <div className="bg-pink-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <TrendingUp className="h-8 w-8 text-pink-600" />
                            </div>
                            <h3 className="text-lg md:text-xl font-semibold mb-2">2025 Trends</h3>
                            <p className="text-gray-600 text-sm">Latest naming trends and popularity rankings updated weekly</p>
                        </div>

                        <div className="text-center">
                            <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <BookOpen className="h-8 w-8 text-green-600" />
                            </div>
                            <h3 className="text-lg md:text-xl font-semibold mb-2">Rich Meanings</h3>
                            <p className="text-gray-600 text-sm">Detailed meanings, origins, and cultural significance</p>
                        </div>

                        <div className="text-center">
                            <div className="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <Heart className="h-8 w-8 text-purple-600" />
                            </div>
                            <h3 className="text-lg md:text-xl font-semibold mb-2">Personal Touch</h3>
                            <p className="text-gray-600 text-sm">Save favorites, explore similar names, and get recommendations</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-12 md:py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Find the Perfect Name?</h2>
                    <p className="text-lg md:text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
                        Join millions of parents worldwide who trust our global baby name collection
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
                        <Link href="/usa/english-boy-names/">
                            <Button size="lg" className="bg-white text-blue-800 hover:bg-blue-100 w-full">
                                American Names
                            </Button>
                        </Link>
                        <Link href="/uk/english-boy-names/">
                            <Button size="lg" className="bg-white text-red-600 hover:bg-red-50 w-full">
                                British Names
                            </Button>
                        </Link>
                        <Link href="/canada/english-boy-names/">
                            <Button size="lg" className="bg-white text-green-800 hover:bg-green-100 w-full">
                                Canadian Names
                            </Button>
                        </Link>
                        <Link href="/australia/english-boy-names/">
                            <Button size="lg" className="bg-white text-orange-600 hover:bg-orange-50 w-full">
                                Australian Names
                            </Button>
                        </Link>
                    </div>
                </div>
            </section>

            {/* Stats Section */}
            <section className="py-12 md:py-16 bg-white">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 text-center">
                        <div>
                            <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-2">25,000+</div>
                            <div className="text-gray-600 text-sm md:text-base">Baby Names</div>
                        </div>
                        <div>
                            <div className="text-2xl md:text-3xl font-bold text-pink-600 mb-2">12</div>
                            <div className="text-gray-600 text-sm md:text-base">Countries</div>
                        </div>
                        <div>
                            <div className="text-2xl md:text-3xl font-bold text-green-600 mb-2">15+</div>
                            <div className="text-gray-600 text-sm md:text-base">Languages</div>
                        </div>
                        <div>
                            <div className="text-2xl md:text-3xl font-bold text-purple-600 mb-2">★★★★★</div>
                            <div className="text-gray-600 text-sm md:text-base">Trusted Globally</div>
                        </div>
                    </div>
                </div>
            </section>

            {/* New FAQ Section */}
            <section className="py-16 px-4 bg-white">
                <div className="container mx-auto max-w-4xl">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
                        Frequently Asked Questions
                    </h2>
                    <div className="space-y-6">
                        {homepageFAQs.map((faq, index) => (
                            <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                <h3 className="text-lg font-semibold text-gray-800 mb-3">{faq.question}</h3>
                                <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* FAQ Structured Data */}
            <Script
                id="homepage-faq-structured-data"
                type="application/ld+json"
                strategy="beforeInteractive"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
            />
        </div>
    )
} 