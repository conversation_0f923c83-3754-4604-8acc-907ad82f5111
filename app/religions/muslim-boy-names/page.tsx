import LanguageNamesPageWrapper from "@/components/language-names-page-wrapper"
import { generatePageMetadata, metadataConfigs } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata(metadataConfigs.muslimBoy)

export default function MuslimBoyNamesPage() {
  return (
    <LanguageNamesPageWrapper
      language="Muslim"
      religions={["Muslim"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="male"
      colorTheme="blue"
      apiEndpoint="/api/names/global/religions/muslim/boy-names"
      headerLabels={{
        title: "Muslim Boy Names with Meanings",
        subtitle: "Beautiful Islamic Boy Names for 2025",
        description: "Discover meaningful Muslim boy names with deep Islamic significance. Find traditional and modern names that reflect Islamic values and cultural heritage."
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
      seoContent={
        <>
          <h2>About Muslim Boy Names</h2>
          <p>
            Muslim boy names are deeply rooted in Islamic tradition, Arabic culture, and the teachings of the Quran. These names often carry profound spiritual meanings and reflect virtues that parents wish to instill in their sons. Many names are derived from Arabic, Persian, and other Islamic cultural traditions.
          </p>
          
          <h3>Popular Muslim Boy Names in 2025</h3>
          <p>
            Traditional names like <PERSON>, <PERSON>, and <PERSON> continue to be beloved choices, while modern names like Ayaan, Zayan, and Rayan are trending among Muslim parents. These names reflect both the timeless appeal of Islamic tradition and contemporary naming trends.
          </p>
          
          <h3>Choosing the Right Muslim Boy Name</h3>
          <p>
            When selecting a Muslim boy name, consider these important factors:
          </p>
          <ul>
            <li><strong>Islamic Significance:</strong> Choose names with positive meanings from Islamic tradition</li>
            <li><strong>Pronunciation:</strong> Ensure the name is easy to pronounce in multiple languages</li>
            <li><strong>Cultural Heritage:</strong> Honor family traditions and Islamic values</li>
            <li><strong>Modern Appeal:</strong> Balance traditional significance with contemporary style</li>
            <li><strong>Global Recognition:</strong> Consider how the name will be received in different cultures</li>
          </ul>
          
          <h3>Traditional vs Modern Muslim Names</h3>
          <p>
            Traditional Muslim names often have roots in Arabic, Persian, and Islamic history, carrying centuries of cultural and spiritual significance. Modern Muslim names blend traditional meanings with contemporary appeal, making them suitable for today's global Muslim community.
          </p>
          
          <h3>Spiritual Significance of Muslim Names</h3>
          <p>
            Many Muslim boy names are inspired by Islamic prophets, historical figures, and spiritual concepts. Names like Ibrahim (Abraham), Yusuf (Joseph), and Ismail carry deep religious significance and are believed to bring blessings and positive energy to the child's life.
          </p>
          
          <h3>Cultural Diversity in Muslim Names</h3>
          <p>
            Muslim names reflect the rich diversity of the global Muslim community, with influences from various cultural backgrounds including Arabic, Persian, Turkish, South Asian, and African traditions. This diversity creates a unique naming landscape that celebrates multicultural Islamic heritage.
          </p>
        </>
      }
    />
  )
} 