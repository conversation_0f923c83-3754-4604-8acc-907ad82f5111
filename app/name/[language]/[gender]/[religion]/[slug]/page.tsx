import { allNames } from "@/data"
import type { Metadata } from "next"
import NameDetailClient from "./name-detail-client"

interface PageProps {
  params: Promise<{ language: string, gender: string, religion: string, slug: string }>
}

// Server-side metadata generation for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { language, gender, religion, slug } = await params

  try {
    const decodedName = decodeURIComponent(slug)
    const decodedLanguage = decodeURIComponent(language)
    const decodedGender = decodeURIComponent(gender)
    const decodedReligion = decodeURIComponent(religion)

    // Find the specific name directly from data instead of making HTTP request
    const nameData = allNames.find(n =>
      n.language.toLowerCase() === decodedLanguage.toLowerCase() &&
      n.gender.toLowerCase() === decodedGender.toLowerCase() &&
      n.religion.toLowerCase() === decodedReligion.toLowerCase() &&
      n.name_en.toLowerCase() === decodedName.toLowerCase()
    )

    if (!nameData) {
      throw new Error('Name not found')
    }

    // Generate SEO-optimized title (competing with top sites)
    const capitalizedName = nameData.name_en.charAt(0).toUpperCase() + nameData.name_en.slice(1)
    const genderLabel = decodedGender === 'boy' ? 'Boy' : 'Girl'
    const capitalizedLanguage = decodedLanguage.charAt(0).toUpperCase() + decodedLanguage.slice(1)
    const capitalizedReligion = decodedReligion.charAt(0).toUpperCase() + decodedReligion.slice(1)

    // Multiple title variations based on top sites' patterns
    const titleVariations = [
      `${capitalizedName} Name Meaning, Origin & Popularity 2025 - ${capitalizedLanguage} ${genderLabel} Names`,
      `${capitalizedName} - ${capitalizedLanguage} ${genderLabel} Name Meaning & Origin 2025`,
      `${capitalizedName} Baby Name: Meaning, Origin, Popularity & Reviews 2025`
    ]

    const title = titleVariations[0] // Use the most comprehensive one

    // Generate rich, keyword-optimized description
    const description = `✨ Discover ${capitalizedName}, a beautiful ${capitalizedLanguage} ${genderLabel.toLowerCase()} name meaning "${nameData.meaning_en}". Find origin, pronunciation, popularity trends for 2025, and similar ${capitalizedReligion} names. Test how ${capitalizedName} sounds with your surname using our compatibility checker! Perfect for your baby ${genderLabel.toLowerCase()}!`

    // Advanced keyword strategy based on top sites
    const keywords = [
      capitalizedName,
      `${capitalizedName} name meaning`,
      `${capitalizedName} baby name`,
      `${capitalizedName} origin`,
      `${capitalizedName} pronunciation`,
      `${capitalizedLanguage} ${genderLabel.toLowerCase()} names`,
      `${capitalizedLanguage} baby names`,
      `${capitalizedReligion} ${genderLabel.toLowerCase()} names`,
      `${capitalizedReligion} baby names`,
      `${genderLabel.toLowerCase()} names 2025`,
      `${capitalizedLanguage} names with meaning`,
      `baby names meaning ${nameData.meaning_en}`,
      `${capitalizedLanguage} names starting with ${nameData.name_en.charAt(0).toUpperCase()}`,
      `popular ${capitalizedLanguage} names`,
      `unique ${genderLabel.toLowerCase()} names`,
      `${capitalizedLanguage} name meanings`,
      `${capitalizedReligion} names with meaning`,
      `baby name ${capitalizedName}`,
      `${capitalizedName} name popularity`,
      `${capitalizedName} baby name 2025`,
      // Compatibility-related keywords
      `${capitalizedName} surname compatibility`,
      `how ${capitalizedName} sounds with surname`,
      `name compatibility test`,
      `baby name surname test`,
      `name flow with surname`,
      `name pronunciation test`,
      `name initials check`,
      `baby name compatibility checker`,
      `${capitalizedName} with your last name`,
      `test ${capitalizedName} with surname`
    ]

    // Add popularity-based keywords if available
    if (nameData.popularity_rank) {
      keywords.push(
        `popular ${capitalizedLanguage} names`,
        `top ${genderLabel.toLowerCase()} names 2025`,
        `trending baby names 2025`
      )
    }

    // Add astrological keywords for Hindu names
    if (decodedReligion.toLowerCase() === 'hindu') {
      keywords.push(
        `Hindu ${genderLabel.toLowerCase()} names`,
        `Sanskrit baby names`,
        `Indian baby names`,
        `Hindu names with meaning`
      )

      if (nameData.rashi) {
        keywords.push(`${nameData.rashi} names`, `Hindu astrology names`)
      }
    }

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (process.env.NODE_ENV === 'development' ? 'http://localhost:3001' : 'https://www.babynamediaries.com')
    const canonicalUrl = `${baseUrl}/name/${encodeURIComponent(decodedLanguage)}/${encodeURIComponent(decodedGender)}/${encodeURIComponent(decodedReligion)}/${encodeURIComponent(decodedName)}/`

    return {
      title,
      description,
      keywords: keywords.join(", "),

      // Enhanced OpenGraph for social sharing
      openGraph: {
        title: `${capitalizedName} - ${capitalizedLanguage} ${genderLabel} Name Meaning 2025`,
        description: `Discover the beautiful meaning of ${capitalizedName}: "${nameData.meaning_en}". Find origin, pronunciation & popularity trends. Test how ${capitalizedName} sounds with your surname using our compatibility checker!`,
        url: canonicalUrl,
        siteName: "BabyNamesDiary",
        type: "article",
        images: [
          {
            url: `${baseUrl}/icons/logo_full_hd.png`,
            width: 1200,
            height: 630,
            alt: `${capitalizedName} - ${capitalizedLanguage} ${genderLabel} Name Meaning`,
          },
        ],
        locale: "en_US",
      },

      // Twitter Card optimization
      twitter: {
        card: "summary_large_image",
        title: `${capitalizedName} Name Meaning & Origin 2025`,
        description: `${capitalizedName} means "${nameData.meaning_en}" - Beautiful ${capitalizedLanguage} ${genderLabel.toLowerCase()} name with ${capitalizedReligion} origins. Test compatibility with your surname!`,
        images: [`${baseUrl}/icons/logo_full_hd.png`],
      },

      // Canonical URL for SEO
      alternates: {
        canonical: canonicalUrl,
      },

      // Advanced robots configuration
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },

      // Additional metadata for rich snippets
      other: {
        'article:author': 'Baby Names Expert Team',
        'article:section': 'Baby Names',
        'article:tag': keywords.slice(0, 10).join(', '),
        'name-origin': nameData.origin || 'Unknown',
        'name-language': decodedLanguage,
        'name-gender': decodedGender,
        'name-religion': decodedReligion,
        'name-meaning': nameData.meaning_en,
        'name-compatibility': 'true',
        'compatibility-tool': 'name-surname-test',
        'speech-pronunciation': 'available',
        'name-flow-analysis': 'available',
        'initials-check': 'available'
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)

    // Fallback metadata
    const decodedName = decodeURIComponent(slug)
    const capitalizedName = decodedName.charAt(0).toUpperCase() + decodedName.slice(1)

    return {
      title: `${capitalizedName} - Baby Name Meaning & Origin 2025`,
      description: `Discover the meaning and origin of ${capitalizedName}, a beautiful baby name. Find pronunciation, popularity trends, and similar names for 2025.`,
      keywords: `${capitalizedName}, baby names, name meaning, name origin, baby name 2025`,
    }
  }
}



export default async function NameDetailPage({ params }: PageProps) {
  const { language, gender, religion, slug } = await params

  return (
    <NameDetailClient
      params={{ language, gender, religion, slug }}
    />
  )
} 