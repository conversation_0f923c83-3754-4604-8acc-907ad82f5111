"use client"

import NameCompatibilityModal from "@/components/name-compatibility-modal"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useTranslation } from "@/hooks/use-translation"
import { getNameBreadcrumbs, getRelatedNavigation, getSmartBackNavigation } from "@/lib/navigation-utils"
import type { NameData } from "@/types/name-data"
import { ArrowLeft, BarChart3, BookOpen, Calendar, Check, Copy, Crown, Globe, Heart, Info, Mail, MapPin, MessageCircle, Share2, Sparkles, Star, TestTube, TrendingUp, Trophy, Users, Volume2, Zap } from "lucide-react"
import dynamic from "next/dynamic"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

// Dynamic imports for chart components to prevent SSR issues
const PopularityRankingChart = dynamic(() => import("@/components/popularity-ranking-chart"), {
    ssr: false,
    loading: () => <div className="h-48 sm:h-64 bg-gray-100 rounded-lg animate-pulse" />
})

const PopularityTrendChart = dynamic(() => import("@/components/popularity-trend-chart"), {
    ssr: false,
    loading: () => <div className="h-48 sm:h-64 bg-gray-100 rounded-lg animate-pulse" />
})

interface NameDetailClientProps {
    params: { language: string, gender: string, religion: string, slug: string }
}

function generateNameStructuredData(nameData: NameData) {
    const properties = [
        {
            "@type": "PropertyValue",
            "name": "gender",
            "value": nameData.gender
        },
        {
            "@type": "PropertyValue",
            "name": "religion",
            "value": nameData.religion
        },
        {
            "@type": "PropertyValue",
            "name": "language",
            "value": nameData.language
        },
        {
            "@type": "PropertyValue",
            "name": "meaning",
            "value": nameData.meaning_en
        },
        {
            "@type": "PropertyValue",
            "name": "origin",
            "value": nameData.origin || "Unknown"
        }
    ]

    // Add optional properties if they exist
    if (nameData.pronunciation) {
        properties.push({
            "@type": "PropertyValue",
            "name": "pronunciation",
            "value": nameData.pronunciation
        })
    }
    if (nameData.popularity_rank) {
        properties.push({
            "@type": "PropertyValue",
            "name": "popularity_rank",
            "value": nameData.popularity_rank.toString()
        })
    }
    if (nameData.rashi) {
        properties.push({
            "@type": "PropertyValue",
            "name": "rashi",
            "value": nameData.rashi
        })
    }
    if (nameData.trending_status) {
        properties.push({
            "@type": "PropertyValue",
            "name": "trending_status",
            "value": nameData.trending_status
        })
    }

    return {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": nameData.name_en,
        "alternateName": nameData.name_native !== nameData.name_en ? nameData.name_native : undefined,
        "description": `${nameData.name_en} is a ${nameData.gender} name meaning "${nameData.meaning_en}"`,
        "birthPlace": {
            "@type": "Place",
            "name": nameData.origin || "Unknown"
        },
        "additionalProperty": properties
    }
}

function generateBreadcrumbSchema(nameData: NameData) {
    return {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": typeof window !== 'undefined' ? window.location.origin : ""
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": `${nameData.language} Names`,
                "item": typeof window !== 'undefined' ? `${window.location.origin}/${nameData.language.toLowerCase()}-names` : ""
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": `${nameData.gender} Names`,
                "item": typeof window !== 'undefined' ? `${window.location.origin}/${nameData.language.toLowerCase()}-${nameData.gender}-names` : ""
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": nameData.name_en,
                "item": typeof window !== 'undefined' ? window.location.href : ""
            }
        ]
    }
}

function generateArticleStructuredData(nameData: NameData) {
    return {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": `${nameData.name_en} - Meaning, Origin & Details`,
        "description": `Discover the meaning, origin, and details about the ${nameData.gender} name ${nameData.name_en}. ${nameData.meaning_en}`,
        "author": {
            "@type": "Organization",
            "name": "Baby Names App"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Baby Names App"
        },
        "datePublished": new Date().toISOString(),
        "dateModified": new Date().toISOString(),
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": typeof window !== 'undefined' ? window.location.href : ""
        }
    }
}

export default function NameDetailClient({ params }: NameDetailClientProps) {
    const router = useRouter()
    const [nameData, setNameData] = useState<NameData | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [copied, setCopied] = useState(false)
    const [isCompatibilityOpen, setIsCompatibilityOpen] = useState(false)
    const [isMounted, setIsMounted] = useState(false)
    const { t } = useTranslation()

    // Ensure component is mounted before rendering charts
    useEffect(() => {
        setIsMounted(true)
    }, [])

    useEffect(() => {
        const fetchNameData = async () => {
            try {
                setLoading(true)
                setError(null)

                const decodedName = decodeURIComponent(params.slug)
                const decodedLanguage = decodeURIComponent(params.language)
                const decodedGender = decodeURIComponent(params.gender)
                const decodedReligion = decodeURIComponent(params.religion)

                const response = await fetch(
                    `/api/names/detail?language=${encodeURIComponent(decodedLanguage)}&gender=${encodeURIComponent(decodedGender)}&religion=${encodeURIComponent(decodedReligion)}&name=${encodeURIComponent(decodedName)}`
                )

                if (!response.ok) {
                    throw new Error('Failed to fetch name data')
                }

                const result = await response.json()

                if (!result.success) {
                    throw new Error(result.error || 'Failed to fetch name data')
                }

                if (!result.data) {
                    throw new Error('Name not found')
                }

                setNameData(result.data)

                // Add structured data to the page
                const structuredData = generateNameStructuredData(result.data)
                const breadcrumbData = generateBreadcrumbSchema(result.data)
                const articleData = generateArticleStructuredData(result.data)

                const script1 = document.createElement('script')
                script1.type = 'application/ld+json'
                script1.innerHTML = JSON.stringify(structuredData)
                document.head.appendChild(script1)

                const script2 = document.createElement('script')
                script2.type = 'application/ld+json'
                script2.innerHTML = JSON.stringify(breadcrumbData)
                document.head.appendChild(script2)

                const script3 = document.createElement('script')
                script3.type = 'application/ld+json'
                script3.innerHTML = JSON.stringify(articleData)
                document.head.appendChild(script3)

                // Cleanup function to remove scripts when component unmounts
                return () => {
                    document.head.removeChild(script1)
                    document.head.removeChild(script2)
                    document.head.removeChild(script3)
                }
            } catch (err) {
                console.error('Error fetching name data:', err)
                setError(err instanceof Error ? err.message : 'An error occurred')
            } finally {
                setLoading(false)
            }
        }

        fetchNameData()
    }, [params.language, params.gender, params.religion, params.slug])

    const toggleFavorite = () => {
        if (!nameData) return

        const favorites = JSON.parse(localStorage.getItem('favorites') || '[]')
        const isCurrentlyFavorite = favorites.some((fav: any) => fav.name_en === nameData.name_en)

        if (isCurrentlyFavorite) {
            const updatedFavorites = favorites.filter((fav: any) => fav.name_en !== nameData.name_en)
            localStorage.setItem('favorites', JSON.stringify(updatedFavorites))
        } else {
            const updatedFavorites = [...favorites, nameData]
            localStorage.setItem('favorites', JSON.stringify(updatedFavorites))
        }

        // Force re-render by updating a dummy state
        setNameData({ ...nameData })
    }

    const speakName = () => {
        if (!nameData || !nameData.name_en) return
        const utterance = new SpeechSynthesisUtterance(nameData.name_en)
        window.speechSynthesis.speak(utterance)
    }

    const getShareUrl = () => {
        return typeof window !== 'undefined' ? window.location.href : ''
    }

    const getShareTitle = () => {
        return `${nameData?.name_en} - Baby Name Meaning & Details`
    }

    const getShareText = () => {
        return `Check out the name ${nameData?.name_en}: ${nameData?.meaning_en}`
    }

    const copyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(getShareUrl())
            setCopied(true)
            setTimeout(() => setCopied(false), 2000)
        } catch (err) {
            console.error('Failed to copy: ', err)
        }
    }

    const shareViaWhatsApp = () => {
        const url = `https://wa.me/?text=${encodeURIComponent(getShareText() + ' ' + getShareUrl())}`
        window.open(url, '_blank')
    }

    const shareViaFacebook = () => {
        const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(getShareUrl())}`
        window.open(url, '_blank')
    }

    const shareViaTwitter = () => {
        const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(getShareText())}&url=${encodeURIComponent(getShareUrl())}`
        window.open(url, '_blank')
    }

    const shareViaLinkedIn = () => {
        const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(getShareUrl())}`
        window.open(url, '_blank')
    }

    const shareViaTelegram = () => {
        const url = `https://t.me/share/url?url=${encodeURIComponent(getShareUrl())}&text=${encodeURIComponent(getShareText())}`
        window.open(url, '_blank')
    }

    const shareViaEmail = async () => {
        const subject = encodeURIComponent(getShareTitle())
        const body = encodeURIComponent(`${getShareText()}\n\n${getShareUrl()}`)

        try {
            // Try to use the mailto: protocol
            window.location.href = `mailto:?subject=${subject}&body=${body}`
        } catch (error) {
            // Fallback: copy to clipboard
            try {
                await navigator.clipboard.writeText(`${getShareTitle()}\n\n${getShareText()}\n\n${getShareUrl()}`)
                alert('Email content copied to clipboard!')
            } catch (clipboardError) {
                console.error('Failed to copy to clipboard:', clipboardError)
                alert('Unable to open email client. Please copy the URL manually.')
            }
        }
    }

    const shareViaNative = () => {
        if (navigator.share) {
            navigator.share({
                title: getShareTitle(),
                text: getShareText(),
                url: getShareUrl(),
            }).catch((error) => {
                console.error('Error sharing:', error)
            })
        }
    }

    const isNativeShareSupported = () => {
        return navigator.share !== undefined
    }

    // Helper function to get trending status display
    const getTrendingDisplay = (status: string) => {
        switch (status) {
            case 'rising':
                return { text: 'Rising', icon: TrendingUp, color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' }
            case 'falling':
                return { text: 'Falling', icon: TrendingUp, color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200' }
            case 'stable':
                return { text: 'Stable', icon: BarChart3, color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' }
            case 'new':
                return { text: 'New Entry', icon: Sparkles, color: 'text-purple-600', bg: 'bg-purple-50', border: 'border-purple-200' }
            case 'trending':
                return { text: 'Trending', icon: Zap, color: 'text-orange-600', bg: 'bg-orange-50', border: 'border-orange-200' }
            default:
                return { text: status, icon: BarChart3, color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' }
        }
    }

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
                <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 max-w-6xl">
                    <div className="space-y-6 sm:space-y-8">
                        {/* Header Skeleton */}
                        <div className="space-y-3 sm:space-y-4">
                            <Skeleton className="h-6 sm:h-8 w-24 sm:w-32" />
                            <div className="bg-white rounded-2xl sm:rounded-3xl shadow-xl p-4 sm:p-6 lg:p-8">
                                <Skeleton className="h-8 sm:h-12 w-32 sm:w-48 mb-3 sm:mb-4" />
                                <div className="flex gap-1.5 sm:gap-2 mb-4 sm:mb-6">
                                    <Skeleton className="h-5 sm:h-6 w-12 sm:w-16" />
                                    <Skeleton className="h-5 sm:h-6 w-16 sm:w-20" />
                                    <Skeleton className="h-5 sm:h-6 w-14 sm:w-18" />
                                </div>
                                <Skeleton className="h-16 sm:h-24 w-full" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
                <div className="max-w-sm sm:max-w-md mx-auto text-center">
                    <div className="bg-white rounded-2xl sm:rounded-3xl shadow-xl p-6 sm:p-8">
                        <div className="text-4xl sm:text-6xl mb-3 sm:mb-4">😞</div>
                        <h1 className="text-xl sm:text-2xl font-bold text-red-600 mb-3 sm:mb-4">Oops! Something went wrong</h1>
                        <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">{error}</p>
                        <Button onClick={() => router.back()} className="w-full text-sm sm:text-base">
                            <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                            Go Back
                        </Button>
                    </div>
                </div>
            </div>
        )
    }

    if (!nameData) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center p-4">
                <div className="max-w-sm sm:max-w-md mx-auto text-center">
                    <div className="bg-white rounded-2xl sm:rounded-3xl shadow-xl p-6 sm:p-8">
                        <div className="text-4xl sm:text-6xl mb-3 sm:mb-4">🔍</div>
                        <h1 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">Name Not Found</h1>
                        <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">The requested name could not be found in our database.</p>
                        <Button onClick={() => router.back()} className="w-full text-sm sm:text-base">
                            <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                            Go Back
                        </Button>
                    </div>
                </div>
            </div>
        )
    }

    const breadcrumbs = getNameBreadcrumbs({
        language: nameData.language,
        gender: nameData.gender,
        religion: nameData.religion,
        name: nameData.name_en
    })
    const relatedNavigation = getRelatedNavigation({
        language: nameData.language,
        gender: nameData.gender,
        religion: nameData.religion,
        name: nameData.name_en
    })
    const smartBackNavigation = getSmartBackNavigation({
        language: nameData.language,
        gender: nameData.gender,
        religion: nameData.religion,
        name: nameData.name_en
    })

    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]')
    const isFavorite = favorites.some((fav: any) => fav.name_en === nameData.name_en)

    const getGenderGradient = () => {
        switch (nameData.gender.toLowerCase()) {
            case 'boy':
                return 'from-blue-500 to-indigo-600'
            case 'girl':
                return 'from-pink-500 to-rose-600'
            default:
                return 'from-purple-500 to-indigo-600'
        }
    }

    const getGenderBg = () => {
        switch (nameData.gender.toLowerCase()) {
            case 'boy':
                return 'from-blue-50 via-white to-indigo-50'
            case 'girl':
                return 'from-pink-50 via-white to-rose-50'
            default:
                return 'from-purple-50 via-white to-indigo-50'
        }
    }

    // Build details grid items dynamically based on available data
    const detailsGridItems = []

    // Always show origin
    detailsGridItems.push({
        key: 'origin',
        component: (
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-green-200">
                <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                    <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-green-600" />
                    Origin
                </h4>
                <p className="text-gray-700 text-sm sm:text-base">{nameData.origin || "Unknown"}</p>
            </div>
        )
    })

    // Pronunciation (if available)
    if (nameData.pronunciation) {
        detailsGridItems.push({
            key: 'pronunciation',
            component: (
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-blue-200">
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <Volume2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-blue-600" />
                        Pronunciation
                    </h4>
                    <p className="text-gray-700 font-mono text-xs sm:text-sm">{nameData.pronunciation}</p>
                </div>
            )
        })
    }

    // Rashi (if available)
    if (nameData.rashi) {
        detailsGridItems.push({
            key: 'rashi',
            component: (
                <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-purple-200">
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <Star className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-purple-600" />
                        Rashi (Zodiac Sign)
                    </h4>
                    <p className="text-gray-700 text-sm sm:text-base">
                        {nameData.rashi}
                        {nameData.rashi_native && nameData.rashi_native !== nameData.rashi && (
                            <span className="text-xs sm:text-sm text-gray-600 block mt-1">({nameData.rashi_native})</span>
                        )}
                    </p>
                </div>
            )
        })
    }

    // Popularity (if available)
    if (nameData.popularity_rank) {
        detailsGridItems.push({
            key: 'popularity',
            component: (
                <div className="bg-gradient-to-br from-rose-50 to-pink-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-rose-200">
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <Trophy className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-rose-600" />
                        Popularity
                    </h4>
                    <p className="text-gray-700">
                        <span className="font-bold text-base sm:text-lg">#{nameData.popularity_rank}</span>
                        {(nameData.popularity_change || nameData.popularity_trend) && (
                            <span className="text-xs sm:text-sm text-gray-600 block">
                                {nameData.popularity_change || nameData.popularity_trend}
                            </span>
                        )}
                    </p>
                </div>
            )
        })
    }

    // Trending Status (if available)
    if (nameData.trending_status) {
        const trendingInfo = getTrendingDisplay(nameData.trending_status)
        const TrendingIcon = trendingInfo.icon

        detailsGridItems.push({
            key: 'trending',
            component: (
                <div className={`bg-gradient-to-br ${trendingInfo.bg} rounded-lg sm:rounded-xl p-3 sm:p-4 border ${trendingInfo.border}`}>
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <TrendingIcon className={`h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 ${trendingInfo.color}`} />
                        Trending Status
                    </h4>
                    <p className={`${trendingInfo.color} font-medium text-sm sm:text-base`}>{trendingInfo.text}</p>
                </div>
            )
        })
    }

    // Search Volume (if available)
    if (nameData.search_volume) {
        detailsGridItems.push({
            key: 'search_volume',
            component: (
                <div className="bg-gradient-to-br from-cyan-50 to-teal-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-cyan-200">
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-cyan-600" />
                        Search Volume
                    </h4>
                    <p className="text-gray-700 font-bold text-base sm:text-lg">{nameData.search_volume.toLocaleString()}</p>
                </div>
            )
        })
    }

    // Starting Letter (if available)
    if (nameData.starting_letter) {
        detailsGridItems.push({
            key: 'starting_letter',
            component: (
                <div className="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-amber-200">
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <BookOpen className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-amber-600" />
                        Starting Letter
                    </h4>
                    <p className="text-gray-700 font-bold text-xl sm:text-2xl">{nameData.starting_letter}</p>
                </div>
            )
        })
    }

    // Year Rankings (if available)
    if (nameData.year_2025_rank || nameData.year_2024_rank) {
        detailsGridItems.push({
            key: 'year_rankings',
            component: (
                <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg sm:rounded-xl p-3 sm:p-4 border border-indigo-200">
                    <h4 className="font-semibold text-gray-800 mb-1.5 sm:mb-2 flex items-center text-sm sm:text-base">
                        <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-indigo-600" />
                        Year Rankings
                    </h4>
                    <div className="space-y-0.5 sm:space-y-1">
                        {nameData.year_2025_rank && (
                            <p className="text-gray-700 text-xs sm:text-sm">2025: <span className="font-bold">#{nameData.year_2025_rank}</span></p>
                        )}
                        {nameData.year_2024_rank && (
                            <p className="text-gray-700 text-xs sm:text-sm">2024: <span className="font-bold">#{nameData.year_2024_rank}</span></p>
                        )}
                    </div>
                </div>
            )
        })
    }

    return (
        <div className={`min-h-screen bg-gradient-to-br ${getGenderBg()}`}>
            <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 max-w-6xl">
                {/* Navigation */}
                <div className="mb-3 sm:mb-6 lg:mb-8">
                    <Button
                        variant="ghost"
                        onClick={() => router.push(smartBackNavigation.backLink)}
                        className="mb-2 sm:mb-4 hover:bg-white/50 transition-colors text-xs sm:text-sm lg:text-base"
                    >
                        <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2" />
                        {smartBackNavigation.backLabel}
                    </Button>
                </div>

                {/* Hero Section */}
                <div className="mb-4 sm:mb-6 lg:mb-8">
                    <div className="bg-white rounded-2xl sm:rounded-3xl shadow-xl overflow-hidden">
                        {/* Header with gradient */}
                        <div className={`bg-gradient-to-r ${getGenderGradient()} p-4 sm:p-6 lg:p-8 text-white`}>
                            <div className="flex items-start justify-between gap-2 sm:gap-3">
                                <div className="flex-1 min-w-0">
                                    <div className="flex flex-col sm:flex-row sm:items-center gap-1.5 sm:gap-2 lg:gap-4 mb-2 sm:mb-3 lg:mb-4">
                                        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight break-words">
                                            {nameData.name_en}
                                        </h1>
                                        {nameData.name_native && nameData.name_native !== nameData.name_en && (
                                            <span className="text-sm sm:text-base lg:text-lg xl:text-xl text-white/80 font-medium">
                                                ({nameData.name_native})
                                            </span>
                                        )}
                                    </div>
                                    <div className="flex flex-wrap gap-1 sm:gap-1.5 lg:gap-2 mb-2 sm:mb-3 lg:mb-4">
                                        <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs sm:text-sm">
                                            <Users className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1" />
                                            {nameData.gender}
                                        </Badge>
                                        <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs sm:text-sm">
                                            <Globe className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1" />
                                            {nameData.language}
                                        </Badge>
                                        <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs sm:text-sm">
                                            <BookOpen className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1" />
                                            {nameData.religion}
                                        </Badge>
                                        {nameData.trending_status && (
                                            <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 text-xs sm:text-sm">
                                                <Zap className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1" />
                                                {getTrendingDisplay(nameData.trending_status).text}
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col sm:flex-row gap-1.5 sm:gap-2 flex-shrink-0">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={speakName}
                                        className="bg-white/20 hover:bg-white/30 text-white border-white/30 h-7 w-7 sm:h-8 sm:w-8 lg:h-9 lg:w-9"
                                        title="Pronounce name"
                                    >
                                        <Volume2 className="h-3 w-3 sm:h-4 sm:w-4" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={toggleFavorite}
                                        className={`${isFavorite ? 'bg-white text-red-500' : 'bg-white/20 text-white border-white/30'} hover:bg-white/30 h-7 w-7 sm:h-8 sm:w-8 lg:h-9 lg:w-9`}
                                        title={isFavorite ? "Remove from favorites" : "Add to favorites"}
                                    >
                                        <Heart className={`h-3 w-3 sm:h-4 sm:w-4 ${isFavorite ? 'fill-red-500' : ''}`} />
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="p-3 sm:p-4 lg:p-6 xl:p-8">
                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                                {/* Main Content */}
                                <div className="lg:col-span-2 space-y-3 sm:space-y-4 lg:space-y-6">
                                    {/* Meaning - Featured */}
                                    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl sm:rounded-2xl p-3 sm:p-4 lg:p-6 border border-yellow-200">
                                        <h3 className="font-bold text-base sm:text-lg lg:text-xl mb-2 sm:mb-3 flex items-center text-gray-800">
                                            <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 mr-2 sm:mr-3 text-yellow-600" />
                                            Meaning & Significance
                                        </h3>
                                        <p className="text-gray-800 text-sm sm:text-base lg:text-lg leading-relaxed">{nameData.meaning_en}</p>
                                        {nameData.meaning_native && nameData.meaning_native !== nameData.meaning_en && (
                                            <p className="text-gray-700 mt-2 sm:mt-3 text-xs sm:text-sm lg:text-base italic">{nameData.meaning_native}</p>
                                        )}
                                    </div>

                                    {/* Details Grid - Mobile-first responsive */}
                                    {detailsGridItems.length > 0 && (
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 lg:gap-4">
                                            {detailsGridItems.map((item) => (
                                                <div key={item.key}>
                                                    {item.component}
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    {/* USA Popularity Charts - Mobile optimized */}
                                    {isMounted && nameData.historical_data && Object.keys(nameData.historical_data).length > 0 && (
                                        <div className="space-y-3 sm:space-y-4 lg:space-y-6">
                                            <div className="w-full">
                                                <PopularityTrendChart nameData={nameData} className="w-full" />
                                            </div>
                                            <div className="w-full">
                                                <PopularityRankingChart nameData={nameData} className="w-full" />
                                            </div>
                                        </div>
                                    )}

                                    {/* Regional Popularity - Mobile optimized */}
                                    {nameData.regional_popularity && Object.keys(nameData.regional_popularity).length > 0 && (
                                        <Card className="border-0 shadow-md">
                                            <CardHeader className="pb-2 sm:pb-3">
                                                <CardTitle className="flex items-center text-sm sm:text-base lg:text-lg">
                                                    <MapPin className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 text-green-600" />
                                                    Regional Popularity
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3">
                                                    {Object.entries(nameData.regional_popularity).map(([region, rank]) => (
                                                        <div key={region} className="bg-gray-50 rounded-lg p-2 sm:p-3 text-center">
                                                            <div className="text-xs sm:text-sm text-gray-600 capitalize mb-1">{region}</div>
                                                            <div className="font-bold text-sm sm:text-base lg:text-lg text-blue-600">#{rank}</div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Usage Stats */}
                                    {nameData.usage_stats && (
                                        <Card className="border-0 shadow-md">
                                            <CardHeader className="pb-2 sm:pb-3">
                                                <CardTitle className="flex items-center text-sm sm:text-base lg:text-lg">
                                                    <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 text-blue-600" />
                                                    Usage Statistics
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-gray-700 leading-relaxed text-xs sm:text-sm lg:text-base">{nameData.usage_stats}</p>
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Additional Information */}
                                    {(nameData.cultural_significance || (nameData.famous_people && nameData.famous_people.length > 0) || (nameData.variations && nameData.variations.length > 0)) && (
                                        <div className="space-y-3 sm:space-y-4">
                                            {/* Cultural Significance */}
                                            {nameData.cultural_significance && (
                                                <Card className="border-0 shadow-md">
                                                    <CardHeader className="pb-2 sm:pb-3">
                                                        <CardTitle className="flex items-center text-sm sm:text-base lg:text-lg">
                                                            <Info className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 text-blue-600" />
                                                            Cultural Significance
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <p className="text-gray-700 leading-relaxed text-xs sm:text-sm lg:text-base">{nameData.cultural_significance}</p>
                                                    </CardContent>
                                                </Card>
                                            )}

                                            {/* Famous People - Mobile optimized grid */}
                                            {nameData.famous_people && nameData.famous_people.length > 0 && (
                                                <Card className="border-0 shadow-md">
                                                    <CardHeader className="pb-2 sm:pb-3">
                                                        <CardTitle className="flex items-center text-sm sm:text-base lg:text-lg">
                                                            <Crown className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 text-yellow-600" />
                                                            Notable People
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                                                            {nameData.famous_people.map((person, index) => (
                                                                <div key={index} className="bg-gray-50 rounded-lg p-2 sm:p-3 lg:p-4 hover:bg-gray-100 transition-colors">
                                                                    <div className="font-semibold text-gray-800 text-xs sm:text-sm lg:text-base">{person.name}</div>
                                                                    <div className="text-xs sm:text-sm text-blue-600 mb-1">{person.profession}</div>
                                                                    {person.description && (
                                                                        <div className="text-xs sm:text-sm text-gray-600">{person.description}</div>
                                                                    )}
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            )}

                                            {/* Variations - Mobile optimized */}
                                            {nameData.variations && nameData.variations.length > 0 && (
                                                <Card className="border-0 shadow-md">
                                                    <CardHeader className="pb-2 sm:pb-3">
                                                        <CardTitle className="text-sm sm:text-base lg:text-lg">Name Variations</CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <div className="flex flex-wrap gap-1 sm:gap-1.5 lg:gap-2">
                                                            {nameData.variations.map((variation, index) => (
                                                                <Badge key={index} variant="secondary" className="text-xs sm:text-sm py-1 px-2 sm:px-3">
                                                                    {variation}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            )}
                                        </div>
                                    )}
                                </div>

                                {/* Sidebar - Mobile optimized */}
                                <div className="space-y-3 sm:space-y-4 lg:space-y-6 order-last lg:order-none">
                                    {/* Quick Actions - Mobile optimized */}
                                    <Card className="border-0 shadow-md">
                                        <CardHeader className="pb-2 sm:pb-3">
                                            <CardTitle className="flex items-center text-sm sm:text-base lg:text-lg">
                                                <Share2 className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2" />
                                                Share This Name
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-1.5 sm:space-y-2">
                                            {isNativeShareSupported() && (
                                                <Button
                                                    onClick={shareViaNative}
                                                    className="w-full justify-start text-xs sm:text-sm lg:text-base"
                                                    variant="ghost"
                                                >
                                                    <Share2 className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                                    Share
                                                </Button>
                                            )}

                                            <Button
                                                onClick={shareViaWhatsApp}
                                                className="w-full justify-start text-xs sm:text-sm lg:text-base"
                                                variant="ghost"
                                            >
                                                <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                                WhatsApp
                                            </Button>

                                            <Button
                                                onClick={copyToClipboard}
                                                className="w-full justify-start text-xs sm:text-sm lg:text-base"
                                                variant="ghost"
                                            >
                                                {copied ? (
                                                    <>
                                                        <Check className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                                        Copied!
                                                    </>
                                                ) : (
                                                    <>
                                                        <Copy className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                                        Copy Link
                                                    </>
                                                )}
                                            </Button>

                                            {/* Mobile-optimized share buttons grid */}
                                            <div className="grid grid-cols-2 gap-1 sm:gap-1.5 lg:gap-2 pt-2">
                                                <Button onClick={shareViaFacebook} variant="outline" size="sm" className="text-xs sm:text-sm">
                                                    Facebook
                                                </Button>
                                                <Button onClick={shareViaTwitter} variant="outline" size="sm" className="text-xs sm:text-sm">
                                                    Twitter
                                                </Button>
                                                <Button onClick={shareViaLinkedIn} variant="outline" size="sm" className="text-xs sm:text-sm">
                                                    LinkedIn
                                                </Button>
                                                <Button onClick={shareViaEmail} variant="outline" size="sm" className="text-xs sm:text-sm">
                                                    <Mail className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1" />
                                                    Email
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Related Names */}
                                    <Card className="border-0 shadow-md">
                                        <CardHeader className="pb-2 sm:pb-3">
                                            <CardTitle className="text-sm sm:text-base lg:text-lg">Related Names</CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-1.5 sm:space-y-2">
                                            {relatedNavigation.map((item, index) => (
                                                <Link
                                                    key={index}
                                                    href={item.href}
                                                    className="block p-2 sm:p-2.5 lg:p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                                                >
                                                    <div className="font-semibold text-blue-600 group-hover:text-blue-800 text-xs sm:text-sm lg:text-base">{item.name}</div>
                                                    <div className="text-xs sm:text-sm text-gray-600">{item.description}</div>
                                                </Link>
                                            ))}
                                        </CardContent>
                                    </Card>

                                    {/* Name Compatibility Checker */}
                                    <Card className="border-0 shadow-md">
                                        <CardHeader className="pb-2 sm:pb-3">
                                            <CardTitle className="flex items-center text-sm sm:text-base lg:text-lg">
                                                <TestTube className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 text-purple-600" />
                                                Name Compatibility
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-2 sm:space-y-3">
                                            <p className="text-xs sm:text-sm text-gray-600">
                                                Test how {nameData?.name_en} sounds with your surname
                                            </p>
                                            <Button
                                                onClick={() => setIsCompatibilityOpen(true)}
                                                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white text-xs sm:text-sm lg:text-base"
                                            >
                                                <TestTube className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                                Test Compatibility
                                            </Button>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Name Compatibility Modal */}
            {nameData && (
                <NameCompatibilityModal
                    babyName={nameData.name_en}
                    isOpen={isCompatibilityOpen}
                    onClose={() => setIsCompatibilityOpen(false)}
                />
            )}
        </div>
    )
} 