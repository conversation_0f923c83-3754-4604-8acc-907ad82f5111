import BabyNameGeneratorPage from "@/components/BabyNameGenerator";
import { generatePageMetadata } from "@/lib/metadata-utils";

export const metadata = generatePageMetadata({
    path: "/baby-name-generator",
    title: "AI Baby Name Generator 2025 - Personalized, Unique & Trending Name Suggestions",
    description:
        "Generate the perfect baby name with our advanced AI-powered baby name generator. Get personalized, unique, and trending name suggestions based on meaning, origin, style, culture, and your preferences. Discover 25,000+ names from around the world, tailored just for your baby.",
    keywords: [
        "AI baby name generator",
        "baby name generator",
        "personalized baby names",
        "unique baby names",
        "trending baby names",
        "name suggestions",
        "baby name finder",
        "AI name suggestions",
        "custom baby names",
        "intelligent name generator",
        "modern baby names",
        "traditional baby names",
        "international baby names",
        "cultural baby names",
        "religious baby names",
        "boy names",
        "girl names",
        "unisex baby names",
        "baby name quiz",
        "name compatibility",
        "name matching tool",
        "baby name search",
        "baby name meanings",
        "baby name origins",
        "baby name trends 2025",
        "popular baby names",
        "rare baby names",
        "creative baby names",
        "family name generator",
        "surname matching names",
        "parenting tools",
        "expecting parents",
        "find baby name",
        "choose baby name",
        "best baby names 2025"
    ],
    lastModified: new Date().toISOString(),
});

export default function Page() {
    return <BabyNameGeneratorPage />;
} 