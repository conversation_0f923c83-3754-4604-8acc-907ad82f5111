import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/switzerland/french-girl-names",
  title: "Swiss French Girl Names 2025 - Switzerland Baby Names with Meanings",
  description: "Discover beautiful Swiss French girl names for 2025. Find traditional and modern French baby girl names popular in Switzerland with meanings, origins, and cultural significance.",
  keywords: [
    "Swiss French girl names",
    "Switzerland baby names",
    "French girl names Switzerland",
    "Swiss baby girl names",
    "French names with meanings",
    "Swiss French names 2025",
    "Switzerland girl names",
    "French baby names",
    "Swiss names for girls",
    "French Swiss names"
  ]
})

export default function SwissFrenchGirlNamesPage() {
  return (
    <LanguageNamesPage
      language="french"
      country="switzerland"
      colorTheme="pink"
      apiEndpoint="/api/names/switzerland/french/girl"
      headerLabels={{
        title: "Swiss French Girl Names",
        subtitle: "Discover beautiful French girl names popular in Switzerland",
        description: "Explore traditional and modern French baby girl names with meanings, origins, and cultural significance from Switzerland."
      }}
      defaultGender="female"
      showGenderFilter={false}
    />
  )
}
