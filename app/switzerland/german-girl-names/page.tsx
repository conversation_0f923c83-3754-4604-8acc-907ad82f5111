import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/switzerland/german-girl-names",
  title: "Swiss German Girl Names 2025 - Switzerland Baby Names with Meanings",
  description: "Discover beautiful Swiss German girl names for 2025. Find traditional and modern German baby girl names popular in Switzerland with meanings, origins, and cultural significance.",
  keywords: [
    "Swiss German girl names",
    "Switzerland baby names",
    "German girl names Switzerland",
    "Swiss baby girl names",
    "German names with meanings",
    "Swiss German names 2025",
    "Switzerland girl names",
    "German baby names",
    "Swiss names for girls",
    "German Swiss names"
  ]
})

export default function SwissGermanGirlNamesPage() {
  return (
    <LanguageNamesPage
      language="German"
      country="Switzerland"
      religions={["Christian"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="female"
      colorTheme="green"
      apiEndpoint="/api/names/countries/switzerland/languages/german/girl-names"
      headerLabels={{
        title: "Swiss German Girl Names",
        subtitle: "Traditional & Modern German Names in Switzerland",
        description: "Discover authentic Swiss German girl names with Alpine heritage and modern Swiss culture"
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
    />
  )
}
