import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/switzerland/french-boy-names",
  title: "Swiss French Boy Names 2025 - Switzerland Baby Names with Meanings",
  description: "Discover beautiful Swiss French boy names for 2025. Find traditional and modern French baby boy names popular in Switzerland with meanings, origins, and cultural significance.",
  keywords: [
    "Swiss French boy names",
    "Switzerland baby names",
    "French boy names Switzerland",
    "Swiss baby boy names",
    "French names with meanings",
    "Swiss French names 2025",
    "Switzerland boy names",
    "French baby names",
    "Swiss names for boys",
    "French Swiss names"
  ]
})

export default function SwissFrenchBoyNamesPage() {
  return (
    <LanguageNamesPage
      language="french"
      country="switzerland"
      colorTheme="blue"
      apiEndpoint="/api/names/switzerland/french/boy"
      headerLabels={{
        title: "Swiss French Boy Names",
        subtitle: "Discover beautiful French boy names popular in Switzerland",
        description: "Explore traditional and modern French baby boy names with meanings, origins, and cultural significance from Switzerland."
      }}
      defaultGender="male"
      showGenderFilter={false}
    />
  )
}
