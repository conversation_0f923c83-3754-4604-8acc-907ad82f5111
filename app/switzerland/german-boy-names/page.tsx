import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata({
  path: "/switzerland/german-boy-names",
  title: "Swiss German Boy Names 2025 - Switzerland Baby Names with Meanings",
  description: "Discover beautiful Swiss German boy names for 2025. Find traditional and modern German baby boy names popular in Switzerland with meanings, origins, and cultural significance.",
  keywords: [
    "Swiss German boy names",
    "Switzerland baby names",
    "German boy names Switzerland",
    "Swiss baby boy names",
    "German names with meanings",
    "Swiss German names 2025",
    "Switzerland boy names",
    "German baby names",
    "Swiss names for boys",
    "German Swiss names"
  ]
})

export default function SwissGermanBoyNamesPage() {
  return (
    <LanguageNamesPage
      language="German"
      country="Switzerland"
      religions={["Christian"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="male"
      colorTheme="green"
      apiEndpoint="/api/names/countries/switzerland/languages/german/boy-names"
      headerLabels={{
        title: "Swiss German Boy Names",
        subtitle: "Traditional & Modern German Names in Switzerland",
        description: "Discover authentic Swiss German boy names with Alpine heritage and modern Swiss culture"
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
    />
  )
}
