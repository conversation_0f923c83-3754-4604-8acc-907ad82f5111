import { allNames } from "@/data"
import type { NameData } from "@/types/name-data"
import { NextRequest, NextResponse } from "next/server"

interface GeneratorPreferences {
    gender?: string
    language?: string
    religion?: string
    origin?: string
    startingLetter?: string
    meaning?: string
    popularity?: string
    length?: string
    style?: string
    country?: string
}

// Helper function to calculate name score based on preferences
function calculateNameScore(name: NameData, preferences: GeneratorPreferences): number {
    let score = 0

    // Gender preference (highest weight)
    if (preferences.gender) {
        const genders = preferences.gender.split(',').map(g => g.trim().toLowerCase())
        const nameGender = name.gender.toLowerCase()
        // Map male/female to boy/girl for compatibility
        const mappedGender = nameGender === 'male' ? 'boy' : nameGender === 'female' ? 'girl' : nameGender
        if (genders.includes(mappedGender) || genders.includes('unisex')) {
            score += 50
        } else {
            score -= 30
        }
    }

    // Language preference
    if (preferences.language) {
        const languages = preferences.language.split(',').map(l => l.trim().toLowerCase())
        const nameLanguage = name.language.toLowerCase()
        if (languages.some(lang => nameLanguage.includes(lang))) {
            score += 20
        }
    }

    // Religion preference
    if (preferences.religion) {
        const religions = preferences.religion.split(',').map(r => r.trim().toLowerCase())
        if (religions.includes(name.religion.toLowerCase())) {
            score += 15
        }
    }

    // Origin preference
    if (preferences.origin) {
        const origins = preferences.origin.split(',').map(o => o.trim().toLowerCase())
        const nameOrigin = name.origin.toLowerCase()
        if (origins.some(origin => nameOrigin.includes(origin))) {
            score += 10
        }
    }

    // Starting letter preference
    if (preferences.startingLetter) {
        const letters = preferences.startingLetter.split(',').map(l => l.trim().toUpperCase())
        if (letters.includes(name.starting_letter)) {
            score += 15
        }
    }

    // Meaning preference (search in meaning)
    if (preferences.meaning && name.meaning_en.toLowerCase().includes(preferences.meaning.toLowerCase())) {
        score += 25
    }

    // Popularity preference
    if (preferences.popularity) {
        const popularityTypes = preferences.popularity.split(',').map(p => p.trim().toLowerCase())
        const namePopularityRank = name.popularity_rank || 1000

        if (popularityTypes.includes('popular') && namePopularityRank <= 100) {
            score += 15
        }
        if (popularityTypes.includes('unique') && namePopularityRank > 500) {
            score += 15
        }
        if (popularityTypes.includes('trending') && name.trending_status === "rising") {
            score += 20
        }
    }

    // Length preference
    if (preferences.length) {
        const lengths = preferences.length.split(',').map(l => l.trim().toLowerCase())
        const nameLength = name.name_en.length

        if (lengths.includes('short') && nameLength <= 4) {
            score += 10
        }
        if (lengths.includes('medium') && nameLength >= 5 && nameLength <= 7) {
            score += 10
        }
        if (lengths.includes('long') && nameLength >= 8) {
            score += 10
        }
    }

    // Style preference
    if (preferences.style) {
        const styles = preferences.style.split(',').map(s => s.trim().toLowerCase())
        const meaning = name.meaning_en.toLowerCase()
        const origin = name.origin.toLowerCase()

        if (styles.includes('traditional') && (origin.includes("latin") || origin.includes("greek") || origin.includes("hebrew"))) {
            score += 10
        }
        if (styles.includes('modern') && (origin.includes("modern") || origin.includes("contemporary"))) {
            score += 10
        }
        if (styles.includes('nature') && (meaning.includes("flower") || meaning.includes("tree") || meaning.includes("river") ||
            meaning.includes("mountain") || meaning.includes("star") || meaning.includes("sun"))) {
            score += 15
        }
        if (styles.includes('biblical') && (origin.includes("hebrew") || origin.includes("biblical") || meaning.includes("god"))) {
            score += 10
        }
    }

    // Country preference
    if (preferences.country) {
        const countries = preferences.country.split(',').map(c => c.trim().toLowerCase())
        const countryMap: { [key: string]: string[] } = {
            "usa": ["English"],
            "uk": ["English"],
            "canada": ["English", "French"],
            "australia": ["English"],
            "germany": ["German"],
            "france": ["French"],
            "netherlands": ["Dutch"],
            "sweden": ["Swedish"],
            "switzerland": ["German", "French"],
            "austria": ["German"],
            "belgium": ["Dutch", "French"],
            "india": ["Hindi", "Gujarati", "Bengali", "Marathi", "Punjabi", "Tamil", "Urdu"]
        }

        const countryLanguages = countries.flatMap(country => countryMap[country] || [])
        if (countryLanguages.includes(name.language)) {
            score += 15
        }
    }

    // Bonus for trending names
    if (name.trending_status === "rising") {
        score += 5
    }

    // Bonus for names with good search volume
    if (name.search_volume && name.search_volume > 10000) {
        score += 3
    }

    return score
}

// Helper function to get style suggestions based on meaning
function getStyleFromMeaning(meaning: string): string[] {
    const meaningLower = meaning.toLowerCase()
    const styles = []

    if (meaningLower.includes("flower") || meaningLower.includes("tree") || meaningLower.includes("river")) {
        styles.push("nature")
    }
    if (meaningLower.includes("god") || meaningLower.includes("lord") || meaningLower.includes("divine")) {
        styles.push("biblical")
    }
    if (meaningLower.includes("warrior") || meaningLower.includes("strength") || meaningLower.includes("brave")) {
        styles.push("traditional")
    }
    if (meaningLower.includes("modern") || meaningLower.includes("new") || meaningLower.includes("contemporary")) {
        styles.push("modern")
    }

    return styles.length > 0 ? styles : ["traditional"]
}

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)

        // Parse preferences from query parameters
        const preferences: GeneratorPreferences = {
            gender: searchParams.get("gender") || undefined,
            language: searchParams.get("language") || undefined,
            religion: searchParams.get("religion") || undefined,
            origin: searchParams.get("origin") || undefined,
            startingLetter: searchParams.get("startingLetter") || undefined,
            meaning: searchParams.get("meaning") || undefined,
            popularity: searchParams.get("popularity") || undefined,
            length: searchParams.get("length") || undefined,
            style: searchParams.get("style") || undefined,
            country: searchParams.get("country") || undefined
        }

        const limit = parseInt(searchParams.get("limit") || "10")
        const includeDetails = searchParams.get("includeDetails") === "true"

        // Filter names based on basic criteria first
        let filteredNames = [...allNames]

        // Apply basic filters
        if (preferences.gender) {
            const genders = preferences.gender.split(',').map(g => g.trim().toLowerCase())
            filteredNames = filteredNames.filter(name => {
                const nameGender = name.gender.toLowerCase()
                // Map male/female to boy/girl for compatibility
                const mappedGender = nameGender === 'male' ? 'boy' : nameGender === 'female' ? 'girl' : nameGender
                return genders.includes(mappedGender) || genders.includes('unisex')
            })
        }

        if (preferences.language) {
            const languages = preferences.language.split(',').map(l => l.trim().toLowerCase())
            filteredNames = filteredNames.filter(name =>
                languages.some(lang => name.language.toLowerCase().includes(lang))
            )
        }

        if (preferences.religion) {
            const religions = preferences.religion.split(',').map(r => r.trim().toLowerCase())
            filteredNames = filteredNames.filter(name =>
                religions.includes(name.religion.toLowerCase())
            )
        }

        if (preferences.startingLetter) {
            const letters = preferences.startingLetter.split(',').map(l => l.trim().toUpperCase())
            filteredNames = filteredNames.filter(name =>
                letters.includes(name.starting_letter)
            )
        }

        // Calculate scores for remaining names
        const scoredNames = filteredNames.map(name => ({
            ...name,
            score: calculateNameScore(name, preferences),
            styles: getStyleFromMeaning(name.meaning_en)
        }))

        // Sort by score (highest first) and take top results
        const topNames = scoredNames
            .filter(name => name.score > 0) // Only include names with positive scores
            .sort((a, b) => b.score - a.score)
            .slice(0, limit)

        // Prepare response data
        const responseData = topNames.map(name => {
            const baseData = {
                name: name.name_en,
                nativeName: name.name_native,
                meaning: name.meaning_en,
                origin: name.origin,
                gender: name.gender,
                language: name.language,
                religion: name.religion,
                pronunciation: name.pronunciation,
                startingLetter: name.starting_letter,
                score: name.score,
                styles: name.styles
            }

            if (includeDetails) {
                return {
                    ...baseData,
                    popularityRank: name.popularity_rank,
                    trendingStatus: name.trending_status,
                    searchVolume: name.search_volume,
                    rashi: name.rashi,
                    nakshatra: name.nakshatra,
                    regionalPopularity: name.regional_popularity
                }
            }

            return baseData
        })

        // Generate suggestions for improvement
        const suggestions = []
        if (topNames.length < limit) {
            suggestions.push("Try relaxing some filters to get more suggestions")
        }
        if (preferences.meaning && topNames.length < 5) {
            suggestions.push("Try different meaning keywords")
        }
        if (preferences.style && topNames.length < 5) {
            suggestions.push("Try different style preferences")
        }

        return NextResponse.json({
            success: true,
            data: {
                names: responseData,
                totalGenerated: responseData.length,
                preferences: preferences,
                suggestions: suggestions
            },
            metadata: {
                totalNamesInDatabase: allNames.length,
                filtersApplied: Object.keys(preferences).filter(key => preferences[key as keyof GeneratorPreferences]),
                generationTimestamp: new Date().toISOString()
            }
        })

    } catch (error) {
        console.error("Name Generator API Error:", error)
        return NextResponse.json(
            {
                success: false,
                error: "Failed to generate names",
                details: error instanceof Error ? error.message : "Unknown error"
            },
            { status: 500 }
        )
    }
} 