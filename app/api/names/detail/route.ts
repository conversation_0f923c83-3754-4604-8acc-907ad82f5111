import { NextRequest, NextResponse } from "next/server"
import { allNames } from "@/data"
import type { NameData } from "@/types/name-data"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get("language")
    const gender = searchParams.get("gender")
    const religion = searchParams.get("religion")
    const name = searchParams.get("name")

    if (!language || !gender || !religion || !name) {
      return NextResponse.json(
        { success: false, error: "Missing required parameters: language, gender, religion, name" },
        { status: 400 }
      )
    }

    // Find the specific name
    const nameData = allNames.find(n => 
      n.language.toLowerCase() === language.toLowerCase() &&
      n.gender.toLowerCase() === gender.toLowerCase() &&
      n.religion.toLowerCase() === religion.toLowerCase() &&
      n.name_en.toLowerCase() === name.toLowerCase()
    )

    if (!nameData) {
      return NextResponse.json(
        { success: false, error: "Name not found" },
        { status: 404 }
      )
    }

    // Get related names (same language, gender, religion)
    const relatedNames = allNames.filter(n => 
      n.language.toLowerCase() === language.toLowerCase() &&
      n.gender.toLowerCase() === gender.toLowerCase() &&
      n.religion.toLowerCase() === religion.toLowerCase() &&
      n.name_en.toLowerCase() !== name.toLowerCase()
    ).slice(0, 6) // Limit to 6 related names

    return NextResponse.json({
      success: true,
      data: nameData,
      relatedNames,
    })
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
} 