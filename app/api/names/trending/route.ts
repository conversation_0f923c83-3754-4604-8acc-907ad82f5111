import type { NameData } from "@/types/name-data"
import { NextRequest, NextResponse } from "next/server"

// Import all country data with correct export names
import { EnglishBoyNames as AustraliaBoyNames } from "@/data/countries/australia/languages/english/boy-names"
import { EnglishGirlNames as AustraliaGirlNames } from "@/data/countries/australia/languages/english/girl-names"
import { CanadianEnglishBoyNames as CanadaBoyNames } from "@/data/countries/canada/languages/english/boy-names"
import { CanadianEnglishGirlNames as CanadaGirlNames } from "@/data/countries/canada/languages/english/girl-names"
import { EnglishBoyNames as UKBoyNames } from "@/data/countries/uk/languages/english/boy-names"
import { EnglishGirlNames as UKGirlNames } from "@/data/countries/uk/languages/english/girl-names"
import { EnglishBoyNames as USABoyNames } from "@/data/countries/usa/languages/english/boy-names"
import { EnglishGirlNames as USAGirlNames } from "@/data/countries/usa/languages/english/girl-names"

// Mock trending data for all 12 countries
const mockTrendingData = {
  "usa": {
    boys: USABoyNames.slice(0, 100).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    girls: USAGirlNames.slice(0, 100).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    flag: "🇺🇸",
    code: "US"
  },
  "uk": {
    boys: UKBoyNames.slice(0, 80).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    girls: UKGirlNames.slice(0, 80).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    flag: "🇬🇧",
    code: "GB"
  },
  "canada": {
    boys: CanadaBoyNames.slice(0, 70).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    girls: CanadaGirlNames.slice(0, 70).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    flag: "🇨🇦",
    code: "CA"
  },
  "australia": {
    boys: AustraliaBoyNames.slice(0, 60).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    girls: AustraliaGirlNames.slice(0, 60).map((name: NameData) => ({ ...name, trending_status: "rising" as const })),
    flag: "🇦🇺",
    code: "AU"
  },
  "germany": {
    boys: generateMockNames("German", "boy", 50),
    girls: generateMockNames("German", "girl", 50),
    flag: "🇩🇪",
    code: "DE"
  },
  "france": {
    boys: generateMockNames("French", "boy", 50),
    girls: generateMockNames("French", "girl", 50),
    flag: "🇫🇷",
    code: "FR"
  },
  "netherlands": {
    boys: generateMockNames("Dutch", "boy", 40),
    girls: generateMockNames("Dutch", "girl", 40),
    flag: "🇳🇱",
    code: "NL"
  },
  "sweden": {
    boys: generateMockNames("Swedish", "boy", 40),
    girls: generateMockNames("Swedish", "girl", 40),
    flag: "🇸🇪",
    code: "SE"
  },
  "switzerland": {
    boys: generateMockNames("German", "boy", 35),
    girls: generateMockNames("German", "girl", 35),
    flag: "🇨🇭",
    code: "CH"
  },
  "austria": {
    boys: generateMockNames("German", "boy", 35),
    girls: generateMockNames("German", "girl", 35),
    flag: "🇦🇹",
    code: "AT"
  },
  "belgium": {
    boys: generateMockNames("Dutch", "boy", 35),
    girls: generateMockNames("Dutch", "girl", 35),
    flag: "🇧🇪",
    code: "BE"
  },
  "india": {
    boys: generateMockNames("Hindi", "boy", 80),
    girls: generateMockNames("Hindi", "girl", 80),
    flag: "🇮🇳",
    code: "IN"
  }
}

function generateMockNames(language: string, gender: string, count: number): NameData[] {
  const mockNames: NameData[] = []
  const baseNames = {
    "German": {
      boy: ["Noah", "Ben", "Henry", "Finn", "Leon", "Elias", "Paul", "Emil", "Max", "Felix"],
      girl: ["Emma", "Hanna", "Sofia", "Emilia", "Lina", "Mia", "Clara", "Ella", "Marie", "Anna"]
    },
    "French": {
      boy: ["Gabriel", "Raphael", "Leo", "Arthur", "Louis", "Jules", "Adam", "Lucas", "Hugo", "Liam"],
      girl: ["Emma", "Jade", "Louise", "Alice", "Chloe", "Lina", "Rose", "Anna", "Julia", "Lea"]
    },
    "Dutch": {
      boy: ["Noah", "Liam", "Lucas", "Finn", "Daan", "Sem", "Bram", "Max", "Jesse", "Milan"],
      girl: ["Emma", "Olivia", "Tess", "Sophie", "Nora", "Zoë", "Julia", "Sara", "Eva", "Lotte"]
    },
    "Swedish": {
      boy: ["Noah", "William", "Liam", "Hugo", "Oliver", "Adam", "Elias", "Lucas", "Nils", "Oscar"],
      girl: ["Alice", "Maja", "Vera", "Alma", "Astrid", "Ella", "Wilma", "Freja", "Selma", "Agnes"]
    },
    "Hindi": {
      boy: ["Arjun", "Aarav", "Vihaan", "Aditya", "Sai", "Aryan", "Krishna", "Ishaan", "Shaurya", "Atharv"],
      girl: ["Aadhya", "Diya", "Saanvi", "Ananya", "Kavya", "Arya", "Myra", "Sara", "Aanya", "Pari"]
    }
  }

  const names = baseNames[language as keyof typeof baseNames]?.[gender as 'boy' | 'girl'] || []

  for (let i = 0; i < count; i++) {
    const baseName = names[i % names.length]
    const trendingStatus = i < 15 ? "rising" : i < 25 ? "stable" : "falling"
    mockNames.push({
      name_en: baseName,
      name_native: baseName,
      gender: gender,
      religion: "All",
      language: language,
      meaning_en: "Strong and brave",
      meaning_native: "Strong and brave",
      starting_letter: baseName[0],
      pronunciation: baseName.toLowerCase(),
      origin: language,
      popularity_rank: i + 1,
      popularity_change: i < 10 ? "+15%" : i < 20 ? "+8%" : "+5%",
      year_2025_rank: i + 1,
      year_2024_rank: i + 2,
      trending_status: trendingStatus as "rising" | "stable" | "falling",
      search_volume: Math.floor(Math.random() * 50000) + 10000,
      regional_popularity: {}
    })
  }

  return mockNames
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const countries = searchParams.get("countries")?.split(",") || Object.keys(mockTrendingData)
    const type = searchParams.get("type") || "all"
    const limit = parseInt(searchParams.get("limit") || "20")

    // Aggregate trending data from multiple countries
    let globalRising: NameData[] = []
    let globalFalling: NameData[] = []
    let globalStable: NameData[] = []
    let globalNew: NameData[] = []

    countries.forEach(country => {
      const countryKey = country.toLowerCase() as keyof typeof mockTrendingData
      const data = mockTrendingData[countryKey]
      if (data) {
        const allNames = [...data.boys, ...data.girls]
        globalRising.push(...allNames.filter(name => name.trending_status === "rising"))
        globalFalling.push(...allNames.filter(name => name.trending_status === "falling"))
        globalStable.push(...allNames.filter(name => name.trending_status === "stable"))
        globalNew.push(...allNames.filter(name => name.trending_status === "new"))
      }
    })

    // Filter by type if specified
    let filteredData: NameData[] = []
    switch (type) {
      case "rising":
        filteredData = globalRising
        break
      case "falling":
        filteredData = globalFalling
        break
      case "stable":
        filteredData = globalStable
        break
      case "new":
        filteredData = globalNew
        break
      default:
        filteredData = [...globalRising, ...globalStable, ...globalFalling, ...globalNew]
    }

    const headers = {
      'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=43200',
      'Vary': 'Accept-Encoding'
    }
    return NextResponse.json({
      success: true,
      data: {
        names: filteredData.slice(0, limit),
        global_rising: globalRising.slice(0, 10),
        global_falling: globalFalling.slice(0, 10),
        global_stable: globalStable.slice(0, 10),
        global_new: globalNew.slice(0, 10),
        countries_analyzed: countries,
        total_count: filteredData.length,
        stats: {
          rising: globalRising.length,
          falling: globalFalling.length,
          stable: globalStable.length,
          new: globalNew.length
        }
      }
    }, { headers })

  } catch (error) {
    console.error("Trending API Error:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
} 