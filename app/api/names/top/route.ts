import type { NameData, TopNamesResponse, TrendingNamesData } from "@/types/name-data"
import { NextRequest, NextResponse } from "next/server"

// Import all country data
import { EnglishBoyNames as AustraliaBoyNames } from "@/data/countries/australia/languages/english/boy-names"
import { EnglishGirlNames as AustraliaGirlNames } from "@/data/countries/australia/languages/english/girl-names"
import { CanadianEnglishBoyNames } from "@/data/countries/canada/languages/english/boy-names"
import { CanadianEnglishGirlNames } from "@/data/countries/canada/languages/english/girl-names"
import { CanadianFrenchBoyNames } from "@/data/countries/canada/languages/french/boy-names"
import { CanadianFrenchGirlNames } from "@/data/countries/canada/languages/french/girl-names"
import { FrenchBoyNames } from "@/data/countries/france/languages/french/boy-names"
import { FrenchGirlNames } from "@/data/countries/france/languages/french/girl-names"
import { GermanBoyNames } from "@/data/countries/germany/languages/german/boy-names"
import { GermanGirlNames } from "@/data/countries/germany/languages/german/girl-names"
import { HindiBoyNames } from "@/data/countries/india/languages/hindi/boy-names"
import { HindiGirlNames } from "@/data/countries/india/languages/hindi/girl-names"
import { DutchBoyNames } from "@/data/countries/netherlands/languages/dutch/boy-names"
import { DutchGirlNames } from "@/data/countries/netherlands/languages/dutch/girl-names"
import { SwedishBoyNames } from "@/data/countries/sweden/languages/swedish/boy-names"
import { SwedishGirlNames } from "@/data/countries/sweden/languages/swedish/girl-names"
import { EnglishBoyNames as UKBoyNames } from "@/data/countries/uk/languages/english/boy-names"
import { EnglishGirlNames as UKGirlNames } from "@/data/countries/uk/languages/english/girl-names"
import { EnglishBoyNames as USABoyNames } from "@/data/countries/usa/languages/english/boy-names"
import { EnglishGirlNames as USAGirlNames } from "@/data/countries/usa/languages/english/girl-names"

// Comprehensive country data mapping
const countryData: { [key: string]: { boys: NameData[], girls: NameData[] } } = {
  "usa": {
    boys: USABoyNames,
    girls: USAGirlNames
  },
  "uk": {
    boys: UKBoyNames,
    girls: UKGirlNames
  },
  "canada": {
    boys: [...CanadianEnglishBoyNames, ...CanadianFrenchBoyNames],
    girls: [...CanadianEnglishGirlNames, ...CanadianFrenchGirlNames]
  },
  "australia": {
    boys: AustraliaBoyNames,
    girls: AustraliaGirlNames
  },
  "germany": {
    boys: GermanBoyNames,
    girls: GermanGirlNames
  },
  "france": {
    boys: FrenchBoyNames,
    girls: FrenchGirlNames
  },
  "netherlands": {
    boys: DutchBoyNames,
    girls: DutchGirlNames
  },
  "sweden": {
    boys: SwedishBoyNames,
    girls: SwedishGirlNames
  },
  "india": {
    boys: HindiBoyNames,
    girls: HindiGirlNames
  }
}

// Complete trending data for all 12 countries
const trendingData: { [key: string]: TrendingNamesData } = {
  "usa": {
    country: "USA",
    year: 2025,
    top_boy: "Liam",
    top_girl: "Emma",
    growth_percentage: "+15%",
    notable_trends: [
      "Hawaiian names like Kai rising rapidly",
      "Mythology names like Atlas gaining popularity",
      "Italian names like Enzo trending up",
      "Traditional names maintaining strong positions"
    ]
  },
  "uk": {
    country: "United Kingdom",
    year: 2025,
    top_boy: "Oliver",
    top_girl: "Olivia",
    growth_percentage: "+12%",
    notable_trends: [
      "Royal names seeing renewed interest",
      "Short, strong names gaining traction",
      "Classic English names holding steady",
      "Arthur experiencing major revival"
    ]
  },
  "canada": {
    country: "Canada",
    year: 2025,
    top_boy: "Oliver",
    top_girl: "Charlotte",
    growth_percentage: "+18%",
    notable_trends: [
      "Bilingual-friendly names rising",
      "Nature-inspired names popular",
      "French influences in Quebec region",
      "Indigenous names gaining recognition"
    ]
  },
  "australia": {
    country: "Australia",
    year: 2025,
    top_boy: "Oliver",
    top_girl: "Charlotte",
    growth_percentage: "+10%",
    notable_trends: [
      "Nature names reflecting Australian landscape",
      "Shortened traditional names popular",
      "Multicultural influences growing",
      "Beach-inspired names trending"
    ]
  },
  "germany": {
    country: "Germany",
    year: 2025,
    top_boy: "Noah",
    top_girl: "Emilia",
    growth_percentage: "+8%",
    notable_trends: [
      "International names gaining popularity",
      "Traditional German names stable",
      "Nature-themed names rising",
      "Short, modern variations preferred"
    ]
  },
  "france": {
    country: "France",
    year: 2025,
    top_boy: "Gabriel",
    top_girl: "Louise",
    growth_percentage: "+14%",
    notable_trends: [
      "Classic French names experiencing revival",
      "Saints' names maintaining popularity",
      "Unisex names becoming more common",
      "Regional French variations trending"
    ]
  },
  "netherlands": {
    country: "Netherlands",
    year: 2025,
    top_boy: "Noah",
    top_girl: "Julia",
    growth_percentage: "+9%",
    notable_trends: [
      "International names with Dutch appeal",
      "Shortened versions of traditional names",
      "Nature-inspired choices growing",
      "Biblical names remaining strong"
    ]
  },
  "sweden": {
    country: "Sweden",
    year: 2025,
    top_boy: "William",
    top_girl: "Alice",
    growth_percentage: "+13%",
    notable_trends: [
      "Norse mythology names returning",
      "International names with Swedish pronunciation",
      "Nature names reflecting Nordic heritage",
      "Royal family influence continuing"
    ]
  },
  "switzerland": {
    country: "Switzerland",
    year: 2025,
    top_boy: "Noah",
    top_girl: "Mia",
    growth_percentage: "+11%",
    notable_trends: [
      "Multi-language friendly names",
      "Alpine-inspired nature names",
      "Traditional Swiss names stable",
      "International business influence"
    ]
  },
  "austria": {
    country: "Austria",
    year: 2025,
    top_boy: "Noah",
    top_girl: "Emilia",
    growth_percentage: "+7%",
    notable_trends: [
      "German names with Austrian flair",
      "Classical music-inspired names",
      "Habsburg heritage names",
      "Modern European preferences"
    ]
  },
  "belgium": {
    country: "Belgium",
    year: 2025,
    top_boy: "Noah",
    top_girl: "Emma",
    growth_percentage: "+9%",
    notable_trends: [
      "Flemish and Walloon influences",
      "Multi-language compatibility",
      "European Union cultural mixing",
      "Traditional Belgian names evolving"
    ]
  },
  "india": {
    country: "India",
    year: 2025,
    top_boy: "Aarav",
    top_girl: "Aadhya",
    growth_percentage: "+22%",
    notable_trends: [
      "Modern Sanskrit names dominating",
      "Bollywood celebrity influences",
      "Tech industry cultural impact",
      "Regional language variations growing"
    ]
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const country = searchParams.get("country")?.toLowerCase() || "usa"
    const gender = searchParams.get("gender")?.toLowerCase() || "all"
    const limit = parseInt(searchParams.get("limit") || "10")
    const type = searchParams.get("type") || "top" // top, trending, new, regional
    const region = searchParams.get("region")

    // Validate country
    if (!countryData[country]) {
      return NextResponse.json({
        success: false,
        error: `Data not available for country: ${country}`
      }, { status: 404 })
    }

    const data = countryData[country]
    const trending = trendingData[country]

    // Get top names
    let boys = data.boys.slice(0, limit)
    let girls = data.girls.slice(0, limit)

    // Filter by gender if specified
    if (gender === "boy" || gender === "male") {
      girls = []
    } else if (gender === "girl" || gender === "female") {
      boys = []
    }

    // Filter by region if specified
    if (region && boys.length > 0) {
      boys = boys.filter(name => name.regional_popularity?.[region])
        .sort((a, b) => (a.regional_popularity?.[region] || 999) - (b.regional_popularity?.[region] || 999))
        .slice(0, limit)
    }

    // Get trending names based on type
    let trendingNames = {
      rising: [...data.boys, ...data.girls].filter((name: NameData) => name.trending_status === "rising").slice(0, 5),
      falling: [...data.boys, ...data.girls].filter((name: NameData) => name.trending_status === "falling").slice(0, 5),
      new: [...data.boys, ...data.girls].filter((name: NameData) => name.trending_status === "trending").slice(0, 5)
    }

    // Build response based on type
    let responseData: any = {
      country: country.toUpperCase(),
      year: 2025,
      boys,
      girls,
      trending: trendingNames
    }

    // Add regional data if requested
    if (type === "regional") {
      responseData.regional = {
        northeast: {
          boys: data.boys.sort((a, b) => (a.regional_popularity?.northeast || 999) - (b.regional_popularity?.northeast || 999)).slice(0, 5),
          girls: data.girls.sort((a, b) => (a.regional_popularity?.northeast || 999) - (b.regional_popularity?.northeast || 999)).slice(0, 5)
        },
        south: {
          boys: data.boys.sort((a, b) => (a.regional_popularity?.south || 999) - (b.regional_popularity?.south || 999)).slice(0, 5),
          girls: data.girls.sort((a, b) => (a.regional_popularity?.south || 999) - (b.regional_popularity?.south || 999)).slice(0, 5)
        },
        midwest: {
          boys: data.boys.sort((a, b) => (a.regional_popularity?.midwest || 999) - (b.regional_popularity?.midwest || 999)).slice(0, 5),
          girls: data.girls.sort((a, b) => (a.regional_popularity?.midwest || 999) - (b.regional_popularity?.midwest || 999)).slice(0, 5)
        },
        west: {
          boys: data.boys.sort((a, b) => (a.regional_popularity?.west || 999) - (b.regional_popularity?.west || 999)).slice(0, 5),
          girls: data.girls.sort((a, b) => (a.regional_popularity?.west || 999) - (b.regional_popularity?.west || 999)).slice(0, 5)
        }
      }
    }

    const response: TopNamesResponse = {
      success: true,
      data: responseData,
      metadata: {
        total_boys: data.boys.length,
        total_girls: data.girls.length,
        last_updated: "2025-01-15",
        data_source: "Baby Name Diaries Analytics"
      }
    }

    const headers = {
      'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=43200',
      'Vary': 'Accept-Encoding'
    }
    return NextResponse.json(response, { headers })

  } catch (error) {
    console.error("Top Names API Error:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Additional endpoint for trending summary
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { countries } = body

    if (!countries || !Array.isArray(countries)) {
      return NextResponse.json({
        success: false,
        error: "Invalid request: countries array required"
      }, { status: 400 })
    }

    const summary = countries.map((country: string) => {
      const countryLower = country.toLowerCase()
      const trending = trendingData[countryLower]

      if (!trending) {
        return {
          country,
          error: "No data available"
        }
      }

      return trending
    })

    return NextResponse.json({
      success: true,
      data: summary,
      metadata: {
        generated_at: new Date().toISOString(),
        countries_requested: countries.length
      }
    })

  } catch (error) {
    console.error("Trending Summary API Error:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
} 