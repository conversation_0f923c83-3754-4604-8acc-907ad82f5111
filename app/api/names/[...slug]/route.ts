import { getUniqueGenders, getUniqueLanguages, getUniqueReligions, getUniqueStartingLetters } from "@/data"
import type { NameData } from "@/types/name-data"
import { NextRequest, NextResponse } from "next/server"

// Cache for frequently accessed data
const dataCache = new Map<string, NameData[]>()

const searchNamesFromArray = (query: string, names: NameData[]): NameData[] => {
  const lowercaseQuery = query.toLowerCase()
  return names.filter(name =>
    name.name_en.toLowerCase().includes(lowercaseQuery) ||
    name.name_native.toLowerCase().includes(lowercaseQuery) ||
    name.meaning_en.toLowerCase().includes(lowercaseQuery) ||
    name.meaning_native.toLowerCase().includes(lowercaseQuery)
  )
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ slug: string[] }> }) {
  try {
    const { searchParams } = new URL(request.url)
    const resolvedParams = await params
    const slug = resolvedParams.slug || []
    const limit = searchParams.get("limit")
    const offset = searchParams.get("offset")
    const search = searchParams.get("search")
    const startingLetter = searchParams.get("startingLetter")
    const rashi = searchParams.get("rashi")
    const nakshatra = searchParams.get("nakshatra")

    // Check cache first
    const cacheKey = slug.join("/")
    let names: NameData[] = dataCache.get(cacheKey) || []

    if (names.length === 0) {
      try {
        const dataModule = await import(`@/data/${cacheKey}`)
        names = dataModule.default || []
        // Cache the data for future requests
        dataCache.set(cacheKey, names)
      } catch (e) {
        return NextResponse.json({ success: false, error: "Data not found" }, { status: 404 })
      }
    }

    // Apply search
    let filteredNames = names
    if (search) {
      filteredNames = searchNamesFromArray(search, filteredNames)
    }
    // Apply filters
    if (startingLetter) {
      filteredNames = filteredNames.filter(name => name.starting_letter === startingLetter.toUpperCase())
    }
    if (rashi) {
      filteredNames = filteredNames.filter(name => name.rashi === rashi)
    }
    if (nakshatra) {
      filteredNames = filteredNames.filter(name => name.nakshatra === nakshatra)
    }

    // Apply alphabetical sorting by default
    filteredNames.sort((a, b) => a.name_en.localeCompare(b.name_en))

    // Pagination
    const limitNum = limit ? parseInt(limit) : 20
    const offsetNum = offset ? parseInt(offset) : 0
    const paginatedNames = filteredNames.slice(offsetNum, offsetNum + limitNum)

    // Metadata
    const metadata = {
      total: filteredNames.length,
      limit: limitNum,
      offset: offsetNum,
      hasMore: offsetNum + limitNum < filteredNames.length,
      languages: getUniqueLanguages(),
      religions: getUniqueReligions(),
      genders: getUniqueGenders(),
      startingLetters: getUniqueStartingLetters(),
    }

    const response = NextResponse.json({
      success: true,
      data: paginatedNames,
      metadata,
    })

    // Add caching headers
    response.headers.set('Cache-Control', 'public, max-age=3600, s-maxage=86400')
    response.headers.set('Vary', 'Accept-Encoding')

    return response
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
} 