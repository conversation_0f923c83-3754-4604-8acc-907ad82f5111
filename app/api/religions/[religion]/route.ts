import { getNamesByReligion } from "@/data"
import { NextRequest, NextResponse } from "next/server"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ religion: string }> }
) {
  try {
    const { searchParams } = new URL(request.url)
    const resolvedParams = await params

    const search = searchParams.get("search")
    const gender = searchParams.get("gender")
    const language = searchParams.get("language")
    const startingLetter = searchParams.get("startingLetter")
    const rashi = searchParams.get("rashi")
    const limit = searchParams.get("limit")
    const offset = searchParams.get("offset")

    // Get only the specific religion data
    let religionNames = getNamesByReligion(resolvedParams.religion)

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      religionNames = religionNames.filter(name =>
        name.name_en.toLowerCase().includes(searchLower) ||
        name.name_native.toLowerCase().includes(searchLower) ||
        name.meaning_en.toLowerCase().includes(searchLower) ||
        name.meaning_native.toLowerCase().includes(searchLower)
      )
    }

    // Apply other filters
    if (gender) {
      religionNames = religionNames.filter(name => name.gender === gender)
    }

    if (language) {
      religionNames = religionNames.filter(name => name.language === language)
    }

    if (startingLetter) {
      religionNames = religionNames.filter(name => name.starting_letter === startingLetter.toUpperCase())
    }

    // Apply astrological filters (only for Hindu names)
    if (rashi && resolvedParams.religion === "Hindu") {
      religionNames = religionNames.filter(name => name.rashi === rashi)
    }

    // Apply alphabetical sorting by default
    religionNames.sort((a, b) => a.name_en.localeCompare(b.name_en))

    // Apply pagination
    const limitNum = limit ? parseInt(limit) : 20
    const offsetNum = offset ? parseInt(offset) : 0
    const paginatedNames = religionNames.slice(offsetNum, offsetNum + limitNum)

    // Get metadata for this religion only
    const uniqueLanguages = [...new Set(religionNames.map(name => name.language))]
    const uniqueGenders = [...new Set(religionNames.map(name => name.gender))]
    const uniqueStartingLetters = [...new Set(religionNames.map(name => name.starting_letter))].sort()

    const metadata = {
      total: religionNames.length,
      limit: limitNum,
      offset: offsetNum,
      hasMore: offsetNum + limitNum < religionNames.length,
      religion: resolvedParams.religion,
      languages: uniqueLanguages,
      genders: uniqueGenders,
      startingLetters: uniqueStartingLetters,
    }

    return NextResponse.json({
      success: true,
      data: paginatedNames,
      metadata,
    })
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error"
      },
      { status: 500 }
    )
  }
} 