import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.babynamediaries.com'

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          // Language-specific paths
          '/gujarati',
          '/hindi',
          '/punjabi',
          '/tamil',
          '/urdu',
          '/bengali',
          '/marathi',
          '/language/*',
          '/community/*',
          '/gender/*',
          '/blog/*',
          '/api/names',
          // CRITICAL FIX: Add all country paths that exist
          '/usa/*',
          '/uk/*',
          '/canada/*',
          '/australia/*',
          '/austria/*',
          '/belgium/*',
          '/switzerland/*',
          '/sweden/*',
          '/germany/*',
          '/france/*',
          '/netherlands/*',
          '/india/*',
          // Religious name paths
          '/religions/*',
          // Trending and popular pages
          '/trending-names',
          '/trending-baby-names-2025',
          '/popular-names-2025',
          '/popular-american-names-2025',
          '/unique-baby-names-2025',
          // Baby name generator
          '/baby-name-generator',
          // Individual name detail pages
          '/name/*',
          // Legal and informational pages
          '/about',
          '/contact',
          '/privacy-policy',
          '/terms-of-service',
        ],
        disallow: [
          '/api/_next/*',
          '/_next/*',
          '/admin/*',
          '/private/*',
          '/temp/*',
          '*.json',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        crawlDelay: 1,
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        crawlDelay: 2,
      },
    ],
    sitemap: [
      `${baseUrl}/sitemap.xml`,
      `${baseUrl}/sitemap-index.xml`,
      `${baseUrl}/sitemap-pages.xml`,
      `${baseUrl}/sitemap-names-1.xml`,
      `${baseUrl}/sitemap-names-2.xml`,
      `${baseUrl}/sitemap-countries.xml`
    ],
    host: baseUrl,
  }
}
