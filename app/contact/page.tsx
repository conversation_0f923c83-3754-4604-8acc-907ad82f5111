import ContactForm from "@/components/contact-form"
import { FAQSection } from "@/components/faq-section"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { Clock, Mail } from "lucide-react"
import Link from "next/link"

export const metadata = generatePageMetadata({
  path: "/contact",
  title: "Contact Us - Baby Names",
  description: "Contact Baby Names for support, feedback, or questions about our baby name search services.",
  keywords: [
    "contact baby names",
    "customer support",
    "baby names help",
    "feedback",
  ],
})

export default function ContactPage() {
  const faqs = [
    {
      question: "How quickly will I receive a response to my inquiry?",
      answer: "We typically respond to general questions within 24-48 hours, technical issues within 12-24 hours, and privacy concerns within 24 hours. For urgent matters, please include 'URGENT' in your subject line."
    },
    {
      question: "What types of inquiries can I contact you about?",
      answer: "You can contact us about name suggestions, technical issues, privacy concerns, business partnerships, content corrections, feature requests, or general questions about our baby name database and services."
    },
    {
      question: "Can I suggest new names to add to your database?",
      answer: "Absolutely! We welcome name suggestions from our community. Please include the name, its meaning, origin, cultural background, and any relevant historical or cultural context. Our team will review and verify the information before adding it."
    },
    {
      question: "How can I report incorrect information about a name?",
      answer: "If you find incorrect information about a name's meaning, origin, or cultural background, please contact us with the specific details and any sources that support the correction. We take accuracy seriously and will investigate promptly."
    },
    {
      question: "Do you offer business partnerships or collaborations?",
      answer: "Yes! We're open to partnerships with parenting websites, baby product companies, cultural organizations, and educational institutions. Please contact us with details about your proposed collaboration."
    },
    {
      question: "Can I request names from a specific culture or region not currently covered?",
      answer: "Yes! We're constantly expanding our database to include more cultures and regions. If you'd like to see names from a specific culture, religion, or region, please let us know and we'll prioritize adding those names."
    },
    {
      question: "How can I provide feedback about the website or suggest improvements?",
      answer: "We value your feedback! You can suggest improvements to our search features, user interface, content organization, or any other aspect of the website. Your input helps us create a better experience for all parents."
    },
    {
      question: "What should I do if I'm experiencing technical issues?",
      answer: "For technical issues, please include your browser type, device information, and a detailed description of the problem. Screenshots are helpful. We'll work to resolve the issue as quickly as possible."
    }
  ]

  return (
    <main className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          We're here to help you find the perfect baby name. Get in touch with our team for support, suggestions, or any questions you may have.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        {/* Contact Form */}
        <ContactForm />

        {/* Contact Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Support
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold">General Inquiries</h4>
                <p className="text-muted-foreground"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-semibold">Privacy & Legal</h4>
                <p className="text-muted-foreground"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-semibold">Business Partnerships</h4>
                <p className="text-muted-foreground"><EMAIL></p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Response Times
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>General Questions</span>
                <span className="text-muted-foreground">24-48 hours</span>
              </div>
              <div className="flex justify-between">
                <span>Technical Issues</span>
                <span className="text-muted-foreground">12-24 hours</span>
              </div>
              <div className="flex justify-between">
                <span>Privacy Concerns</span>
                <span className="text-muted-foreground">Within 24 hours</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mt-12">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
          Frequently Asked Questions
        </h2>
        <FAQSection faqs={faqs} />
      </div>

      {/* Quick Links */}
      <div className="mt-12 p-6 bg-muted/50 rounded-lg">
        <h2 className="text-xl font-bold mb-4">Related Pages</h2>
        <div className="grid md:grid-cols-3 gap-4">
          <Link href="/privacy-policy" className="block p-4 bg-white dark:bg-zinc-800 rounded-lg border hover:shadow-md transition-shadow">
            <h3 className="font-semibold">Privacy Policy</h3>
            <p className="text-sm text-muted-foreground">How we protect your data</p>
          </Link>
          <Link href="/terms-of-service" className="block p-4 bg-white dark:bg-zinc-800 rounded-lg border hover:shadow-md transition-shadow">
            <h3 className="font-semibold">Terms of Service</h3>
            <p className="text-sm text-muted-foreground">Our terms and conditions</p>
          </Link>
          <Link href="/blog" className="block p-4 bg-white dark:bg-zinc-800 rounded-lg border hover:shadow-md transition-shadow">
            <h3 className="font-semibold">Blog</h3>
            <p className="text-sm text-muted-foreground">Name guides and articles</p>
          </Link>
        </div>
      </div>
    </main>
  )
} 