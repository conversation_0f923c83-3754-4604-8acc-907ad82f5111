import { allNames } from '@/data'
import { MetadataRoute } from 'next'

// Cache sitemap for 1 hour to reduce server load
const SITEMAP_CACHE_DURATION = 3600 // 1 hour
let sitemapCache: MetadataRoute.Sitemap | null = null
let cacheTimestamp: number = 0

// Pre-processed names cache to avoid repeated processing
let processedNamesCache: any[] | null = null
let processedNamesCacheTimestamp: number = 0

// Maximum URLs per sitemap (Google limit is 50,000, we use 45,000 for safety)
const MAX_URLS_PER_SITEMAP = 45000

// Cache invalidation and memory management
function checkMemoryUsage() {
  if (process.memoryUsage().heapUsed > 500 * 1024 * 1024) { // 500MB threshold
    console.log('🧹 High memory usage detected, clearing caches')
    processedNamesCache = null
    processedNamesCacheTimestamp = 0
  }
}

// Utility function to get dynamic lastModified dates
function getLastModified(type: 'home' | 'trending' | 'country' | 'religion' | 'language' | 'static' | 'name'): Date {
  const now = new Date()

  switch (type) {
    case 'home':
      // Home page updates daily
      return new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000)
    case 'trending':
      // Trending pages update frequently
      return new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    case 'country':
      // Country pages update weekly
      return new Date(now.getTime() - Math.random() * 14 * 24 * 60 * 60 * 1000)
    case 'religion':
      // Religion pages update monthly
      return new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    case 'language':
      // Language pages update monthly
      return new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    case 'name':
      // Individual name pages update less frequently
      return new Date(now.getTime() - Math.random() * 60 * 24 * 60 * 60 * 1000)
    case 'static':
      // Static pages update rarely
      return new Date(now.getTime() - Math.random() * 90 * 24 * 60 * 60 * 1000)
    default:
      return now
  }
}

// Helper function to generate individual name URLs
function generateNameUrl(baseUrl: string, name: any): string {
  const language = encodeURIComponent(name.language.toLowerCase())
  const gender = encodeURIComponent(name.gender.toLowerCase())
  const religion = encodeURIComponent(name.religion.toLowerCase())
  const nameSlug = encodeURIComponent(name.name_en.toLowerCase().replace(/\s+/g, '-'))

  return `${baseUrl}/name/${language}/${gender}/${religion}/${nameSlug}`
}

// Helper function to get priority based on name popularity
function getNamePriority(name: any): number {
  // Base priority for individual names
  let priority = 0.6

  // Boost priority for popular names
  if (name.popularity_rank) {
    if (name.popularity_rank <= 10) priority = 0.8
    else if (name.popularity_rank <= 50) priority = 0.7
    else if (name.popularity_rank <= 100) priority = 0.65
  }

  // Boost for trending names
  if (name.trending_status === 'rising' || name.trending_status === 'trending') {
    priority += 0.1
  }

  // Boost for high search volume
  if (name.search_volume && name.search_volume > 10000) {
    priority += 0.05
  }

  // Cap at 0.9 (reserve 1.0 for homepage)
  return Math.min(priority, 0.9)
}

// Optimized deduplication and processing function
function getProcessedNames(): any[] {
  const now = Date.now()

  // Check if processed names cache is still valid (2 hours)
  if (processedNamesCache && processedNamesCacheTimestamp &&
    (now - processedNamesCacheTimestamp) < 7200000) { // 2 hours
    console.log(`📦 Using cached processed names (${processedNamesCache.length} names)`)
    return processedNamesCache
  }

  console.log(`⚡ Processing ${allNames.length} names for sitemap...`)
  const startTime = Date.now()

  // Use Map for O(1) deduplication instead of O(n²) findIndex
  const uniqueNamesMap = new Map<string, any>()

  for (const name of allNames) {
    const uniqueKey = `${name.name_en.toLowerCase()}-${name.language.toLowerCase()}-${name.gender.toLowerCase()}-${name.religion.toLowerCase()}`

    if (!uniqueNamesMap.has(uniqueKey)) {
      // Pre-calculate priority and URL to avoid repeated computation
      const processedName = {
        ...name,
        priority: getNamePriority(name),
        uniqueKey
      }
      uniqueNamesMap.set(uniqueKey, processedName)
    }
  }

  // Convert Map to array and sort by priority (highest first)
  const processedNames = Array.from(uniqueNamesMap.values())
    .sort((a, b) => b.priority - a.priority)

  const processingTime = Date.now() - startTime
  console.log(`📋 Deduplicated from ${allNames.length} to ${processedNames.length} unique names in ${processingTime}ms`)

  // Cache the processed names with timestamp
  processedNamesCache = processedNames
  processedNamesCacheTimestamp = Date.now()
  return processedNames
}

export default function sitemap(): MetadataRoute.Sitemap {
  // Check memory usage and clear caches if needed
  checkMemoryUsage()

  // Check if we have a valid cached sitemap
  const now = Date.now()
  if (sitemapCache && cacheTimestamp && (now - cacheTimestamp) < SITEMAP_CACHE_DURATION * 1000) {
    console.log(`📦 Serving cached sitemap with ${sitemapCache.length} URLs`)
    return sitemapCache
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.babynamediaries.com'

  const sitemap: MetadataRoute.Sitemap = [
    // ===== MAIN PAGES - HIGHEST PRIORITY =====
    {
      url: baseUrl,
      lastModified: getLastModified('home'),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/baby-name-generator`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'daily',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },

    // ===== TRENDING PAGES - HIGH SEO VALUE =====
    {
      url: `${baseUrl}/trending-names`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/usa/official-baby-names-2025`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/blog/how-to-choose-perfect-baby-name-ultimate-guide-2025`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/name-compatibility-checker`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/cultural-fusion-names`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/blog/belgian-dutch-boy-names-2025-complete-guide`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/popular-names-2025`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/trending-baby-names-2025`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/unique-baby-names-2025`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/popular-american-names-2025`,
      lastModified: getLastModified('trending'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },

    // ===== LEGAL & INFORMATIONAL PAGES - REQUIRED FOR ADSENSE =====
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: getLastModified('static'),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/terms-of-service`,
      lastModified: getLastModified('static'),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: getLastModified('static'),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: getLastModified('static'),
      changeFrequency: 'monthly',
      priority: 0.8,
    },

    // ===== COUNTRY-SPECIFIC PAGES - USA HIGHEST PRIORITY =====
    {
      url: `${baseUrl}/usa/english-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/usa/top-100-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/usa/top-100-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/usa/english-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.95,
    },

    // ===== UK PAGES - SECOND PRIORITY =====
    {
      url: `${baseUrl}/uk/english-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/uk/english-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.9,
    },

    // ===== CANADA PAGES - THIRD PRIORITY =====
    {
      url: `${baseUrl}/canada/english-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/canada/english-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/canada/french-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/canada/french-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },

    // ===== AUSTRALIA PAGES =====
    {
      url: `${baseUrl}/australia/english-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/australia/english-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== GERMANY PAGES =====
    {
      url: `${baseUrl}/germany/german-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/germany/german-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== FRANCE PAGES =====
    {
      url: `${baseUrl}/france/french-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/france/french-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== NETHERLANDS PAGES =====
    {
      url: `${baseUrl}/netherlands/dutch-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/netherlands/dutch-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== AUSTRIA PAGES =====
    {
      url: `${baseUrl}/austria/german-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/austria/german-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== BELGIUM PAGES =====
    {
      url: `${baseUrl}/belgium/dutch-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/belgium/dutch-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/belgium/french-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/belgium/french-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== SWITZERLAND PAGES =====
    {
      url: `${baseUrl}/switzerland/german-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/switzerland/german-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/switzerland/french-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/switzerland/french-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // Sweden
    {
      url: `${baseUrl}/sweden/swedish-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/sweden/swedish-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },

    // ===== INDIA PAGES - HIGH VOLUME TRAFFIC =====
    {
      url: `${baseUrl}/india/hindi-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/india/hindi-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/india/gujarati-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.75,
    },
    {
      url: `${baseUrl}/india/gujarati-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.75,
    },
    {
      url: `${baseUrl}/india/punjabi-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.75,
    },
    {
      url: `${baseUrl}/india/punjabi-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.75,
    },
    {
      url: `${baseUrl}/india/tamil-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.75,
    },
    {
      url: `${baseUrl}/india/tamil-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.75,
    },
    {
      url: `${baseUrl}/india/bengali-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/india/bengali-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/india/marathi-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/india/marathi-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/india/urdu-boy-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/india/urdu-girl-names`,
      lastModified: getLastModified('country'),
      changeFrequency: 'weekly',
      priority: 0.7,
    },

    // ===== RELIGION PAGES =====
    {
      url: `${baseUrl}/religions/christian-boy-names`,
      lastModified: getLastModified('religion'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/religions/christian-girl-names`,
      lastModified: getLastModified('religion'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/religions/muslim-boy-names`,
      lastModified: getLastModified('religion'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/religions/muslim-girl-names`,
      lastModified: getLastModified('religion'),
      changeFrequency: 'weekly',
      priority: 0.85,
    },
    {
      url: `${baseUrl}/religions/sikh-boy-names`,
      lastModified: getLastModified('religion'),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/religions/sikh-girl-names`,
      lastModified: getLastModified('religion'),
      changeFrequency: 'weekly',
      priority: 0.8,
    }
  ]

  // ===== INDIVIDUAL NAME PAGES - MASSIVE SEO VALUE =====
  console.log(`Generating sitemap for ${allNames.length} individual names...`)

  try {
    // Get pre-processed and deduplicated names
    let processedNames = getProcessedNames()

    // Validate that we're not exceeding Google's limits
    if (processedNames.length > MAX_URLS_PER_SITEMAP) {
      console.warn(`⚠️ WARNING: ${processedNames.length} names exceeding ${MAX_URLS_PER_SITEMAP} URL limit`)
      // Truncate to stay within limits
      processedNames = processedNames.slice(0, MAX_URLS_PER_SITEMAP - sitemap.length)
      console.log(`📝 Truncated to ${processedNames.length} names to stay within sitemap limits`)
    }

    // Generate name pages (names are already sorted by priority)
    const namePages = processedNames.map(name => ({
      url: generateNameUrl(baseUrl, name),
      lastModified: getLastModified('name'),
      changeFrequency: 'monthly' as const,
      priority: name.priority, // Use pre-calculated priority
    }))

    // Add name pages to sitemap
    sitemap.push(...namePages)

    console.log(`✅ Sitemap generated with ${sitemap.length} total URLs`)
    console.log(`📊 Breakdown: ${sitemap.length - namePages.length} category pages + ${namePages.length} name pages`)

    // Check final sitemap size
    const estimatedSize = JSON.stringify(sitemap).length
    if (estimatedSize > 40000000) { // 40MB warning threshold
      console.warn(`⚠️ WARNING: Sitemap size approaching 50MB limit`)
    }

  } catch (error) {
    console.error('❌ Error generating sitemap:', error)
    // Return basic sitemap without individual names if there's an error
    return sitemap.slice(0, 61) // Just return category pages
  }

  // Cache the generated sitemap
  sitemapCache = sitemap
  cacheTimestamp = Date.now()

  return sitemap
}
