"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  Home, 
  Search, 
  Baby, 
  Heart, 
  Star,
  Globe,
  BookOpen,
  Sparkles
} from "lucide-react"
import { useRouter } from "next/navigation"

export default function NotFound() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center px-4">
      <div className="container mx-auto max-w-4xl">
        {/* Main 404 Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-6 shadow-2xl">
              <Baby className="h-16 w-16 text-white" />
            </div>
          </div>
          
          <h1 className="text-6xl md:text-8xl font-bold text-gray-200 mb-4">404</h1>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Oops! Name Not Found
          </h2>
          <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            We couldn't find the baby name page you were looking for. Don't worry - we have thousands of beautiful names waiting to be discovered!
          </p>

          {/* Quick Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white w-full sm:w-auto">
                <Home className="mr-2 h-5 w-5" />
                Go Home
              </Button>
            </Link>
            <Button 
              size="lg" 
              variant="outline" 
              onClick={() => router.back()}
              className="w-full sm:w-auto"
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Go Back
            </Button>
          </div>
        </div>

        {/* Popular Countries Grid */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Explore Popular Name Collections
          </h3>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* USA */}
            <Link href="/usa/english-boy-names">
              <Card className="h-full hover:shadow-lg transition-all duration-300 cursor-pointer bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200 hover:border-blue-300">
                <CardHeader className="text-center pb-3">
                  <div className="text-3xl mb-2">🇺🇸</div>
                  <CardTitle className="text-lg">American Names</CardTitle>
                  <Badge className="bg-blue-100 text-blue-800 border-blue-300 mt-2">
                    🔥 Most Popular
                  </Badge>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-sm text-gray-600">
                    Discover trending American baby names with modern appeal
                  </p>
                </CardContent>
              </Card>
            </Link>

            {/* UK */}
            <Link href="/uk/english-boy-names">
              <Card className="h-full hover:shadow-lg transition-all duration-300 cursor-pointer bg-gradient-to-br from-red-50 to-pink-100 border-red-200 hover:border-red-300">
                <CardHeader className="text-center pb-3">
                  <div className="text-3xl mb-2">🇬🇧</div>
                  <CardTitle className="text-lg">British Names</CardTitle>
                  <Badge className="bg-red-100 text-red-800 border-red-300 mt-2">
                    👑 Royal Heritage
                  </Badge>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-sm text-gray-600">
                    Classic British names with royal traditions
                  </p>
                </CardContent>
              </Card>
            </Link>

            {/* India */}
            <Link href="/india/hindi-boy-names">
              <Card className="h-full hover:shadow-lg transition-all duration-300 cursor-pointer bg-gradient-to-br from-orange-50 to-yellow-100 border-orange-200 hover:border-orange-300">
                <CardHeader className="text-center pb-3">
                  <div className="text-3xl mb-2">🇮🇳</div>
                  <CardTitle className="text-lg">Indian Names</CardTitle>
                  <Badge className="bg-orange-100 text-orange-800 border-orange-300 mt-2">
                    🕉️ Rich Heritage
                  </Badge>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-sm text-gray-600">
                    Beautiful names from diverse Indian cultures
                  </p>
                </CardContent>
              </Card>
            </Link>

            {/* Germany */}
            <Link href="/germany/german-boy-names">
              <Card className="h-full hover:shadow-lg transition-all duration-300 cursor-pointer bg-gradient-to-br from-gray-50 to-slate-100 border-gray-200 hover:border-gray-300">
                <CardHeader className="text-center pb-3">
                  <div className="text-3xl mb-2">🇩🇪</div>
                  <CardTitle className="text-lg">German Names</CardTitle>
                  <Badge className="bg-gray-100 text-gray-800 border-gray-300 mt-2">
                    ⭐ Strong Heritage
                  </Badge>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-sm text-gray-600">
                    Strong Germanic names with deep meanings
                  </p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>

        {/* Quick Links Section */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Browse by Gender */}
          <Card className="bg-gradient-to-br from-pink-50 to-rose-100 border-pink-200">
            <CardHeader>
              <CardTitle className="flex items-center text-pink-800">
                <Heart className="mr-2 h-5 w-5" />
                Browse by Gender
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/usa/english-boy-names">
                <Button variant="outline" className="w-full text-blue-600 hover:bg-blue-50 border-blue-200">
                  👦 Boy Names
                </Button>
              </Link>
              <Link href="/usa/english-girl-names">
                <Button variant="outline" className="w-full text-pink-600 hover:bg-pink-50 border-pink-200">
                  👧 Girl Names
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Browse by Religion */}
          <Card className="bg-gradient-to-br from-purple-50 to-indigo-100 border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center text-purple-800">
                <BookOpen className="mr-2 h-5 w-5" />
                Browse by Religion
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/religions/muslim-boy-names">
                <Button variant="outline" className="w-full text-green-600 hover:bg-green-50 border-green-200">
                  🌙 Muslim Names
                </Button>
              </Link>
              <Link href="/religions/christian-boy-names">
                <Button variant="outline" className="w-full text-purple-600 hover:bg-purple-50 border-purple-200">
                  ✝️ Christian Names
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Features Section */}
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-6">
            Why Choose Our Baby Name Collection?
          </h3>
          <div className="grid sm:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-3 w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                <Globe className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-semibold mb-2">12 Countries</h4>
              <p className="text-sm text-gray-600">Names from around the world</p>
            </div>
            
            <div className="text-center">
              <div className="bg-pink-100 rounded-full p-3 w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                <Star className="h-6 w-6 text-pink-600" />
              </div>
              <h4 className="font-semibold mb-2">25,000+ Names</h4>
              <p className="text-sm text-gray-600">Comprehensive collection</p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-100 rounded-full p-3 w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                <Sparkles className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-semibold mb-2">Rich Meanings</h4>
              <p className="text-sm text-gray-600">Cultural significance included</p>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center mt-12 pt-8 border-t border-gray-200">
          <p className="text-gray-600 mb-4">
            Need help finding the perfect name?
          </p>
          <Link href="/blog">
            <Button className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white">
              <BookOpen className="mr-2 h-4 w-4" />
              Read Our Name Guide
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
