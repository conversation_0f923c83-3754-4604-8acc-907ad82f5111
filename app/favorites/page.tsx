"use client"

import NameCard from "@/components/name-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useMobile } from "@/hooks/use-mobile"
import { useTranslation } from "@/hooks/use-translation"
import type { NameData } from "@/types/name-data"
import { Heart } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

// Color themes for different categories
const colorThemes = {
  pink: "pink",
  orange: "orange",
  green: "green",
  blue: "blue",
  yellow: "yellow",
  red: "red",
  purple: "purple",
  indigo: "indigo"
} as const

const defaultGenders = [
  { value: "all", label: "All Genders" },
  { value: "boy", label: "Boys" },
  { value: "girl", label: "Girls" }
]

const defaultStartingLetters = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"]

export default function FavoritesPage() {
  const router = useRouter()
  const { t } = useTranslation()
  const isMobile = useMobile()

  const [favoriteNames, setFavoriteNames] = useState<string[]>([])
  const [nameData, setNameData] = useState<NameData[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Load favorites from localStorage
  useEffect(() => {
    const loadFavorites = () => {
      // Check both possible localStorage keys
      const savedFavorites = localStorage.getItem("babyNameFavorites") || localStorage.getItem("favoriteNames")
      if (savedFavorites) {
        const favorites = JSON.parse(savedFavorites)
        setFavoriteNames(favorites)
      }

      // Load full name data
      const savedFavoritesData = localStorage.getItem("babyNameFavoritesData")
      if (savedFavoritesData) {
        const favoritesData = JSON.parse(savedFavoritesData)
        // Apply alphabetical sorting by default
        favoritesData.sort((a: NameData, b: NameData) => a.name_en.localeCompare(b.name_en))
        setNameData(favoritesData)
      }

      setIsLoading(false)
    }
    loadFavorites()
  }, [])

  // Get color theme based on name data
  const getColorTheme = (name: NameData) => {
    const themes = Object.values(colorThemes)
    const index = name.name_en.charCodeAt(0) % themes.length
    return themes[index]
  }

  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: 12 }).map((_, i) => (
        <div key={i} className="overflow-hidden border rounded-md h-full bg-white dark:bg-zinc-900">
          <div className="p-3 h-full flex flex-col">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1 min-w-0">
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
              <Skeleton className="h-6 w-6 ml-1 rounded-full" />
            </div>
            <div className="flex-1">
              <Skeleton className="h-3 w-full mb-1" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <main className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Heart className="h-12 w-12 text-pink-500 mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              My Favorite Names
            </h1>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Your personally curated collection of beautiful baby names
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center p-4">
            <CardTitle className="text-2xl font-bold text-pink-600">{favoriteNames.length}</CardTitle>
            <p className="text-sm text-gray-600">Total Favorites</p>
          </Card>
          <Card className="text-center p-4">
            <CardTitle className="text-2xl font-bold text-blue-600">
              {nameData.filter(name => name.gender === "boy").length}
            </CardTitle>
            <p className="text-sm text-gray-600">Boy Names</p>
          </Card>
          <Card className="text-center p-4">
            <CardTitle className="text-2xl font-bold text-purple-600">
              {nameData.filter(name => name.gender === "girl").length}
            </CardTitle>
            <p className="text-sm text-gray-600">Girl Names</p>
          </Card>
          <Card className="text-center p-4">
            <CardTitle className="text-2xl font-bold text-green-600">
              {nameData.length}
            </CardTitle>
            <p className="text-sm text-gray-600">Names</p>
          </Card>
        </div>

        {/* Content */}
        {isLoading ? (
          <LoadingSkeleton />
        ) : favoriteNames.length === 0 ? (
          <div className="text-center py-16">
            <Heart className="mx-auto h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No favorites yet</h3>
            <p className="text-gray-500 mb-6">
              Start exploring names and click the heart icon to add them to your favorites.
            </p>
            <Button onClick={() => router.push("/")} className="bg-pink-500 hover:bg-pink-600">
              Explore Names
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {nameData.map((name, index) => (
              <div key={`${name.name_en}-${name.language}-${name.religion}-${name.gender}-${index}`}>
                <NameCard
                  name={name}
                  isFavorite={true}
                  onToggleFavorite={() => {
                    const newFavorites = favoriteNames.filter(n => n !== name.name_en)
                    setFavoriteNames(newFavorites)

                    // Update both localStorage keys to ensure compatibility
                    localStorage.setItem("babyNameFavorites", JSON.stringify(newFavorites))
                    localStorage.setItem("favoriteNames", JSON.stringify(newFavorites))

                    // Remove from nameData
                    setNameData(prev => prev.filter(n => n.name_en !== name.name_en))

                    // Remove from stored favorites data
                    const existingFavoritesData = localStorage.getItem("babyNameFavoritesData")
                    if (existingFavoritesData) {
                      const favoritesData = JSON.parse(existingFavoritesData)
                      const updatedFavoritesData = favoritesData.filter((fav: any) => fav.name_en !== name.name_en)
                      localStorage.setItem("babyNameFavoritesData", JSON.stringify(updatedFavoritesData))
                    }
                  }}
                  onClick={() => router.push(`/name/${encodeURIComponent(name.language)}/${encodeURIComponent(name.gender)}/${encodeURIComponent(name.religion)}/${encodeURIComponent(name.name_en)}`)}
                  colorTheme={getColorTheme(name)}
                  compact={true}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </main>
  )
} 