"use client"

import { FAQSection } from "@/components/faq-section"
import SEOStructuredData from "@/components/seo-structured-data"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Globe, Heart, Loader2, RefreshCw, Sparkles, Star, Zap } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface FusionName {
    name: string
    meaning: string
    cultures: string[]
    pronunciation: string
    style: string
    popularity: string
    culturalElements: string[]
    modernAdaptation: string
    score: number
}

interface CultureOption {
    id: string
    name: string
    description: string
    examples: string[]
}

export default function CulturalFusionNamesPage() {
    const [selectedCultures, setSelectedCultures] = useState<string[]>([])
    const [generatedNames, setGeneratedNames] = useState<FusionName[]>([])
    const [isGenerating, setIsGenerating] = useState(false)
    const [favorites, setFavorites] = useState<string[]>([])

    const cultureOptions: CultureOption[] = [
        {
            id: "indian",
            name: "Indian",
            description: "Sanskrit, Hindi, Tamil, Bengali roots",
            examples: ["Aarav", "Aadhya", "Vivaan", "Zara"]
        },
        {
            id: "chinese",
            name: "Chinese",
            description: "Mandarin, Cantonese, traditional meanings",
            examples: ["Li Wei", "Mei Lin", "Jian", "Xia"]
        },
        {
            id: "japanese",
            name: "Japanese",
            description: "Kanji meanings, modern adaptations",
            examples: ["Hiro", "Sakura", "Kenji", "Aiko"]
        },
        {
            id: "arabic",
            name: "Arabic",
            description: "Islamic, Middle Eastern heritage",
            examples: ["Zara", "Amir", "Layla", "Omar"]
        },
        {
            id: "celtic",
            name: "Celtic",
            description: "Irish, Scottish, Welsh origins",
            examples: ["Liam", "Niamh", "Finn", "Siobhan"]
        },
        {
            id: "nordic",
            name: "Nordic",
            description: "Scandinavian, Viking heritage",
            examples: ["Freya", "Thor", "Astrid", "Bjorn"]
        },
        {
            id: "latin",
            name: "Latin",
            description: "Roman, Italian, Spanish roots",
            examples: ["Luna", "Marco", "Sofia", "Dante"]
        },
        {
            id: "greek",
            name: "Greek",
            description: "Ancient Greek, modern adaptations",
            examples: ["Athena", "Zeus", "Iris", "Apollo"]
        },
        {
            id: "african",
            name: "African",
            description: "Various African cultures and languages",
            examples: ["Zara", "Kofi", "Aisha", "Mansa"]
        },
        {
            id: "slavic",
            name: "Slavic",
            description: "Russian, Polish, Czech origins",
            examples: ["Nadia", "Vlad", "Katya", "Mikhail"]
        }
    ]

    const toggleCulture = (cultureId: string) => {
        setSelectedCultures(prev =>
            prev.includes(cultureId)
                ? prev.filter(id => id !== cultureId)
                : [...prev, cultureId]
        )
    }

    const generateFusionNames = async () => {
        if (selectedCultures.length < 2) return

        setIsGenerating(true)

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 3000))

        const fusionNames: FusionName[] = generateFusionNameCombinations(selectedCultures)
        setGeneratedNames(fusionNames)
        setIsGenerating(false)
    }

    const generateFusionNameCombinations = (cultures: string[]): FusionName[] => {
        const combinations: FusionName[] = []

        // Sample fusion combinations based on selected cultures
        const fusionData = {
            "indian-chinese": [
                {
                    name: "Aarav Li",
                    meaning: "Peaceful and Beautiful",
                    cultures: ["Indian", "Chinese"],
                    pronunciation: "AH-rav LEE",
                    style: "Modern Fusion",
                    popularity: "Unique",
                    culturalElements: ["Sanskrit 'peace'", "Chinese 'beautiful'"],
                    modernAdaptation: "Aarav-Li",
                    score: 95
                },
                {
                    name: "Zara Mei",
                    meaning: "Princess of Beauty",
                    cultures: ["Indian", "Chinese"],
                    pronunciation: "ZAH-rah MAY",
                    style: "Elegant Fusion",
                    popularity: "Rising",
                    culturalElements: ["Arabic 'princess'", "Chinese 'beautiful'"],
                    modernAdaptation: "Zara-Mei",
                    score: 92
                }
            ],
            "celtic-japanese": [
                {
                    name: "Finn Hiro",
                    meaning: "Fair Warrior",
                    cultures: ["Celtic", "Japanese"],
                    pronunciation: "FIN HEER-oh",
                    style: "Strong Fusion",
                    popularity: "Unique",
                    culturalElements: ["Irish 'fair'", "Japanese 'generous'"],
                    modernAdaptation: "Finn-Hiro",
                    score: 88
                }
            ],
            "nordic-arabic": [
                {
                    name: "Freya Zara",
                    meaning: "Noble Princess",
                    cultures: ["Nordic", "Arabic"],
                    pronunciation: "FRAY-ah ZAH-rah",
                    style: "Royal Fusion",
                    popularity: "Trending",
                    culturalElements: ["Norse goddess", "Arabic 'princess'"],
                    modernAdaptation: "Freya-Zara",
                    score: 90
                }
            ],
            "greek-indian": [
                {
                    name: "Athena Devi",
                    meaning: "Goddess of Wisdom",
                    cultures: ["Greek", "Indian"],
                    pronunciation: "ah-THEE-nah DAY-vee",
                    style: "Divine Fusion",
                    popularity: "Unique",
                    culturalElements: ["Greek goddess", "Sanskrit 'goddess'"],
                    modernAdaptation: "Athena-Devi",
                    score: 94
                }
            ],
            "latin-chinese": [
                {
                    name: "Luna Mei",
                    meaning: "Moon Beauty",
                    cultures: ["Latin", "Chinese"],
                    pronunciation: "LOO-nah MAY",
                    style: "Poetic Fusion",
                    popularity: "Popular",
                    culturalElements: ["Latin 'moon'", "Chinese 'beautiful'"],
                    modernAdaptation: "Luna-Mei",
                    score: 96
                }
            ]
        }

        // Generate combinations based on selected cultures
        for (let i = 0; i < cultures.length; i++) {
            for (let j = i + 1; j < cultures.length; j++) {
                const combo = `${cultures[i]}-${cultures[j]}`
                const reverseCombo = `${cultures[j]}-${cultures[i]}`

                if (fusionData[combo as keyof typeof fusionData]) {
                    combinations.push(...fusionData[combo as keyof typeof fusionData])
                } else if (fusionData[reverseCombo as keyof typeof fusionData]) {
                    combinations.push(...fusionData[reverseCombo as keyof typeof fusionData])
                } else {
                    // Generate custom combinations
                    combinations.push(generateCustomFusion(cultures[i], cultures[j]))
                }
            }
        }

        return combinations.slice(0, 12) // Limit to 12 results
    }

    const generateCustomFusion = (culture1: string, culture2: string): FusionName => {
        const cultureNames = {
            indian: "Indian",
            chinese: "Chinese",
            japanese: "Japanese",
            arabic: "Arabic",
            celtic: "Celtic",
            nordic: "Nordic",
            latin: "Latin",
            greek: "Greek",
            african: "African",
            slavic: "Slavic"
        }

        const sampleNames = {
            indian: ["Aarav", "Vivaan", "Aadhya", "Zara"],
            chinese: ["Li", "Wei", "Mei", "Lin"],
            japanese: ["Hiro", "Ken", "Sakura", "Aiko"],
            arabic: ["Amir", "Zara", "Layla", "Omar"],
            celtic: ["Liam", "Finn", "Niamh", "Siobhan"],
            nordic: ["Freya", "Thor", "Astrid", "Bjorn"],
            latin: ["Luna", "Marco", "Sofia", "Dante"],
            greek: ["Athena", "Zeus", "Iris", "Apollo"],
            african: ["Zara", "Kofi", "Aisha", "Mansa"],
            slavic: ["Nadia", "Vlad", "Katya", "Mikhail"]
        }

        const name1 = sampleNames[culture1 as keyof typeof sampleNames]?.[Math.floor(Math.random() * 4)] || "Name"
        const name2 = sampleNames[culture2 as keyof typeof sampleNames]?.[Math.floor(Math.random() * 4)] || "Name"

        return {
            name: `${name1} ${name2}`,
            meaning: `Beautiful fusion of ${cultureNames[culture1 as keyof typeof cultureNames]} and ${cultureNames[culture2 as keyof typeof cultureNames]} heritage`,
            cultures: [cultureNames[culture1 as keyof typeof cultureNames], cultureNames[culture2 as keyof typeof cultureNames]],
            pronunciation: `${name1} ${name2}`,
            style: "Modern Fusion",
            popularity: "Unique",
            culturalElements: [`${cultureNames[culture1 as keyof typeof cultureNames]} roots`, `${cultureNames[culture2 as keyof typeof cultureNames]} influence`],
            modernAdaptation: `${name1}-${name2}`,
            score: 85 + Math.floor(Math.random() * 10)
        }
    }

    const toggleFavorite = (name: string) => {
        setFavorites(prev =>
            prev.includes(name)
                ? prev.filter(n => n !== name)
                : [...prev, name]
        )
    }

    const faqs = [
        {
            question: "What are cultural fusion names?",
            answer: "Cultural fusion names blend elements from different cultural heritages to create unique, meaningful names that honor multiple backgrounds. They're perfect for families with mixed heritage or those who want to celebrate global diversity."
        },
        {
            question: "How do you create fusion names?",
            answer: "Our generator combines names, meanings, and cultural elements from different traditions. We ensure the combinations sound harmonious, have meaningful interpretations, and respect the cultural significance of each element."
        },
        {
            question: "Are fusion names appropriate for all families?",
            answer: "Fusion names are wonderful for families with mixed heritage, international backgrounds, or those who appreciate global cultures. They celebrate diversity and create unique identities that honor multiple traditions."
        },
        {
            question: "How do I pronounce fusion names?",
            answer: "Each fusion name comes with a pronunciation guide. We provide phonetic spellings and audio examples when available. You can also customize the pronunciation to match your family's preferences."
        },
        {
            question: "Can I create my own fusion name?",
            answer: "Absolutely! Use our generator as inspiration, then combine elements that are meaningful to your family. Consider the cultural significance, pronunciation, and how it reflects your family's unique heritage."
        },
        {
            question: "Are fusion names becoming more popular?",
            answer: "Yes! As families become more globally connected and celebrate diverse heritages, fusion names are growing in popularity. They represent the beautiful blending of cultures in our modern world."
        }
    ]

    return (
        <>
            <SEOStructuredData
                names={[]}
                pageType="tool"
                title="Cultural Fusion Baby Names - Blend Cultures with Beautiful Name Combinations"
                description="Create unique baby names that blend multiple cultures. Our fusion name generator combines elements from different heritages to create meaningful, multicultural names for modern families."
                url="/cultural-fusion-names"
            />

            <div className="min-h-screen bg-gradient-to-br from-orange-50 via-yellow-50 to-red-50">
                {/* Hero Section */}
                <section className="bg-gradient-to-r from-orange-600 via-red-600 to-purple-600 text-white py-12 md:py-20">
                    <div className="container mx-auto px-4 text-center">
                        <div className="flex items-center justify-center mb-6">
                            <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
                                <Globe className="h-8 w-8 md:h-12 md:w-12" />
                            </div>
                        </div>
                        <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6">
                            Cultural Fusion Names
                        </h1>
                        <p className="text-lg md:text-xl lg:text-2xl mb-8 text-orange-100 max-w-3xl mx-auto">
                            Create unique baby names that blend multiple cultures. Honor your heritage with beautiful fusion combinations.
                        </p>
                        <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 px-2">
                            <Badge variant="secondary" className="text-orange-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                🌍 Multicultural
                            </Badge>
                            <Badge variant="secondary" className="text-orange-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                ✨ Unique
                            </Badge>
                            <Badge variant="secondary" className="text-orange-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                💝 Meaningful
                            </Badge>
                            <Badge variant="secondary" className="text-orange-600 bg-white text-xs sm:text-sm md:text-base px-2 sm:px-4 py-1 sm:py-2">
                                🎁 Modern
                            </Badge>
                        </div>
                    </div>
                </section>

                {/* Culture Selection */}
                <section className="py-8 sm:py-12 md:py-16">
                    <div className="container mx-auto px-4 max-w-6xl">
                        <div className="text-center mb-8">
                            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                                Choose Your Cultures
                            </h2>
                            <p className="text-gray-600 max-w-2xl mx-auto">
                                Select 2 or more cultures to blend. Our generator will create unique fusion names that honor your heritage.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                            {cultureOptions.map((culture) => (
                                <Card
                                    key={culture.id}
                                    className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${selectedCultures.includes(culture.id)
                                        ? 'ring-2 ring-orange-500 bg-orange-50'
                                        : 'hover:bg-gray-50'
                                        }`}
                                    onClick={() => toggleCulture(culture.id)}
                                >
                                    <CardContent className="p-4">
                                        <div className="flex items-start space-x-3">
                                            <Checkbox
                                                checked={selectedCultures.includes(culture.id)}
                                                onChange={() => toggleCulture(culture.id)}
                                            />
                                            <div className="flex-1">
                                                <h3 className="font-semibold text-gray-900 mb-1">
                                                    {culture.name}
                                                </h3>
                                                <p className="text-sm text-gray-600 mb-2">
                                                    {culture.description}
                                                </p>
                                                <div className="flex flex-wrap gap-1">
                                                    {culture.examples.map((example, idx) => (
                                                        <Badge key={idx} variant="outline" className="text-xs">
                                                            {example}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>

                        <div className="text-center">
                            <Button
                                onClick={generateFusionNames}
                                disabled={selectedCultures.length < 2 || isGenerating}
                                className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-8 py-3 text-lg"
                            >
                                {isGenerating ? (
                                    <>
                                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                        Creating Fusion Names...
                                    </>
                                ) : (
                                    <>
                                        <Sparkles className="mr-2 h-5 w-5" />
                                        Generate Fusion Names
                                    </>
                                )}
                            </Button>

                            {selectedCultures.length < 2 && (
                                <p className="text-sm text-gray-500 mt-2">
                                    Please select at least 2 cultures to generate fusion names
                                </p>
                            )}
                        </div>
                    </div>
                </section>

                {/* Results */}
                {generatedNames.length > 0 && (
                    <section className="py-8 sm:py-12 md:py-16 bg-white">
                        <div className="container mx-auto px-4 max-w-6xl">
                            <div className="text-center mb-8">
                                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                                    Your Fusion Names
                                </h2>
                                <p className="text-gray-600">
                                    Beautiful combinations that blend {selectedCultures.length} cultures
                                </p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {generatedNames.map((name, index) => (
                                    <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-orange-600 transition-colors">
                                                        {name.name}
                                                    </CardTitle>
                                                    <p className="text-sm text-gray-600 mt-1">
                                                        {name.pronunciation}
                                                    </p>
                                                </div>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => toggleFavorite(name.name)}
                                                    className={`${favorites.includes(name.name)
                                                        ? 'text-red-500'
                                                        : 'text-gray-400 hover:text-red-500'
                                                        }`}
                                                >
                                                    <Heart className={`h-5 w-5 ${favorites.includes(name.name) ? 'fill-current' : ''
                                                        }`} />
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-3">
                                            <div>
                                                <p className="text-gray-700 font-medium">{name.meaning}</p>
                                            </div>

                                            <div className="flex flex-wrap gap-1">
                                                {name.cultures.map((culture, idx) => (
                                                    <Badge key={idx} variant="secondary" className="text-xs">
                                                        {culture}
                                                    </Badge>
                                                ))}
                                            </div>

                                            <div className="space-y-2">
                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="text-gray-600">Style:</span>
                                                    <span className="font-medium">{name.style}</span>
                                                </div>
                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="text-gray-600">Popularity:</span>
                                                    <span className="font-medium">{name.popularity}</span>
                                                </div>
                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="text-gray-600">Score:</span>
                                                    <div className="flex items-center">
                                                        <Star className="h-4 w-4 text-yellow-500 mr-1" />
                                                        <span className="font-medium">{name.score}/100</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="pt-2 border-t">
                                                <p className="text-xs text-gray-500 mb-2">Cultural Elements:</p>
                                                <ul className="text-xs text-gray-600 space-y-1">
                                                    {name.culturalElements.map((element, idx) => (
                                                        <li key={idx}>• {element}</li>
                                                    ))}
                                                </ul>
                                            </div>

                                            <div className="pt-2 border-t">
                                                <p className="text-xs text-gray-500 mb-1">Modern Adaptation:</p>
                                                <p className="text-sm font-medium text-orange-600">{name.modernAdaptation}</p>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>

                            <div className="text-center mt-8">
                                <Button
                                    onClick={generateFusionNames}
                                    disabled={isGenerating}
                                    variant="outline"
                                    className="px-6 py-3"
                                >
                                    <RefreshCw className="mr-2 h-5 w-5" />
                                    Generate More Names
                                </Button>
                            </div>
                        </div>
                    </section>
                )}

                {/* Call to Action */}
                <section className="py-12 bg-gradient-to-r from-orange-600 to-red-600">
                    <div className="container mx-auto px-4 text-center">
                        <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                            Explore More Name Options
                        </h3>
                        <p className="text-orange-100 mb-6 max-w-2xl mx-auto">
                            Discover traditional names from individual cultures or try our AI-powered name generator for personalized suggestions.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link href="/baby-name-generator">
                                <Button size="lg" className="bg-white text-orange-600 hover:bg-orange-50">
                                    <Zap className="mr-2 h-5 w-5" />
                                    AI Name Generator
                                </Button>
                            </Link>
                            <Link href="/">
                                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600">
                                    Browse All Names
                                </Button>
                            </Link>
                        </div>
                    </div>
                </section>

                {/* FAQ Section */}
                <section className="py-16 bg-gray-50">
                    <div className="container mx-auto px-4">
                        <FAQSection faqs={faqs} />
                    </div>
                </section>
            </div>
        </>
    )
} 