@tailwind base;
@tailwind components;
@tailwind utilities;

/* Critical CSS for faster First Contentful Paint */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 36.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 81.4%;
    --input: 214.3 31.8% 81.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 55.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 27.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/* Performance optimizations for Core Web Vitals */
@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: var(--font-inter), system-ui, sans-serif;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
    /* Prevent layout shift */
    min-height: 100vh;
    overflow-x: hidden;
    /* Prevent horizontal scrolling on mobile */
    width: 100%;
    max-width: 100vw;
  }

  /* Optimize focus states for accessibility and performance */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Optimize animations for better performance */
  @media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Critical component styles to prevent layout shift */
@layer components {

  /* Loading optimization */
  .loading-skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Fast hover effects for better perceived performance */
  .hover-lift {
    @apply transition-transform duration-200 ease-out;
    will-change: transform;
  }

  .hover-lift:hover {
    @apply -translate-y-1;
  }

  /* Optimized card component */
  .card-optimized {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
    contain: layout style paint;
  }

  /* Fast fade-in animation */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Critical button styles */
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    @apply disabled:pointer-events-none disabled:opacity-50;
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
    @apply h-10 px-4 py-2;
  }

  /* Optimized grid layouts */
  .grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  /* Mobile-first container styles */
  .mobile-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-container {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .mobile-container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
}

/* Utility classes for performance */
@layer utilities {

  /* GPU acceleration for animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Content visibility for large lists */
  .content-visibility-auto {
    content-visibility: auto;
    contain-intrinsic-size: 300px;
  }

  /* Optimized scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Fast transitions */
  .transition-fast {
    transition-duration: 150ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Critical text styles */
  .text-balance {
    text-wrap: balance;
  }

  /* Lazy loading optimization */
  .lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .lazy-load.loaded {
    opacity: 1;
  }

  /* Performance hint classes */
  .isolate {
    isolation: isolate;
  }

  .contain-layout {
    contain: layout;
  }

  .contain-paint {
    contain: paint;
  }

  .contain-size {
    contain: size;
  }

  .contain-strict {
    contain: strict;
  }
}

/* Critical responsive utilities */
@media (min-width: 768px) {
  .md\:grid-cols-auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-auto {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

/* Print optimization */
@media print {
  .no-print {
    display: none !important;
  }

  * {
    color: black !important;
    background: white !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 0%;
    --ring: 0 0% 0%;
  }

  .dark {
    --border: 0 0% 100%;
    --ring: 0 0% 100%;
  }
}