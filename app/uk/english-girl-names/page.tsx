import { FAQSection } from "@/components/faq-section"
import LanguageNamesPage from "@/components/language-names-page"
import { generatePageMetadata, metadataConfigs } from "@/lib/metadata-utils"

export const metadata = generatePageMetadata(metadataConfigs.ukGirl)

export default function UKEnglishGirlNamesPage() {
  const faqs = [
    {
      question: "What are the most popular British girl names in 2025?",
      answer: "<PERSON>, <PERSON>, and <PERSON><PERSON> are among the top British girl names in 2025, with traditional names like <PERSON>, <PERSON>, and <PERSON><PERSON> also being very popular across the UK."
    },
    {
      question: "How do British girl names differ from other English-speaking countries?",
      answer: "British girl names often reflect the country's rich cultural heritage, with strong ties to British history and royal traditions. They tend to favor names with traditional meanings and historical significance."
    },
    {
      question: "Do regional differences exist in British girl names?",
      answer: "Yes! Names can vary by region in the UK, with some regional preferences. England, Scotland, Wales, and Northern Ireland each have their own naming traditions and popular choices."
    },
    {
      question: "How much influence do British celebrities and cultural figures have on names?",
      answer: "British celebrities, actresses, and cultural icons influence naming trends. Names of famous British actors, musicians, and public figures often see increased popularity following their success."
    },
    {
      question: "Are traditional British girl names still popular?",
      answer: "Absolutely! Traditional British girl names like <PERSON>, <PERSON>, and <PERSON><PERSON> continue to dominate popularity charts. British parents value names with strong meanings and cultural heritage while embracing modern trends."
    },
    {
      question: "What makes a girl name distinctly British?",
      answer: "British girl names often reflect the country's rich cultural heritage, with strong ties to British history and royal traditions. They typically favor names with traditional meanings and historical significance."
    }
  ]

  return (

    <LanguageNamesPage
      language="English"
      country="UK"
      religions={["Christian", "Muslim"]}
      showReligionFilter={false}
      showGenderFilter={false}
      defaultGender="female"
      colorTheme="pink"
      apiEndpoint="/api/names/countries/uk/languages/english/girl-names"
      headerLabels={{
        title: "Popular British Girl Names 2025",
        subtitle: "Top UK Baby Girl Names with Meanings",
        description: "Discover the most popular British girl names in 2025. Find elegant, classic, and trending UK baby names with meanings and origins from England, Scotland, Wales, and Northern Ireland."
      }}
      showRashiFilter={false}
      showAlphabetFilter={true}
      seoContent={
        <>
          <h2>About British Girl Names</h2>
          <p>
            British girl names embody the elegance and sophistication of the United Kingdom, drawing from
            English, Scottish, Welsh, and Irish heritage. These names often reflect both regal tradition
            and modern femininity, creating beautiful choices that honor British cultural heritage while
            remaining contemporary and stylish.
          </p>

          <h3>Popular British Girl Names in 2025</h3>
          <p>
            Classic names like Olivia, Amelia, and Charlotte continue to lead popularity charts, often
            influenced by the royal family and British cultural traditions. Modern names like Isla,
            Freya, and Luna are also trending, reflecting both Celtic heritage and contemporary preferences.
          </p>

          <h3>Choosing the Right British Girl Name</h3>
          <p>
            When selecting a British girl name, consider these important factors:
          </p>
          <ul>
            <li><strong>Cultural Heritage:</strong> Honor your family's British roots and regional traditions</li>
            <li><strong>Royal Connections:</strong> Consider names with royal or aristocratic associations</li>
            <li><strong>Regional Variations:</strong> Explore names specific to England, Scotland, Wales, or Northern Ireland</li>
            <li><strong>Traditional Spellings:</strong> Prefer classic British spellings over American variations</li>
            <li><strong>Historical Significance:</strong> Choose names with meaningful British cultural connections</li>
            <li><strong>Modern Appeal:</strong> Balance tradition with contemporary British naming trends</li>
          </ul>

          <h3>Traditional vs Modern British Names</h3>
          <p>
            Traditional British girl names often have roots in Anglo-Saxon, Norman, Celtic, and Germanic
            traditions, carrying centuries of cultural significance. Modern British names blend these
            traditional elements with contemporary global influences while maintaining distinctly British character.
          </p>

          <h3>Regional Diversity in British Names</h3>
          <p>
            British girl names vary across the four nations of the UK. Scottish names like Isla and
            Freya, Welsh names like Seren and Nia, and Irish names like Saoirse and Aoife all contribute
            to the rich tapestry of British naming traditions.
          </p>

          <h3>Trending British Girl Names</h3>
          <p>
            Current trends in British girl names include nature-inspired names, Celtic names, and names
            with strong, positive meanings. The influence of British celebrities, cultural icons, and
            royal family members continues to shape naming preferences across the UK.
          </p>

          <h3>Celebrity and Royal Influence</h3>
          <p>
            The British royal family significantly influences naming trends, with names like Charlotte,
            Elizabeth, and Victoria seeing increased popularity following royal births. British
            celebrities and cultural icons also contribute to emerging naming trends across the country.
          </p>

        </>

      }
      additionalContent={<FAQSection faqs={faqs} />}
    />

  )
}
