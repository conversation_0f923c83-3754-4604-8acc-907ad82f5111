import type { NameData } from "@/types/name-data"
import { indiaNames, getIndianNamesByLanguage, getIndianNamesByGender, getIndianNamesByStartingLetter, searchIndianNames } from "./countries/india"
import { USANames, getUSANamesByLanguage, getUSANamesByLanguageAndGender } from "./countries/usa"
import { EnglishBoyNames as CanadianEnglishBoyNames, EnglishGirlNames as CanadianEnglishGirlNames, FrenchBoyNames as CanadianFrenchBoyNames, FrenchGirlNames as CanadianFrenchGirlNames } from "./countries/canada"
import { UKNames } from "./countries/uk"
import { AustraliaNames } from "./countries/australia"
import { GermanyNames } from "./countries/germany"
import { NetherlandsNames } from "./countries/netherlands"
import { FranceNames } from "./countries/france"
import { SwedenNames } from "./countries/sweden"
// Phase 3 Countries (High ROI Reuse)
import { SwitzerlandNames } from "./countries/switzerland"
import { AustriaNames } from "./countries/austria"
import { BelgiumNames } from "./countries/belgium"
import { ChristianBoyNames } from "./global/religions/christian/boy-names"
import { ChristianGirlNames } from "./global/religions/christian/girl-names"
import { MuslimBoyNames } from "./global/religions/muslim/boy-names"
import { MuslimGirlNames } from "./global/religions/muslim/girl-names"
import { SikhBoyNames } from "./global/religions/sikh/boy-names"
import { SikhGirlNames } from "./global/religions/sikh/girl-names"

// Canada names combined
export const canadaNames: NameData[] = [
  ...CanadianEnglishBoyNames,
  ...CanadianEnglishGirlNames,
  ...CanadianFrenchBoyNames,
  ...CanadianFrenchGirlNames,
]

// Combine all names
export const allNames: NameData[] = [
  ...indiaNames,
  ...USANames,
  ...canadaNames,
  ...UKNames,
  ...AustraliaNames,
  ...GermanyNames,
  ...NetherlandsNames,
  ...FranceNames,
  ...SwedenNames,
  // Phase 3 Countries (High ROI Reuse)
  ...SwitzerlandNames,
  ...AustriaNames,
  ...BelgiumNames,
  ...ChristianBoyNames,
  ...ChristianGirlNames,
  ...MuslimBoyNames,
  ...MuslimGirlNames,
  ...SikhBoyNames,
  ...SikhGirlNames,
]

// Helper functions
export const getNamesByLanguage = (language: string): NameData[] => {
  return allNames.filter(name => name.language.toLowerCase() === language.toLowerCase())
}

export const getNamesByCountry = (country: string): NameData[] => {
  if (country.toLowerCase() === "india") return indiaNames
  if (country.toLowerCase() === "usa") return USANames
  if (country.toLowerCase() === "canada") return canadaNames
  if (country.toLowerCase() === "uk") return UKNames
  if (country.toLowerCase() === "australia") return AustraliaNames
  if (country.toLowerCase() === "germany") return GermanyNames
  if (country.toLowerCase() === "netherlands") return NetherlandsNames
  if (country.toLowerCase() === "france") return FranceNames
  if (country.toLowerCase() === "sweden") return SwedenNames
  // Phase 3 Countries (High ROI Reuse)
  if (country.toLowerCase() === "switzerland") return SwitzerlandNames
  if (country.toLowerCase() === "austria") return AustriaNames
  if (country.toLowerCase() === "belgium") return BelgiumNames
  return []
}

export const getNamesByReligion = (religion: string): NameData[] => {
  return allNames.filter(name => name.religion.toLowerCase() === religion.toLowerCase())
}

export const getNamesByGender = (gender: string): NameData[] => {
  return allNames.filter(name => name.gender.toLowerCase() === gender.toLowerCase())
}

export const getNamesByStartingLetter = (letter: string): NameData[] => {
  return allNames.filter(name => name.starting_letter === letter.toUpperCase())
}

export const searchNames = (query: string): NameData[] => {
  const lowercaseQuery = query.toLowerCase()
  return allNames.filter(name => 
    name.name_en.toLowerCase().includes(lowercaseQuery) ||
    name.name_native.toLowerCase().includes(lowercaseQuery) ||
    name.meaning_en.toLowerCase().includes(lowercaseQuery) ||
    name.meaning_native.toLowerCase().includes(lowercaseQuery)
  )
}

// Get unique values for filters
export const getUniqueLanguages = (): string[] => {
  return [...new Set(allNames.map(name => name.language))]
}

export const getUniqueReligions = (): string[] => {
  return [...new Set(allNames.map(name => name.religion))]
}

export const getUniqueGenders = (): string[] => {
  return [...new Set(allNames.map(name => name.gender))]
}

export const getUniqueStartingLetters = (): string[] => {
  return [...new Set(allNames.map(name => name.starting_letter))].sort()
}

// Canada specific functions
export const getCanadianNamesByLanguage = (language: string): NameData[] => {
  return canadaNames.filter(name => name.language.toLowerCase() === language.toLowerCase())
}

export const getCanadianNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return canadaNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

// UK specific functions
export const getUKNamesByLanguage = (language: string): NameData[] => {
  return UKNames.filter(name => name.language.toLowerCase() === language.toLowerCase())
}

export const getUKNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return UKNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

// Australia specific functions
export const getAustraliaNamesByLanguage = (language: string): NameData[] => {
  return AustraliaNames.filter(name => name.language.toLowerCase() === language.toLowerCase())
}

export const getAustraliaNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return AustraliaNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

// Phase 1 and Phase 2 country helper functions
export const getGermanyNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return GermanyNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

export const getNetherlandsNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return NetherlandsNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

export const getFranceNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return FranceNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

export const getSwedenNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return SwedenNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

// Phase 3 Countries (High ROI Reuse) helper functions
export const getSwitzerlandNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return SwitzerlandNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

export const getAustriaNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return AustriaNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

export const getBelgiumNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  return BelgiumNames.filter(name => 
    name.language.toLowerCase() === language.toLowerCase() && 
    name.gender.toLowerCase() === gender.toLowerCase()
  )
}

// Export country-specific names for direct access
export { CanadianEnglishBoyNames, CanadianEnglishGirlNames, CanadianFrenchBoyNames, CanadianFrenchGirlNames }
export { UKNames, AustraliaNames, GermanyNames, NetherlandsNames, FranceNames, SwedenNames, SwitzerlandNames, AustriaNames, BelgiumNames } 