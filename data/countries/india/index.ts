import type { NameData } from "@/types/name-data"

// Import all Indian language names
import { GujaratiBoyNames } from "./languages/gujarati/boy-names"
import { GujaratiGirlNames } from "./languages/gujarati/girl-names"
import { HindiBoyNames } from "./languages/hindi/boy-names"
import { HindiGirlNames } from "./languages/hindi/girl-names"
import { BengaliBoyNames } from "./languages/bengali/boy-names"
import { BengaliGirlNames } from "./languages/bengali/girl-names"
import { MarathiBoyNames } from "./languages/marathi/boy-names"
import { MarathiGirlNames } from "./languages/marathi/girl-names"
import { PunjabiBoyNames } from "./languages/punjabi/boy-names"
import { PunjabiGirlNames } from "./languages/punjabi/girl-names"
import { TamilBoyNames } from "./languages/tamil/boy-names"
import { TamilGirlNames } from "./languages/tamil/girl-names"
import { UrduBoyNames } from "./languages/urdu/boy-names"
import { UrduGirlNames } from "./languages/urdu/girl-names"

// Combine all Indian names
export const indiaNames: NameData[] = [
  ...GujaratiBoyNames,
  ...GujaratiGirlNames,
  ...HindiBoyNames,
  ...HindiGirlNames,
  ...BengaliBoyNames,
  ...BengaliGirlNames,
  ...MarathiBoyNames,
  ...MarathiGirlNames,
  ...PunjabiBoyNames,
  ...PunjabiGirlNames,
  ...TamilBoyNames,
  ...TamilGirlNames,
  ...UrduBoyNames,
  ...UrduGirlNames,
]

// Export individual collections
export const gujaratiNames = [...GujaratiBoyNames, ...GujaratiGirlNames]
export const hindiNames = [...HindiBoyNames, ...HindiGirlNames]
export const bengaliNames = [...BengaliBoyNames, ...BengaliGirlNames]
export const marathiNames = [...MarathiBoyNames, ...MarathiGirlNames]
export const punjabiNames = [...PunjabiBoyNames, ...PunjabiGirlNames]
export const tamilNames = [...TamilBoyNames, ...TamilGirlNames]
export const urduNames = [...UrduBoyNames, ...UrduGirlNames]

// Helper functions for India
export const getIndianNamesByLanguage = (language: string): NameData[] => {
  return indiaNames.filter(name => name.language === language)
}

export const getIndianNamesByGender = (gender: string): NameData[] => {
  return indiaNames.filter(name => name.gender === gender)
}

export const getIndianNamesByStartingLetter = (letter: string): NameData[] => {
  return indiaNames.filter(name => name.starting_letter === letter.toUpperCase())
}

export const searchIndianNames = (query: string): NameData[] => {
  const lowercaseQuery = query.toLowerCase()
  return indiaNames.filter(name => 
    name.name_en.toLowerCase().includes(lowercaseQuery) ||
    name.name_native.toLowerCase().includes(lowercaseQuery) ||
    name.meaning_en.toLowerCase().includes(lowercaseQuery) ||
    name.meaning_native.toLowerCase().includes(lowercaseQuery)
  )
} 