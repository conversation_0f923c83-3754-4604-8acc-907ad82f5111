import type { NameData } from "@/types/name-data";

export const HindiBoyNames: NameData[] = [
  {
    "name_en": "Aarav",
    "name_native": "आरव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Peaceful, calm",
    "meaning_native": "शांत, शांतिपूर्ण",
    "starting_letter": "A",
    "pronunciation": "Aarav",
    "origin": "Sanskrit",
    "rashi": "Ma<PERSON> (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "<PERSON><PERSON><PERSON>",
    "name_native": "अर्जुन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Bright, white, pure",
    "meaning_native": "उज्ज्वल, सफेद, पवित्र",
    "starting_letter": "A",
    "pronunciation": "<PERSON>r<PERSON>",
    "origin": "Sanskrit",
    "rashi": "<PERSON><PERSON> (<PERSON>sces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "Revati"
  },
  {
    "name_en": "Aditya",
    "name_native": "आदित्य",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sun, son of Aditi",
    "meaning_native": "सूर्य, अदिति के पुत्र",
    "starting_letter": "A",
    "pronunciation": "Aditya",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "Jyeshtha"
  },
  {
    "name_en": "Advait",
    "name_native": "अद्वैत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Unique, non-dual",
    "meaning_native": "अनन्य, अद्वैत",
    "starting_letter": "A",
    "pronunciation": "Advait",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Aryan",
    "name_native": "आर्यन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Noble, warrior",
    "meaning_native": "आर्य, योद्धा",
    "starting_letter": "A",
    "pronunciation": "Aryan",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Ansh",
    "name_native": "अंश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Part, portion",
    "meaning_native": "भाग, अंश",
    "starting_letter": "A",
    "pronunciation": "Ansh",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Ayaan",
    "name_native": "अयान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Gift of God, path",
    "meaning_native": "ईश्वर का उपहार, पथ",
    "starting_letter": "A",
    "pronunciation": "Ayaan",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Chitra",
    "nakshatra_native": "Chitra"
  },
  {
    "name_en": "Arnav",
    "name_native": "अर्णव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ocean, sea",
    "meaning_native": "समुद्र, सागर",
    "starting_letter": "A",
    "pronunciation": "Arnav",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Atharv",
    "name_native": "अथर्व",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "First Veda, knowledge",
    "meaning_native": "प्रथम वेद, ज्ञान",
    "starting_letter": "A",
    "pronunciation": "Atharv",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "Mula"
  },
  {
    "name_en": "Bhavesh",
    "name_native": "भावेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord of emotions",
    "meaning_native": "भावनाओं के स्वामी",
    "starting_letter": "B",
    "pronunciation": "Bhavesh",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "Dhanishta"
  },
  {
    "name_en": "Chirag",
    "name_native": "चिराग",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lamp, light",
    "meaning_native": "दीपक, प्रकाश",
    "starting_letter": "C",
    "pronunciation": "Chirag",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Dev",
    "name_native": "देव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "God, divine",
    "meaning_native": "देव, दैवी",
    "starting_letter": "D",
    "pronunciation": "Dev",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Dhruv",
    "name_native": "ध्रुव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pole star, constant",
    "meaning_native": "ध्रुव तारा, स्थिर",
    "starting_letter": "D",
    "pronunciation": "Dhruv",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "Uttara Phalguni"
  },
  {
    "name_en": "Ekansh",
    "name_native": "एकांश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Whole, complete",
    "meaning_native": "संपूर्ण, पूर्ण",
    "starting_letter": "E",
    "pronunciation": "Ekansh",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "Mrigashira"
  },
  {
    "name_en": "Gaurav",
    "name_native": "गौरव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pride, dignity",
    "meaning_native": "गौरव, मान",
    "starting_letter": "G",
    "pronunciation": "Gaurav",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "Bharani"
  },
  {
    "name_en": "Harsh",
    "name_native": "हर्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Joy, happiness",
    "meaning_native": "आनंद, खुशी",
    "starting_letter": "H",
    "pronunciation": "Harsh",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "Jyeshtha"
  },
  {
    "name_en": "Ishaan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sun God, Lord Shiva",
    "meaning_native": "सूर्य देव, भगवान शिव",
    "starting_letter": "I",
    "pronunciation": "Ishaan",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "Ashwini"
  },
  {
    "name_en": "Jai",
    "name_native": "जय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Victory, success",
    "meaning_native": "विजय, सफलता",
    "starting_letter": "J",
    "pronunciation": "Jai",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Karan",
    "name_native": "करण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Doer, creator",
    "meaning_native": "कर्ता, सर्जक",
    "starting_letter": "K",
    "pronunciation": "Karan",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Laksh",
    "name_native": "लक्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Target, aim",
    "meaning_native": "लक्ष्य, ध्येय",
    "starting_letter": "L",
    "pronunciation": "Laksh",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Mihir",
    "name_native": "मिहिर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sun, light",
    "meaning_native": "सूर्य, प्रकाश",
    "starting_letter": "M",
    "pronunciation": "Mihir",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "Mrigashira"
  },
  {
    "name_en": "Naman",
    "name_native": "नमन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Salutation, bow",
    "meaning_native": "नमस्कार, वंदना",
    "starting_letter": "N",
    "pronunciation": "Naman",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Om",
    "name_native": "ओम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred sound, universe",
    "meaning_native": "पवित्र ध्वनि, ब्रह्मांड",
    "starting_letter": "O",
    "pronunciation": "Om",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "Revati"
  },
  {
    "name_en": "Pranav",
    "name_native": "प्रणव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred syllable Om",
    "meaning_native": "पवित्र शब्द ओम",
    "starting_letter": "P",
    "pronunciation": "Pranav",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Rahul",
    "name_native": "राहुल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Efficient, capable",
    "meaning_native": "कुशल, समर्थ",
    "starting_letter": "R",
    "pronunciation": "Rahul",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Shaan",
    "name_native": "शान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pride, dignity",
    "meaning_native": "गौरव, मान",
    "starting_letter": "S",
    "pronunciation": "Shaan",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "Ashwini"
  },
  {
    "name_en": "Tanish",
    "name_native": "तनिष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ambition, desire",
    "meaning_native": "आकांक्षा, इच्छा",
    "starting_letter": "T",
    "pronunciation": "Tanish",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "Krittika"
  },
  {
    "name_en": "Ved",
    "name_native": "वेद",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred knowledge",
    "meaning_native": "पवित्र ज्ञान",
    "starting_letter": "V",
    "pronunciation": "Ved",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "Mula"
  },
  {
    "name_en": "Yash",
    "name_native": "यश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fame, glory",
    "meaning_native": "यश, कीर्ति",
    "starting_letter": "Y",
    "pronunciation": "Yash",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Vivaan",
    "name_native": "विवान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Full of life",
    "meaning_native": "जीवन से भरपूर",
    "starting_letter": "V",
    "pronunciation": "Vivaan",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "Ardra"
  },
  {
    "name_en": "Abhay",
    "name_native": "अभय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fearless, brave",
    "meaning_native": "निर्भय, साहसी",
    "starting_letter": "A",
    "pronunciation": "Abhay",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Akash",
    "name_native": "आकाश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sky, space",
    "meaning_native": "आकाश, अंतरिक्ष",
    "starting_letter": "A",
    "pronunciation": "Akash",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Ankit",
    "name_native": "अंकित",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Marked, distinguished",
    "meaning_native": "चिह्नित, प्रतिष्ठित",
    "starting_letter": "A",
    "pronunciation": "Ankit",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Bhuvan",
    "name_native": "भुवन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, world",
    "meaning_native": "पृथ्वी, संसार",
    "starting_letter": "B",
    "pronunciation": "Bhuvan",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Chetan",
    "name_native": "चेतन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Consciousness, awareness",
    "meaning_native": "चैतन्य, जागरूकता",
    "starting_letter": "C",
    "pronunciation": "Chetan",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Deepak",
    "name_native": "दीपक",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lamp, light",
    "meaning_native": "दीया, प्रकाश",
    "starting_letter": "D",
    "pronunciation": "Deepak",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Eshaan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Shiva, northeast",
    "meaning_native": "भगवान शिव, उत्तर-पूर्व",
    "starting_letter": "E",
    "pronunciation": "Eshaan",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Faizal",
    "name_native": "फैजल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Decisive, judge",
    "meaning_native": "निर्णायक, न्यायकर्ता",
    "starting_letter": "F",
    "pronunciation": "Faizal",
    "origin": "Arabic",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Gaurav",
    "name_native": "गौरव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pride, honor",
    "meaning_native": "गर्व, सम्मान",
    "starting_letter": "G",
    "pronunciation": "Gaurav",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Harsh",
    "name_native": "हर्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Joy, happiness",
    "meaning_native": "आनंद, प्रसन्नता",
    "starting_letter": "H",
    "pronunciation": "Harsh",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Ishaan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sun, Lord Vishnu",
    "meaning_native": "सूर्य, भगवान विष्णु",
    "starting_letter": "I",
    "pronunciation": "Ishaan",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Jishan",
    "name_native": "जीशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Good-hearted",
    "meaning_native": "दयालु हृदय",
    "starting_letter": "J",
    "pronunciation": "Jishan",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Kartik",
    "name_native": "कार्तिक",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Son of Lord Shiva",
    "meaning_native": "भगवान शिव के पुत्र",
    "starting_letter": "K",
    "pronunciation": "Kartik",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Anuradha",
    "nakshatra_native": "अनुराधा"
  },
  {
    "name_en": "Lalit",
    "name_native": "ललित",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful, charming",
    "meaning_native": "सुंदर, मोहक",
    "starting_letter": "L",
    "pronunciation": "Lalit",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Manish",
    "name_native": "मनीष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Intelligent, wise",
    "meaning_native": "बुद्धिमान, ज्ञानी",
    "starting_letter": "M",
    "pronunciation": "Manish",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "उत्तर फाल्गुनी"
  },
  {
    "name_en": "Naveen",
    "name_native": "नवीन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "New, fresh",
    "meaning_native": "नया, ताजा",
    "starting_letter": "N",
    "pronunciation": "Naveen",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाती"
  },
  {
    "name_en": "Ojas",
    "name_native": "ओजस",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Energy, strength",
    "meaning_native": "ऊर्जा, शक्ति",
    "starting_letter": "O",
    "pronunciation": "Ojas",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Prateek",
    "name_native": "प्रतीक",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Symbol, sign",
    "meaning_native": "चिह्न, संकेत",
    "starting_letter": "P",
    "pronunciation": "Prateek",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Raj",
    "name_native": "राज",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "King, rule",
    "meaning_native": "राजा, शासन",
    "starting_letter": "R",
    "pronunciation": "Raj",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "विशाखा"
  },
  {
    "name_en": "Sachin",
    "name_native": "सचिन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pure, real",
    "meaning_native": "शुद्ध, वास्तविक",
    "starting_letter": "S",
    "pronunciation": "Sachin",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Tarun",
    "name_native": "तरुण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Young, youth",
    "meaning_native": "युवा, नवयुवक",
    "starting_letter": "T",
    "pronunciation": "Tarun",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Uday",
    "name_native": "उदय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Rise, sunrise",
    "meaning_native": "उदय, सूर्योदय",
    "starting_letter": "U",
    "pronunciation": "Uday",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Vinay",
    "name_native": "विनय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Modesty, humility",
    "meaning_native": "विनम्रता, नम्रता",
    "starting_letter": "V",
    "pronunciation": "Vinay",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Yatin",
    "name_native": "यतिन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ascetic, devoted",
    "meaning_native": "तपस्वी, समर्पित",
    "starting_letter": "Y",
    "pronunciation": "Yatin",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Zayan",
    "name_native": "जयान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful, graceful",
    "meaning_native": "सुंदर, सुशोभित",
    "starting_letter": "Z",
    "pronunciation": "Zayan",
    "origin": "Arabic",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Advay",
    "name_native": "अद्वय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Unique, one",
    "meaning_native": "अनूठा, एक",
    "starting_letter": "A",
    "pronunciation": "Advay",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Bavish",
    "name_native": "बविष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Future, upcoming",
    "meaning_native": "भविष्य, आगामी",
    "starting_letter": "B",
    "pronunciation": "Bavish",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Darsh",
    "name_native": "दर्श",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Vision, sight",
    "meaning_native": "दृष्टि, नजर",
    "starting_letter": "D",
    "pronunciation": "Darsh",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Eshan",
    "name_native": "एशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Shiva, desire",
    "meaning_native": "भगवान शिव, इच्छा",
    "starting_letter": "E",
    "pronunciation": "Eshan",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Gautam",
    "name_native": "गौतम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Buddha, enlightened",
    "meaning_native": "बुद्ध, प्रबुद्ध",
    "starting_letter": "G",
    "pronunciation": "Gautam",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Hitesh",
    "name_native": "हितेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Well-wisher, lord of goodness",
    "meaning_native": "हितैषी, कल्याण के स्वामी",
    "starting_letter": "H",
    "pronunciation": "Hitesh",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Ashlesha",
    "nakshatra_native": "आश्लेषा"
  },
  {
    "name_en": "Jatin",
    "name_native": "जतिन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ascetic, having matted hair",
    "meaning_native": "तपस्वी, जटाधारी",
    "starting_letter": "J",
    "pronunciation": "Jatin",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Kush",
    "name_native": "कुश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred grass, son of Rama",
    "meaning_native": "पवित्र घास, राम के पुत्र",
    "starting_letter": "K",
    "pronunciation": "Kush",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Mayank",
    "name_native": "मयंक",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Moon, mark",
    "meaning_native": "चंद्रमा, निशान",
    "starting_letter": "M",
    "pronunciation": "Mayank",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Nitin",
    "name_native": "नितिन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Master of the right path",
    "meaning_native": "सही मार्ग के स्वामी",
    "starting_letter": "N",
    "pronunciation": "Nitin",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "ज्येष्ठा"
  },
  {
    "name_en": "Paras",
    "name_native": "पारस",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Touchstone, philosopher's stone",
    "meaning_native": "स्पर्शमणि, पारसमणि",
    "starting_letter": "P",
    "pronunciation": "Paras",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "पूर्वभाद्रपद"
  },
  {
    "name_en": "Rohan",
    "name_native": "रोहन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Rising, ascending",
    "meaning_native": "उत्थान, आरोहण",
    "starting_letter": "R",
    "pronunciation": "Rohan",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाती"
  },
  {
    "name_en": "Samar",
    "name_native": "समर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Battle, war",
    "meaning_native": "युद्ध, संग्राम",
    "starting_letter": "S",
    "pronunciation": "Samar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tanish",
    "name_native": "तनिष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ambition, desire",
    "meaning_native": "महत्वाकांक्षा, इच्छा",
    "starting_letter": "T",
    "pronunciation": "Tanish",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Urmil",
    "name_native": "उर्मिल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Enchanting, captivating",
    "meaning_native": "मोहक, आकर्षक",
    "starting_letter": "U",
    "pronunciation": "Urmil",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vivaan",
    "name_native": "विवान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Full of life",
    "meaning_native": "जीवन से भरपूर",
    "starting_letter": "V",
    "pronunciation": "Vivaan",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "उत्तरफाल्गुनी"
  },
  {
    "name_en": "Yash",
    "name_native": "यश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Success, fame",
    "meaning_native": "सफलता, प्रसिद्धि",
    "starting_letter": "Y",
    "pronunciation": "Yash",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zayan",
    "name_native": "ज़यान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Growth, progress",
    "meaning_native": "विकास, प्रगति",
    "starting_letter": "Z",
    "pronunciation": "Zayan",
    "origin": "Arabic",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Aakash",
    "name_native": "आकाश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sky, space",
    "meaning_native": "आसमान, अंतरिक्ष",
    "starting_letter": "A",
    "pronunciation": "Aakash",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Bhuvan",
    "name_native": "भुवन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "World, universe",
    "meaning_native": "संसार, ब्रह्मांड",
    "starting_letter": "B",
    "pronunciation": "Bhuvan",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Chirag",
    "name_native": "चिराग",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lamp, light",
    "meaning_native": "दीपक, प्रकाश",
    "starting_letter": "C",
    "pronunciation": "Chirag",
    "origin": "Persian",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Dhruv",
    "name_native": "ध्रुव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pole star, constant",
    "meaning_native": "ध्रुव तारा, स्थिर",
    "starting_letter": "D",
    "pronunciation": "Dhruv",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Eshaan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Shiva, northeast",
    "meaning_native": "भगवान शिव, ईशान दिशा",
    "starting_letter": "E",
    "pronunciation": "Eshaan",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Falgun",
    "name_native": "फाल्गुन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Hindu month, spring",
    "meaning_native": "हिंदू महीना, वसंत",
    "starting_letter": "F",
    "pronunciation": "Falgun",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Girish",
    "name_native": "गिरीश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord of mountains, Shiva",
    "meaning_native": "पर्वतों के स्वामी, शिव",
    "starting_letter": "G",
    "pronunciation": "Girish",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Harshit",
    "name_native": "हर्षित",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Happy, joyful",
    "meaning_native": "खुश, आनंदित",
    "starting_letter": "H",
    "pronunciation": "Harshit",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Ishant",
    "name_native": "ईशांत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "End of desires",
    "meaning_native": "इच्छाओं का अंत",
    "starting_letter": "I",
    "pronunciation": "Ishant",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Jivansh",
    "name_native": "जीवांश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Part of life",
    "meaning_native": "जीवन का हिस्सा",
    "starting_letter": "J",
    "pronunciation": "Jivansh",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "श्रवण"
  },
  {
    "name_en": "Kabir",
    "name_native": "कबीर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Great, powerful",
    "meaning_native": "महान, शक्तिशाली",
    "starting_letter": "K",
    "pronunciation": "Kabir",
    "origin": "Arabic",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Lakshit",
    "name_native": "लक्षित",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Aimed, targeted",
    "meaning_native": "लक्षित, निशाना",
    "starting_letter": "L",
    "pronunciation": "Lakshit",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Mohan",
    "name_native": "मोहन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Charming, Krishna",
    "meaning_native": "आकर्षक, कृष्ण",
    "starting_letter": "M",
    "pronunciation": "Mohan",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Nakul",
    "name_native": "नकुल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Mongoose, one of Pandavas",
    "meaning_native": "नेवला, पांडवों में से एक",
    "starting_letter": "N",
    "pronunciation": "Nakul",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Om",
    "name_native": "ओम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred syllable",
    "meaning_native": "पवित्र शब्द",
    "starting_letter": "O",
    "pronunciation": "Om",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Pranav",
    "name_native": "प्रणव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred syllable Om",
    "meaning_native": "पवित्र शब्द ओम",
    "starting_letter": "P",
    "pronunciation": "Pranav",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Rahul",
    "name_native": "राहुल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Efficient, capable",
    "meaning_native": "कुशल, सक्षम",
    "starting_letter": "R",
    "pronunciation": "Rahul",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Sahil",
    "name_native": "साहिल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Guide, leader",
    "meaning_native": "मार्गदर्शक, नेता",
    "starting_letter": "S",
    "pronunciation": "Sahil",
    "origin": "Arabic",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Tanay",
    "name_native": "तनय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Son, child",
    "meaning_native": "बेटा, बच्चा",
    "starting_letter": "T",
    "pronunciation": "Tanay",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Uday",
    "name_native": "उदय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Rising, dawn",
    "meaning_native": "उदय, भोर",
    "starting_letter": "U",
    "pronunciation": "Uday",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vedant",
    "name_native": "वेदांत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "End of Vedas, philosophy",
    "meaning_native": "वेदों का अंत, दर्शन",
    "starting_letter": "V",
    "pronunciation": "Vedant",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Yash",
    "name_native": "यश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fame, glory",
    "meaning_native": "यश, कीर्ति",
    "starting_letter": "Y",
    "pronunciation": "Yash",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zain",
    "name_native": "ज़ैन",
    "gender": "male",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Beauty, grace",
    "meaning_native": "सुंदरता, अनुग्रह",
    "starting_letter": "Z",
    "pronunciation": "Zain",
    "origin": "Arabic"
  },
  {
    "name_en": "Abhay",
    "name_native": "अभय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fearless",
    "meaning_native": "निर्भय",
    "starting_letter": "A",
    "pronunciation": "Abhay",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bharat",
    "name_native": "भारत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "India, maintained",
    "meaning_native": "भारत, पालन किया",
    "starting_letter": "B",
    "pronunciation": "Bharat",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Chandan",
    "name_native": "चंदन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sandalwood",
    "meaning_native": "चंदन",
    "starting_letter": "C",
    "pronunciation": "Chandan",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Darshan",
    "name_native": "दर्शन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Vision, philosophy",
    "meaning_native": "दर्शन, दृष्टि",
    "starting_letter": "D",
    "pronunciation": "Darshan",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Eklavya",
    "name_native": "एकलव्य",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Student of Dronacharya",
    "meaning_native": "द्रोणाचार्य का शिष्य",
    "starting_letter": "E",
    "pronunciation": "Eklavya",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Farhan",
    "name_native": "फरहान",
    "gender": "male",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Happy, joyful",
    "meaning_native": "खुश, आनंदित",
    "starting_letter": "F",
    "pronunciation": "Farhan",
    "origin": "Arabic"
  },
  {
    "name_en": "Gautam",
    "name_native": "गौतम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Buddha, enlightened",
    "meaning_native": "बुद्ध, ज्ञानी",
    "starting_letter": "G",
    "pronunciation": "Gautam",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Harsh",
    "name_native": "हर्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Happiness, joy",
    "meaning_native": "खुशी, आनंद",
    "starting_letter": "H",
    "pronunciation": "Harsh",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Ishaan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sun, Lord Vishnu",
    "meaning_native": "सूर्य, भगवान विष्णु",
    "starting_letter": "I",
    "pronunciation": "Ishaan",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Jai",
    "name_native": "जय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Victory",
    "meaning_native": "विजय",
    "starting_letter": "J",
    "pronunciation": "Jai",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Karan",
    "name_native": "करण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Doer, maker",
    "meaning_native": "कर्ता, बनाने वाला",
    "starting_letter": "K",
    "pronunciation": "Karan",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Lakshay",
    "name_native": "लक्ष्य",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Target, aim",
    "meaning_native": "लक्ष्य, निशाना",
    "starting_letter": "L",
    "pronunciation": "Lakshay",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Manav",
    "name_native": "मानव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Human, mankind",
    "meaning_native": "मानव, मनुष्य",
    "starting_letter": "M",
    "pronunciation": "Manav",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Naman",
    "name_native": "नमन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Salutation, bow",
    "meaning_native": "नमन, प्रणाम",
    "starting_letter": "N",
    "pronunciation": "Naman",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Omkar",
    "name_native": "ओमकार",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred syllable Om",
    "meaning_native": "पवित्र शब्द ओम",
    "starting_letter": "O",
    "pronunciation": "Omkar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Parth",
    "name_native": "पार्थ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Arjuna, son of Pritha",
    "meaning_native": "अर्जुन, पृथा का पुत्र",
    "starting_letter": "P",
    "pronunciation": "Parth",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Rishabh",
    "name_native": "ऋषभ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Bull, best",
    "meaning_native": "बैल, सर्वश्रेष्ठ",
    "starting_letter": "R",
    "pronunciation": "Rishabh",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Satyam",
    "name_native": "सत्यम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Truth",
    "meaning_native": "सत्य",
    "starting_letter": "S",
    "pronunciation": "Satyam",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Tanish",
    "name_native": "तनिष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ambition, desire",
    "meaning_native": "महत्वाकांक्षा, इच्छा",
    "starting_letter": "T",
    "pronunciation": "Tanish",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Utkarsh",
    "name_native": "उत्कर्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Progress, excellence",
    "meaning_native": "प्रगति, उत्कृष्टता",
    "starting_letter": "U",
    "pronunciation": "Utkarsh",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vivaan",
    "name_native": "विवान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Full of life",
    "meaning_native": "जीवन से भरपूर",
    "starting_letter": "V",
    "pronunciation": "Vivaan",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Yuvan",
    "name_native": "युवन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Youthful, young",
    "meaning_native": "युवा, जवान",
    "starting_letter": "Y",
    "pronunciation": "Yuvan",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "male",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Bright, shining",
    "meaning_native": "चमकदार, दीप्तिमान",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Abhinav",
    "name_native": "अभिनव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "New, fresh",
    "meaning_native": "नया, ताजा",
    "starting_letter": "A",
    "pronunciation": "Abhinav",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bhaskar",
    "name_native": "भास्कर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sun, light giver",
    "meaning_native": "सूर्य, प्रकाश देने वाला",
    "starting_letter": "B",
    "pronunciation": "Bhaskar",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Chiranjeev",
    "name_native": "चिरंजीव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Immortal, long-lived",
    "meaning_native": "अमर, दीर्घजीवी",
    "starting_letter": "C",
    "pronunciation": "Chiranjeev",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Dharmesh",
    "name_native": "धर्मेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord of dharma",
    "meaning_native": "धर्म के स्वामी",
    "starting_letter": "D",
    "pronunciation": "Dharmesh",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Eshwar",
    "name_native": "ईश्वर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "God, supreme being",
    "meaning_native": "ईश्वर, परमात्मा",
    "starting_letter": "E",
    "pronunciation": "Eshwar",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Firoz",
    "name_native": "फिरोज़",
    "gender": "male",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Victorious, successful",
    "meaning_native": "विजयी, सफल",
    "starting_letter": "F",
    "pronunciation": "Firoz",
    "origin": "Persian"
  },
  {
    "name_en": "Giriraj",
    "name_native": "गिरिराज",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "King of mountains",
    "meaning_native": "पर्वतों का राजा",
    "starting_letter": "G",
    "pronunciation": "Giriraj",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Himanshu",
    "name_native": "हिमांशु",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Moon, cool rayed",
    "meaning_native": "चंद्रमा, शीतल किरणों वाला",
    "starting_letter": "H",
    "pronunciation": "Himanshu",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Indra",
    "name_native": "इंद्र",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "King of gods",
    "meaning_native": "देवताओं का राजा",
    "starting_letter": "I",
    "pronunciation": "Indra",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jagdish",
    "name_native": "जगदीश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord of the world",
    "meaning_native": "जगत के स्वामी",
    "starting_letter": "J",
    "pronunciation": "Jagdish",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Keshav",
    "name_native": "केशव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Krishna",
    "meaning_native": "भगवान कृष्ण",
    "starting_letter": "K",
    "pronunciation": "Keshav",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Lakshman",
    "name_native": "लक्ष्मण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Brother of Rama",
    "meaning_native": "राम के भाई",
    "starting_letter": "L",
    "pronunciation": "Lakshman",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Madhav",
    "name_native": "माधव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Krishna",
    "meaning_native": "भगवान कृष्ण",
    "starting_letter": "M",
    "pronunciation": "Madhav",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Naresh",
    "name_native": "नरेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "King of men",
    "meaning_native": "मनुष्यों का राजा",
    "starting_letter": "N",
    "pronunciation": "Naresh",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Om",
    "name_native": "ओम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred syllable",
    "meaning_native": "पवित्र शब्द",
    "starting_letter": "O",
    "pronunciation": "Om",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Prakash",
    "name_native": "प्रकाश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Light, brightness",
    "meaning_native": "प्रकाश, चमक",
    "starting_letter": "P",
    "pronunciation": "Prakash",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Raghav",
    "name_native": "राघव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Rama",
    "meaning_native": "भगवान राम",
    "starting_letter": "R",
    "pronunciation": "Raghav",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Sachin",
    "name_native": "सचिन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pure, true",
    "meaning_native": "शुद्ध, सच्चा",
    "starting_letter": "S",
    "pronunciation": "Sachin",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Tarun",
    "name_native": "तरुण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Young, youthful",
    "meaning_native": "युवा, जवान",
    "starting_letter": "T",
    "pronunciation": "Tarun",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Umesh",
    "name_native": "उमेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lord Shiva",
    "meaning_native": "भगवान शिव",
    "starting_letter": "U",
    "pronunciation": "Umesh",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vikram",
    "name_native": "विक्रम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Valor, courage",
    "meaning_native": "वीरता, साहस",
    "starting_letter": "V",
    "pronunciation": "Vikram",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Yashwant",
    "name_native": "यशवंत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Famous, glorious",
    "meaning_native": "प्रसिद्ध, यशस्वी",
    "starting_letter": "Y",
    "pronunciation": "Yashwant",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "male",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Bright, shining",
    "meaning_native": "चमकदार, दीप्तिमान",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  }
];

export default HindiBoyNames;
