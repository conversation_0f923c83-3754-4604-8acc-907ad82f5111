import type { NameData } from "@/types/name-data";

export const HindiGirlNames: NameData[] = [
  {
    "name_en": "<PERSON><PERSON>",
    "name_native": "आइशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Living, prosperous",
    "meaning_native": "जीवंत, समृद्ध",
    "starting_letter": "A",
    "pronunciation": "Aisha",
    "origin": "Sanskrit",
    "rashi": "<PERSON><PERSON> (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Anaya",
    "name_native": "अनया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Caring, compassionate",
    "meaning_native": "स्नेही, दयालु",
    "starting_letter": "A",
    "pronunciation": "Anaya",
    "origin": "Sanskrit",
    "rashi": "<PERSON><PERSON> (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "<PERSON>hani<PERSON><PERSON>",
    "nakshatra_native": "<PERSON>hani<PERSON><PERSON>"
  },
  {
    "name_en": "Aaradhya",
    "name_native": "आराध्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Worshipped, divine",
    "meaning_native": "पूज्य, दैवी",
    "starting_letter": "A",
    "pronunciation": "Aaradhya",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Advika",
    "name_native": "अद्विका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Unique, unparalleled",
    "meaning_native": "अनुपम, अद्वितीय",
    "starting_letter": "A",
    "pronunciation": "Advika",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "Dhanishta"
  },
  {
    "name_en": "Arya",
    "name_native": "आर्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Noble, honorable",
    "meaning_native": "आर्य, सम्मानित",
    "starting_letter": "A",
    "pronunciation": "Arya",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "Rohini"
  },
  {
    "name_en": "Bhavya",
    "name_native": "भव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Magnificent, grand",
    "meaning_native": "भव्य, महान",
    "starting_letter": "B",
    "pronunciation": "Bhavya",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Chaya",
    "name_native": "छाया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Shadow, reflection",
    "meaning_native": "छाया, प्रतिबिंब",
    "starting_letter": "C",
    "pronunciation": "Chaya",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "Ashwini"
  },
  {
    "name_en": "Disha",
    "name_native": "दिशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Direction, path",
    "meaning_native": "दिशा, मार्ग",
    "starting_letter": "D",
    "pronunciation": "Disha",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "Dhanishta"
  },
  {
    "name_en": "Esha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Desire, wish",
    "meaning_native": "इच्छा, कामना",
    "starting_letter": "E",
    "pronunciation": "Esha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Gauri",
    "name_native": "गौरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fair, Goddess Parvati",
    "meaning_native": "गौरी, देवी पार्वती",
    "starting_letter": "G",
    "pronunciation": "Gauri",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Harshita",
    "name_native": "हर्षिता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Happy, joyful",
    "meaning_native": "प्रसन्न, आनंदित",
    "starting_letter": "H",
    "pronunciation": "Harshita",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Isha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess, divine",
    "meaning_native": "देवी, दैवी",
    "starting_letter": "I",
    "pronunciation": "Isha",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Jiya",
    "name_native": "जिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Heart, soul",
    "meaning_native": "हृदय, आत्मा",
    "starting_letter": "J",
    "pronunciation": "Jiya",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Chitra",
    "nakshatra_native": "Chitra"
  },
  {
    "name_en": "Kavya",
    "name_native": "काव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Poetry, literature",
    "meaning_native": "काव्य, साहित्य",
    "starting_letter": "K",
    "pronunciation": "Kavya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Lakshmi",
    "name_native": "लक्ष्मी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "धन की देवी",
    "starting_letter": "L",
    "pronunciation": "Lakshmi",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Mira",
    "name_native": "मीरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Ocean, devotee",
    "meaning_native": "समुद्र, भक्त",
    "starting_letter": "M",
    "pronunciation": "Mira",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Nisha",
    "name_native": "निशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Night",
    "meaning_native": "रात्रि",
    "starting_letter": "N",
    "pronunciation": "Nisha",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Ojasvi",
    "name_native": "ओजस्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Bright, radiant",
    "meaning_native": "तेजस्वी, चमकदार",
    "starting_letter": "O",
    "pronunciation": "Ojasvi",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Pari",
    "name_native": "परी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fairy, angel",
    "meaning_native": "परी, देवदूत",
    "starting_letter": "P",
    "pronunciation": "Pari",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Anuradha",
    "nakshatra_native": "Anuradha"
  },
  {
    "name_en": "Riya",
    "name_native": "रिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Singer, melody",
    "meaning_native": "गायिका, संगीत",
    "starting_letter": "R",
    "pronunciation": "Riya",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Saanvi",
    "name_native": "सान्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess Lakshmi",
    "meaning_native": "देवी लक्ष्मी",
    "starting_letter": "S",
    "pronunciation": "Saanvi",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Tara",
    "name_native": "तारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "T",
    "pronunciation": "Tara",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Uma",
    "name_native": "उमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "देवी पार्वती",
    "starting_letter": "U",
    "pronunciation": "Uma",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Vanya",
    "name_native": "वन्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Forest, nature",
    "meaning_native": "वन, प्रकृति",
    "starting_letter": "V",
    "pronunciation": "Vanya",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Yashvi",
    "name_native": "यश्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Famous, glorious",
    "meaning_native": "प्रसिद्ध, यशस्वी",
    "starting_letter": "Y",
    "pronunciation": "Yashvi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Zara",
    "name_native": "जारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Princess, flower",
    "meaning_native": "राजकुमारी, फूल",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "Swati"
  },
  {
    "name_en": "Kiara",
    "name_native": "कियारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Dark, little one",
    "meaning_native": "श्यामल, छोटी",
    "starting_letter": "K",
    "pronunciation": "Kiara",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Sara",
    "name_native": "सारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pure, noble",
    "meaning_native": "पवित्र, कुलीन",
    "starting_letter": "S",
    "pronunciation": "Sara",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "Swati"
  },
  {
    "name_en": "Priya",
    "name_native": "प्रिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beloved, dear",
    "meaning_native": "प्रिय, प्यारी",
    "starting_letter": "P",
    "pronunciation": "Priya",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Divya",
    "name_native": "दिव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Divine, heavenly",
    "meaning_native": "दिव्य, स्वर्गीय",
    "starting_letter": "D",
    "pronunciation": "Divya",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Aanya",
    "name_native": "आन्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Gracious, merciful",
    "meaning_native": "कृपालु, दयालु",
    "starting_letter": "A",
    "pronunciation": "Aanya",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Abhilasha",
    "name_native": "अभिलाषा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Desire, wish",
    "meaning_native": "इच्छा, कामना",
    "starting_letter": "A",
    "pronunciation": "Abhilasha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Aditi",
    "name_native": "अदिति",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Boundless, Mother of Gods",
    "meaning_native": "असीम, देवमाता",
    "starting_letter": "A",
    "pronunciation": "Aditi",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Poorva Phalguni",
    "nakshatra_native": "पूर्वा फाल्गुनी"
  },
  {
    "name_en": "Ameya",
    "name_native": "अमेया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Immeasurable, boundless",
    "meaning_native": "असीम, अमाप",
    "starting_letter": "A",
    "pronunciation": "Ameya",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Anika",
    "name_native": "अनिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Gracious, sweet face",
    "meaning_native": "दयालु, मधुर मुख",
    "starting_letter": "A",
    "pronunciation": "Anika",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "उत्तरा फाल्गुनी"
  },
  {
    "name_en": "Anushka",
    "name_native": "अनुष्का",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lightning, brightness",
    "meaning_native": "बिजली, चमक",
    "starting_letter": "A",
    "pronunciation": "Anushka",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Anuradha",
    "nakshatra_native": "अनुराधा"
  },
  {
    "name_en": "Avani",
    "name_native": "अवनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, goddess",
    "meaning_native": "पृथ्वी, देवी",
    "starting_letter": "A",
    "pronunciation": "Avani",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Moola",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Bani",
    "name_native": "बानी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Speech, goddess Saraswati",
    "meaning_native": "वाणी, देवी सरस्वती",
    "starting_letter": "B",
    "pronunciation": "Bani",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Charvi",
    "name_native": "चार्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful, attractive",
    "meaning_native": "सुंदर, आकर्षक",
    "starting_letter": "C",
    "pronunciation": "Charvi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Daksha",
    "name_native": "दक्षा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Skilled, competent",
    "meaning_native": "कुशल, योग्य",
    "starting_letter": "D",
    "pronunciation": "Daksha",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "श्रवण"
  },
  {
    "name_en": "Deepika",
    "name_native": "दीपिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Light, lamp",
    "meaning_native": "प्रकाश, दीप",
    "starting_letter": "D",
    "pronunciation": "Deepika",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Eesha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Pure, goddess Parvati",
    "meaning_native": "पवित्र, देवी पार्वती",
    "starting_letter": "E",
    "pronunciation": "Eesha",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Falak",
    "name_native": "फलक",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sky, heaven",
    "meaning_native": "आकाश, स्वर्ग",
    "starting_letter": "F",
    "pronunciation": "Falak",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Garima",
    "name_native": "गरिमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Honor, dignity",
    "meaning_native": "सम्मान, गौरव",
    "starting_letter": "G",
    "pronunciation": "Garima",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Hansika",
    "name_native": "हंसिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Swan, pure",
    "meaning_native": "हंस, पवित्र",
    "starting_letter": "H",
    "pronunciation": "Hansika",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "श्रवण"
  },
  {
    "name_en": "Ishita",
    "name_native": "इशिता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Superior, mastery",
    "meaning_native": "श्रेष्ठ, निपुणता",
    "starting_letter": "I",
    "pronunciation": "Ishita",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Janvi",
    "name_native": "जान्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "River Ganga",
    "meaning_native": "गंगा नदी",
    "starting_letter": "J",
    "pronunciation": "Janvi",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Kashvi",
    "name_native": "काश्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Shining, radiant",
    "meaning_native": "चमकीली, तेजस्वी",
    "starting_letter": "K",
    "pronunciation": "Kashvi",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Poorva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Lavanya",
    "name_native": "लावण्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Grace, beauty",
    "meaning_native": "सुंदरता, शोभा",
    "starting_letter": "L",
    "pronunciation": "Lavanya",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Mahika",
    "name_native": "महिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fragrance, dew",
    "meaning_native": "सुगंध, ओस",
    "starting_letter": "M",
    "pronunciation": "Mahika",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Poorva Phalguni",
    "nakshatra_native": "पूर्वा फाल्गुनी"
  },
  {
    "name_en": "Navya",
    "name_native": "नव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "New, young",
    "meaning_native": "नया, युवा",
    "starting_letter": "N",
    "pronunciation": "Navya",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "उत्तरा फाल्गुनी"
  },
  {
    "name_en": "Ojaswi",
    "name_native": "ओजस्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lustrous, radiant",
    "meaning_native": "तेजस्वी, दीप्तिमान",
    "starting_letter": "O",
    "pronunciation": "Ojaswi",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "ज्येष्ठा"
  },
  {
    "name_en": "Pallavi",
    "name_native": "पल्लवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "New leaves, young shoot",
    "meaning_native": "नए पत्ते, कोंपल",
    "starting_letter": "P",
    "pronunciation": "Pallavi",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "पूर्व भाद्रपद"
  },
  {
    "name_en": "Radhika",
    "name_native": "राधिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Successful, beloved of Krishna",
    "meaning_native": "सफल, कृष्ण प्रिया",
    "starting_letter": "R",
    "pronunciation": "Radhika",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Shreya",
    "name_native": "श्रेया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Auspicious, best",
    "meaning_native": "शुभ, श्रेष्ठ",
    "starting_letter": "S",
    "pronunciation": "Shreya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Poorva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Tanvi",
    "name_native": "तन्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Delicate, beautiful",
    "meaning_native": "नाजुक, सुंदर",
    "starting_letter": "T",
    "pronunciation": "Tanvi",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Urvi",
    "name_native": "उर्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, wide",
    "meaning_native": "पृथ्वी, विस्तृत",
    "starting_letter": "U",
    "pronunciation": "Urvi",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "उत्तरा फाल्गुनी"
  },
  {
    "name_en": "Vedika",
    "name_native": "वेदिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Altar, consciousness",
    "meaning_native": "वेदी, चेतना",
    "starting_letter": "V",
    "pronunciation": "Vedika",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Yamini",
    "name_native": "यामिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Night, nocturnal",
    "meaning_native": "रात्रि, रैनी",
    "starting_letter": "Y",
    "pronunciation": "Yamini",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "उत्तर भाद्रपद"
  },
  {
    "name_en": "Zoya",
    "name_native": "जोया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Life, loving",
    "meaning_native": "जीवन, प्रेमी",
    "starting_letter": "Z",
    "pronunciation": "Zoya",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Aahana",
    "name_native": "आहना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "First ray of sun",
    "meaning_native": "सूर्य की पहली किरण",
    "starting_letter": "A",
    "pronunciation": "Aahana",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Akshara",
    "name_native": "अक्षरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Letter, imperishable",
    "meaning_native": "अक्षर, अविनाशी",
    "starting_letter": "A",
    "pronunciation": "Akshara",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Antariksha",
    "name_native": "अंतरिक्षा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Space, sky",
    "meaning_native": "अंतरिक्ष, आकाश",
    "starting_letter": "A",
    "pronunciation": "Antariksha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "विशाखा"
  },
  {
    "name_en": "Anvi",
    "name_native": "अन्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess of forest",
    "meaning_native": "वन देवी",
    "starting_letter": "A",
    "pronunciation": "Anvi",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Moola",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Arushi",
    "name_native": "अरुषि",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "First ray of sun",
    "meaning_native": "सूर्य की पहली किरण",
    "starting_letter": "A",
    "pronunciation": "Arushi",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Brinda",
    "name_native": "बृंदा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Tulsi, sacred basil",
    "meaning_native": "तुलसी, पवित्र बेसिल",
    "starting_letter": "B",
    "pronunciation": "Brinda",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Chinmayi",
    "name_native": "चिन्मयी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Blissful, consciousness",
    "meaning_native": "आनंदमय, चेतना",
    "starting_letter": "C",
    "pronunciation": "Chinmayi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "उत्तर भाद्रपद"
  },
  {
    "name_en": "Darshana",
    "name_native": "दर्शना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Seeing, vision",
    "meaning_native": "देखना, दर्शन",
    "starting_letter": "D",
    "pronunciation": "Darshana",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Evana",
    "name_native": "इवाना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, life",
    "meaning_native": "पृथ्वी, जीवन",
    "starting_letter": "E",
    "pronunciation": "Evana",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Anuradha",
    "nakshatra_native": "अनुराधा"
  },
  {
    "name_en": "Freya",
    "name_native": "फ्रेया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Noble lady",
    "meaning_native": "कुलीन महिला",
    "starting_letter": "F",
    "pronunciation": "Freya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Poorva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Grishma",
    "name_native": "ग्रीष्मा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Summer, warmth",
    "meaning_native": "गर्मी, उष्णता",
    "starting_letter": "G",
    "pronunciation": "Grishma",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Hrithika",
    "name_native": "ह्रितिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "From heart",
    "meaning_native": "हृदय से",
    "starting_letter": "H",
    "pronunciation": "Hrithika",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Inaya",
    "name_native": "इनाया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Concern, care",
    "meaning_native": "चिंता, देखभाल",
    "starting_letter": "I",
    "pronunciation": "Inaya",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Jhanvi",
    "name_native": "झान्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "River Ganga",
    "meaning_native": "गंगा नदी",
    "starting_letter": "J",
    "pronunciation": "Jhanvi",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Poorva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Kavitha",
    "name_native": "कविता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Poetry, poem",
    "meaning_native": "कविता, काव्य",
    "starting_letter": "K",
    "pronunciation": "Kavitha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Laxmi",
    "name_native": "लक्ष्मी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "धन की देवी",
    "starting_letter": "L",
    "pronunciation": "Laxmi",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Madhura",
    "name_native": "मधुरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sweet, melodious",
    "meaning_native": "मीठा, मधुर",
    "starting_letter": "M",
    "pronunciation": "Madhura",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Nitya",
    "name_native": "नित्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Eternal, always",
    "meaning_native": "शाश्वत, हमेशा",
    "starting_letter": "N",
    "pronunciation": "Nitya",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "ज्येष्ठा"
  },
  {
    "name_en": "Oviya",
    "name_native": "ओविया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Artist, beautiful drawing",
    "meaning_native": "कलाकार, सुंदर चित्र",
    "starting_letter": "O",
    "pronunciation": "Oviya",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Parineeta",
    "name_native": "परिणीता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Expert, married woman",
    "meaning_native": "निपुण, विवाहित महिला",
    "starting_letter": "P",
    "pronunciation": "Parineeta",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "उत्तरा फाल्गुनी"
  },
  {
    "name_en": "Ridhi",
    "name_native": "रिधि",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Prosperity, good fortune",
    "meaning_native": "समृद्धि, सौभाग्य",
    "starting_letter": "R",
    "pronunciation": "Ridhi",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Sanya",
    "name_native": "सान्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Born on Saturday",
    "meaning_native": "शनिवार को जन्मी",
    "starting_letter": "S",
    "pronunciation": "Sanya",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Trisha",
    "name_native": "तृषा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Thirst, wish",
    "meaning_native": "प्यास, इच्छा",
    "starting_letter": "T",
    "pronunciation": "Trisha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Moola",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Unnati",
    "name_native": "उन्नति",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Progress, advancement",
    "meaning_native": "प्रगति, उन्नति",
    "starting_letter": "U",
    "pronunciation": "Unnati",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "विशाखा"
  },
  {
    "name_en": "Varsha",
    "name_native": "वर्षा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Rain, monsoon",
    "meaning_native": "बारिश, मानसून",
    "starting_letter": "V",
    "pronunciation": "Varsha",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Yukti",
    "name_native": "युक्ति",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Trick, solution",
    "meaning_native": "तरकीब, समाधान",
    "starting_letter": "Y",
    "pronunciation": "Yukti",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Poorva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zinia",
    "name_native": "जिनिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Flower name",
    "meaning_native": "फूल का नाम",
    "starting_letter": "Z",
    "pronunciation": "Zinia",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Aadhya",
    "name_native": "आध्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "First power, beginning",
    "meaning_native": "प्रथम शक्ति, आदि",
    "starting_letter": "A",
    "pronunciation": "Aadhya",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Bhumika",
    "name_native": "भूमिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, role",
    "meaning_native": "धरती, भूमिका",
    "starting_letter": "B",
    "pronunciation": "Bhumika",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Charvi",
    "name_native": "चार्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful woman",
    "meaning_native": "सुंदर स्त्री",
    "starting_letter": "C",
    "pronunciation": "Charvi",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Drishti",
    "name_native": "दृष्टि",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sight, vision",
    "meaning_native": "नजर, दृष्टि",
    "starting_letter": "D",
    "pronunciation": "Drishti",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Evani",
    "name_native": "एवानी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, nature",
    "meaning_native": "पृथ्वी, प्रकृति",
    "starting_letter": "E",
    "pronunciation": "Evani",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "कृत्तिका"
  },
  {
    "name_en": "Garima",
    "name_native": "गरिमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Dignity, pride",
    "meaning_native": "गरिमा, गौरव",
    "starting_letter": "G",
    "pronunciation": "Garima",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Harshita",
    "name_native": "हर्षिता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Full of joy",
    "meaning_native": "खुशी से भरपूर",
    "starting_letter": "H",
    "pronunciation": "Harshita",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Ishika",
    "name_native": "ईशिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred pen, brush",
    "meaning_native": "पवित्र कलम, तूलिका",
    "starting_letter": "I",
    "pronunciation": "Ishika",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Jivika",
    "name_native": "जीविका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Source of life",
    "meaning_native": "जीवन का स्रोत",
    "starting_letter": "J",
    "pronunciation": "Jivika",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "श्रवण"
  },
  {
    "name_en": "Khushi",
    "name_native": "खुशी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Happiness, joy",
    "meaning_native": "प्रसन्नता, आनंद",
    "starting_letter": "K",
    "pronunciation": "Khushi",
    "origin": "Hindi",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Lavanya",
    "name_native": "लावण्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Grace, beauty",
    "meaning_native": "अनुग्रह, सुंदरता",
    "starting_letter": "L",
    "pronunciation": "Lavanya",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Manasvi",
    "name_native": "मानसी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Intelligent, wise",
    "meaning_native": "बुद्धिमान, ज्ञानी",
    "starting_letter": "M",
    "pronunciation": "Manasvi",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Navya",
    "name_native": "नव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "New, fresh",
    "meaning_native": "नया, ताजा",
    "starting_letter": "N",
    "pronunciation": "Navya",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Ojaswi",
    "name_native": "ओजस्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Bright, lustrous",
    "meaning_native": "तेजस्वी, चमकदार",
    "starting_letter": "O",
    "pronunciation": "Ojaswi",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Pihu",
    "name_native": "पीहू",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Peacock's sound",
    "meaning_native": "मोर की आवाज",
    "starting_letter": "P",
    "pronunciation": "Pihu",
    "origin": "Hindi",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Riya",
    "name_native": "रिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Singer, graceful",
    "meaning_native": "गायिका, मनोहर",
    "starting_letter": "R",
    "pronunciation": "Riya",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Saanvi",
    "name_native": "सांवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess Lakshmi",
    "meaning_native": "देवी लक्ष्मी",
    "starting_letter": "S",
    "pronunciation": "Saanvi",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tanvi",
    "name_native": "तन्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Delicate, beautiful",
    "meaning_native": "नाजुक, सुंदर",
    "starting_letter": "T",
    "pronunciation": "Tanvi",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Urvi",
    "name_native": "उर्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Earth, wide",
    "meaning_native": "पृथ्वी, विस्तृत",
    "starting_letter": "U",
    "pronunciation": "Urvi",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vanya",
    "name_native": "वन्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Forest, nature",
    "meaning_native": "वन, प्रकृति",
    "starting_letter": "V",
    "pronunciation": "Vanya",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Yashika",
    "name_native": "यशिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fame, glory",
    "meaning_native": "यश, कीर्ति",
    "starting_letter": "Y",
    "pronunciation": "Yashika",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Princess, flower",
    "meaning_native": "राजकुमारी, फूल",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Aarushi",
    "name_native": "आरुषि",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "First ray of sun",
    "meaning_native": "सूर्य की पहली किरण",
    "starting_letter": "A",
    "pronunciation": "Aarushi",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bhavika",
    "name_native": "भाविका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Expressive, emotional",
    "meaning_native": "भावुक, संवेदनशील",
    "starting_letter": "B",
    "pronunciation": "Bhavika",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Charvi",
    "name_native": "चार्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful, graceful",
    "meaning_native": "सुंदर, मनोहर",
    "starting_letter": "C",
    "pronunciation": "Charvi",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Disha",
    "name_native": "दिशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Direction, path",
    "meaning_native": "दिशा, मार्ग",
    "starting_letter": "D",
    "pronunciation": "Disha",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Esha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Desire, wish",
    "meaning_native": "इच्छा, कामना",
    "starting_letter": "E",
    "pronunciation": "Esha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Fatima",
    "name_native": "फातिमा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Daughter of Prophet",
    "meaning_native": "नबी की बेटी",
    "starting_letter": "F",
    "pronunciation": "Fatima",
    "origin": "Arabic"
  },
  {
    "name_en": "Gayatri",
    "name_native": "गायत्री",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Sacred verse",
    "meaning_native": "पवित्र मंत्र",
    "starting_letter": "G",
    "pronunciation": "Gayatri",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Harini",
    "name_native": "हरिणी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Deer, graceful",
    "meaning_native": "हिरणी, मनोहर",
    "starting_letter": "H",
    "pronunciation": "Harini",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Indira",
    "name_native": "इंदिरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess Lakshmi",
    "meaning_native": "देवी लक्ष्मी",
    "starting_letter": "I",
    "pronunciation": "Indira",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jaya",
    "name_native": "जया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Victory",
    "meaning_native": "विजय",
    "starting_letter": "J",
    "pronunciation": "Jaya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Kavya",
    "name_native": "काव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Poetry, literature",
    "meaning_native": "कविता, साहित्य",
    "starting_letter": "K",
    "pronunciation": "Kavya",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Lakshmi",
    "name_native": "लक्ष्मी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "धन की देवी",
    "starting_letter": "L",
    "pronunciation": "Lakshmi",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Madhavi",
    "name_native": "माधवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Spring creeper",
    "meaning_native": "वसंत लता",
    "starting_letter": "M",
    "pronunciation": "Madhavi",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Nandini",
    "name_native": "नंदिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Daughter, blessed",
    "meaning_native": "बेटी, आशीर्वादित",
    "starting_letter": "N",
    "pronunciation": "Nandini",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Oviya",
    "name_native": "ओविया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful drawing",
    "meaning_native": "सुंदर चित्र",
    "starting_letter": "O",
    "pronunciation": "Oviya",
    "origin": "Tamil",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Padma",
    "name_native": "पद्मा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lotus",
    "meaning_native": "कमल",
    "starting_letter": "P",
    "pronunciation": "Padma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Rekha",
    "name_native": "रेखा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Line, streak",
    "meaning_native": "रेखा, लकीर",
    "starting_letter": "R",
    "pronunciation": "Rekha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Sangeeta",
    "name_native": "संगीता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Music",
    "meaning_native": "संगीत",
    "starting_letter": "S",
    "pronunciation": "Sangeeta",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tara",
    "name_native": "तारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "T",
    "pronunciation": "Tara",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Uma",
    "name_native": "उमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "देवी पार्वती",
    "starting_letter": "U",
    "pronunciation": "Uma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vasantha",
    "name_native": "वसंत",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Spring",
    "meaning_native": "वसंत",
    "starting_letter": "V",
    "pronunciation": "Vasantha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Yamini",
    "name_native": "यामिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Night",
    "meaning_native": "रात",
    "starting_letter": "Y",
    "pronunciation": "Yamini",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Princess",
    "meaning_native": "राजकुमारी",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Abirami",
    "name_native": "अबिरामी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful",
    "meaning_native": "सुंदर",
    "starting_letter": "A",
    "pronunciation": "Abirami",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bhuvana",
    "name_native": "भुवना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "World",
    "meaning_native": "संसार",
    "starting_letter": "B",
    "pronunciation": "Bhuvana",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Chandrika",
    "name_native": "चंद्रिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Moonlight",
    "meaning_native": "चांदनी",
    "starting_letter": "C",
    "pronunciation": "Chandrika",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Dhanalakshmi",
    "name_native": "धनलक्ष्मी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "धन की देवी",
    "starting_letter": "D",
    "pronunciation": "Dhanalakshmi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Eswari",
    "name_native": "ईश्वरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess",
    "meaning_native": "देवी",
    "starting_letter": "E",
    "pronunciation": "Eswari",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Fatima",
    "name_native": "फातिमा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Daughter of Prophet",
    "meaning_native": "नबी की बेटी",
    "starting_letter": "F",
    "pronunciation": "Fatima",
    "origin": "Arabic"
  },
  {
    "name_en": "Gowri",
    "name_native": "गौरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Fair complexioned",
    "meaning_native": "गोरी",
    "starting_letter": "G",
    "pronunciation": "Gowri",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Hamsini",
    "name_native": "हंसिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Swan",
    "meaning_native": "हंसिनी",
    "starting_letter": "H",
    "pronunciation": "Hamsini",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Indumathi",
    "name_native": "इंदुमती",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Full moon",
    "meaning_native": "पूर्णिमा",
    "starting_letter": "I",
    "pronunciation": "Indumathi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jaya",
    "name_native": "जया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Victory",
    "meaning_native": "विजय",
    "starting_letter": "J",
    "pronunciation": "Jaya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Kavitha",
    "name_native": "कविता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Poem",
    "meaning_native": "कविता",
    "starting_letter": "K",
    "pronunciation": "Kavitha",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Lakshana",
    "name_native": "लक्षणा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Good qualities",
    "meaning_native": "अच्छे गुण",
    "starting_letter": "L",
    "pronunciation": "Lakshana",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Madhavi",
    "name_native": "माधवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Spring creeper",
    "meaning_native": "वसंत लता",
    "starting_letter": "M",
    "pronunciation": "Madhavi",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Nandini",
    "name_native": "नंदिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Daughter",
    "meaning_native": "बेटी",
    "starting_letter": "N",
    "pronunciation": "Nandini",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Oviya",
    "name_native": "ओविया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Beautiful drawing",
    "meaning_native": "सुंदर चित्र",
    "starting_letter": "O",
    "pronunciation": "Oviya",
    "origin": "Tamil",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Padma",
    "name_native": "पद्मा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Lotus",
    "meaning_native": "कमल",
    "starting_letter": "P",
    "pronunciation": "Padma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Rekha",
    "name_native": "रेखा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Line",
    "meaning_native": "रेखा",
    "starting_letter": "R",
    "pronunciation": "Rekha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Sangeeta",
    "name_native": "संगीता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Music",
    "meaning_native": "संगीत",
    "starting_letter": "S",
    "pronunciation": "Sangeeta",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tara",
    "name_native": "तारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "T",
    "pronunciation": "Tara",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Uma",
    "name_native": "उमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "देवी पार्वती",
    "starting_letter": "U",
    "pronunciation": "Uma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vasantha",
    "name_native": "वसंत",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Spring",
    "meaning_native": "वसंत",
    "starting_letter": "V",
    "pronunciation": "Vasantha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Yamini",
    "name_native": "यामिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Hindi",
    "meaning_en": "Night",
    "meaning_native": "रात",
    "starting_letter": "Y",
    "pronunciation": "Yamini",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Hindi",
    "meaning_en": "Princess",
    "meaning_native": "राजकुमारी",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  }
];

export default HindiGirlNames;
