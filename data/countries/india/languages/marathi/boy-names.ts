import type { NameData } from "@/types/name-data";

export const MarathiBoyNames: NameData[] = [
  {
    "name_en": "Aarav",
    "name_native": "आरव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Peaceful, calm",
    "meaning_native": "शांत, शांतिपूर्ण",
    "starting_letter": "A",
    "pronunciation": "Aarav",
    "origin": "Sanskrit",
    "rashi": "<PERSON><PERSON><PERSON> (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "<PERSON><PERSON><PERSON>",
    "name_native": "अर्जुन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Bright, pure",
    "meaning_native": "उज्ज्वल, पवित्र",
    "starting_letter": "A",
    "pronunciation": "<PERSON>r<PERSON>",
    "origin": "Sanskrit",
    "rashi": "<PERSON><PERSON> (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Has<PERSON>",
    "nakshatra_native": "Has<PERSON>"
  },
  {
    "name_en": "Aditya",
    "name_native": "आदित्य",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sun God",
    "meaning_native": "सूर्य देव",
    "starting_letter": "A",
    "pronunciation": "Aditya",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "Bharani"
  },
  {
    "name_en": "Atharv",
    "name_native": "अथर्व",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Knowledge, wisdom",
    "meaning_native": "ज्ञान, बुद्धी",
    "starting_letter": "A",
    "pronunciation": "Atharv",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "Rohini"
  },
  {
    "name_en": "Arnav",
    "name_native": "अर्णव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ocean",
    "meaning_native": "समुद्र",
    "starting_letter": "A",
    "pronunciation": "Arnav",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "Bharani"
  },
  {
    "name_en": "Bhavesh",
    "name_native": "भावेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of emotions",
    "meaning_native": "भावना नाथ",
    "starting_letter": "B",
    "pronunciation": "Bhavesh",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Ashlesha",
    "nakshatra_native": "Ashlesha"
  },
  {
    "name_en": "Chirag",
    "name_native": "चिराग",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lamp",
    "meaning_native": "दिवा",
    "starting_letter": "C",
    "pronunciation": "Chirag",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Anuradha",
    "nakshatra_native": "Anuradha"
  },
  {
    "name_en": "Dev",
    "name_native": "देव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "God",
    "meaning_native": "देव",
    "starting_letter": "D",
    "pronunciation": "Dev",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Dhruv",
    "name_native": "ध्रुव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Pole star",
    "meaning_native": "ध्रुव तारा",
    "starting_letter": "D",
    "pronunciation": "Dhruv",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "Ashwini"
  },
  {
    "name_en": "Gaurav",
    "name_native": "गौरव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Pride",
    "meaning_native": "गौरव",
    "starting_letter": "G",
    "pronunciation": "Gaurav",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Harsh",
    "name_native": "हर्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Happiness",
    "meaning_native": "आनंद",
    "starting_letter": "H",
    "pronunciation": "Harsh",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Ishaan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Shiva",
    "meaning_native": "भगवान शिव",
    "starting_letter": "I",
    "pronunciation": "Ishaan",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Jai",
    "name_native": "जय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Victory",
    "meaning_native": "विजय",
    "starting_letter": "J",
    "pronunciation": "Jai",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Kiran",
    "name_native": "किरण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ray of light",
    "meaning_native": "प्रकाश किरण",
    "starting_letter": "K",
    "pronunciation": "Kiran",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Laksh",
    "name_native": "लक्ष",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Target",
    "meaning_native": "लक्ष्य",
    "starting_letter": "L",
    "pronunciation": "Laksh",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "Ardra"
  },
  {
    "name_en": "Mihir",
    "name_native": "मिहिर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sun",
    "meaning_native": "सूर्य",
    "starting_letter": "M",
    "pronunciation": "Mihir",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Om",
    "name_native": "ओम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sacred sound",
    "meaning_native": "पवित्र ध्वनी",
    "starting_letter": "O",
    "pronunciation": "Om",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Pranav",
    "name_native": "प्रणव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Om",
    "meaning_native": "ओंकार",
    "starting_letter": "P",
    "pronunciation": "Pranav",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "Ardra"
  },
  {
    "name_en": "Rahul",
    "name_native": "राहुल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Efficient",
    "meaning_native": "कुशल",
    "starting_letter": "R",
    "pronunciation": "Rahul",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "Ashwini"
  },
  {
    "name_en": "Shaan",
    "name_native": "शान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Pride",
    "meaning_native": "अभिमान",
    "starting_letter": "S",
    "pronunciation": "Shaan",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "Uttara Phalguni"
  },
  {
    "name_en": "Tanmay",
    "name_native": "तन्मय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Absorbed",
    "meaning_native": "तन्मय",
    "starting_letter": "T",
    "pronunciation": "Tanmay",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "Mrigashira"
  },
  {
    "name_en": "Ved",
    "name_native": "वेद",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Knowledge",
    "meaning_native": "वेद",
    "starting_letter": "V",
    "pronunciation": "Ved",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "Swati"
  },
  {
    "name_en": "Yash",
    "name_native": "यश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Fame",
    "meaning_native": "यश",
    "starting_letter": "Y",
    "pronunciation": "Yash",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Vivek",
    "name_native": "विवेक",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Wisdom",
    "meaning_native": "विवेक",
    "starting_letter": "V",
    "pronunciation": "Vivek",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "Krittika"
  },
  {
    "name_en": "Siddharth",
    "name_native": "सिद्धार्थ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Achiever",
    "meaning_native": "सिद्धार्थ",
    "starting_letter": "S",
    "pronunciation": "Siddharth",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Aaditya",
    "name_native": "आदित्य",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sun",
    "meaning_native": "सूर्य",
    "starting_letter": "A",
    "pronunciation": "Aaditya",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Krittika",
    "nakshatra_native": "Krittika"
  },
  {
    "name_en": "Bhavesh",
    "name_native": "भावेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of universe",
    "meaning_native": "ब्रह्मांडाचा स्वामी",
    "starting_letter": "B",
    "pronunciation": "Bhavesh",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Chaitanya",
    "name_native": "चैतन्य",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Consciousness",
    "meaning_native": "चैतन्य",
    "starting_letter": "C",
    "pronunciation": "Chaitanya",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Darshan",
    "name_native": "दर्शन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Vision",
    "meaning_native": "दर्शन",
    "starting_letter": "D",
    "pronunciation": "Darshan",
    "origin": "Sanskrit",
    "rashi": "Karkata (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "Pushya"
  },
  {
    "name_en": "Eknath",
    "name_native": "एकनाथ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "One lord",
    "meaning_native": "एक नाथ",
    "starting_letter": "E",
    "pronunciation": "Eknath",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "Rohini"
  },
  {
    "name_en": "Ganesh",
    "name_native": "गणेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Ganesha",
    "meaning_native": "गणेश",
    "starting_letter": "G",
    "pronunciation": "Ganesh",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "Dhanishta"
  },
  {
    "name_en": "Hemant",
    "name_native": "हेमंत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Winter season",
    "meaning_native": "शिशिर ऋतू",
    "starting_letter": "H",
    "pronunciation": "Hemant",
    "origin": "Sanskrit",
    "rashi": "Karkata (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Ishan",
    "name_native": "ईशान",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Vishnu",
    "meaning_native": "ईशान",
    "starting_letter": "I",
    "pronunciation": "Ishan",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "Krittika"
  },
  {
    "name_en": "Jayesh",
    "name_native": "जयेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of victory",
    "meaning_native": "विजयाचा स्वामी",
    "starting_letter": "J",
    "pronunciation": "Jayesh",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Kartik",
    "name_native": "कार्तिक",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Son of Lord Shiva",
    "meaning_native": "शिवपुत्र",
    "starting_letter": "K",
    "pronunciation": "Kartik",
    "origin": "Sanskrit",
    "rashi": "Karkata (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Laxman",
    "name_native": "लक्ष्मण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Prosperous",
    "meaning_native": "समृद्ध",
    "starting_letter": "L",
    "pronunciation": "Laxman",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "Mrigashira"
  },
  {
    "name_en": "Mahesh",
    "name_native": "महेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Shiva",
    "meaning_native": "महेश",
    "starting_letter": "M",
    "pronunciation": "Mahesh",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Nilesh",
    "name_native": "नीलेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Blue god",
    "meaning_native": "नील देव",
    "starting_letter": "N",
    "pronunciation": "Nilesh",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "Jyeshtha"
  },
  {
    "name_en": "Omkar",
    "name_native": "ओंकार",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "The sacred syllable Om, Creator",
    "meaning_native": "पवित्र ओंकार, सृष्टीकर्ता",
    "starting_letter": "O",
    "pronunciation": "Omkar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Parag",
    "name_native": "पराग",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Pollen, fragrance",
    "meaning_native": "परागकण, सुगंध",
    "starting_letter": "P",
    "pronunciation": "Parag",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Ravindra",
    "name_native": "रवींद्र",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of the Sun",
    "meaning_native": "सूर्याचा स्वामी",
    "starting_letter": "R",
    "pronunciation": "Ravindra",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Sankalp",
    "name_native": "संकल्प",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Resolution, determination",
    "meaning_native": "संकल्प, निर्धार",
    "starting_letter": "S",
    "pronunciation": "Sankalp",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tanmay",
    "name_native": "तनय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Engrossed, absorbed",
    "meaning_native": "तल्लीन, मग्न",
    "starting_letter": "T",
    "pronunciation": "Tanmay",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Uday",
    "name_native": "उदय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Rise, to rise",
    "meaning_native": "उदय, उगवणे",
    "starting_letter": "U",
    "pronunciation": "Uday",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूळा"
  },
  {
    "name_en": "Vasant",
    "name_native": "वसंत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Spring season",
    "meaning_native": "वसंत ऋतु",
    "starting_letter": "V",
    "pronunciation": "Vasant",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Yogesh",
    "name_native": "योगेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "God of yoga",
    "meaning_native": "योगाचा देव",
    "starting_letter": "Y",
    "pronunciation": "Yogesh",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "ज्येष्ठा"
  },
  {
    "name_en": "Shrikant",
    "name_native": "श्रीकांत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Vishnu",
    "meaning_native": "भगवान विष्णू",
    "starting_letter": "S",
    "pronunciation": "Shrikant",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Ninad",
    "name_native": "निनाद",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sound, resonance",
    "meaning_native": "नाद, प्रतिध्वनी",
    "starting_letter": "N",
    "pronunciation": "Ninad",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Pranit",
    "name_native": "प्रणित",
    "gender": "boy",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Modest, humble",
    "meaning_native": "नम्र, विनम्र",
    "starting_letter": "P",
    "pronunciation": "Pranit",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Vedant",
    "name_native": "वेदांत",
    "gender": "boy",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ultimate knowledge",
    "meaning_native": "परम ज्ञान",
    "starting_letter": "V",
    "pronunciation": "Vedant",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Ruturaj",
    "name_native": "ऋतुराज",
    "gender": "boy",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of seasons",
    "meaning_native": "ऋतूंचा राजा",
    "starting_letter": "R",
    "pronunciation": "Ruturaj",
    "origin": "Sanskrit",
    "rashi": "Mithun (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Omkar",
    "name_native": "ओंकार",
    "gender": "boy",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "The sound Om",
    "meaning_native": "ॐचा ध्वनी",
    "starting_letter": "O",
    "pronunciation": "Omkar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tanmay",
    "name_native": "तनमय",
    "gender": "boy",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Engrossed, absorbed",
    "meaning_native": "तल्लीन, मग्न",
    "starting_letter": "T",
    "pronunciation": "Tanmay",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Anuradha",
    "nakshatra_native": "अनुराधा"
  },
  {
    "name_en": "Yash",
    "name_native": "यश",
    "gender": "boy",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Fame, success",
    "meaning_native": "यश, प्रसिद्धी",
    "starting_letter": "Y",
    "pronunciation": "Yash",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "male",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Bright",
    "meaning_native": "चमकदार",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Abhinav",
    "name_native": "अभिनव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "New",
    "meaning_native": "नवीन",
    "starting_letter": "A",
    "pronunciation": "Abhinav",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bhaskar",
    "name_native": "भास्कर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sun",
    "meaning_native": "सूर्य",
    "starting_letter": "B",
    "pronunciation": "Bhaskar",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Chiranjeev",
    "name_native": "चिरंजीव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Immortal",
    "meaning_native": "अमर",
    "starting_letter": "C",
    "pronunciation": "Chiranjeev",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Dharmesh",
    "name_native": "धर्मेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of dharma",
    "meaning_native": "धर्माचा स्वामी",
    "starting_letter": "D",
    "pronunciation": "Dharmesh",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Eshwar",
    "name_native": "ईश्वर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "God",
    "meaning_native": "ईश्वर",
    "starting_letter": "E",
    "pronunciation": "Eshwar",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Firoz",
    "name_native": "फिरोज़",
    "gender": "male",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Victorious",
    "meaning_native": "विजयी",
    "starting_letter": "F",
    "pronunciation": "Firoz",
    "origin": "Persian"
  },
  {
    "name_en": "Giriraj",
    "name_native": "गिरिराज",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of mountains",
    "meaning_native": "पर्वतांचा राजा",
    "starting_letter": "G",
    "pronunciation": "Giriraj",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Himanshu",
    "name_native": "हिमांशु",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Moon",
    "meaning_native": "चंद्र",
    "starting_letter": "H",
    "pronunciation": "Himanshu",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Indra",
    "name_native": "इंद्र",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of gods",
    "meaning_native": "देवांचा राजा",
    "starting_letter": "I",
    "pronunciation": "Indra",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jagdish",
    "name_native": "जगदीश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of the world",
    "meaning_native": "जगदीश",
    "starting_letter": "J",
    "pronunciation": "Jagdish",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Kiran",
    "name_native": "किरण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ray of light",
    "meaning_native": "प्रकाशाची किरण",
    "starting_letter": "K",
    "pronunciation": "Kiran",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Loknath",
    "name_native": "लोकनाथ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of people",
    "meaning_native": "लोकनाथ",
    "starting_letter": "L",
    "pronunciation": "Loknath",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Manohar",
    "name_native": "मनोहर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Attractive, beautiful",
    "meaning_native": "आकर्षक, सुंदर",
    "starting_letter": "M",
    "pronunciation": "Manohar",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Nareshwar",
    "name_native": "नरेश्वर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of men",
    "meaning_native": "मानवांचा राजा",
    "starting_letter": "N",
    "pronunciation": "Nareshwar",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "ज्येष्ठा"
  },
  {
    "name_en": "Onkar",
    "name_native": "ओंकार",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Creator",
    "meaning_native": "निर्माता",
    "starting_letter": "O",
    "pronunciation": "Onkar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Pranav",
    "name_native": "प्रणव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sacred sound Om",
    "meaning_native": "पवित्र ओं ध्वनी",
    "starting_letter": "P",
    "pronunciation": "Pranav",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Ruchit",
    "name_native": "रुचित",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Splendid",
    "meaning_native": "शानदार",
    "starting_letter": "R",
    "pronunciation": "Ruchit",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Siddharth",
    "name_native": "सिद्धार्थ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Accomplished goal",
    "meaning_native": "सिद्ध अर्थ",
    "starting_letter": "S",
    "pronunciation": "Siddharth",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tushar",
    "name_native": "तुषार",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Snow",
    "meaning_native": "बर्फ",
    "starting_letter": "T",
    "pronunciation": "Tushar",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पुर्वापंजीवी"
  },
  {
    "name_en": "Umesh",
    "name_native": "उमेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Shiva",
    "meaning_native": "शिवजी",
    "starting_letter": "U",
    "pronunciation": "Umesh",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vikram",
    "name_native": "विक्रम",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Valor",
    "meaning_native": "शौर्य",
    "starting_letter": "V",
    "pronunciation": "Vikram",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Yashwant",
    "name_native": "यशवंत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Famous",
    "meaning_native": "प्रसिद्ध",
    "starting_letter": "Y",
    "pronunciation": "Yashwant",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "male",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Bright",
    "meaning_native": "चमकदार",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Abhinav",
    "name_native": "अभिनव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "New",
    "meaning_native": "नवीन",
    "starting_letter": "A",
    "pronunciation": "Abhinav",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bhaskar",
    "name_native": "भास्कर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sun",
    "meaning_native": "सूर्य",
    "starting_letter": "B",
    "pronunciation": "Bhaskar",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूल"
  },
  {
    "name_en": "Chiranjeev",
    "name_native": "चिरंजीव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Immortal",
    "meaning_native": "अमर",
    "starting_letter": "C",
    "pronunciation": "Chiranjeev",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Dharmesh",
    "name_native": "धर्मेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of dharma",
    "meaning_native": "धर्माचा स्वामी",
    "starting_letter": "D",
    "pronunciation": "Dharmesh",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Eshwar",
    "name_native": "ईश्वर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "God",
    "meaning_native": "ईश्वर",
    "starting_letter": "E",
    "pronunciation": "Eshwar",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Firoz",
    "name_native": "फिरोज़",
    "gender": "male",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Victorious",
    "meaning_native": "विजयी",
    "starting_letter": "F",
    "pronunciation": "Firoz",
    "origin": "Persian"
  },
  {
    "name_en": "Giriraj",
    "name_native": "गिरिराज",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of mountains",
    "meaning_native": "पर्वतांचा राजा",
    "starting_letter": "G",
    "pronunciation": "Giriraj",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Himanshu",
    "name_native": "हिमांशु",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Moon",
    "meaning_native": "चंद्र",
    "starting_letter": "H",
    "pronunciation": "Himanshu",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Indra",
    "name_native": "इंद्र",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of gods",
    "meaning_native": "देवांचा राजा",
    "starting_letter": "I",
    "pronunciation": "Indra",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jagdish",
    "name_native": "जगदीश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of the world",
    "meaning_native": "जगदीश",
    "starting_letter": "J",
    "pronunciation": "Jagdish",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Kiran",
    "name_native": "किरण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ray of light",
    "meaning_native": "प्रकाशाची किरण",
    "starting_letter": "K",
    "pronunciation": "Kiran",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Loknath",
    "name_native": "लोकनाथ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of people",
    "meaning_native": "लोकनाथ",
    "starting_letter": "L",
    "pronunciation": "Loknath",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Manohar",
    "name_native": "मनोहर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Attractive, beautiful",
    "meaning_native": "आकर्षक, सुंदर",
    "starting_letter": "M",
    "pronunciation": "Manohar",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Nareshwar",
    "name_native": "नरेश्वर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "King of men",
    "meaning_native": "मानवांचा राजा",
    "starting_letter": "N",
    "pronunciation": "Nareshwar",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "ज्येष्ठा"
  },
  {
    "name_en": "Onkar",
    "name_native": "ओंकार",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Creator",
    "meaning_native": "निर्माता",
    "starting_letter": "O",
    "pronunciation": "Onkar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Pranav",
    "name_native": "प्रणव",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sacred sound Om",
    "meaning_native": "पवित्र ओं ध्वनी",
    "starting_letter": "P",
    "pronunciation": "Pranav",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Ruchit",
    "name_native": "रुचित",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Splendid",
    "meaning_native": "शानदार",
    "starting_letter": "R",
    "pronunciation": "Ruchit",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Siddharth",
    "name_native": "सिद्धार्थ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Accomplished goal",
    "meaning_native": "सिद्ध अर्थ",
    "starting_letter": "S",
    "pronunciation": "Siddharth",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tushar",
    "name_native": "तुषार",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Snow",
    "meaning_native": "बर्फ",
    "starting_letter": "T",
    "pronunciation": "Tushar",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पुर्वापंजीवी"
  },
  {
    "name_en": "Umesh",
    "name_native": "उमेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Shiva",
    "meaning_native": "शिवजी",
    "starting_letter": "U",
    "pronunciation": "Umesh",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vikrant",
    "name_native": "विक्रांत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Brave, victorious",
    "meaning_native": "शूर, विजयी",
    "starting_letter": "V",
    "pronunciation": "Vikrant",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Yash",
    "name_native": "यश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Fame, success",
    "meaning_native": "यश, प्रसिद्धी",
    "starting_letter": "Y",
    "pronunciation": "Yash",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Shridhar",
    "name_native": "श्रीधर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Vishnu",
    "meaning_native": "भगवान विष्णू",
    "starting_letter": "S",
    "pronunciation": "Shridhar",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Nilesh",
    "name_native": "निलेश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord Krishna, blue god",
    "meaning_native": "भगवान कृष्ण, निळा देव",
    "starting_letter": "N",
    "pronunciation": "Nilesh",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Harshal",
    "name_native": "हर्षल",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Glad, happy",
    "meaning_native": "आनंदी, प्रसन्न",
    "starting_letter": "H",
    "pronunciation": "Harshal",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Akshay",
    "name_native": "अक्षय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Immortal, indestructible",
    "meaning_native": "अमर, अविनाशी",
    "starting_letter": "A",
    "pronunciation": "Akshay",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "भरणी"
  },
  {
    "name_en": "Balavant",
    "name_native": "बलवंत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Strong, powerful",
    "meaning_native": "बलवान, शक्तिशाली",
    "starting_letter": "B",
    "pronunciation": "Balavant",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Chandrakant",
    "name_native": "चंद्रकांत",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Beloved of moon",
    "meaning_native": "चंद्राचा प्रिय",
    "starting_letter": "C",
    "pronunciation": "Chandrakant",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Dhananjay",
    "name_native": "धनंजय",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Winner of wealth, Arjuna",
    "meaning_native": "धन जिंकणारा, अर्जुन",
    "starting_letter": "D",
    "pronunciation": "Dhananjay",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "मूळा"
  },
  {
    "name_en": "Eknath",
    "name_native": "एकनाथ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "One lord",
    "meaning_native": "एक नाथ",
    "starting_letter": "E",
    "pronunciation": "Eknath",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Gajanan",
    "name_native": "गजानन",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Elephant faced, Ganesh",
    "meaning_native": "गजानन, गणेश",
    "starting_letter": "G",
    "pronunciation": "Gajanan",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Harshad",
    "name_native": "हर्षद",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Giver of joy",
    "meaning_native": "आनंद देणारा",
    "starting_letter": "H",
    "pronunciation": "Harshad",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "पुष्य"
  },
  {
    "name_en": "Ishwar",
    "name_native": "ईश्वर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "God, supreme being",
    "meaning_native": "ईश्वर, परमेश्वर",
    "starting_letter": "I",
    "pronunciation": "Ishwar",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Jagdish",
    "name_native": "जगदीश",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of the world",
    "meaning_native": "जगदीश",
    "starting_letter": "J",
    "pronunciation": "Jagdish",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुंभ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "धनिष्ठा"
  },
  {
    "name_en": "Kiran",
    "name_native": "किरण",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ray of light",
    "meaning_native": "प्रकाशाची किरण",
    "starting_letter": "K",
    "pronunciation": "Kiran",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "पुनर्वसु"
  },
  {
    "name_en": "Loknath",
    "name_native": "लोकनाथ",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lord of people",
    "meaning_native": "लोकनाथ",
    "starting_letter": "L",
    "pronunciation": "Loknath",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Manohar",
    "name_native": "मनोहर",
    "gender": "male",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Attractive, beautiful",
    "meaning_native": "आकर्षक, सुंदर",
    "starting_letter": "M",
    "pronunciation": "Manohar",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  }
];

export default MarathiBoyNames;
