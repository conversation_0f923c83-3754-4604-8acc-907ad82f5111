import type { NameData } from "@/types/name-data";

export const MarathiGirlNames: NameData[] = [
  {
    "name_en": "<PERSON><PERSON>",
    "name_native": "आइशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Living",
    "meaning_native": "जिवंत",
    "starting_letter": "A",
    "pronunciation": "Aisha",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Ananya",
    "name_native": "अनन्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Unique",
    "meaning_native": "अनन्य",
    "starting_letter": "A",
    "pronunciation": "Ananya",
    "origin": "Sanskrit",
    "rashi": "<PERSON><PERSON> (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "<PERSON>ritti<PERSON>",
    "nakshatra_native": "<PERSON>rittika"
  },
  {
    "name_en": "Aaradhya",
    "name_native": "आराध्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Worshipped",
    "meaning_native": "आराध्या",
    "starting_letter": "A",
    "pronunciation": "Aaradhya",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "Pushya"
  },
  {
    "name_en": "Bhavya",
    "name_native": "भव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Grand",
    "meaning_native": "भव्य",
    "starting_letter": "B",
    "pronunciation": "Bhavya",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "Shatabhisha"
  },
  {
    "name_en": "Disha",
    "name_native": "दिशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Direction",
    "meaning_native": "दिशा",
    "starting_letter": "D",
    "pronunciation": "Disha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "Mula"
  },
  {
    "name_en": "Gauri",
    "name_native": "गौरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Fair",
    "meaning_native": "गौरी",
    "starting_letter": "G",
    "pronunciation": "Gauri",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Harshita",
    "name_native": "हर्षिता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Happy",
    "meaning_native": "हर्षिता",
    "starting_letter": "H",
    "pronunciation": "Harshita",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Isha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess",
    "meaning_native": "ईशा",
    "starting_letter": "I",
    "pronunciation": "Isha",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Ashlesha",
    "nakshatra_native": "Ashlesha"
  },
  {
    "name_en": "Kavya",
    "name_native": "काव्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Poetry",
    "meaning_native": "काव्य",
    "starting_letter": "K",
    "pronunciation": "Kavya",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Lakshmi",
    "name_native": "लक्ष्मी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "लक्ष्मी",
    "starting_letter": "L",
    "pronunciation": "Lakshmi",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Mira",
    "name_native": "मीरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ocean",
    "meaning_native": "मीरा",
    "starting_letter": "M",
    "pronunciation": "Mira",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Nisha",
    "name_native": "निशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Night",
    "meaning_native": "निशा",
    "starting_letter": "N",
    "pronunciation": "Nisha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Priya",
    "name_native": "प्रिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Beloved",
    "meaning_native": "प्रिया",
    "starting_letter": "P",
    "pronunciation": "Priya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "Mula"
  },
  {
    "name_en": "Riya",
    "name_native": "रिया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Singer",
    "meaning_native": "रिया",
    "starting_letter": "R",
    "pronunciation": "Riya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "Mula"
  },
  {
    "name_en": "Saanvi",
    "name_native": "सान्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Lakshmi",
    "meaning_native": "सान्वी",
    "starting_letter": "S",
    "pronunciation": "Saanvi",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Tara",
    "name_native": "तारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "T",
    "pronunciation": "Tara",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Uttara Phalguni",
    "nakshatra_native": "Uttara Phalguni"
  },
  {
    "name_en": "Uma",
    "name_native": "उमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "उमा",
    "starting_letter": "U",
    "pronunciation": "Uma",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Vanya",
    "name_native": "वन्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Forest",
    "meaning_native": "वन्या",
    "starting_letter": "V",
    "pronunciation": "Vanya",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Purva Bhadrapada",
    "nakshatra_native": "Purva Bhadrapada"
  },
  {
    "name_en": "Yashvi",
    "name_native": "यश्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Famous",
    "meaning_native": "यशवी",
    "starting_letter": "Y",
    "pronunciation": "Yashvi",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Shreya",
    "name_native": "श्रेया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Auspicious",
    "meaning_native": "श्रेया",
    "starting_letter": "S",
    "pronunciation": "Shreya",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Aarohi",
    "name_native": "आरोही",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Musical note",
    "meaning_native": "संगीत स्वर",
    "starting_letter": "A",
    "pronunciation": "Aarohi",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "Bharani"
  },
  {
    "name_en": "Bhavana",
    "name_native": "भावना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Feeling",
    "meaning_native": "भावना",
    "starting_letter": "B",
    "pronunciation": "Bhavana",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Chaitali",
    "name_native": "चैताली",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Born in spring",
    "meaning_native": "वसंत ऋतूत जन्मलेली",
    "starting_letter": "C",
    "pronunciation": "Chaitali",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Uttara Bhadrapada",
    "nakshatra_native": "Uttara Bhadrapada"
  },
  {
    "name_en": "Deepika",
    "name_native": "दीपिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Little lamp",
    "meaning_native": "छोटा दीप",
    "starting_letter": "D",
    "pronunciation": "Deepika",
    "origin": "Sanskrit",
    "rashi": "Karkata (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "Pushya"
  },
  {
    "name_en": "Ekta",
    "name_native": "एकता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Unity",
    "meaning_native": "एकता",
    "starting_letter": "E",
    "pronunciation": "Ekta",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "Rohini"
  },
  {
    "name_en": "Gauri",
    "name_native": "गौरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "गौरी",
    "starting_letter": "G",
    "pronunciation": "Gauri",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "Dhanishta"
  },
  {
    "name_en": "Harshada",
    "name_native": "हर्षदा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Giver of joy",
    "meaning_native": "आनंद देणारी",
    "starting_letter": "H",
    "pronunciation": "Harshada",
    "origin": "Sanskrit",
    "rashi": "Karkata (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Ishani",
    "name_native": "ईशानी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Durga",
    "meaning_native": "ईशानी",
    "starting_letter": "I",
    "pronunciation": "Ishani",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "Krittika"
  },
  {
    "name_en": "Jyoti",
    "name_native": "ज्योती",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Light",
    "meaning_native": "प्रकाश",
    "starting_letter": "J",
    "pronunciation": "Jyoti",
    "origin": "Sanskrit",
    "rashi": "Makara (Capricorn)",
    "rashi_native": "मकर",
    "nakshatra": "Shravana",
    "nakshatra_native": "Shravana"
  },
  {
    "name_en": "Ketaki",
    "name_native": "केतकी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Flower",
    "meaning_native": "फूल",
    "starting_letter": "K",
    "pronunciation": "Ketaki",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Lata",
    "name_native": "लता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Creeper",
    "meaning_native": "वेल",
    "starting_letter": "L",
    "pronunciation": "Lata",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "Mrigashira"
  },
  {
    "name_en": "Madhuri",
    "name_native": "माधुरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sweetness",
    "meaning_native": "गोडवा",
    "starting_letter": "M",
    "pronunciation": "Madhuri",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Namrata",
    "name_native": "नम्रता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Humility",
    "meaning_native": "नम्रता",
    "starting_letter": "N",
    "pronunciation": "Namrata",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "Jyeshtha"
  },
  {
    "name_en": "Ojasvi",
    "name_native": "ओजस्वी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Bright",
    "meaning_native": "तेजस्वी",
    "starting_letter": "O",
    "pronunciation": "Ojasvi",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "Shatabhisha"
  },
  {
    "name_en": "Pallavi",
    "name_native": "पल्लवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "New leaves",
    "meaning_native": "नवीन पाने",
    "starting_letter": "P",
    "pronunciation": "Pallavi",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Rutuja",
    "name_native": "ऋतुजा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Born in seasons",
    "meaning_native": "ऋतूत जन्मलेली",
    "starting_letter": "R",
    "pronunciation": "Rutuja",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "Swati"
  },
  {
    "name_en": "Sunita",
    "name_native": "सुनीता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Well conducted",
    "meaning_native": "चांगल्या वर्तनाची",
    "starting_letter": "S",
    "pronunciation": "Sunita",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "Shatabhisha"
  },
  {
    "name_en": "Tejashri",
    "name_native": "तेजश्री",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Radiant beauty",
    "meaning_native": "तेजस्वी सुंदरता",
    "starting_letter": "T",
    "pronunciation": "Tejashri",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Urmila",
    "name_native": "उर्मिला",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Enchantress",
    "meaning_native": "मोहिनी",
    "starting_letter": "U",
    "pronunciation": "Urmila",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Krittika",
    "nakshatra_native": "Krittika"
  },
  {
    "name_en": "Varsha",
    "name_native": "वर्षा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Rain",
    "meaning_native": "पाऊस",
    "starting_letter": "V",
    "pronunciation": "Varsha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Yogita",
    "name_native": "योगिता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Female yogi",
    "meaning_native": "योगिनी",
    "starting_letter": "Y",
    "pronunciation": "Yogita",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Aarohi",
    "name_native": "आरोही",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Ascending",
    "meaning_native": "चढता",
    "starting_letter": "A",
    "pronunciation": "Aarohi",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "Bharani"
  },
  {
    "name_en": "Bhavana",
    "name_native": "भावना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Feeling",
    "meaning_native": "भावना",
    "starting_letter": "B",
    "pronunciation": "Bhavana",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Chaitali",
    "name_native": "चैताली",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Spring season",
    "meaning_native": "वसंत ऋतू",
    "starting_letter": "C",
    "pronunciation": "Chaitali",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Dhanashri",
    "name_native": "धनश्री",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Wealthy and prosperous",
    "meaning_native": "श्रीमंत आणि समृद्ध",
    "starting_letter": "D",
    "pronunciation": "Dhanashri",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Mula",
    "nakshatra_native": "Mula"
  },
  {
    "name_en": "Eesha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "देवी पार्वती",
    "starting_letter": "E",
    "pronunciation": "Eesha",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "Ardra"
  },
  {
    "name_en": "Falguni",
    "name_native": "फाल्गुनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Born in Falgun month",
    "meaning_native": "फाल्गुन महिन्यात जन्मलेली",
    "starting_letter": "F",
    "pronunciation": "Falguni",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Gauri",
    "name_native": "गौरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Fair complexioned",
    "meaning_native": "गोरी",
    "starting_letter": "G",
    "pronunciation": "Gauri",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "Ardra"
  },
  {
    "name_en": "Harshada",
    "name_native": "हर्षदा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Giver of joy",
    "meaning_native": "आनंद देणारी",
    "starting_letter": "H",
    "pronunciation": "Harshada",
    "origin": "Sanskrit",
    "rashi": "Karka (Cancer)",
    "rashi_native": "कर्क",
    "nakshatra": "Pushya",
    "nakshatra_native": "Pushya"
  },
  {
    "name_en": "Isha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess",
    "meaning_native": "देवी",
    "starting_letter": "I",
    "pronunciation": "Isha",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "Ardra"
  },
  {
    "name_en": "Janhavi",
    "name_native": "जान्हवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "River Ganga",
    "meaning_native": "गंगा नदी",
    "starting_letter": "J",
    "pronunciation": "Janhavi",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Dhanishta",
    "nakshatra_native": "Dhanishta"
  },
  {
    "name_en": "Kshitija",
    "name_native": "क्षितिजा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Horizon",
    "meaning_native": "क्षितिज",
    "starting_letter": "K",
    "pronunciation": "Kshitija",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Punarvasu",
    "nakshatra_native": "Punarvasu"
  },
  {
    "name_en": "Lavanya",
    "name_native": "लावण्या",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Grace",
    "meaning_native": "कृपा",
    "starting_letter": "L",
    "pronunciation": "Lavanya",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "Chitra"
  },
  {
    "name_en": "Manali",
    "name_native": "मनाली",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Bird catcher",
    "meaning_native": "पक्षी पकडणारी",
    "starting_letter": "M",
    "pronunciation": "Manali",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "Magha"
  },
  {
    "name_en": "Nandini",
    "name_native": "नंदिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Delightful",
    "meaning_native": "आनंददायक",
    "starting_letter": "N",
    "pronunciation": "Nandini",
    "origin": "Sanskrit",
    "rashi": "Vrishchika (Scorpio)",
    "rashi_native": "वृश्चिक",
    "nakshatra": "Jyeshtha",
    "nakshatra_native": "Jyeshtha"
  },
  {
    "name_en": "Omshree",
    "name_native": "ओमश्री",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sacred sound",
    "meaning_native": "पवित्र ध्वनी",
    "starting_letter": "O",
    "pronunciation": "Omshree",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "Shatabhisha"
  },
  {
    "name_en": "Pratiksha",
    "name_native": "प्रतीक्षा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Wait",
    "meaning_native": "प्रतीक्षा",
    "starting_letter": "P",
    "pronunciation": "Pratiksha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Ruchira",
    "name_native": "रुचिरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Beautiful",
    "meaning_native": "सुंदर",
    "starting_letter": "R",
    "pronunciation": "Ruchira",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "Swati"
  },
  {
    "name_en": "Shubhada",
    "name_native": "शुभदा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Giver of good",
    "meaning_native": "शुभ देणारी",
    "starting_letter": "S",
    "pronunciation": "Shubhada",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "Shatabhisha"
  },
  {
    "name_en": "Trupti",
    "name_native": "तृप्ती",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Satisfaction",
    "meaning_native": "समाधान",
    "starting_letter": "T",
    "pronunciation": "Trupti",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "Purva Phalguni"
  },
  {
    "name_en": "Upasna",
    "name_native": "उपासना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Worship",
    "meaning_native": "उपासना",
    "starting_letter": "U",
    "pronunciation": "Upasna",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "Uttara Ashadha"
  },
  {
    "name_en": "Vishakha",
    "name_native": "विशाखा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "V",
    "pronunciation": "Vishakha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Vishakha",
    "nakshatra_native": "Vishakha"
  },
  {
    "name_en": "Yashoda",
    "name_native": "यशोदा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Giver of fame",
    "meaning_native": "यश देणारी",
    "starting_letter": "Y",
    "pronunciation": "Yashoda",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Zoysha",
    "name_native": "झोयशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Life",
    "meaning_native": "जीवन",
    "starting_letter": "Z",
    "pronunciation": "Zoysha",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "Hasta"
  },
  {
    "name_en": "Ankita",
    "name_native": "अंकिता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Marked",
    "meaning_native": "चिन्हांकित",
    "starting_letter": "A",
    "pronunciation": "Ankita",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Bharani",
    "nakshatra_native": "Bharani"
  },
  {
    "name_en": "Bharati",
    "name_native": "भारती",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Saraswati",
    "meaning_native": "देवी सरस्वती",
    "starting_letter": "B",
    "pronunciation": "Bharati",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "Purva Ashadha"
  },
  {
    "name_en": "Chandrika",
    "name_native": "चंद्रिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Moonlight",
    "meaning_native": "चांदनी",
    "starting_letter": "C",
    "pronunciation": "Chandrika",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Dhanashree",
    "name_native": "धनश्री",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "धनाची देवी",
    "starting_letter": "D",
    "pronunciation": "Dhanashree",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Esha",
    "name_native": "ईशा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Desire",
    "meaning_native": "इच्छा",
    "starting_letter": "E",
    "pronunciation": "Esha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Fatima",
    "name_native": "फातिमा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Daughter of Prophet",
    "meaning_native": "नबीची मुलगी",
    "starting_letter": "F",
    "pronunciation": "Fatima",
    "origin": "Arabic"
  },
  {
    "name_en": "Gayatri",
    "name_native": "गायत्री",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Sacred verse",
    "meaning_native": "पवित्र मंत्र",
    "starting_letter": "G",
    "pronunciation": "Gayatri",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Harini",
    "name_native": "हरिणी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Deer",
    "meaning_native": "हरीण",
    "starting_letter": "H",
    "pronunciation": "Harini",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Indira",
    "name_native": "इंदिरा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Lakshmi",
    "meaning_native": "देवी लक्ष्मी",
    "starting_letter": "I",
    "pronunciation": "Indira",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jaya",
    "name_native": "जया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Victory",
    "meaning_native": "विजय",
    "starting_letter": "J",
    "pronunciation": "Jaya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Kavitha",
    "name_native": "कविता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Poem",
    "meaning_native": "कविता",
    "starting_letter": "K",
    "pronunciation": "Kavitha",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Lakshana",
    "name_native": "लक्षणा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Good qualities",
    "meaning_native": "चांगले गुण",
    "starting_letter": "L",
    "pronunciation": "Lakshana",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Madhavi",
    "name_native": "माधवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Spring creeper",
    "meaning_native": "वसंत लता",
    "starting_letter": "M",
    "pronunciation": "Madhavi",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Nandini",
    "name_native": "नंदिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Daughter",
    "meaning_native": "मुलगी",
    "starting_letter": "N",
    "pronunciation": "Nandini",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Oviya",
    "name_native": "ओविया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Beautiful drawing",
    "meaning_native": "सुंदर चित्र",
    "starting_letter": "O",
    "pronunciation": "Oviya",
    "origin": "Tamil",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Padma",
    "name_native": "पद्मा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lotus",
    "meaning_native": "कमळ",
    "starting_letter": "P",
    "pronunciation": "Padma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Rekha",
    "name_native": "रेखा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Line",
    "meaning_native": "रेखा",
    "starting_letter": "R",
    "pronunciation": "Rekha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Sangeeta",
    "name_native": "संगीता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Music",
    "meaning_native": "संगीत",
    "starting_letter": "S",
    "pronunciation": "Sangeeta",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tara",
    "name_native": "तारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "T",
    "pronunciation": "Tara",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Uma",
    "name_native": "उमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "देवी पार्वती",
    "starting_letter": "U",
    "pronunciation": "Uma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vasantha",
    "name_native": "वसंत",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Spring",
    "meaning_native": "वसंत",
    "starting_letter": "V",
    "pronunciation": "Vasantha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Yamini",
    "name_native": "यामिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Night",
    "meaning_native": "रात्र",
    "starting_letter": "Y",
    "pronunciation": "Yamini",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Princess",
    "meaning_native": "राजकुमारी",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Abirami",
    "name_native": "अबिरामी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Beautiful",
    "meaning_native": "सुंदर",
    "starting_letter": "A",
    "pronunciation": "Abirami",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Bhuvana",
    "name_native": "भुवना",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "World",
    "meaning_native": "जग",
    "starting_letter": "B",
    "pronunciation": "Bhuvana",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Chandrika",
    "name_native": "चंद्रिका",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Moonlight",
    "meaning_native": "चांदनी",
    "starting_letter": "C",
    "pronunciation": "Chandrika",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Chitra",
    "nakshatra_native": "चित्रा"
  },
  {
    "name_en": "Dhanalakshmi",
    "name_native": "धनलक्ष्मी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess of wealth",
    "meaning_native": "धनाची देवी",
    "starting_letter": "D",
    "pronunciation": "Dhanalakshmi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Eswari",
    "name_native": "ईश्वरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess",
    "meaning_native": "देवी",
    "starting_letter": "E",
    "pronunciation": "Eswari",
    "origin": "Sanskrit",
    "rashi": "Mesha (Aries)",
    "rashi_native": "मेष",
    "nakshatra": "Ashwini",
    "nakshatra_native": "अश्विनी"
  },
  {
    "name_en": "Fatima",
    "name_native": "फातिमा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Daughter of Prophet",
    "meaning_native": "नबीची मुलगी",
    "starting_letter": "F",
    "pronunciation": "Fatima",
    "origin": "Arabic"
  },
  {
    "name_en": "Gowri",
    "name_native": "गौरी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Fair complexioned",
    "meaning_native": "गोरी",
    "starting_letter": "G",
    "pronunciation": "Gowri",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Rohini",
    "nakshatra_native": "रोहिणी"
  },
  {
    "name_en": "Hamsini",
    "name_native": "हंसिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Swan",
    "meaning_native": "हंसिनी",
    "starting_letter": "H",
    "pronunciation": "Hamsini",
    "origin": "Sanskrit",
    "rashi": "Mithuna (Gemini)",
    "rashi_native": "मिथुन",
    "nakshatra": "Ardra",
    "nakshatra_native": "आर्द्रा"
  },
  {
    "name_en": "Indumathi",
    "name_native": "इंदुमती",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Full moon",
    "meaning_native": "पौर्णिमा",
    "starting_letter": "I",
    "pronunciation": "Indumathi",
    "origin": "Sanskrit",
    "rashi": "Meena (Pisces)",
    "rashi_native": "मीन",
    "nakshatra": "Revati",
    "nakshatra_native": "रेवती"
  },
  {
    "name_en": "Jaya",
    "name_native": "जया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Victory",
    "meaning_native": "विजय",
    "starting_letter": "J",
    "pronunciation": "Jaya",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Kavitha",
    "name_native": "कविता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Poem",
    "meaning_native": "कविता",
    "starting_letter": "K",
    "pronunciation": "Kavitha",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Magha",
    "nakshatra_native": "मघा"
  },
  {
    "name_en": "Lakshana",
    "name_native": "लक्षणा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Good qualities",
    "meaning_native": "चांगले गुण",
    "starting_letter": "L",
    "pronunciation": "Lakshana",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Madhavi",
    "name_native": "माधवी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Spring creeper",
    "meaning_native": "वसंत लता",
    "starting_letter": "M",
    "pronunciation": "Madhavi",
    "origin": "Sanskrit",
    "rashi": "Kanya (Virgo)",
    "rashi_native": "कन्या",
    "nakshatra": "Hasta",
    "nakshatra_native": "हस्त"
  },
  {
    "name_en": "Nandini",
    "name_native": "नंदिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Daughter",
    "meaning_native": "मुलगी",
    "starting_letter": "N",
    "pronunciation": "Nandini",
    "origin": "Sanskrit",
    "rashi": "Vrishabha (Taurus)",
    "rashi_native": "वृषभ",
    "nakshatra": "Mrigashira",
    "nakshatra_native": "मृगशिरा"
  },
  {
    "name_en": "Oviya",
    "name_native": "ओविया",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Beautiful drawing",
    "meaning_native": "सुंदर चित्र",
    "starting_letter": "O",
    "pronunciation": "Oviya",
    "origin": "Tamil",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Padma",
    "name_native": "पद्मा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Lotus",
    "meaning_native": "कमळ",
    "starting_letter": "P",
    "pronunciation": "Padma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Rekha",
    "name_native": "रेखा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Line",
    "meaning_native": "रेखा",
    "starting_letter": "R",
    "pronunciation": "Rekha",
    "origin": "Sanskrit",
    "rashi": "Tula (Libra)",
    "rashi_native": "तुला",
    "nakshatra": "Swati",
    "nakshatra_native": "स्वाति"
  },
  {
    "name_en": "Sangeeta",
    "name_native": "संगीता",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Music",
    "meaning_native": "संगीत",
    "starting_letter": "S",
    "pronunciation": "Sangeeta",
    "origin": "Sanskrit",
    "rashi": "Kumbha (Aquarius)",
    "rashi_native": "कुम्भ",
    "nakshatra": "Shatabhisha",
    "nakshatra_native": "शतभिषा"
  },
  {
    "name_en": "Tara",
    "name_native": "तारा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Star",
    "meaning_native": "तारा",
    "starting_letter": "T",
    "pronunciation": "Tara",
    "origin": "Sanskrit",
    "rashi": "Simha (Leo)",
    "rashi_native": "सिंह",
    "nakshatra": "Purva Phalguni",
    "nakshatra_native": "पूर्वफाल्गुनी"
  },
  {
    "name_en": "Uma",
    "name_native": "उमा",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Goddess Parvati",
    "meaning_native": "देवी पार्वती",
    "starting_letter": "U",
    "pronunciation": "Uma",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Uttara Ashadha",
    "nakshatra_native": "उत्तराषाढ़ा"
  },
  {
    "name_en": "Vasantha",
    "name_native": "वसंत",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Spring",
    "meaning_native": "वसंत",
    "starting_letter": "V",
    "pronunciation": "Vasantha",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Yamini",
    "name_native": "यामिनी",
    "gender": "female",
    "religion": "Hindu",
    "language": "Marathi",
    "meaning_en": "Night",
    "meaning_native": "रात्र",
    "starting_letter": "Y",
    "pronunciation": "Yamini",
    "origin": "Sanskrit",
    "rashi": "Dhanu (Sagittarius)",
    "rashi_native": "धनु",
    "nakshatra": "Purva Ashadha",
    "nakshatra_native": "पूर्वाषाढ़ा"
  },
  {
    "name_en": "Zara",
    "name_native": "ज़ारा",
    "gender": "female",
    "religion": "Muslim",
    "language": "Marathi",
    "meaning_en": "Princess",
    "meaning_native": "राजकुमारी",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  }
];

export default MarathiGirlNames;
