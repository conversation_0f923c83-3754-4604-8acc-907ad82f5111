import type { NameData } from "@/types/name-data";

export const UrduGirlNames: NameData[] = [
  {
    "name_en": "<PERSON><PERSON>",
    "name_native": "عائشہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Living, prosperous",
    "meaning_native": "زندہ، خوشحال",
    "starting_letter": "A",
    "pronunciation": "<PERSON><PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "<PERSON><PERSON>",
    "name_native": "فاطمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Captivating",
    "meaning_native": "دل کو موہ لینے والی",
    "starting_letter": "F",
    "pronunciation": "Fatima",
    "origin": "Arabic"
  },
  {
    "name_en": "Hafsa",
    "name_native": "حفصہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Young lioness",
    "meaning_native": "چھوٹی شیرنی",
    "starting_letter": "H",
    "pronunciation": "Ha<PERSON>a",
    "origin": "Arabic"
  },
  {
    "name_en": "Khad<PERSON>",
    "name_native": "خدیجہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Early baby",
    "meaning_native": "قبل از وقت پیدا ہونے والی",
    "starting_letter": "K",
    "pronunciation": "Khadija",
    "origin": "Arabic"
  },
  {
    "name_en": "Mariam",
    "name_native": "مریم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Wished for child",
    "meaning_native": "مطلوبہ بچہ",
    "starting_letter": "M",
    "pronunciation": "Mariam",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadia",
    "name_native": "نادیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Hope",
    "meaning_native": "امید",
    "starting_letter": "N",
    "pronunciation": "Nadia",
    "origin": "Arabic"
  },
  {
    "name_en": "Rabia",
    "name_native": "ربیعہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Spring",
    "meaning_native": "بہار",
    "starting_letter": "R",
    "pronunciation": "Rabia",
    "origin": "Arabic"
  },
  {
    "name_en": "Sadia",
    "name_native": "سعدیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Lucky, fortunate",
    "meaning_native": "خوش قسمت",
    "starting_letter": "S",
    "pronunciation": "Sadia",
    "origin": "Arabic"
  },
  {
    "name_en": "Uzma",
    "name_native": "عظمیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Supreme",
    "meaning_native": "سب سے اعلیٰ",
    "starting_letter": "U",
    "pronunciation": "Uzma",
    "origin": "Arabic"
  },
  {
    "name_en": "Zainab",
    "name_native": "زینب",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fragrant flower",
    "meaning_native": "خوشبودار پھول",
    "starting_letter": "Z",
    "pronunciation": "Zainab",
    "origin": "Arabic"
  },
  {
    "name_en": "Amina",
    "name_native": "آمنہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Honest, faithful",
    "meaning_native": "ایمان دار، وفادار",
    "starting_letter": "A",
    "pronunciation": "Amina",
    "origin": "Arabic"
  },
  {
    "name_en": "Bushra",
    "name_native": "بشری",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Good news",
    "meaning_native": "خوشخبری",
    "starting_letter": "B",
    "pronunciation": "Bushra",
    "origin": "Arabic"
  },
  {
    "name_en": "Farah",
    "name_native": "فرح",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Joy",
    "meaning_native": "خوشی",
    "starting_letter": "F",
    "pronunciation": "Farah",
    "origin": "Arabic"
  },
  {
    "name_en": "Hina",
    "name_native": "حنا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Henna",
    "meaning_native": "مہندی",
    "starting_letter": "H",
    "pronunciation": "Hina",
    "origin": "Arabic"
  },
  {
    "name_en": "Laila",
    "name_native": "لیلیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Night beauty",
    "meaning_native": "رات کی خوبصورتی",
    "starting_letter": "L",
    "pronunciation": "Laila",
    "origin": "Arabic"
  },
  {
    "name_en": "Nimra",
    "name_native": "نمرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Number",
    "meaning_native": "نمبر",
    "starting_letter": "N",
    "pronunciation": "Nimra",
    "origin": "Arabic"
  },
  {
    "name_en": "Qurat",
    "name_native": "قرۃ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Coolness of eyes",
    "meaning_native": "آنکھوں کی ٹھنڈک",
    "starting_letter": "Q",
    "pronunciation": "Qurat",
    "origin": "Arabic"
  },
  {
    "name_en": "Samia",
    "name_native": "سامیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Elevated",
    "meaning_native": "بلند",
    "starting_letter": "S",
    "pronunciation": "Samia",
    "origin": "Arabic"
  },
  {
    "name_en": "Warda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Rose",
    "meaning_native": "گلاب",
    "starting_letter": "W",
    "pronunciation": "Warda",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasmeen",
    "name_native": "یاسمین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Jasmine flower",
    "meaning_native": "چنبیلی کا پھول",
    "starting_letter": "Y",
    "pronunciation": "Yasmeen",
    "origin": "Persian"
  },
  {
    "name_en": "Saba",
    "name_native": "صبا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Morning breeze",
    "meaning_native": "صبح کی ہوا",
    "starting_letter": "S",
    "pronunciation": "Saba",
    "origin": "Arabic"
  },
  {
    "name_en": "Sahar",
    "name_native": "سحر",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Dawn",
    "meaning_native": "فجر",
    "starting_letter": "S",
    "pronunciation": "Sahar",
    "origin": "Arabic"
  },
  {
    "name_en": "Saima",
    "name_native": "صائمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fasting woman",
    "meaning_native": "روزہ رکھنے والی",
    "starting_letter": "S",
    "pronunciation": "Saima",
    "origin": "Arabic"
  },
  {
    "name_en": "Sajida",
    "name_native": "ساجدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "One who prostrates",
    "meaning_native": "سجدہ کرنے والی",
    "starting_letter": "S",
    "pronunciation": "Sajida",
    "origin": "Arabic"
  },
  {
    "name_en": "Sakina",
    "name_native": "سکینہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Tranquility",
    "meaning_native": "سکون",
    "starting_letter": "S",
    "pronunciation": "Sakina",
    "origin": "Arabic"
  },
  {
    "name_en": "Salma",
    "name_native": "سلمیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Safe, peaceful",
    "meaning_native": "محفوظ، پرامن",
    "starting_letter": "S",
    "pronunciation": "Salma",
    "origin": "Arabic"
  },
  {
    "name_en": "Samina",
    "name_native": "ثمینہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Precious, valuable",
    "meaning_native": "قیمتی، قابل قدر",
    "starting_letter": "S",
    "pronunciation": "Samina",
    "origin": "Arabic"
  },
  {
    "name_en": "Samira",
    "name_native": "ثامرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Companion in evening talk",
    "meaning_native": "شام کی گفتگو کی ساتھی",
    "starting_letter": "S",
    "pronunciation": "Samira",
    "origin": "Arabic"
  },
  {
    "name_en": "Sana",
    "name_native": "ثناء",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Praise, applause",
    "meaning_native": "تعریف، داد",
    "starting_letter": "S",
    "pronunciation": "Sana",
    "origin": "Arabic"
  },
  {
    "name_en": "Sara",
    "name_native": "سارہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Princess",
    "meaning_native": "شہزادی",
    "starting_letter": "S",
    "pronunciation": "Sara",
    "origin": "Arabic"
  },
  {
    "name_en": "Sawda",
    "name_native": "سودہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Black",
    "meaning_native": "کالی",
    "starting_letter": "S",
    "pronunciation": "Sawda",
    "origin": "Arabic"
  },
  {
    "name_en": "Shabana",
    "name_native": "شبانہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Night beauty",
    "meaning_native": "رات کی خوبصورتی",
    "starting_letter": "S",
    "pronunciation": "Shabana",
    "origin": "Arabic"
  },
  {
    "name_en": "Shahana",
    "name_native": "شاہانہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Royal, queenly",
    "meaning_native": "شاہانہ، ملکہ جیسی",
    "starting_letter": "S",
    "pronunciation": "Shahana",
    "origin": "Persian"
  },
  {
    "name_en": "Shahida",
    "name_native": "شاہدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Witness",
    "meaning_native": "گواہ",
    "starting_letter": "S",
    "pronunciation": "Shahida",
    "origin": "Arabic"
  },
  {
    "name_en": "Shahina",
    "name_native": "شاہینہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Falcon",
    "meaning_native": "شاہین",
    "starting_letter": "S",
    "pronunciation": "Shahina",
    "origin": "Persian"
  },
  {
    "name_en": "Shahira",
    "name_native": "شاہیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Famous, well-known",
    "meaning_native": "مشہور، معروف",
    "starting_letter": "S",
    "pronunciation": "Shahira",
    "origin": "Arabic"
  },
  {
    "name_en": "Shahnaz",
    "name_native": "شاہناز",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pride of the king",
    "meaning_native": "بادشاہ کی شان",
    "starting_letter": "S",
    "pronunciation": "Shahnaz",
    "origin": "Persian"
  },
  {
    "name_en": "Shahzadi",
    "name_native": "شہزادی",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Princess",
    "meaning_native": "شہزادی",
    "starting_letter": "S",
    "pronunciation": "Shahzadi",
    "origin": "Persian"
  },
  {
    "name_en": "Shakila",
    "name_native": "شکیلہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beautiful",
    "meaning_native": "خوبصورت",
    "starting_letter": "S",
    "pronunciation": "Shakila",
    "origin": "Arabic"
  },
  {
    "name_en": "Shama",
    "name_native": "شمع",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Lamp, light",
    "meaning_native": "چراغ، روشنی",
    "starting_letter": "S",
    "pronunciation": "Shama",
    "origin": "Persian"
  },
  {
    "name_en": "Shamim",
    "name_native": "شمیم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fragrance",
    "meaning_native": "خوشبو",
    "starting_letter": "S",
    "pronunciation": "Shamim",
    "origin": "Arabic"
  },
  {
    "name_en": "Shamshad",
    "name_native": "شمشاد",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Box tree",
    "meaning_native": "شیشم کا درخت",
    "starting_letter": "S",
    "pronunciation": "Shamshad",
    "origin": "Persian"
  },
  {
    "name_en": "Shanaz",
    "name_native": "شاناز",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pride, glory",
    "meaning_native": "شان، شوکت",
    "starting_letter": "S",
    "pronunciation": "Shanaz",
    "origin": "Persian"
  },
  {
    "name_en": "Shazia",
    "name_native": "شازیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Rare, precious",
    "meaning_native": "نایاب، قیمتی",
    "starting_letter": "S",
    "pronunciation": "Shazia",
    "origin": "Arabic"
  },
  {
    "name_en": "Shehla",
    "name_native": "شہلا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Dark-eyed",
    "meaning_native": "کالی آنکھوں والی",
    "starting_letter": "S",
    "pronunciation": "Shehla",
    "origin": "Persian"
  },
  {
    "name_en": "Shehnaz",
    "name_native": "شہناز",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pride of the king",
    "meaning_native": "بادشاہ کی شان",
    "starting_letter": "S",
    "pronunciation": "Shehnaz",
    "origin": "Persian"
  },
  {
    "name_en": "Shehraz",
    "name_native": "شہراز",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "City decoration",
    "meaning_native": "شہر کی زینت",
    "starting_letter": "S",
    "pronunciation": "Shehraz",
    "origin": "Persian"
  },
  {
    "name_en": "Shehreen",
    "name_native": "شہرین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Sweet like honey",
    "meaning_native": "شہد جیسی میٹھی",
    "starting_letter": "S",
    "pronunciation": "Shehreen",
    "origin": "Persian"
  },
  {
    "name_en": "Shehwar",
    "name_native": "شہوار",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Queen",
    "meaning_native": "ملکہ",
    "starting_letter": "S",
    "pronunciation": "Shehwar",
    "origin": "Persian"
  },
  {
    "name_en": "Shireen",
    "name_native": "شیرین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Sweet",
    "meaning_native": "میٹھی",
    "starting_letter": "S",
    "pronunciation": "Shireen",
    "origin": "Persian"
  },
  {
    "name_en": "Shiza",
    "name_native": "شیزہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Gift",
    "meaning_native": "تحفہ",
    "starting_letter": "S",
    "pronunciation": "Shiza",
    "origin": "Arabic"
  },
  {
    "name_en": "Sidra",
    "name_native": "سدرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Lote tree",
    "meaning_native": "بیری کا درخت",
    "starting_letter": "S",
    "pronunciation": "Sidra",
    "origin": "Arabic"
  },
  {
    "name_en": "Sobia",
    "name_native": "صوبیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beautiful",
    "meaning_native": "خوبصورت",
    "starting_letter": "S",
    "pronunciation": "Sobia",
    "origin": "Arabic"
  },
  {
    "name_en": "Sonia",
    "name_native": "سونیا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beautiful",
    "meaning_native": "خوبصورت",
    "starting_letter": "S",
    "pronunciation": "Sonia",
    "origin": "Greek"
  },
  {
    "name_en": "Sughra",
    "name_native": "صغرا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Small, younger",
    "meaning_native": "چھوٹی، چھوٹی عمر کی",
    "starting_letter": "S",
    "pronunciation": "Sughra",
    "origin": "Arabic"
  },
  {
    "name_en": "Sultana",
    "name_native": "سلطانہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Queen, female ruler",
    "meaning_native": "ملکہ، حکمران عورت",
    "starting_letter": "S",
    "pronunciation": "Sultana",
    "origin": "Arabic"
  },
  {
    "name_en": "Sumaira",
    "name_native": "سمیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Brown color",
    "meaning_native": "بھورا رنگ",
    "starting_letter": "S",
    "pronunciation": "Sumaira",
    "origin": "Arabic"
  },
  {
    "name_en": "Sumayya",
    "name_native": "سمیعہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "High above",
    "meaning_native": "بلند مقام",
    "starting_letter": "S",
    "pronunciation": "Sumayya",
    "origin": "Arabic"
  },
  {
    "name_en": "Sumbul",
    "name_native": "سنبل",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Hyacinth flower",
    "meaning_native": "سنبل کا پھول",
    "starting_letter": "S",
    "pronunciation": "Sumbul",
    "origin": "Persian"
  },
  {
    "name_en": "Sundus",
    "name_native": "سندس",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Silk",
    "meaning_native": "ریشم",
    "starting_letter": "S",
    "pronunciation": "Sundus",
    "origin": "Arabic"
  },
  {
    "name_en": "Suraiya",
    "name_native": "ثریا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pleiades star cluster",
    "meaning_native": "ستاروں کا جھرمٹ",
    "starting_letter": "S",
    "pronunciation": "Suraiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Tabassum",
    "name_native": "تبسم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Smile",
    "meaning_native": "مسکراہٹ",
    "starting_letter": "T",
    "pronunciation": "Tabassum",
    "origin": "Arabic"
  },
  {
    "name_en": "Tahira",
    "name_native": "طاہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pure, clean",
    "meaning_native": "پاک، صاف",
    "starting_letter": "T",
    "pronunciation": "Tahira",
    "origin": "Arabic"
  },
  {
    "name_en": "Tahmina",
    "name_native": "تہمینہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Strong, powerful",
    "meaning_native": "طاقتور، مضبوط",
    "starting_letter": "T",
    "pronunciation": "Tahmina",
    "origin": "Persian"
  },
  {
    "name_en": "Taj",
    "name_native": "تاج",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Crown",
    "meaning_native": "تاج",
    "starting_letter": "T",
    "pronunciation": "Taj",
    "origin": "Persian"
  },
  {
    "name_en": "Talat",
    "name_native": "طلعت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Face, appearance",
    "meaning_native": "چہرہ، شکل",
    "starting_letter": "T",
    "pronunciation": "Talat",
    "origin": "Arabic"
  },
  {
    "name_en": "Tania",
    "name_native": "تانیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fairy queen",
    "meaning_native": "پریوں کی ملکہ",
    "starting_letter": "T",
    "pronunciation": "Tania",
    "origin": "Russian"
  },
  {
    "name_en": "Tasleem",
    "name_native": "تسلیم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Submission, greeting",
    "meaning_native": "تسلیم، سلام",
    "starting_letter": "T",
    "pronunciation": "Tasleem",
    "origin": "Arabic"
  },
  {
    "name_en": "Tayyaba",
    "name_native": "طیبہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Good, pure",
    "meaning_native": "اچھی، پاک",
    "starting_letter": "T",
    "pronunciation": "Tayyaba",
    "origin": "Arabic"
  },
  {
    "name_en": "Ujala",
    "name_native": "اجالا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Light, brightness",
    "meaning_native": "روشنی، چمک",
    "starting_letter": "U",
    "pronunciation": "Ujala",
    "origin": "Persian"
  },
  {
    "name_en": "Wafa",
    "name_native": "وفا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Faithfulness, loyalty",
    "meaning_native": "وفاداری، اخلاص",
    "starting_letter": "W",
    "pronunciation": "Wafa",
    "origin": "Arabic"
  },
  {
    "name_en": "Wajiha",
    "name_native": "واجہہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Distinguished, eminent",
    "meaning_native": "ممتاز، معروف",
    "starting_letter": "W",
    "pronunciation": "Wajiha",
    "origin": "Arabic"
  },
  {
    "name_en": "Yumna",
    "name_native": "یمنہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Blessed, fortunate",
    "meaning_native": "مبارک، خوش قسمت",
    "starting_letter": "Y",
    "pronunciation": "Yumna",
    "origin": "Arabic"
  },
  {
    "name_en": "Zahra",
    "name_native": "زہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Bright, shining",
    "meaning_native": "روشن، چمکدار",
    "starting_letter": "Z",
    "pronunciation": "Zahra",
    "origin": "Arabic"
  },
  {
    "name_en": "Zara",
    "name_native": "زارا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Princess",
    "meaning_native": "شہزادی",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Zareen",
    "name_native": "زرین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Golden",
    "meaning_native": "سنہری",
    "starting_letter": "Z",
    "pronunciation": "Zareen",
    "origin": "Persian"
  },
  {
    "name_en": "Zehra",
    "name_native": "زہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Bright, shining",
    "meaning_native": "روشن، چمکدار",
    "starting_letter": "Z",
    "pronunciation": "Zehra",
    "origin": "Arabic"
  },
  {
    "name_en": "Zohra",
    "name_native": "زہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Venus, bright star",
    "meaning_native": "زہرہ، روشن ستارہ",
    "starting_letter": "Z",
    "pronunciation": "Zohra",
    "origin": "Arabic"
  },
  {
    "name_en": "Zubaida",
    "name_native": "زبیدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Elite, cream",
    "meaning_native": "اعلیٰ، منتخب",
    "starting_letter": "Z",
    "pronunciation": "Zubaida",
    "origin": "Arabic"
  },
  {
    "name_en": "Zulekha",
    "name_native": "زلیخا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fair, beautiful",
    "meaning_native": "گورا، خوبصورت",
    "starting_letter": "Z",
    "pronunciation": "Zulekha",
    "origin": "Arabic"
  },
  {
    "name_en": "Arooba",
    "name_native": "عروبہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Loving to her husband",
    "meaning_native": "اپنے شوہر سے محبت کرنے والی",
    "starting_letter": "A",
    "pronunciation": "Arooba",
    "origin": "Arabic"
  },
  {
    "name_en": "Bareera",
    "name_native": "بریرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pious, righteous",
    "meaning_native": "نیک، متقی",
    "starting_letter": "B",
    "pronunciation": "Bareera",
    "origin": "Arabic"
  },
  {
    "name_en": "Daniya",
    "name_native": "دانیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beautiful, attractive",
    "meaning_native": "خوبصورت، دلکش",
    "starting_letter": "D",
    "pronunciation": "Daniya",
    "origin": "Arabic"
  },
  {
    "name_en": "Eshaal",
    "name_native": "اشعال",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "The name itself is flower",
    "meaning_native": "یہ نام خود پھول ہے",
    "starting_letter": "E",
    "pronunciation": "Eshaal",
    "origin": "Arabic"
  },
  {
    "name_en": "Gulnar",
    "name_native": "گلنار",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pomegranate flower",
    "meaning_native": "انار کا پھول",
    "starting_letter": "G",
    "pronunciation": "Gulnar",
    "origin": "Persian"
  },
  {
    "name_en": "Haleema",
    "name_native": "حلیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Gentle, patient",
    "meaning_native": "نرم، صابر",
    "starting_letter": "H",
    "pronunciation": "Haleema",
    "origin": "Arabic"
  },
  {
    "name_en": "Ifrah",
    "name_native": "افراح",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Happiness, joy",
    "meaning_native": "خوشی، مسرت",
    "starting_letter": "I",
    "pronunciation": "Ifrah",
    "origin": "Arabic"
  },
  {
    "name_en": "Jannat",
    "name_native": "جنت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Paradise",
    "meaning_native": "جنت",
    "starting_letter": "J",
    "pronunciation": "Jannat",
    "origin": "Arabic"
  },
  {
    "name_en": "Kiran",
    "name_native": "کرن",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Ray of light",
    "meaning_native": "روشنی کی کرن",
    "starting_letter": "K",
    "pronunciation": "Kiran",
    "origin": "Sanskrit"
  },
  {
    "name_en": "Laiba",
    "name_native": "لائبہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Angel of heaven",
    "meaning_native": "آسمان کی فرشتہ",
    "starting_letter": "L",
    "pronunciation": "Laiba",
    "origin": "Arabic"
  },
  {
    "name_en": "Manahil",
    "name_native": "مناہل",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Springs of fresh water",
    "meaning_native": "تازہ پانی کے چشمے",
    "starting_letter": "M",
    "pronunciation": "Manahil",
    "origin": "Arabic"
  },
  {
    "name_en": "Naila",
    "name_native": "نائلہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Successful, achiever",
    "meaning_native": "کامیاب، حاصل کرنے والی",
    "starting_letter": "N",
    "pronunciation": "Naila",
    "origin": "Arabic"
  },
  {
    "name_en": "Omaima",
    "name_native": "امیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Little mother",
    "meaning_native": "چھوٹی ماں",
    "starting_letter": "O",
    "pronunciation": "Omaima",
    "origin": "Arabic"
  },
  {
    "name_en": "Parveen",
    "name_native": "پروین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Star cluster",
    "meaning_native": "ستاروں کا جھرمٹ",
    "starting_letter": "P",
    "pronunciation": "Parveen",
    "origin": "Persian"
  },
  {
    "name_en": "Qudsia",
    "name_native": "قدسیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Holy, sacred",
    "meaning_native": "مقدس، پاک",
    "starting_letter": "Q",
    "pronunciation": "Qudsia",
    "origin": "Arabic"
  },
  {
    "name_en": "Rafia",
    "name_native": "رافعہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "High, exalted",
    "meaning_native": "بلند، عالی",
    "starting_letter": "R",
    "pronunciation": "Rafia",
    "origin": "Arabic"
  },
  {
    "name_en": "Saba",
    "name_native": "صبا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Morning breeze",
    "meaning_native": "صبح کی ہوا",
    "starting_letter": "S",
    "pronunciation": "Saba",
    "origin": "Arabic"
  },
  {
    "name_en": "Tahira",
    "name_native": "طاہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pure, chaste",
    "meaning_native": "پاک، پاکیزہ",
    "starting_letter": "T",
    "pronunciation": "Tahira",
    "origin": "Arabic"
  },
  {
    "name_en": "Ujala",
    "name_native": "اجالا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Light, brightness",
    "meaning_native": "روشنی، چمک",
    "starting_letter": "U",
    "pronunciation": "Ujala",
    "origin": "Sanskrit"
  },
  {
    "name_en": "Varda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Rose",
    "meaning_native": "گلاب",
    "starting_letter": "V",
    "pronunciation": "Varda",
    "origin": "Persian"
  },
  {
    "name_en": "Warda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Rose flower",
    "meaning_native": "گلاب کا پھول",
    "starting_letter": "W",
    "pronunciation": "Warda",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasmin",
    "name_native": "یاسمین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Jasmine flower",
    "meaning_native": "چنبیلی کا پھول",
    "starting_letter": "Y",
    "pronunciation": "Yasmin",
    "origin": "Persian"
  },
  {
    "name_en": "Zahra",
    "name_native": "زہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Bright, shining",
    "meaning_native": "روشن، چمکدار",
    "starting_letter": "Z",
    "pronunciation": "Zahra",
    "origin": "Arabic"
  },
  {
    "name_en": "Aalia",
    "name_native": "عالیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Exalted, noble",
    "meaning_native": "بلند، شریف",
    "starting_letter": "A",
    "pronunciation": "Aalia",
    "origin": "Arabic"
  },
  {
    "name_en": "Bisma",
    "name_native": "بسمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Smile",
    "meaning_native": "مسکراہٹ",
    "starting_letter": "B",
    "pronunciation": "Bisma",
    "origin": "Arabic"
  },
  {
    "name_en": "Dua",
    "name_native": "دعا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Prayer",
    "meaning_native": "دعا",
    "starting_letter": "D",
    "pronunciation": "Dua",
    "origin": "Arabic"
  },
  {
    "name_en": "Eman",
    "name_native": "ایمان",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Faith, belief",
    "meaning_native": "ایمان، عقیدہ",
    "starting_letter": "E",
    "pronunciation": "Eman",
    "origin": "Arabic"
  },
  {
    "name_en": "Fiza",
    "name_native": "فضا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Atmosphere, environment",
    "meaning_native": "فضا، ماحول",
    "starting_letter": "F",
    "pronunciation": "Fiza",
    "origin": "Arabic"
  },
  {
    "name_en": "Gul",
    "name_native": "گل",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Flower",
    "meaning_native": "پھول",
    "starting_letter": "G",
    "pronunciation": "Gul",
    "origin": "Persian"
  },
  {
    "name_en": "Hoor",
    "name_native": "حور",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beautiful maiden of paradise",
    "meaning_native": "جنت کی خوبصورت کنواری",
    "starting_letter": "H",
    "pronunciation": "Hoor",
    "origin": "Arabic"
  },
  {
    "name_en": "Iman",
    "name_native": "ایمان",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Faith, trust",
    "meaning_native": "ایمان، بھروسہ",
    "starting_letter": "I",
    "pronunciation": "Iman",
    "origin": "Arabic"
  },
  {
    "name_en": "Javeria",
    "name_native": "جویریہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Name of a companion of Prophet Muhammad",
    "meaning_native": "حضرت محمد صلی اللہ علیہ وسلم کی صحابیہ کا نام",
    "starting_letter": "J",
    "pronunciation": "Javeria",
    "origin": "Arabic"
  },
  {
    "name_en": "Kashaf",
    "name_native": "کشف",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Revelation, discovery",
    "meaning_native": "وحی، دریافت",
    "starting_letter": "K",
    "pronunciation": "Kashaf",
    "origin": "Arabic"
  },
  {
    "name_en": "Lubna",
    "name_native": "لبنیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Storax tree",
    "meaning_native": "ستورکس کا درخت",
    "starting_letter": "L",
    "pronunciation": "Lubna",
    "origin": "Arabic"
  },
  {
    "name_en": "Mariya",
    "name_native": "مریم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beloved, cherished",
    "meaning_native": "پیاری، عزیز",
    "starting_letter": "M",
    "pronunciation": "Mariya",
    "origin": "Arabic"
  },
  {
    "name_en": "Nazia",
    "name_native": "نازیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pride, honor",
    "meaning_native": "فخر، عزت",
    "starting_letter": "N",
    "pronunciation": "Nazia",
    "origin": "Arabic"
  },
  {
    "name_en": "Omaima",
    "name_native": "امیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Little mother",
    "meaning_native": "چھوٹی ماں",
    "starting_letter": "O",
    "pronunciation": "Omaima",
    "origin": "Arabic"
  },
  {
    "name_en": "Palwasha",
    "name_native": "پلوشہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Light of dawn",
    "meaning_native": "صبح کی روشنی",
    "starting_letter": "P",
    "pronunciation": "Palwasha",
    "origin": "Persian"
  },
  {
    "name_en": "Qirat",
    "name_native": "قرأت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Recitation of Quran",
    "meaning_native": "قرآن کی تلاوت",
    "starting_letter": "Q",
    "pronunciation": "Qirat",
    "origin": "Arabic"
  },
  {
    "name_en": "Rida",
    "name_native": "رضا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Contentment, satisfaction",
    "meaning_native": "رضا، اطمینان",
    "starting_letter": "R",
    "pronunciation": "Rida",
    "origin": "Arabic"
  },
  {
    "name_en": "Sana",
    "name_native": "ثناء",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Praise, commendation",
    "meaning_native": "تعریف، توصیف",
    "starting_letter": "S",
    "pronunciation": "Sana",
    "origin": "Arabic"
  },
  {
    "name_en": "Tuba",
    "name_native": "طوبیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Blessed tree in paradise",
    "meaning_native": "جنت میں برکت والا درخت",
    "starting_letter": "T",
    "pronunciation": "Tuba",
    "origin": "Arabic"
  },
  {
    "name_en": "Uzma",
    "name_native": "عظمیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Greatest, supreme",
    "meaning_native": "سب سے بڑی، اعلیٰ",
    "starting_letter": "U",
    "pronunciation": "Uzma",
    "origin": "Arabic"
  },
  {
    "name_en": "Varda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Rose",
    "meaning_native": "گلاب",
    "starting_letter": "V",
    "pronunciation": "Varda",
    "origin": "Persian"
  },
  {
    "name_en": "Wajiha",
    "name_native": "واجہہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Distinguished, prominent",
    "meaning_native": "ممتاز، نمایاں",
    "starting_letter": "W",
    "pronunciation": "Wajiha",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasmeen",
    "name_native": "یاسمین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Jasmine flower",
    "meaning_native": "چنبیلی کا پھول",
    "starting_letter": "Y",
    "pronunciation": "Yasmeen",
    "origin": "Persian"
  },
  {
    "name_en": "Zara",
    "name_native": "زارہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Princess, flower",
    "meaning_native": "شہزادی، پھول",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  }
];

export default UrduGirlNames;
