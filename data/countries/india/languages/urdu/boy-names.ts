import type { NameData } from "@/types/name-data";

export const UrduBoyNames: NameData[] = [
  {
    "name_en": "<PERSON><PERSON><PERSON>",
    "name_native": "عامر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Prince, rich",
    "meaning_native": "شہزادہ، امیر",
    "starting_letter": "A",
    "pronunciation": "<PERSON><PERSON><PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "احمد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Praised one",
    "meaning_native": "تعریف شدہ",
    "starting_letter": "A",
    "pronunciation": "<PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "Ali",
    "name_native": "علی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "High, elevated",
    "meaning_native": "بلند، اعلیٰ",
    "starting_letter": "A",
    "pronunciation": "<PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "Asif",
    "name_native": "آصف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Gathering, strong",
    "meaning_native": "جمع کرنے والا، مضبوط",
    "starting_letter": "A",
    "pronunciation": "Asif",
    "origin": "Arabic"
  },
  {
    "name_en": "Farouk",
    "name_native": "فاروق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Who distinguishes truth",
    "meaning_native": "حق و باطل میں فرق کرنے والا",
    "starting_letter": "F",
    "pronunciation": "Farouk",
    "origin": "Arabic"
  },
  {
    "name_en": "Hassan",
    "name_native": "حسن",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beautiful, good",
    "meaning_native": "خوبصورت، اچھا",
    "starting_letter": "H",
    "pronunciation": "Hassan",
    "origin": "Arabic"
  },
  {
    "name_en": "Ibrahim",
    "name_native": "ابراہیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Father of nations",
    "meaning_native": "قوموں کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibrahim",
    "origin": "Arabic"
  },
  {
    "name_en": "Junaid",
    "name_native": "جنید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Warrior",
    "meaning_native": "جنگجو",
    "starting_letter": "J",
    "pronunciation": "Junaid",
    "origin": "Arabic"
  },
  {
    "name_en": "Kamran",
    "name_native": "کامران",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Successful",
    "meaning_native": "کامیاب",
    "starting_letter": "K",
    "pronunciation": "Kamran",
    "origin": "Arabic"
  },
  {
    "name_en": "Mansoor",
    "name_native": "منصور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Divinely aided",
    "meaning_native": "خدا کی مدد سے",
    "starting_letter": "M",
    "pronunciation": "Mansoor",
    "origin": "Arabic"
  },
  {
    "name_en": "Omar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Long life",
    "meaning_native": "لمبی زندگی",
    "starting_letter": "O",
    "pronunciation": "Omar",
    "origin": "Arabic"
  },
  {
    "name_en": "Qasim",
    "name_native": "قاسم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Distributor",
    "meaning_native": "تقسیم کرنے والا",
    "starting_letter": "Q",
    "pronunciation": "Qasim",
    "origin": "Arabic"
  },
  {
    "name_en": "Rashid",
    "name_native": "راشد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Rightly guided",
    "meaning_native": "ہدایت یافتہ",
    "starting_letter": "R",
    "pronunciation": "Rashid",
    "origin": "Arabic"
  },
  {
    "name_en": "Salman",
    "name_native": "سلمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Safe, peaceful",
    "meaning_native": "محفوظ، پرامن",
    "starting_letter": "S",
    "pronunciation": "Salman",
    "origin": "Arabic"
  },
  {
    "name_en": "Tariq",
    "name_native": "طارق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Morning star",
    "meaning_native": "صبح کا ستارہ",
    "starting_letter": "T",
    "pronunciation": "Tariq",
    "origin": "Arabic"
  },
  {
    "name_en": "Usman",
    "name_native": "عثمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Most powerful",
    "meaning_native": "سب سے طاقتور",
    "starting_letter": "U",
    "pronunciation": "Usman",
    "origin": "Arabic"
  },
  {
    "name_en": "Waseem",
    "name_native": "وسیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Handsome",
    "meaning_native": "خوبصورت",
    "starting_letter": "W",
    "pronunciation": "Waseem",
    "origin": "Arabic"
  },
  {
    "name_en": "Yusuf",
    "name_native": "یوسف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "God increases",
    "meaning_native": "خدا بڑھائے",
    "starting_letter": "Y",
    "pronunciation": "Yusuf",
    "origin": "Arabic"
  },
  {
    "name_en": "Zain",
    "name_native": "زین",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beauty, grace",
    "meaning_native": "خوبصورتی، حسن",
    "starting_letter": "Z",
    "pronunciation": "Zain",
    "origin": "Arabic"
  },
  {
    "name_en": "Adnan",
    "name_native": "عدنان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Settler",
    "meaning_native": "آباد کرنے والا",
    "starting_letter": "A",
    "pronunciation": "Adnan",
    "origin": "Arabic"
  },
  {
    "name_en": "Abdullah",
    "name_native": "عبداللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Servant of God",
    "meaning_native": "خدا کا بندہ",
    "starting_letter": "A",
    "pronunciation": "Abdullah",
    "origin": "Arabic"
  },
  {
    "name_en": "Ahmad",
    "name_native": "احمد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Most praised",
    "meaning_native": "سب سے زیادہ تعریف شدہ",
    "starting_letter": "A",
    "pronunciation": "Ahmad",
    "origin": "Arabic"
  },
  {
    "name_en": "Akbar",
    "name_native": "اکبر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Greatest",
    "meaning_native": "سب سے بڑا",
    "starting_letter": "A",
    "pronunciation": "Akbar",
    "origin": "Arabic"
  },
  {
    "name_en": "Amjad",
    "name_native": "امجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "More glorious",
    "meaning_native": "زیادہ شاندار",
    "starting_letter": "A",
    "pronunciation": "Amjad",
    "origin": "Arabic"
  },
  {
    "name_en": "Anwar",
    "name_native": "انور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Brighter",
    "meaning_native": "زیادہ روشن",
    "starting_letter": "A",
    "pronunciation": "Anwar",
    "origin": "Arabic"
  },
  {
    "name_en": "Arif",
    "name_native": "عارف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Knowledgeable",
    "meaning_native": "عالم",
    "starting_letter": "A",
    "pronunciation": "Arif",
    "origin": "Arabic"
  },
  {
    "name_en": "Asad",
    "name_native": "اسد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Lion",
    "meaning_native": "شیر",
    "starting_letter": "A",
    "pronunciation": "Asad",
    "origin": "Arabic"
  },
  {
    "name_en": "Ashraf",
    "name_native": "اشرف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Most honorable",
    "meaning_native": "سب سے معزز",
    "starting_letter": "A",
    "pronunciation": "Ashraf",
    "origin": "Arabic"
  },
  {
    "name_en": "Azhar",
    "name_native": "اظہر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Bright, clear",
    "meaning_native": "روشن، واضح",
    "starting_letter": "A",
    "pronunciation": "Azhar",
    "origin": "Arabic"
  },
  {
    "name_en": "Bilal",
    "name_native": "بلال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Water",
    "meaning_native": "پانی",
    "starting_letter": "B",
    "pronunciation": "Bilal",
    "origin": "Arabic"
  },
  {
    "name_en": "Danish",
    "name_native": "دانش",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Knowledge, wisdom",
    "meaning_native": "علم، حکمت",
    "starting_letter": "D",
    "pronunciation": "Danish",
    "origin": "Persian"
  },
  {
    "name_en": "Ehsan",
    "name_native": "احسان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Charity, kindness",
    "meaning_native": "احسان، مہربانی",
    "starting_letter": "E",
    "pronunciation": "Ehsan",
    "origin": "Arabic"
  },
  {
    "name_en": "Fahad",
    "name_native": "فہد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Leopard",
    "meaning_native": "چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahad",
    "origin": "Arabic"
  },
  {
    "name_en": "Faisal",
    "name_native": "فیصل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Decisive",
    "meaning_native": "فیصلہ کن",
    "starting_letter": "F",
    "pronunciation": "Faisal",
    "origin": "Arabic"
  },
  {
    "name_en": "Fawad",
    "name_native": "فواد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Heart",
    "meaning_native": "دل",
    "starting_letter": "F",
    "pronunciation": "Fawad",
    "origin": "Arabic"
  },
  {
    "name_en": "Ghulam",
    "name_native": "غلام",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Servant",
    "meaning_native": "غلام",
    "starting_letter": "G",
    "pronunciation": "Ghulam",
    "origin": "Arabic"
  },
  {
    "name_en": "Hamza",
    "name_native": "حمزہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Lion",
    "meaning_native": "شیر",
    "starting_letter": "H",
    "pronunciation": "Hamza",
    "origin": "Arabic"
  },
  {
    "name_en": "Haroon",
    "name_native": "ہارون",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Aaron",
    "meaning_native": "ہارون",
    "starting_letter": "H",
    "pronunciation": "Haroon",
    "origin": "Arabic"
  },
  {
    "name_en": "Imran",
    "name_native": "عمران",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Prosperity",
    "meaning_native": "خوشحالی",
    "starting_letter": "I",
    "pronunciation": "Imran",
    "origin": "Arabic"
  },
  {
    "name_en": "Irfan",
    "name_native": "عرفان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Knowledge, recognition",
    "meaning_native": "علم، پہچان",
    "starting_letter": "I",
    "pronunciation": "Irfan",
    "origin": "Arabic"
  },
  {
    "name_en": "Javed",
    "name_native": "جاوید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Eternal",
    "meaning_native": "ہمیشہ کے لیے",
    "starting_letter": "J",
    "pronunciation": "Javed",
    "origin": "Persian"
  },
  {
    "name_en": "Khalid",
    "name_native": "خالد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Immortal",
    "meaning_native": "ہمیشہ زندہ",
    "starting_letter": "K",
    "pronunciation": "Khalid",
    "origin": "Arabic"
  },
  {
    "name_en": "Mahmood",
    "name_native": "محمود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Praiseworthy",
    "meaning_native": "قابل تعریف",
    "starting_letter": "M",
    "pronunciation": "Mahmood",
    "origin": "Arabic"
  },
  {
    "name_en": "Majid",
    "name_native": "ماجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Noble, glorious",
    "meaning_native": "عظیم، شاندار",
    "starting_letter": "M",
    "pronunciation": "Majid",
    "origin": "Arabic"
  },
  {
    "name_en": "Malik",
    "name_native": "مالک",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "King, owner",
    "meaning_native": "بادشاہ، مالک",
    "starting_letter": "M",
    "pronunciation": "Malik",
    "origin": "Arabic"
  },
  {
    "name_en": "Mubashir",
    "name_native": "مبشر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Bringer of good news",
    "meaning_native": "خوشخبری لانے والا",
    "starting_letter": "M",
    "pronunciation": "Mubashir",
    "origin": "Arabic"
  },
  {
    "name_en": "Muhammad",
    "name_native": "محمد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Praiseworthy",
    "meaning_native": "قابل تعریف",
    "starting_letter": "M",
    "pronunciation": "Muhammad",
    "origin": "Arabic"
  },
  {
    "name_en": "Mustafa",
    "name_native": "مصطفیٰ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Chosen one",
    "meaning_native": "منتخب",
    "starting_letter": "M",
    "pronunciation": "Mustafa",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadeem",
    "name_native": "ندیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "N",
    "pronunciation": "Nadeem",
    "origin": "Arabic"
  },
  {
    "name_en": "Naeem",
    "name_native": "نعیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Blessing, comfort",
    "meaning_native": "برکت، آرام",
    "starting_letter": "N",
    "pronunciation": "Naeem",
    "origin": "Arabic"
  },
  {
    "name_en": "Naseer",
    "name_native": "نصیر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Helper, supporter",
    "meaning_native": "مددگار، حامی",
    "starting_letter": "N",
    "pronunciation": "Naseer",
    "origin": "Arabic"
  },
  {
    "name_en": "Nawaz",
    "name_native": "نواز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Kind, generous",
    "meaning_native": "مہربان، فیاض",
    "starting_letter": "N",
    "pronunciation": "Nawaz",
    "origin": "Arabic"
  },
  {
    "name_en": "Nazir",
    "name_native": "نذیر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Warner, adviser",
    "meaning_native": "تنبیہ کرنے والا، مشیر",
    "starting_letter": "N",
    "pronunciation": "Nazir",
    "origin": "Arabic"
  },
  {
    "name_en": "Noman",
    "name_native": "نعمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Blood",
    "meaning_native": "خون",
    "starting_letter": "N",
    "pronunciation": "Noman",
    "origin": "Arabic"
  },
  {
    "name_en": "Noor",
    "name_native": "نور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Light",
    "meaning_native": "روشنی",
    "starting_letter": "N",
    "pronunciation": "Noor",
    "origin": "Arabic"
  },
  {
    "name_en": "Obaid",
    "name_native": "عبید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا غلام",
    "starting_letter": "O",
    "pronunciation": "Obaid",
    "origin": "Arabic"
  },
  {
    "name_en": "Qadir",
    "name_native": "قادر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Powerful, capable",
    "meaning_native": "طاقتور، قابل",
    "starting_letter": "Q",
    "pronunciation": "Qadir",
    "origin": "Arabic"
  },
  {
    "name_en": "Qaiser",
    "name_native": "قیصر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Emperor",
    "meaning_native": "بادشاہ",
    "starting_letter": "Q",
    "pronunciation": "Qaiser",
    "origin": "Arabic"
  },
  {
    "name_en": "Qamar",
    "name_native": "قمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Moon",
    "meaning_native": "چاند",
    "starting_letter": "Q",
    "pronunciation": "Qamar",
    "origin": "Arabic"
  },
  {
    "name_en": "Rafiq",
    "name_native": "رفیق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "R",
    "pronunciation": "Rafiq",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahman",
    "name_native": "رحمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Merciful",
    "meaning_native": "رحم والا",
    "starting_letter": "R",
    "pronunciation": "Rahman",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahim",
    "name_native": "رحیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Compassionate",
    "meaning_native": "مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahim",
    "origin": "Arabic"
  },
  {
    "name_en": "Rauf",
    "name_native": "رؤف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Kind, compassionate",
    "meaning_native": "مہربان، رحم دل",
    "starting_letter": "R",
    "pronunciation": "Rauf",
    "origin": "Arabic"
  },
  {
    "name_en": "Saeed",
    "name_native": "سعید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Happy, fortunate",
    "meaning_native": "خوش، خوش قسمت",
    "starting_letter": "S",
    "pronunciation": "Saeed",
    "origin": "Arabic"
  },
  {
    "name_en": "Sajid",
    "name_native": "ساجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "One who prostrates",
    "meaning_native": "سجدہ کرنے والا",
    "starting_letter": "S",
    "pronunciation": "Sajid",
    "origin": "Arabic"
  },
  {
    "name_en": "Saleem",
    "name_native": "سلیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Peaceful, safe",
    "meaning_native": "پرامن، محفوظ",
    "starting_letter": "S",
    "pronunciation": "Saleem",
    "origin": "Arabic"
  },
  {
    "name_en": "Sameer",
    "name_native": "سمیر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Companion in evening talk",
    "meaning_native": "شام کی گفتگو کا ساتھی",
    "starting_letter": "S",
    "pronunciation": "Sameer",
    "origin": "Arabic"
  },
  {
    "name_en": "Sami",
    "name_native": "سامی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Elevated, exalted",
    "meaning_native": "بلند، عالی",
    "starting_letter": "S",
    "pronunciation": "Sami",
    "origin": "Arabic"
  },
  {
    "name_en": "Shahid",
    "name_native": "شہید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Witness, martyr",
    "meaning_native": "گواہ، شہید",
    "starting_letter": "S",
    "pronunciation": "Shahid",
    "origin": "Arabic"
  },
  {
    "name_en": "Shakeel",
    "name_native": "شکیل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Handsome",
    "meaning_native": "خوبصورت",
    "starting_letter": "S",
    "pronunciation": "Shakeel",
    "origin": "Arabic"
  },
  {
    "name_en": "Shams",
    "name_native": "شمس",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Sun",
    "meaning_native": "سورج",
    "starting_letter": "S",
    "pronunciation": "Shams",
    "origin": "Arabic"
  },
  {
    "name_en": "Sharif",
    "name_native": "شریف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Noble, honorable",
    "meaning_native": "عظیم، معزز",
    "starting_letter": "S",
    "pronunciation": "Sharif",
    "origin": "Arabic"
  },
  {
    "name_en": "Shoaib",
    "name_native": "شعیب",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Who shows the right path",
    "meaning_native": "جو سیدھا راستہ دکھائے",
    "starting_letter": "S",
    "pronunciation": "Shoaib",
    "origin": "Arabic"
  },
  {
    "name_en": "Sufyan",
    "name_native": "سفیان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fast runner",
    "meaning_native": "تیز دوڑنے والا",
    "starting_letter": "S",
    "pronunciation": "Sufyan",
    "origin": "Arabic"
  },
  {
    "name_en": "Suleman",
    "name_native": "سلیمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Man of peace",
    "meaning_native": "امن کا آدمی",
    "starting_letter": "S",
    "pronunciation": "Suleman",
    "origin": "Arabic"
  },
  {
    "name_en": "Tahir",
    "name_native": "طاہر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pure, clean",
    "meaning_native": "پاک، صاف",
    "starting_letter": "T",
    "pronunciation": "Tahir",
    "origin": "Arabic"
  },
  {
    "name_en": "Talha",
    "name_native": "طلحہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Kind of tree",
    "meaning_native": "درخت کی قسم",
    "starting_letter": "T",
    "pronunciation": "Talha",
    "origin": "Arabic"
  },
  {
    "name_en": "Ubaid",
    "name_native": "عبید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا غلام",
    "starting_letter": "U",
    "pronunciation": "Ubaid",
    "origin": "Arabic"
  },
  {
    "name_en": "Umar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Long life",
    "meaning_native": "لمبی زندگی",
    "starting_letter": "U",
    "pronunciation": "Umar",
    "origin": "Arabic"
  },
  {
    "name_en": "Wahid",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "W",
    "pronunciation": "Wahid",
    "origin": "Arabic"
  },
  {
    "name_en": "Wajid",
    "name_native": "واجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Finder, discoverer",
    "meaning_native": "پانے والا، دریافت کرنے والا",
    "starting_letter": "W",
    "pronunciation": "Wajid",
    "origin": "Arabic"
  },
  {
    "name_en": "Waqar",
    "name_native": "وقار",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Dignity, majesty",
    "meaning_native": "عزت، شان",
    "starting_letter": "W",
    "pronunciation": "Waqar",
    "origin": "Arabic"
  },
  {
    "name_en": "Yahya",
    "name_native": "یحییٰ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "John",
    "meaning_native": "یحییٰ",
    "starting_letter": "Y",
    "pronunciation": "Yahya",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasir",
    "name_native": "یاسر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Wealthy, prosperous",
    "meaning_native": "امیر، خوشحال",
    "starting_letter": "Y",
    "pronunciation": "Yasir",
    "origin": "Arabic"
  },
  {
    "name_en": "Zafar",
    "name_native": "ظفر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Victory",
    "meaning_native": "فتح",
    "starting_letter": "Z",
    "pronunciation": "Zafar",
    "origin": "Arabic"
  },
  {
    "name_en": "Zahid",
    "name_native": "زاہد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pious, devout",
    "meaning_native": "متقی، پرہیزگار",
    "starting_letter": "Z",
    "pronunciation": "Zahid",
    "origin": "Arabic"
  },
  {
    "name_en": "Zakir",
    "name_native": "ذاکر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "One who remembers God",
    "meaning_native": "جو خدا کو یاد کرے",
    "starting_letter": "Z",
    "pronunciation": "Zakir",
    "origin": "Arabic"
  },
  {
    "name_en": "Zaman",
    "name_native": "زمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Time, era",
    "meaning_native": "وقت، دور",
    "starting_letter": "Z",
    "pronunciation": "Zaman",
    "origin": "Arabic"
  },
  {
    "name_en": "Zubair",
    "name_native": "زبیر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Strong",
    "meaning_native": "طاقتور",
    "starting_letter": "Z",
    "pronunciation": "Zubair",
    "origin": "Arabic"
  },
  {
    "name_en": "Aadil",
    "name_native": "عادل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Just, fair",
    "meaning_native": "عادل، منصف",
    "starting_letter": "A",
    "pronunciation": "Aadil",
    "origin": "Arabic"
  },
  {
    "name_en": "Bilal",
    "name_native": "بلال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Water, moisture",
    "meaning_native": "پانی، نمی",
    "starting_letter": "B",
    "pronunciation": "Bilal",
    "origin": "Arabic"
  },
  {
    "name_en": "Danish",
    "name_native": "دانش",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Knowledge, wisdom",
    "meaning_native": "علم، حکمت",
    "starting_letter": "D",
    "pronunciation": "Danish",
    "origin": "Persian"
  },
  {
    "name_en": "Ehsan",
    "name_native": "احسان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Charity, kindness",
    "meaning_native": "احسان، مہربانی",
    "starting_letter": "E",
    "pronunciation": "Ehsan",
    "origin": "Arabic"
  },
  {
    "name_en": "Fahad",
    "name_native": "فہد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Leopard",
    "meaning_native": "چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahad",
    "origin": "Arabic"
  },
  {
    "name_en": "Ghazi",
    "name_native": "غازی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Warrior, conqueror",
    "meaning_native": "جنگجو، فاتح",
    "starting_letter": "G",
    "pronunciation": "Ghazi",
    "origin": "Arabic"
  },
  {
    "name_en": "Hamza",
    "name_native": "حمزہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Lion",
    "meaning_native": "شیر",
    "starting_letter": "H",
    "pronunciation": "Hamza",
    "origin": "Arabic"
  },
  {
    "name_en": "Imran",
    "name_native": "عمران",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Prosperity",
    "meaning_native": "خوشحالی",
    "starting_letter": "I",
    "pronunciation": "Imran",
    "origin": "Arabic"
  },
  {
    "name_en": "Jawad",
    "name_native": "جواد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Generous",
    "meaning_native": "سخی",
    "starting_letter": "J",
    "pronunciation": "Jawad",
    "origin": "Arabic"
  },
  {
    "name_en": "Khalid",
    "name_native": "خالد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Eternal, immortal",
    "meaning_native": "ہمیشہ رہنے والا",
    "starting_letter": "K",
    "pronunciation": "Khalid",
    "origin": "Arabic"
  },
  {
    "name_en": "Lutfi",
    "name_native": "لطفی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Kind, gentle",
    "meaning_native": "مہربان، نرم",
    "starting_letter": "L",
    "pronunciation": "Lutfi",
    "origin": "Arabic"
  },
  {
    "name_en": "Mahmood",
    "name_native": "محمود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Praised, commendable",
    "meaning_native": "تعریف شدہ، قابل تعریف",
    "starting_letter": "M",
    "pronunciation": "Mahmood",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadeem",
    "name_native": "ندیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "N",
    "pronunciation": "Nadeem",
    "origin": "Arabic"
  },
  {
    "name_en": "Obaid",
    "name_native": "عبید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا غلام",
    "starting_letter": "O",
    "pronunciation": "Obaid",
    "origin": "Arabic"
  },
  {
    "name_en": "Parvez",
    "name_native": "پرویز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Victorious",
    "meaning_native": "فاتح",
    "starting_letter": "P",
    "pronunciation": "Parvez",
    "origin": "Persian"
  },
  {
    "name_en": "Qais",
    "name_native": "قیس",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Firm, solid",
    "meaning_native": "مضبوط، ٹھوس",
    "starting_letter": "Q",
    "pronunciation": "Qais",
    "origin": "Arabic"
  },
  {
    "name_en": "Rafiq",
    "name_native": "رفیق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "R",
    "pronunciation": "Rafiq",
    "origin": "Arabic"
  },
  {
    "name_en": "Saad",
    "name_native": "سعد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Good fortune",
    "meaning_native": "خوش قسمتی",
    "starting_letter": "S",
    "pronunciation": "Saad",
    "origin": "Arabic"
  },
  {
    "name_en": "Taha",
    "name_native": "طہٰ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pure, clean",
    "meaning_native": "پاک، صاف",
    "starting_letter": "T",
    "pronunciation": "Taha",
    "origin": "Arabic"
  },
  {
    "name_en": "Ubaidullah",
    "name_native": "عبید اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Little servant of God",
    "meaning_native": "خدا کا چھوٹا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidullah",
    "origin": "Arabic"
  },
  {
    "name_en": "Vaqar",
    "name_native": "وقار",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Dignity, majesty",
    "meaning_native": "عزت، شان",
    "starting_letter": "V",
    "pronunciation": "Vaqar",
    "origin": "Arabic"
  },
  {
    "name_en": "Waseem",
    "name_native": "وسیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Handsome, beautiful",
    "meaning_native": "خوبصورت، حسین",
    "starting_letter": "W",
    "pronunciation": "Waseem",
    "origin": "Arabic"
  },
  {
    "name_en": "Younis",
    "name_native": "یونس",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Dove",
    "meaning_native": "فاختہ",
    "starting_letter": "Y",
    "pronunciation": "Younis",
    "origin": "Arabic"
  },
  {
    "name_en": "Zain",
    "name_native": "زین",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beauty, grace",
    "meaning_native": "خوبصورتی، حسن",
    "starting_letter": "Z",
    "pronunciation": "Zain",
    "origin": "Arabic"
  },
  {
    "name_en": "Abrar",
    "name_native": "ابرار",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pious, righteous",
    "meaning_native": "نیک، متقی",
    "starting_letter": "A",
    "pronunciation": "Abrar",
    "origin": "Arabic"
  },
  {
    "name_en": "Basit",
    "name_native": "باسط",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Expander, generous",
    "meaning_native": "وسعت دینے والا، سخی",
    "starting_letter": "B",
    "pronunciation": "Basit",
    "origin": "Arabic"
  },
  {
    "name_en": "Daud",
    "name_native": "داود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Beloved, friend",
    "meaning_native": "پیارا، دوست",
    "starting_letter": "D",
    "pronunciation": "Daud",
    "origin": "Arabic"
  },
  {
    "name_en": "Eisa",
    "name_native": "عیسیٰ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Jesus",
    "meaning_native": "عیسیٰ",
    "starting_letter": "E",
    "pronunciation": "Eisa",
    "origin": "Arabic"
  },
  {
    "name_en": "Faisal",
    "name_native": "فیصل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Decisive, judge",
    "meaning_native": "فیصلہ کرنے والا، قاضی",
    "starting_letter": "F",
    "pronunciation": "Faisal",
    "origin": "Arabic"
  },
  {
    "name_en": "Ghalib",
    "name_native": "غالب",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Conqueror, victorious",
    "meaning_native": "فاتح، غالب",
    "starting_letter": "G",
    "pronunciation": "Ghalib",
    "origin": "Arabic"
  },
  {
    "name_en": "Hadi",
    "name_native": "ہادی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Guide, leader",
    "meaning_native": "رہنما، قائد",
    "starting_letter": "H",
    "pronunciation": "Hadi",
    "origin": "Arabic"
  },
  {
    "name_en": "Iqbal",
    "name_native": "اقبال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Fortune, prosperity",
    "meaning_native": "قسمت، خوشحالی",
    "starting_letter": "I",
    "pronunciation": "Iqbal",
    "origin": "Arabic"
  },
  {
    "name_en": "Jalal",
    "name_native": "جلال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Majesty, glory",
    "meaning_native": "عظمت، شان",
    "starting_letter": "J",
    "pronunciation": "Jalal",
    "origin": "Arabic"
  },
  {
    "name_en": "Kashif",
    "name_native": "کاشف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Revealer, discoverer",
    "meaning_native": "ظاہر کرنے والا، دریافت کرنے والا",
    "starting_letter": "K",
    "pronunciation": "Kashif",
    "origin": "Arabic"
  },
  {
    "name_en": "Latif",
    "name_native": "لطیف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Kind, gentle",
    "meaning_native": "مہربان، نرم",
    "starting_letter": "L",
    "pronunciation": "Latif",
    "origin": "Arabic"
  },
  {
    "name_en": "Mujahid",
    "name_native": "مجاہد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Warrior, fighter",
    "meaning_native": "جنگجو، لڑنے والا",
    "starting_letter": "M",
    "pronunciation": "Mujahid",
    "origin": "Arabic"
  },
  {
    "name_en": "Naeem",
    "name_native": "نعیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Blessing, comfort",
    "meaning_native": "برکت، آرام",
    "starting_letter": "N",
    "pronunciation": "Naeem",
    "origin": "Arabic"
  },
  {
    "name_en": "Omar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Long life",
    "meaning_native": "لمبی زندگی",
    "starting_letter": "O",
    "pronunciation": "Omar",
    "origin": "Arabic"
  },
  {
    "name_en": "Pervaiz",
    "name_native": "پرویز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Victorious",
    "meaning_native": "فاتح",
    "starting_letter": "P",
    "pronunciation": "Pervaiz",
    "origin": "Persian"
  },
  {
    "name_en": "Qadir",
    "name_native": "قادر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Powerful, capable",
    "meaning_native": "طاقتور، قابل",
    "starting_letter": "Q",
    "pronunciation": "Qadir",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahim",
    "name_native": "رحیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Merciful, compassionate",
    "meaning_native": "رحم کرنے والا، مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahim",
    "origin": "Arabic"
  },
  {
    "name_en": "Sajid",
    "name_native": "ساجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "One who prostrates",
    "meaning_native": "سجدہ کرنے والا",
    "starting_letter": "S",
    "pronunciation": "Sajid",
    "origin": "Arabic"
  },
  {
    "name_en": "Tayyab",
    "name_native": "طیب",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Good, pure",
    "meaning_native": "اچھا، پاک",
    "starting_letter": "T",
    "pronunciation": "Tayyab",
    "origin": "Arabic"
  },
  {
    "name_en": "Umar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Long life",
    "meaning_native": "لمبی زندگی",
    "starting_letter": "U",
    "pronunciation": "Umar",
    "origin": "Arabic"
  },
  {
    "name_en": "Wahid",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "W",
    "pronunciation": "Wahid",
    "origin": "Arabic"
  },
  {
    "name_en": "Yusuf",
    "name_native": "یوسف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Joseph",
    "meaning_native": "یوسف",
    "starting_letter": "Y",
    "pronunciation": "Yusuf",
    "origin": "Arabic"
  },
  {
    "name_en": "Zaki",
    "name_native": "ذکی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Urdu",
    "meaning_en": "Pure, intelligent",
    "meaning_native": "پاک، ذہین",
    "starting_letter": "Z",
    "pronunciation": "Zaki",
    "origin": "Arabic"
  }
];

export default UrduBoyNames;
