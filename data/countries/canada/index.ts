// Canada - Baby Names Data
// English and French languages supported

export { default as EnglishBoyNames } from './languages/english/boy-names'
export { default as EnglishGirlNames } from './languages/english/girl-names'
export { default as FrenchBoyNames } from './languages/french/boy-names'
export { default as FrenchGirlNames } from './languages/french/girl-names'

// Country metadata
export const countryInfo = {
  name: 'Canada',
  code: 'CA',
  languages: ['English', 'French'],
  regions: ['Ontario', 'Quebec', 'British Columbia', 'Alberta', 'New Brunswick', 'Manitoba'],
  population: 38000000,
  officialLanguages: ['English', 'French']
} 