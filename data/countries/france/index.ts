import { FrenchBoyNames } from './languages/french/boy-names'
import { FrenchGirlNames } from './languages/french/girl-names'
import type { NameData } from '@/types/name-data'

export const FranceNames: NameData[] = [
  ...FrenchBoyNames,
  ...FrenchGirlNames
]

export const FranceBoyNames: NameData[] = FrenchBoyNames
export const FranceGirlNames: NameData[] = FrenchGirlNames

export const getFranceNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'french':
      return FranceNames
    default:
      return []
  }
}

export const getFranceNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'french':
      return gender.toLowerCase() === 'boy' ? FranceBoyNames : FranceGirlNames
    default:
      return []
  }
}
