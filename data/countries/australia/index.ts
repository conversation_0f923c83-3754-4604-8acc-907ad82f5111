import { EnglishBoyNames } from './languages/english/boy-names'
import { EnglishGirlNames } from './languages/english/girl-names'
import type { NameData } from '@/types/name-data'

export const AustraliaNames: NameData[] = [
  ...EnglishBoyNames,
  ...EnglishGirlNames
]

export const AustraliaBoyNames: NameData[] = EnglishBoyNames
export const AustraliaGirlNames: NameData[] = EnglishGirlNames

export const getAustraliaNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'english':
      return AustraliaNames
    default:
      return []
  }
}

export const getAustraliaNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'english':
      return gender.toLowerCase() === 'boy' ? AustraliaBoyNames : AustraliaGirlNames
    default:
      return []
  }
}
