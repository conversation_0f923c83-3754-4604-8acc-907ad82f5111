import { SwedishBoyNames } from './languages/swedish/boy-names'
import { SwedishGirlNames } from './languages/swedish/girl-names'
import type { NameData } from '@/types/name-data'

export const SwedenNames: NameData[] = [
  ...SwedishBoyNames,
  ...SwedishGirlNames
]

export const SwedenBoyNames: NameData[] = SwedishBoyNames
export const SwedenGirlNames: NameData[] = SwedishGirlNames

export const getSwedenNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'swedish':
      return SwedenNames
    default:
      return []
  }
}

export const getSwedenNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'swedish':
      return gender.toLowerCase() === 'boy' ? SwedenBoyNames : SwedenGirlNames
    default:
      return []
  }
}
