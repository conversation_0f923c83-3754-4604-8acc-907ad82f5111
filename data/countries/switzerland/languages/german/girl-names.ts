import type { NameData } from "@/types/name-data";

export const SwissGermanGirlNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Mine, beloved",
    "meaning_native": "Mein, geliebt",
    "starting_letter": "M",
    "pronunciation": "MEE-ah",
    "origin": "Italian",
    "popularity_rank": 1,
    "popularity_change": "+2%",
    "year_2025_rank": 1,
    "year_2024_rank": 3,
    "trending_status": "rising",
    "search_volume": 16800,
    "regional_popularity": {
      "zurich": 1,
      "bern": 1,
      "aargau": 1,
      "st_gallen": 2
    }
  },
  {
    "name_en": "Emma",
    "name_native": "Emma",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Whole, universal",
    "meaning_native": "Ganz, universal",
    "starting_letter": "E",
    "pronunciation": "EM-mah",
    "origin": "Germanic",
    "popularity_rank": 2,
    "popularity_change": "+1%",
    "year_2025_rank": 2,
    "year_2024_rank": 3,
    "trending_status": "stable",
    "search_volume": 15400,
    "regional_popularity": {
      "zurich": 2,
      "bern": 2,
      "aargau": 2,
      "st_gallen": 1
    }
  },
  {
    "name_en": "Elena",
    "name_native": "Elena",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "E",
    "pronunciation": "eh-LEH-nah",
    "origin": "Greek",
    "popularity_rank": 3,
    "popularity_change": "+4%",
    "year_2025_rank": 3,
    "year_2024_rank": 7,
    "trending_status": "rising",
    "search_volume": 13200,
    "regional_popularity": {
      "zurich": 3,
      "bern": 4,
      "aargau": 3,
      "st_gallen": 3
    }
  },
  {
    "name_en": "Lina",
    "name_native": "Lina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Tender, delicate",
    "meaning_native": "Zart, zärtlich",
    "starting_letter": "L",
    "pronunciation": "LEE-nah",
    "origin": "Arabic",
    "popularity_rank": 4,
    "popularity_change": "+2%",
    "year_2025_rank": 4,
    "year_2024_rank": 6,
    "trending_status": "rising",
    "search_volume": 11800,
    "regional_popularity": {
      "zurich": 4,
      "bern": 3,
      "aargau": 4,
      "st_gallen": 4
    }
  },
  {
    "name_en": "Sofia",
    "name_native": "Sofia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Wisdom",
    "meaning_native": "Weisheit",
    "starting_letter": "S",
    "pronunciation": "so-FEE-ah",
    "origin": "Greek",
    "popularity_rank": 5,
    "popularity_change": "+1%",
    "year_2025_rank": 5,
    "year_2024_rank": 6,
    "trending_status": "stable",
    "search_volume": 10600,
    "regional_popularity": {
      "zurich": 5,
      "bern": 5,
      "aargau": 5,
      "st_gallen": 5
    }
  },
  {
    "name_en": "Lea",
    "name_native": "Lea",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Weary",
    "meaning_native": "Müde",
    "starting_letter": "L",
    "pronunciation": "LEH-ah",
    "origin": "Hebrew",
    "popularity_rank": 6,
    "popularity_change": "+3%",
    "year_2025_rank": 6,
    "year_2024_rank": 9,
    "trending_status": "rising",
    "search_volume": 9800,
    "regional_popularity": {
      "zurich": 6,
      "bern": 6,
      "aargau": 6,
      "st_gallen": 6
    }
  },
  {
    "name_en": "Anna",
    "name_native": "Anna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace, favor",
    "meaning_native": "Gnade, Gunst",
    "starting_letter": "A",
    "pronunciation": "AH-nah",
    "origin": "Hebrew",
    "popularity_rank": 7,
    "popularity_change": "+1%",
    "year_2025_rank": 7,
    "year_2024_rank": 8,
    "trending_status": "stable",
    "search_volume": 9200,
    "regional_popularity": {
      "zurich": 7,
      "bern": 7,
      "aargau": 7,
      "st_gallen": 7
    }
  },
  {
    "name_en": "Laura",
    "name_native": "Laura",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Laurel tree",
    "meaning_native": "Lorberbaum",
    "starting_letter": "L",
    "pronunciation": "LAU-rah",
    "origin": "Latin",
    "popularity_rank": 8,
    "popularity_change": "+2%",
    "year_2025_rank": 8,
    "year_2024_rank": 10,
    "trending_status": "rising",
    "search_volume": 8600,
    "regional_popularity": {
      "zurich": 8,
      "bern": 8,
      "aargau": 8,
      "st_gallen": 8
    }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoe",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "Z",
    "pronunciation": "TSO-eh",
    "origin": "Greek",
    "popularity_rank": 9,
    "popularity_change": "+2%",
    "year_2025_rank": 9,
    "year_2024_rank": 12,
    "trending_status": "rising",
    "search_volume": 9000,
    "regional_popularity": {
      "zurich": 9,
      "bern": 9,
      "aargau": 9,
      "st_gallen": 9
    }
  },
  {
    "name_en": "Hannah",
    "name_native": "Hannah",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace, favor",
    "meaning_native": "Gnade, Gunst",
    "starting_letter": "H",
    "pronunciation": "HAN-nah",
    "origin": "Hebrew",
    "popularity_rank": 10,
    "popularity_change": "+1%",
    "year_2025_rank": 10,
    "year_2024_rank": 11,
    "trending_status": "stable",
    "search_volume": 7600,
    "regional_popularity": {
      "zurich": 10,
      "bern": 10,
      "aargau": 10,
      "st_gallen": 10
    }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Cheerful",
    "meaning_native": "Fröhlich",
    "starting_letter": "L",
    "pronunciation": "LAH-rah",
    "origin": "Latin",
    "popularity_rank": 11,
    "popularity_change": "+2%",
    "year_2025_rank": 11,
    "year_2024_rank": 13,
    "trending_status": "rising",
    "search_volume": 7200,
    "regional_popularity": {
      "zurich": 11,
      "bern": 11,
      "aargau": 11,
      "st_gallen": 11
    }
  },
  {
    "name_en": "Julia",
    "name_native": "Julia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Youthful",
    "meaning_native": "Jugendlich",
    "starting_letter": "J",
    "pronunciation": "YU-lee-ah",
    "origin": "Latin",
    "popularity_rank": 12,
    "popularity_change": "+1%",
    "year_2025_rank": 12,
    "year_2024_rank": 13,
    "trending_status": "stable",
    "search_volume": 6800,
    "regional_popularity": {
      "zurich": 12,
      "bern": 12,
      "aargau": 12,
      "st_gallen": 12
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amelie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Work, industrious",
    "meaning_native": "Arbeit, fleißig",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 13,
    "popularity_change": "+3%",
    "year_2025_rank": 13,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 6400,
    "regional_popularity": {
      "zurich": 13,
      "bern": 13,
      "aargau": 13,
      "st_gallen": 13
    }
  },
  {
    "name_en": "Sarah",
    "name_native": "Sarah",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Princess",
    "meaning_native": "Prinzessin",
    "starting_letter": "S",
    "pronunciation": "SAH-rah",
    "origin": "Hebrew",
    "popularity_rank": 14,
    "popularity_change": "+1%",
    "year_2025_rank": 14,
    "year_2024_rank": 15,
    "trending_status": "stable",
    "search_volume": 6100,
    "regional_popularity": {
      "zurich": 14,
      "bern": 14,
      "aargau": 14,
      "st_gallen": 14
    }
  },
  {
    "name_en": "Leni",
    "name_native": "Leni",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "L",
    "pronunciation": "LEH-nee",
    "origin": "Greek",
    "popularity_rank": 15,
    "popularity_change": "+5%",
    "year_2025_rank": 15,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 5800,
    "regional_popularity": {
      "zurich": 15,
      "bern": 15,
      "aargau": 15,
      "st_gallen": 15
    }
  },
  {
    "name_en": "Nora",
    "name_native": "Nora",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honor",
    "meaning_native": "Ehre",
    "starting_letter": "N",
    "pronunciation": "NO-rah",
    "origin": "Irish",
    "popularity_rank": 16,
    "popularity_change": "+2%",
    "year_2025_rank": 16,
    "year_2024_rank": 18,
    "trending_status": "rising",
    "search_volume": 5500,
    "regional_popularity": {
      "zurich": 16,
      "bern": 16,
      "aargau": 16,
      "st_gallen": 16
    }
  },
  {
    "name_en": "Lia",
    "name_native": "Lia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Weary",
    "meaning_native": "Müde",
    "starting_letter": "L",
    "pronunciation": "LEE-ah",
    "origin": "Hebrew",
    "popularity_rank": 17,
    "popularity_change": "+4%",
    "year_2025_rank": 17,
    "year_2024_rank": 21,
    "trending_status": "rising",
    "search_volume": 5200,
    "regional_popularity": {
      "zurich": 17,
      "bern": 17,
      "aargau": 17,
      "st_gallen": 17
    }
  },
  {
    "name_en": "Clara",
    "name_native": "Clara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright, clear",
    "meaning_native": "Hell, klar",
    "starting_letter": "C",
    "pronunciation": "KLAH-rah",
    "origin": "Latin",
    "popularity_rank": 18,
    "popularity_change": "+1%",
    "year_2025_rank": 18,
    "year_2024_rank": 19,
    "trending_status": "stable",
    "search_volume": 4900,
    "regional_popularity": {
      "zurich": 18,
      "bern": 18,
      "aargau": 18,
      "st_gallen": 18
    }
  },
  {
    "name_en": "Nina",
    "name_native": "Nina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Little girl",
    "meaning_native": "Kleines Mädchen",
    "starting_letter": "N",
    "pronunciation": "NEE-nah",
    "origin": "Spanish",
    "popularity_rank": 19,
    "popularity_change": "+3%",
    "year_2025_rank": 19,
    "year_2024_rank": 22,
    "trending_status": "rising",
    "search_volume": 4600,
    "regional_popularity": {
      "zurich": 19,
      "bern": 19,
      "aargau": 19,
      "st_gallen": 19
    }
  },
  {
    "name_en": "Emilia",
    "name_native": "Emilia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rival",
    "meaning_native": "Rivale",
    "starting_letter": "E",
    "pronunciation": "eh-MEE-lee-ah",
    "origin": "Latin",
    "popularity_rank": 20,
    "popularity_change": "+2%",
    "year_2025_rank": 20,
    "year_2024_rank": 22,
    "trending_status": "rising",
    "search_volume": 4400,
    "regional_popularity": {
      "zurich": 20,
      "bern": 20,
      "aargau": 20,
      "st_gallen": 20
    }
  },
  {
    "name_en": "Alina",
    "name_native": "Alina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Noble, bright",
    "meaning_native": "Edel, hell",
    "starting_letter": "A",
    "pronunciation": "ah-LEE-nah",
    "origin": "Arabic",
    "popularity_rank": 21,
    "popularity_change": "+4%",
    "year_2025_rank": 21,
    "year_2024_rank": 25,
    "trending_status": "rising",
    "search_volume": 4100,
    "regional_popularity": {
      "zurich": 21,
      "bern": 21,
      "aargau": 21,
      "st_gallen": 21
    }
  },
  {
    "name_en": "Eva",
    "name_native": "Eva",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "E",
    "pronunciation": "EH-vah",
    "origin": "Hebrew",
    "popularity_rank": 22,
    "popularity_change": "+1%",
    "year_2025_rank": 22,
    "year_2024_rank": 23,
    "trending_status": "stable",
    "search_volume": 3900,
    "regional_popularity": {
      "zurich": 22,
      "bern": 22,
      "aargau": 22,
      "st_gallen": 22
    }
  },
  {
    "name_en": "Olivia",
    "name_native": "Olivia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Olive tree",
    "meaning_native": "Olivenbaum",
    "starting_letter": "O",
    "pronunciation": "oh-LEE-vee-ah",
    "origin": "Latin",
    "popularity_rank": 23,
    "popularity_change": "+6%",
    "year_2025_rank": 23,
    "year_2024_rank": 29,
    "trending_status": "rising",
    "search_volume": 3700,
    "regional_popularity": {
      "zurich": 23,
      "bern": 23,
      "aargau": 23,
      "st_gallen": 23
    }
  },
  {
    "name_en": "Maja",
    "name_native": "Maja",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Illusion, dream",
    "meaning_native": "Illusion, Traum",
    "starting_letter": "M",
    "pronunciation": "MAH-yah",
    "origin": "Sanskrit",
    "popularity_rank": 24,
    "popularity_change": "+2%",
    "year_2025_rank": 24,
    "year_2024_rank": 26,
    "trending_status": "rising",
    "search_volume": 3500,
    "regional_popularity": {
      "zurich": 24,
      "bern": 24,
      "aargau": 24,
      "st_gallen": 24
    }
  },
  {
    "name_en": "Elin",
    "name_native": "Elin",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "E",
    "pronunciation": "EH-leen",
    "origin": "Welsh",
    "popularity_rank": 25,
    "popularity_change": "+3%",
    "year_2025_rank": 25,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 3300,
    "regional_popularity": {
      "zurich": 25,
      "bern": 25,
      "aargau": 25,
      "st_gallen": 25
    }
  },
  {
    "name_en": "Leonie",
    "name_native": "Leonie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lioness",
    "meaning_native": "Löwin",
    "starting_letter": "L",
    "pronunciation": "leh-oh-NEE",
    "origin": "Latin",
    "popularity_rank": 26,
    "popularity_change": "+1%",
    "year_2025_rank": 26,
    "year_2024_rank": 27,
    "trending_status": "stable",
    "search_volume": 3100,
    "regional_popularity": {
      "zurich": 26,
      "bern": 26,
      "aargau": 26,
      "st_gallen": 26
    }
  },
  {
    "name_en": "Chiara",
    "name_native": "Chiara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright, clear",
    "meaning_native": "Hell, klar",
    "starting_letter": "C",
    "pronunciation": "kee-AH-rah",
    "origin": "Italian",
    "popularity_rank": 27,
    "popularity_change": "+4%",
    "year_2025_rank": 27,
    "year_2024_rank": 31,
    "trending_status": "rising",
    "search_volume": 2900,
    "regional_popularity": {
      "zurich": 27,
      "bern": 27,
      "aargau": 27,
      "st_gallen": 27
    }
  },
  {
    "name_en": "Valentina",
    "name_native": "Valentina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Stark, gesund",
    "starting_letter": "V",
    "pronunciation": "vah-len-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 28,
    "popularity_change": "+2%",
    "year_2025_rank": 28,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 2800,
    "regional_popularity": {
      "zurich": 28,
      "bern": 28,
      "aargau": 28,
      "st_gallen": 28
    }
  },
  {
    "name_en": "Stella",
    "name_native": "Stella",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Star",
    "meaning_native": "Stern",
    "starting_letter": "S",
    "pronunciation": "STEL-lah",
    "origin": "Latin",
    "popularity_rank": 29,
    "popularity_change": "+1%",
    "year_2025_rank": 29,
    "year_2024_rank": 30,
    "trending_status": "stable",
    "search_volume": 2600,
    "regional_popularity": {
      "zurich": 29,
      "bern": 29,
      "aargau": 29,
      "st_gallen": 29
    }
  },
  {
    "name_en": "Luna",
    "name_native": "Luna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Moon",
    "meaning_native": "Mond",
    "starting_letter": "L",
    "pronunciation": "LU-nah",
    "origin": "Latin",
    "popularity_rank": 30,
    "popularity_change": "+5%",
    "year_2025_rank": 30,
    "year_2024_rank": 35,
    "trending_status": "rising",
    "search_volume": 2500,
    "regional_popularity": {
      "zurich": 30,
      "bern": 30,
      "aargau": 30,
      "st_gallen": 30
    }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoe",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "Z",
    "pronunciation": "ZO-ee",
    "origin": "Greek",
    "popularity_rank": 31,
    "popularity_change": "+2%",
    "year_2025_rank": 31,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 6400,
    "regional_popularity": {
      "zurich": 31,
      "bern": 31,
      "aargau": 31,
      "st_gallen": 31
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amélie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Hardworking",
    "meaning_native": "Fleißig",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 32,
    "popularity_change": "+2%",
    "year_2025_rank": 32,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 6500,
    "regional_popularity": {
      "zurich": 32,
      "bern": 32,
      "aargau": 32,
      "st_gallen": 32
    }
  },
  {
    "name_en": "Nora",
    "name_native": "Nora",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honor",
    "meaning_native": "Ehre",
    "starting_letter": "N",
    "pronunciation": "NO-rah",
    "origin": "Irish",
    "popularity_rank": 33,
    "popularity_change": "+4%",
    "year_2025_rank": 33,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 6400,
    "regional_popularity": {
      "zurich": 33,
      "bern": 33,
      "aargau": 33,
      "st_gallen": 33
    }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Cheerful",
    "meaning_native": "Fröhlich",
    "starting_letter": "L",
    "pronunciation": "LAH-rah",
    "origin": "Latin",
    "popularity_rank": 34,
    "popularity_change": "+1%",
    "year_2025_rank": 34,
    "year_2024_rank": 35,
    "trending_status": "stable",
    "search_volume": 2100,
    "regional_popularity": {
      "zurich": 34,
      "bern": 34,
      "aargau": 34,
      "st_gallen": 34
    }
  },
  {
    "name_en": "Aria",
    "name_native": "Aria",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Air, melody",
    "meaning_native": "Luft, Melodie",
    "starting_letter": "A",
    "pronunciation": "AH-ree-ah",
    "origin": "Italian",
    "popularity_rank": 35,
    "popularity_change": "+6%",
    "year_2025_rank": 35,
    "year_2024_rank": 41,
    "trending_status": "rising",
    "search_volume": 2000,
    "regional_popularity": {
      "zurich": 35,
      "bern": 35,
      "aargau": 35,
      "st_gallen": 35
    }
  },
  {
    "name_en": "Ava",
    "name_native": "Ava",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life, bird",
    "meaning_native": "Leben, Vogel",
    "starting_letter": "A",
    "pronunciation": "AH-vah",
    "origin": "Latin",
    "popularity_rank": 36,
    "popularity_change": "+3%",
    "year_2025_rank": 36,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 1900,
    "regional_popularity": {
      "zurich": 36,
      "bern": 36,
      "aargau": 36,
      "st_gallen": 36
    }
  },
  {
    "name_en": "Lina",
    "name_native": "Lina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Tender",
    "meaning_native": "Zart",
    "starting_letter": "L",
    "pronunciation": "LEE-nah",
    "origin": "Arabic",
    "popularity_rank": 37,
    "popularity_change": "+2%",
    "year_2025_rank": 37,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 1800,
    "regional_popularity": {
      "zurich": 37,
      "bern": 37,
      "aargau": 37,
      "st_gallen": 37
    }
  },
  {
    "name_en": "Mila",
    "name_native": "Mila",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gracious, dear",
    "meaning_native": "Gnädig, lieb",
    "starting_letter": "M",
    "pronunciation": "MEE-lah",
    "origin": "Slavic",
    "popularity_rank": 38,
    "popularity_change": "+4%",
    "year_2025_rank": 38,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 1700,
    "regional_popularity": {
      "zurich": 38,
      "bern": 38,
      "aargau": 38,
      "st_gallen": 38
    }
  },
  {
    "name_en": "Elina",
    "name_native": "Elina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "E",
    "pronunciation": "eh-LEE-nah",
    "origin": "Greek",
    "popularity_rank": 39,
    "popularity_change": "+3%",
    "year_2025_rank": 39,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 1600,
    "regional_popularity": {
      "zurich": 39,
      "bern": 39,
      "aargau": 39,
      "st_gallen": 39
    }
  },
  {
    "name_en": "Chloe",
    "name_native": "Chloe",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Green shoot",
    "meaning_native": "Grüner Trieb",
    "starting_letter": "C",
    "pronunciation": "KLOH-eh",
    "origin": "Greek",
    "popularity_rank": 40,
    "popularity_change": "+2%",
    "year_2025_rank": 40,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 1500,
    "regional_popularity": {
      "zurich": 40,
      "bern": 40,
      "aargau": 40,
      "st_gallen": 40
    }
  },
  {
    "name_en": "Alina",
    "name_native": "Alina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Noble",
    "meaning_native": "Edel",
    "starting_letter": "A",
    "pronunciation": "ah-LEE-nah",
    "origin": "Germanic",
    "popularity_rank": 41,
    "popularity_change": "+3%",
    "year_2025_rank": 41,
    "year_2024_rank": 44,
    "trending_status": "rising",
    "search_volume": 1400,
    "regional_popularity": {
      "zurich": 41,
      "bern": 41,
      "aargau": 41,
      "st_gallen": 41
    }
  },
  {
    "name_en": "Chiara",
    "name_native": "Chiara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Clear, bright",
    "meaning_native": "Klar, hell",
    "starting_letter": "C",
    "pronunciation": "kee-AH-rah",
    "origin": "Italian",
    "popularity_rank": 42,
    "popularity_change": "+4%",
    "year_2025_rank": 42,
    "year_2024_rank": 46,
    "trending_status": "rising",
    "search_volume": 1300,
    "regional_popularity": {
      "zurich": 42,
      "bern": 42,
      "aargau": 42,
      "st_gallen": 42
    }
  },
  {
    "name_en": "Daria",
    "name_native": "Daria",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Wealthy",
    "meaning_native": "Wohlhabend",
    "starting_letter": "D",
    "pronunciation": "DAH-ree-ah",
    "origin": "Persian",
    "popularity_rank": 43,
    "popularity_change": "+1%",
    "year_2025_rank": 43,
    "year_2024_rank": 44,
    "trending_status": "stable",
    "search_volume": 1200,
    "regional_popularity": {
      "zurich": 43,
      "bern": 43,
      "aargau": 43,
      "st_gallen": 43
    }
  },
  {
    "name_en": "Elena",
    "name_native": "Elena",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "E",
    "pronunciation": "eh-LEH-nah",
    "origin": "Greek",
    "popularity_rank": 44,
    "popularity_change": "+2%",
    "year_2025_rank": 44,
    "year_2024_rank": 46,
    "trending_status": "rising",
    "search_volume": 1100,
    "regional_popularity": {
      "zurich": 44,
      "bern": 44,
      "aargau": 44,
      "st_gallen": 44
    }
  },
  {
    "name_en": "Fiona",
    "name_native": "Fiona",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Fair, white",
    "meaning_native": "Hell, weiß",
    "starting_letter": "F",
    "pronunciation": "fee-OH-nah",
    "origin": "Gaelic",
    "popularity_rank": 45,
    "popularity_change": "+3%",
    "year_2025_rank": 45,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 1000,
    "regional_popularity": {
      "zurich": 45,
      "bern": 45,
      "aargau": 45,
      "st_gallen": 45
    }
  },
  {
    "name_en": "Giulia",
    "name_native": "Giulia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Youthful",
    "meaning_native": "Jugendlich",
    "starting_letter": "G",
    "pronunciation": "joo-LEE-ah",
    "origin": "Italian",
    "popularity_rank": 46,
    "popularity_change": "+5%",
    "year_2025_rank": 46,
    "year_2024_rank": 51,
    "trending_status": "rising",
    "search_volume": 900,
    "regional_popularity": {
      "zurich": 46,
      "bern": 46,
      "aargau": 46,
      "st_gallen": 46
    }
  },
  {
    "name_en": "Hanna",
    "name_native": "Hanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace",
    "meaning_native": "Gnade",
    "starting_letter": "H",
    "pronunciation": "HAH-nah",
    "origin": "Hebrew",
    "popularity_rank": 47,
    "popularity_change": "+1%",
    "year_2025_rank": 47,
    "year_2024_rank": 48,
    "trending_status": "stable",
    "search_volume": 800,
    "regional_popularity": {
      "zurich": 47,
      "bern": 47,
      "aargau": 47,
      "st_gallen": 47
    }
  },
  {
    "name_en": "Ines",
    "name_native": "Ines",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pure",
    "meaning_native": "Rein",
    "starting_letter": "I",
    "pronunciation": "ee-NEHS",
    "origin": "Spanish",
    "popularity_rank": 48,
    "popularity_change": "+2%",
    "year_2025_rank": 48,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 700,
    "regional_popularity": {
      "zurich": 48,
      "bern": 48,
      "aargau": 48,
      "st_gallen": 48
    }
  },
  {
    "name_en": "Jana",
    "name_native": "Jana",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YAH-nah",
    "origin": "Hebrew",
    "popularity_rank": 49,
    "popularity_change": "+4%",
    "year_2025_rank": 49,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 600,
    "regional_popularity": {
      "zurich": 49,
      "bern": 49,
      "aargau": 49,
      "st_gallen": 49
    }
  },
  {
    "name_en": "Kira",
    "name_native": "Kira",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Beam of light",
    "meaning_native": "Lichtstrahl",
    "starting_letter": "K",
    "pronunciation": "KEE-rah",
    "origin": "Persian",
    "popularity_rank": 50,
    "popularity_change": "+3%",
    "year_2025_rank": 50,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 500,
    "regional_popularity": {
      "zurich": 50,
      "bern": 50,
      "aargau": 50,
      "st_gallen": 50
    }
  },
  {
    "name_en": "Marlene",
    "name_native": "Marlene",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rebelliousness",
    "meaning_native": "Rebellisch",
    "starting_letter": "M",
    "pronunciation": "mar-LEH-nuh",
    "origin": "Germanic",
    "popularity_rank": 10,
    "popularity_change": "+1%",
    "year_2025_rank": 10,
    "year_2024_rank": 14,
    "trending_status": "rising",
    "search_volume": 8700,
    "regional_popularity": { "zurich": 10, "bern": 10, "aargau": 10, "st_gallen": 10 }
  },
  {
    "name_en": "Greta",
    "name_native": "Greta",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "G",
    "pronunciation": "GREH-tah",
    "origin": "Greek",
    "popularity_rank": 11,
    "popularity_change": "+2%",
    "year_2025_rank": 11,
    "year_2024_rank": 15,
    "trending_status": "rising",
    "search_volume": 8600,
    "regional_popularity": { "zurich": 11, "bern": 11, "aargau": 11, "st_gallen": 11 }
  },
  {
    "name_en": "Livia",
    "name_native": "Livia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Blue, envious",
    "meaning_native": "Blau, neidisch",
    "starting_letter": "L",
    "pronunciation": "LEE-vee-ah",
    "origin": "Latin",
    "popularity_rank": 12,
    "popularity_change": "+1%",
    "year_2025_rank": 12,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 8500,
    "regional_popularity": { "zurich": 12, "bern": 12, "aargau": 12, "st_gallen": 12 }
  },
  {
    "name_en": "Ronja",
    "name_native": "Ronja",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God's joyful song",
    "meaning_native": "Gottes fröhliches Lied",
    "starting_letter": "R",
    "pronunciation": "RON-yah",
    "origin": "Scandinavian",
    "popularity_rank": 13,
    "popularity_change": "+2%",
    "year_2025_rank": 13,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 8400,
    "regional_popularity": { "zurich": 13, "bern": 13, "aargau": 13, "st_gallen": 13 }
  },
  {
    "name_en": "Mira",
    "name_native": "Mira",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Wonderful",
    "meaning_native": "Wunderbar",
    "starting_letter": "M",
    "pronunciation": "MEE-rah",
    "origin": "Slavic",
    "popularity_rank": 22,
    "popularity_change": "+1%",
    "year_2025_rank": 22,
    "year_2024_rank": 26,
    "trending_status": "rising",
    "search_volume": 7500,
    "regional_popularity": { "zurich": 22, "bern": 22, "aargau": 22, "st_gallen": 22 }
  },
  {
    "name_en": "Alina",
    "name_native": "Alina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright, beautiful",
    "meaning_native": "Hell, schön",
    "starting_letter": "A",
    "pronunciation": "ah-LEE-nah",
    "origin": "Slavic",
    "popularity_rank": 23,
    "popularity_change": "+2%",
    "year_2025_rank": 23,
    "year_2024_rank": 27,
    "trending_status": "rising",
    "search_volume": 7400,
    "regional_popularity": { "zurich": 23, "bern": 23, "aargau": 23, "st_gallen": 23 }
  },
  {
    "name_en": "Lena",
    "name_native": "Lena",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Light",
    "meaning_native": "Licht",
    "starting_letter": "L",
    "pronunciation": "LAY-nah",
    "origin": "Greek",
    "popularity_rank": 24,
    "popularity_change": "+1%",
    "year_2025_rank": 24,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 7300,
    "regional_popularity": { "zurich": 24, "bern": 24, "aargau": 24, "st_gallen": 24 }
  },
  {
    "name_en": "Mara",
    "name_native": "Mara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bitter",
    "meaning_native": "Bitter",
    "starting_letter": "M",
    "pronunciation": "MAH-rah",
    "origin": "Hebrew",
    "popularity_rank": 25,
    "popularity_change": "+2%",
    "year_2025_rank": 25,
    "year_2024_rank": 29,
    "trending_status": "rising",
    "search_volume": 7200,
    "regional_popularity": { "zurich": 25, "bern": 25, "aargau": 25, "st_gallen": 25 }
  },
  {
    "name_en": "Mina",
    "name_native": "Mina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Love",
    "meaning_native": "Liebe",
    "starting_letter": "M",
    "pronunciation": "MEE-nah",
    "origin": "German",
    "popularity_rank": 26,
    "popularity_change": "+1%",
    "year_2025_rank": 26,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 7100,
    "regional_popularity": { "zurich": 26, "bern": 26, "aargau": 26, "st_gallen": 26 }
  },
  {
    "name_en": "Nina",
    "name_native": "Nina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Little girl",
    "meaning_native": "Kleines Mädchen",
    "starting_letter": "N",
    "pronunciation": "NEE-nah",
    "origin": "Spanish",
    "popularity_rank": 27,
    "popularity_change": "+2%",
    "year_2025_rank": 27,
    "year_2024_rank": 31,
    "trending_status": "rising",
    "search_volume": 7000,
    "regional_popularity": { "zurich": 27, "bern": 27, "aargau": 27, "st_gallen": 27 }
  },
  {
    "name_en": "Sina",
    "name_native": "Sina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Treasure",
    "meaning_native": "Schatz",
    "starting_letter": "S",
    "pronunciation": "SEE-nah",
    "origin": "Persian",
    "popularity_rank": 28,
    "popularity_change": "+1%",
    "year_2025_rank": 28,
    "year_2024_rank": 32,
    "trending_status": "rising",
    "search_volume": 6900,
    "regional_popularity": { "zurich": 28, "bern": 28, "aargau": 28, "st_gallen": 28 }
  },
  {
    "name_en": "Tilda",
    "name_native": "Tilda",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Mighty in battle",
    "meaning_native": "Mächtig im Kampf",
    "starting_letter": "T",
    "pronunciation": "TIL-dah",
    "origin": "Germanic",
    "popularity_rank": 29,
    "popularity_change": "+2%",
    "year_2025_rank": 29,
    "year_2024_rank": 33,
    "trending_status": "rising",
    "search_volume": 6800,
    "regional_popularity": { "zurich": 29, "bern": 29, "aargau": 29, "st_gallen": 29 }
  },
  {
    "name_en": "Tabea",
    "name_native": "Tabea",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gazelle",
    "meaning_native": "Gazelle",
    "starting_letter": "T",
    "pronunciation": "tah-BAY-ah",
    "origin": "Hebrew",
    "popularity_rank": 30,
    "popularity_change": "+1%",
    "year_2025_rank": 30,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 6700,
    "regional_popularity": { "zurich": 30, "bern": 30, "aargau": 30, "st_gallen": 30 }
  },
  {
    "name_en": "Ylva",
    "name_native": "Ylva",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "She-wolf",
    "meaning_native": "Wölfin",
    "starting_letter": "Y",
    "pronunciation": "IL-vah",
    "origin": "Scandinavian",
    "popularity_rank": 31,
    "popularity_change": "+2%",
    "year_2025_rank": 31,
    "year_2024_rank": 35,
    "trending_status": "rising",
    "search_volume": 6600,
    "regional_popularity": { "zurich": 31, "bern": 31, "aargau": 31, "st_gallen": 31 }
  },
  {
    "name_en": "Vera",
    "name_native": "Vera",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Faith",
    "meaning_native": "Glaube",
    "starting_letter": "V",
    "pronunciation": "VEH-rah",
    "origin": "Slavic",
    "popularity_rank": 32,
    "popularity_change": "+1%",
    "year_2025_rank": 32,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 6500,
    "regional_popularity": { "zurich": 32, "bern": 32, "aargau": 32, "st_gallen": 32 }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amelie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Hardworking",
    "meaning_native": "Fleißig",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 33,
    "popularity_change": "+3%",
    "year_2025_rank": 33,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 6400,
    "regional_popularity": { "zurich": 33, "bern": 33, "aargau": 33, "st_gallen": 33 }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoe",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "Z",
    "pronunciation": "ZOH-eh",
    "origin": "Greek",
    "popularity_rank": 34,
    "popularity_change": "+4%",
    "year_2025_rank": 34,
    "year_2024_rank": 38,
    "trending_status": "rising",
    "search_volume": 6300,
    "regional_popularity": { "zurich": 34, "bern": 34, "aargau": 34, "st_gallen": 34 }
  },
  {
    "name_en": "Leonie",
    "name_native": "Leonie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lioness",
    "meaning_native": "Löwin",
    "starting_letter": "L",
    "pronunciation": "leh-oh-NEE",
    "origin": "Latin",
    "popularity_rank": 35,
    "popularity_change": "+2%",
    "year_2025_rank": 35,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 6200,
    "regional_popularity": { "zurich": 35, "bern": 35, "aargau": 35, "st_gallen": 35 }
  },
  {
    "name_en": "Freya",
    "name_native": "Freya",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Noble lady",
    "meaning_native": "Edle Dame",
    "starting_letter": "F",
    "pronunciation": "FRAY-ah",
    "origin": "Scandinavian",
    "popularity_rank": 36,
    "popularity_change": "+5%",
    "year_2025_rank": 36,
    "year_2024_rank": 41,
    "trending_status": "rising",
    "search_volume": 6100,
    "regional_popularity": { "zurich": 36, "bern": 36, "aargau": 36, "st_gallen": 36 }
  },
  {
    "name_en": "Clara",
    "name_native": "Clara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright",
    "meaning_native": "Hell",
    "starting_letter": "C",
    "pronunciation": "KLAH-rah",
    "origin": "Latin",
    "popularity_rank": 37,
    "popularity_change": "+2%",
    "year_2025_rank": 37,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 6000,
    "regional_popularity": { "zurich": 37, "bern": 37, "aargau": 37, "st_gallen": 37 }
  },
  {
    "name_en": "Nara",
    "name_native": "Nara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Happy",
    "meaning_native": "Glücklich",
    "starting_letter": "N",
    "pronunciation": "NAH-rah",
    "origin": "Celtic",
    "popularity_rank": 38,
    "popularity_change": "+3%",
    "year_2025_rank": 38,
    "year_2024_rank": 41,
    "trending_status": "rising",
    "search_volume": 5900,
    "regional_popularity": { "zurich": 38, "bern": 38, "aargau": 38, "st_gallen": 38 }
  },
  {
    "name_en": "Alina",
    "name_native": "Alina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright, beautiful",
    "meaning_native": "Hell, schön",
    "starting_letter": "A",
    "pronunciation": "ah-LEE-nah",
    "origin": "Germanic",
    "popularity_rank": 39,
    "popularity_change": "+4%",
    "year_2025_rank": 39,
    "year_2024_rank": 43,
    "trending_status": "rising",
    "search_volume": 5800,
    "regional_popularity": { "zurich": 39, "bern": 39, "aargau": 39, "st_gallen": 39 }
  },
  {
    "name_en": "Bella",
    "name_native": "Bella",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Beautiful",
    "meaning_native": "Schön",
    "starting_letter": "B",
    "pronunciation": "BEL-lah",
    "origin": "Italian",
    "popularity_rank": 40,
    "popularity_change": "+2%",
    "year_2025_rank": 40,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 5700,
    "regional_popularity": { "zurich": 40, "bern": 40, "aargau": 40, "st_gallen": 40 }
  },
  {
    "name_en": "Celine",
    "name_native": "Celine",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Heavenly",
    "meaning_native": "Himmlisch",
    "starting_letter": "C",
    "pronunciation": "seh-LEEN",
    "origin": "Latin",
    "popularity_rank": 41,
    "popularity_change": "+3%",
    "year_2025_rank": 41,
    "year_2024_rank": 44,
    "trending_status": "rising",
    "search_volume": 5600,
    "regional_popularity": { "zurich": 41, "bern": 41, "aargau": 41, "st_gallen": 41 }
  },
  {
    "name_en": "Diana",
    "name_native": "Diana",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Divine",
    "meaning_native": "Göttlich",
    "starting_letter": "D",
    "pronunciation": "dee-AH-nah",
    "origin": "Latin",
    "popularity_rank": 42,
    "popularity_change": "+1%",
    "year_2025_rank": 42,
    "year_2024_rank": 43,
    "trending_status": "stable",
    "search_volume": 5500,
    "regional_popularity": { "zurich": 42, "bern": 42, "aargau": 42, "st_gallen": 42 }
  },
  {
    "name_en": "Elisa",
    "name_native": "Elisa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my oath",
    "meaning_native": "Gott ist mein Eid",
    "starting_letter": "E",
    "pronunciation": "eh-LEE-sah",
    "origin": "Hebrew",
    "popularity_rank": 43,
    "popularity_change": "+2%",
    "year_2025_rank": 43,
    "year_2024_rank": 45,
    "trending_status": "rising",
    "search_volume": 5400,
    "regional_popularity": { "zurich": 43, "bern": 43, "aargau": 43, "st_gallen": 43 }
  },
  {
    "name_en": "Fiona",
    "name_native": "Fiona",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Fair, white",
    "meaning_native": "Hell, weiß",
    "starting_letter": "F",
    "pronunciation": "fee-OH-nah",
    "origin": "Celtic",
    "popularity_rank": 44,
    "popularity_change": "+4%",
    "year_2025_rank": 44,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 5300,
    "regional_popularity": { "zurich": 44, "bern": 44, "aargau": 44, "st_gallen": 44 }
  },
  {
    "name_en": "Greta",
    "name_native": "Greta",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "G",
    "pronunciation": "GREH-tah",
    "origin": "Greek",
    "popularity_rank": 45,
    "popularity_change": "+3%",
    "year_2025_rank": 45,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 5200,
    "regional_popularity": { "zurich": 45, "bern": 45, "aargau": 45, "st_gallen": 45 }
  },
  {
    "name_en": "Hanna",
    "name_native": "Hanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace, favor",
    "meaning_native": "Gnade, Gunst",
    "starting_letter": "H",
    "pronunciation": "HAH-nah",
    "origin": "Hebrew",
    "popularity_rank": 46,
    "popularity_change": "+1%",
    "year_2025_rank": 46,
    "year_2024_rank": 47,
    "trending_status": "stable",
    "search_volume": 5100,
    "regional_popularity": { "zurich": 46, "bern": 46, "aargau": 46, "st_gallen": 46 }
  },
  {
    "name_en": "Iris",
    "name_native": "Iris",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rainbow",
    "meaning_native": "Regenbogen",
    "starting_letter": "I",
    "pronunciation": "EE-ris",
    "origin": "Greek",
    "popularity_rank": 47,
    "popularity_change": "+2%",
    "year_2025_rank": 47,
    "year_2024_rank": 49,
    "trending_status": "rising",
    "search_volume": 5000,
    "regional_popularity": { "zurich": 47, "bern": 47, "aargau": 47, "st_gallen": 47 }
  },
  {
    "name_en": "Julia",
    "name_native": "Julia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Youthful",
    "meaning_native": "Jugendlich",
    "starting_letter": "J",
    "pronunciation": "YOO-lee-ah",
    "origin": "Latin",
    "popularity_rank": 48,
    "popularity_change": "+3%",
    "year_2025_rank": 48,
    "year_2024_rank": 51,
    "trending_status": "rising",
    "search_volume": 4900,
    "regional_popularity": { "zurich": 48, "bern": 48, "aargau": 48, "st_gallen": 48 }
  },
  {
    "name_en": "Kira",
    "name_native": "Kira",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lady, ruler",
    "meaning_native": "Dame, Herrscherin",
    "starting_letter": "K",
    "pronunciation": "KEE-rah",
    "origin": "Persian",
    "popularity_rank": 49,
    "popularity_change": "+4%",
    "year_2025_rank": 49,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 4800,
    "regional_popularity": { "zurich": 49, "bern": 49, "aargau": 49, "st_gallen": 49 }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Protection",
    "meaning_native": "Schutz",
    "starting_letter": "L",
    "pronunciation": "LAH-rah",
    "origin": "Latin",
    "popularity_rank": 50,
    "popularity_change": "+2%",
    "year_2025_rank": 50,
    "year_2024_rank": 52,
    "trending_status": "rising",
    "search_volume": 4700,
    "regional_popularity": { "zurich": 50, "bern": 50, "aargau": 50, "st_gallen": 50 }
  },
  {
    "name_en": "Maya",
    "name_native": "Maya",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Water",
    "meaning_native": "Wasser",
    "starting_letter": "M",
    "pronunciation": "MAH-yah",
    "origin": "Hebrew",
    "popularity_rank": 51,
    "popularity_change": "+3%",
    "year_2025_rank": 51,
    "year_2024_rank": 54,
    "trending_status": "rising",
    "search_volume": 4600,
    "regional_popularity": { "zurich": 51, "bern": 51, "aargau": 51, "st_gallen": 51 }
  },
  {
    "name_en": "Nina",
    "name_native": "Nina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Little girl",
    "meaning_native": "Kleines Mädchen",
    "starting_letter": "N",
    "pronunciation": "NEE-nah",
    "origin": "Spanish",
    "popularity_rank": 52,
    "popularity_change": "+1%",
    "year_2025_rank": 52,
    "year_2024_rank": 53,
    "trending_status": "stable",
    "search_volume": 4500,
    "regional_popularity": { "zurich": 52, "bern": 52, "aargau": 52, "st_gallen": 52 }
  },
  {
    "name_en": "Olivia",
    "name_native": "Olivia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Olive tree",
    "meaning_native": "Olivenbaum",
    "starting_letter": "O",
    "pronunciation": "oh-LIV-ee-ah",
    "origin": "Latin",
    "popularity_rank": 53,
    "popularity_change": "+2%",
    "year_2025_rank": 53,
    "year_2024_rank": 55,
    "trending_status": "rising",
    "search_volume": 4400,
    "regional_popularity": { "zurich": 53, "bern": 53, "aargau": 53, "st_gallen": 53 }
  },
  {
    "name_en": "Paula",
    "name_native": "Paula",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Small, humble",
    "meaning_native": "Klein, bescheiden",
    "starting_letter": "P",
    "pronunciation": "POW-lah",
    "origin": "Latin",
    "popularity_rank": 54,
    "popularity_change": "+3%",
    "year_2025_rank": 54,
    "year_2024_rank": 57,
    "trending_status": "rising",
    "search_volume": 4300,
    "regional_popularity": { "zurich": 54, "bern": 54, "aargau": 54, "st_gallen": 54 }
  },
  {
    "name_en": "Rosa",
    "name_native": "Rosa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rose",
    "meaning_native": "Rose",
    "starting_letter": "R",
    "pronunciation": "ROH-sah",
    "origin": "Latin",
    "popularity_rank": 55,
    "popularity_change": "+4%",
    "year_2025_rank": 55,
    "year_2024_rank": 59,
    "trending_status": "rising",
    "search_volume": 4200,
    "regional_popularity": { "zurich": 55, "bern": 55, "aargau": 55, "st_gallen": 55 }
  },
  {
    "name_en": "Sara",
    "name_native": "Sara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Princess",
    "meaning_native": "Prinzessin",
    "starting_letter": "S",
    "pronunciation": "SAH-rah",
    "origin": "Hebrew",
    "popularity_rank": 56,
    "popularity_change": "+1%",
    "year_2025_rank": 56,
    "year_2024_rank": 57,
    "trending_status": "stable",
    "search_volume": 4100,
    "regional_popularity": { "zurich": 56, "bern": 56, "aargau": 56, "st_gallen": 56 }
  },
  {
    "name_en": "Tina",
    "name_native": "Tina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "River",
    "meaning_native": "Fluss",
    "starting_letter": "T",
    "pronunciation": "TEE-nah",
    "origin": "Greek",
    "popularity_rank": 57,
    "popularity_change": "+2%",
    "year_2025_rank": 57,
    "year_2024_rank": 59,
    "trending_status": "rising",
    "search_volume": 4000,
    "regional_popularity": { "zurich": 57, "bern": 57, "aargau": 57, "st_gallen": 57 }
  },
  {
    "name_en": "Uma",
    "name_native": "Uma",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Flax",
    "meaning_native": "Lein",
    "starting_letter": "U",
    "pronunciation": "OO-mah",
    "origin": "Sanskrit",
    "popularity_rank": 58,
    "popularity_change": "+3%",
    "year_2025_rank": 58,
    "year_2024_rank": 61,
    "trending_status": "rising",
    "search_volume": 3900,
    "regional_popularity": { "zurich": 58, "bern": 58, "aargau": 58, "st_gallen": 58 }
  },
  {
    "name_en": "Valentina",
    "name_native": "Valentina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Stark, gesund",
    "starting_letter": "V",
    "pronunciation": "vah-len-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 59,
    "popularity_change": "+4%",
    "year_2025_rank": 59,
    "year_2024_rank": 63,
    "trending_status": "rising",
    "search_volume": 3800,
    "regional_popularity": { "zurich": 59, "bern": 59, "aargau": 59, "st_gallen": 59 }
  },
  {
    "name_en": "Willa",
    "name_native": "Willa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Resolute protection",
    "meaning_native": "Entschlossener Schutz",
    "starting_letter": "W",
    "pronunciation": "VIL-lah",
    "origin": "Germanic",
    "popularity_rank": 60,
    "popularity_change": "+2%",
    "year_2025_rank": 60,
    "year_2024_rank": 62,
    "trending_status": "rising",
    "search_volume": 3700,
    "regional_popularity": { "zurich": 60, "bern": 60, "aargau": 60, "st_gallen": 60 }
  },
  {
    "name_en": "Xenia",
    "name_native": "Xenia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Hospitality",
    "meaning_native": "Gastfreundschaft",
    "starting_letter": "X",
    "pronunciation": "KSEH-nee-ah",
    "origin": "Greek",
    "popularity_rank": 61,
    "popularity_change": "+3%",
    "year_2025_rank": 61,
    "year_2024_rank": 64,
    "trending_status": "rising",
    "search_volume": 3600,
    "regional_popularity": { "zurich": 61, "bern": 61, "aargau": 61, "st_gallen": 61 }
  },
  {
    "name_en": "Yara",
    "name_native": "Yara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Small butterfly",
    "meaning_native": "Kleiner Schmetterling",
    "starting_letter": "Y",
    "pronunciation": "YAH-rah",
    "origin": "Arabic",
    "popularity_rank": 62,
    "popularity_change": "+4%",
    "year_2025_rank": 62,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 3500,
    "regional_popularity": { "zurich": 62, "bern": 62, "aargau": 62, "st_gallen": 62 }
  },
  {
    "name_en": "Zara",
    "name_native": "Zara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Princess",
    "meaning_native": "Prinzessin",
    "starting_letter": "Z",
    "pronunciation": "ZAH-rah",
    "origin": "Arabic",
    "popularity_rank": 63,
    "popularity_change": "+2%",
    "year_2025_rank": 63,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 3400,
    "regional_popularity": { "zurich": 63, "bern": 63, "aargau": 63, "st_gallen": 63 }
  },
  {
    "name_en": "Bella",
    "name_native": "Bella",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Beautiful",
    "meaning_native": "Schön",
    "starting_letter": "B",
    "pronunciation": "BEL-lah",
    "origin": "Italian",
    "popularity_rank": 97,
    "popularity_change": "+3%",
    "year_2025_rank": 97,
    "year_2024_rank": 100,
    "trending_status": "rising",
    "search_volume": 420,
    "regional_popularity": {
      "zurich": 97,
      "bern": 97,
      "basel": 97,
      "geneva": 97
    }
  },
  {
    "name_en": "Celine",
    "name_native": "Celine",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Heavenly",
    "meaning_native": "Himmlisch",
    "starting_letter": "C",
    "pronunciation": "seh-LEEN",
    "origin": "Latin",
    "popularity_rank": 98,
    "popularity_change": "+5%",
    "year_2025_rank": 98,
    "year_2024_rank": 103,
    "trending_status": "rising",
    "search_volume": 380,
    "regional_popularity": {
      "zurich": 98,
      "bern": 98,
      "basel": 98,
      "geneva": 98
    }
  },
  {
    "name_en": "Diana",
    "name_native": "Diana",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Divine",
    "meaning_native": "Göttlich",
    "starting_letter": "D",
    "pronunciation": "dee-AH-nah",
    "origin": "Latin",
    "popularity_rank": 99,
    "popularity_change": "+2%",
    "year_2025_rank": 99,
    "year_2024_rank": 101,
    "trending_status": "stable",
    "search_volume": 340,
    "regional_popularity": {
      "zurich": 99,
      "bern": 99,
      "basel": 99,
      "geneva": 99
    }
  },
  {
    "name_en": "Eva",
    "name_native": "Eva",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "E",
    "pronunciation": "AY-vah",
    "origin": "Hebrew",
    "popularity_rank": 100,
    "popularity_change": "+4%",
    "year_2025_rank": 100,
    "year_2024_rank": 104,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": {
      "zurich": 100,
      "bern": 100,
      "basel": 100,
      "geneva": 100
    }
  },
  {
    "name_en": "Fiona",
    "name_native": "Fiona",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Fair, white",
    "meaning_native": "Fair, weiß",
    "starting_letter": "F",
    "pronunciation": "fee-OH-nah",
    "origin": "Celtic",
    "popularity_rank": 101,
    "popularity_change": "+6%",
    "year_2025_rank": 101,
    "year_2024_rank": 107,
    "trending_status": "rising",
    "search_volume": 260,
    "regional_popularity": {
      "zurich": 101,
      "bern": 101,
      "basel": 101,
      "geneva": 101
    }
  },
  {
    "name_en": "Greta",
    "name_native": "Greta",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "G",
    "pronunciation": "GREH-tah",
    "origin": "Greek",
    "popularity_rank": 102,
    "popularity_change": "+3%",
    "year_2025_rank": 102,
    "year_2024_rank": 105,
    "trending_status": "rising",
    "search_volume": 220,
    "regional_popularity": {
      "zurich": 102,
      "bern": 102,
      "basel": 102,
      "geneva": 102
    }
  },
  {
    "name_en": "Hanna",
    "name_native": "Hanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace",
    "meaning_native": "Gnade",
    "starting_letter": "H",
    "pronunciation": "HAN-nah",
    "origin": "Hebrew",
    "popularity_rank": 103,
    "popularity_change": "+5%",
    "year_2025_rank": 103,
    "year_2024_rank": 108,
    "trending_status": "rising",
    "search_volume": 180,
    "regional_popularity": {
      "zurich": 103,
      "bern": 103,
      "basel": 103,
      "geneva": 103
    }
  },
  {
    "name_en": "Iris",
    "name_native": "Iris",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rainbow",
    "meaning_native": "Regenbogen",
    "starting_letter": "I",
    "pronunciation": "EE-ris",
    "origin": "Greek",
    "popularity_rank": 104,
    "popularity_change": "+2%",
    "year_2025_rank": 104,
    "year_2024_rank": 106,
    "trending_status": "stable",
    "search_volume": 140,
    "regional_popularity": {
      "zurich": 104,
      "bern": 104,
      "basel": 104,
      "geneva": 104
    }
  },
  {
    "name_en": "Jasmin",
    "name_native": "Jasmin",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Jasmine flower",
    "meaning_native": "Jasminblume",
    "starting_letter": "J",
    "pronunciation": "yas-MEEN",
    "origin": "Persian",
    "popularity_rank": 105,
    "popularity_change": "+4%",
    "year_2025_rank": 105,
    "year_2024_rank": 109,
    "trending_status": "rising",
    "search_volume": 100,
    "regional_popularity": {
      "zurich": 105,
      "bern": 105,
      "basel": 105,
      "geneva": 105
    }
  },
  {
    "name_en": "Kira",
    "name_native": "Kira",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lady, ruler",
    "meaning_native": "Dame, Herrscherin",
    "starting_letter": "K",
    "pronunciation": "KEE-rah",
    "origin": "Persian",
    "popularity_rank": 106,
    "popularity_change": "+1%",
    "year_2025_rank": 106,
    "year_2024_rank": 107,
    "trending_status": "stable",
    "search_volume": 60,
    "regional_popularity": {
      "zurich": 106,
      "bern": 106,
      "basel": 106,
      "geneva": 106
    }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Protection",
    "meaning_native": "Schutz",
    "starting_letter": "L",
    "pronunciation": "LAH-rah",
    "origin": "Latin",
    "popularity_rank": 107,
    "popularity_change": "+3%",
    "year_2025_rank": 107,
    "year_2024_rank": 110,
    "trending_status": "rising",
    "search_volume": 20,
    "regional_popularity": {
      "zurich": 107,
      "bern": 107,
      "basel": 107,
      "geneva": 107
    }
  },
  {
    "name_en": "Maya",
    "name_native": "Maya",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Water",
    "meaning_native": "Wasser",
    "starting_letter": "M",
    "pronunciation": "MAH-yah",
    "origin": "Hebrew",
    "popularity_rank": 108,
    "popularity_change": "+5%",
    "year_2025_rank": 108,
    "year_2024_rank": 113,
    "trending_status": "rising",
    "search_volume": 15,
    "regional_popularity": {
      "zurich": 108,
      "bern": 108,
      "basel": 108,
      "geneva": 108
    }
  },
  {
    "name_en": "Nina",
    "name_native": "Nina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Little girl",
    "meaning_native": "Kleines Mädchen",
    "starting_letter": "N",
    "pronunciation": "NEE-nah",
    "origin": "Spanish",
    "popularity_rank": 109,
    "popularity_change": "+2%",
    "year_2025_rank": 109,
    "year_2024_rank": 111,
    "trending_status": "stable",
    "search_volume": 10,
    "regional_popularity": {
      "zurich": 109,
      "bern": 109,
      "basel": 109,
      "geneva": 109
    }
  },
  {
    "name_en": "Olivia",
    "name_native": "Olivia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Olive tree",
    "meaning_native": "Olivenbaum",
    "starting_letter": "O",
    "pronunciation": "oh-LEE-vee-ah",
    "origin": "Latin",
    "popularity_rank": 110,
    "popularity_change": "+4%",
    "year_2025_rank": 110,
    "year_2024_rank": 114,
    "trending_status": "rising",
    "search_volume": 5,
    "regional_popularity": {
      "zurich": 110,
      "bern": 110,
      "basel": 110,
      "geneva": 110
    }
  }
]

export default SwissGermanGirlNames;
