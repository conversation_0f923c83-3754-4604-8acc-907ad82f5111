import type { NameData } from "@/types/name-data";

export const SwissGermanBoyNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rest, comfort",
    "meaning_native": "Ruh<PERSON>, Trost",
    "starting_letter": "N",
    "pronunciation": "NO-ah",
    "origin": "Hebrew",
    "popularity_rank": 1,
    "popularity_change": "+1%",
    "year_2025_rank": 1,
    "year_2024_rank": 2,
    "trending_status": "stable",
    "search_volume": 15200,
    "regional_popularity": {
      "zurich": 1,
      "bern": 2,
      "aargau": 1,
      "st_gallen": 3
    }
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong-willed protector",
    "meaning_native": "Entschlossener Beschützer",
    "starting_letter": "L",
    "pronunciation": "LEE-ahm",
    "origin": "Irish",
    "popularity_rank": 2,
    "popularity_change": "+3%",
    "year_2025_rank": 2,
    "year_2024_rank": 5,
    "trending_status": "rising",
    "search_volume": 13800,
    "regional_popularity": {
      "zurich": 2,
      "bern": 1,
      "aargau": 2,
      "st_gallen": 1
    }
  },
  {
    "name_en": "Matteo",
    "name_native": "Matteo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "mat-TEH-oh",
    "origin": "Italian",
    "popularity_rank": 3,
    "popularity_change": "+5%",
    "year_2025_rank": 3,
    "year_2024_rank": 8,
    "trending_status": "rising",
    "search_volume": 12600,
    "regional_popularity": {
      "zurich": 3,
      "bern": 4,
      "aargau": 3,
      "st_gallen": 2
    }
  },
  {
    "name_en": "Leon",
    "name_native": "Leon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lion",
    "meaning_native": "Löwe",
    "starting_letter": "L",
    "pronunciation": "LEH-on",
    "origin": "Greek",
    "popularity_rank": 4,
    "popularity_change": "+2%",
    "year_2025_rank": 4,
    "year_2024_rank": 6,
    "trending_status": "rising",
    "search_volume": 11400,
    "regional_popularity": {
      "zurich": 4,
      "bern": 3,
      "aargau": 4,
      "st_gallen": 4
    }
  },
  {
    "name_en": "Elias",
    "name_native": "Elias",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "My God is Yahweh",
    "meaning_native": "Mein Gott ist Jahwe",
    "starting_letter": "E",
    "pronunciation": "eh-LEE-as",
    "origin": "Hebrew",
    "popularity_rank": 5,
    "popularity_change": "+1%",
    "year_2025_rank": 5,
    "year_2024_rank": 6,
    "trending_status": "stable",
    "search_volume": 10200,
    "regional_popularity": {
      "zurich": 5,
      "bern": 5,
      "aargau": 5,
      "st_gallen": 5
    }
  },
  {
    "name_en": "Ethan",
    "name_native": "Ethan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Firm, strong",
    "meaning_native": "Fest, stark",
    "starting_letter": "E",
    "pronunciation": "EE-than",
    "origin": "Hebrew",
    "popularity_rank": 6,
    "popularity_change": "+3%",
    "year_2025_rank": 6,
    "year_2024_rank": 9,
    "trending_status": "rising",
    "search_volume": 9600,
    "regional_popularity": {
      "zurich": 6,
      "bern": 6,
      "aargau": 6,
      "st_gallen": 6
    }
  },
  {
    "name_en": "Nathan",
    "name_native": "Nathan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "He gave",
    "meaning_native": "Er gab",
    "starting_letter": "N",
    "pronunciation": "NAY-than",
    "origin": "Hebrew",
    "popularity_rank": 7,
    "popularity_change": "+2%",
    "year_2025_rank": 7,
    "year_2024_rank": 9,
    "trending_status": "rising",
    "search_volume": 8900,
    "regional_popularity": {
      "zurich": 7,
      "bern": 7,
      "aargau": 7,
      "st_gallen": 7
    }
  },
  {
    "name_en": "Lucas",
    "name_native": "Lucas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Light-bringing",
    "meaning_native": "Lichtbringend",
    "starting_letter": "L",
    "pronunciation": "LU-kas",
    "origin": "Latin",
    "popularity_rank": 8,
    "popularity_change": "+1%",
    "year_2025_rank": 8,
    "year_2024_rank": 9,
    "trending_status": "stable",
    "search_volume": 8400,
    "regional_popularity": {
      "zurich": 8,
      "bern": 8,
      "aargau": 8,
      "st_gallen": 8
    }
  },
  {
    "name_en": "Adam",
    "name_native": "Adam",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Man, earth",
    "meaning_native": "Mensch, Erde",
    "starting_letter": "A",
    "pronunciation": "AH-dahm",
    "origin": "Hebrew",
    "popularity_rank": 9,
    "popularity_change": "+1%",
    "year_2025_rank": 9,
    "year_2024_rank": 12,
    "trending_status": "rising",
    "search_volume": 8000,
    "regional_popularity": {
      "zurich": 9,
      "bern": 9,
      "aargau": 9,
      "st_gallen": 9
    }
  },
  {
    "name_en": "Theo",
    "name_native": "Theo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God's gift",
    "meaning_native": "Gottes Geschenk",
    "starting_letter": "T",
    "pronunciation": "TEH-oh",
    "origin": "Greek",
    "popularity_rank": 10,
    "popularity_change": "+4%",
    "year_2025_rank": 10,
    "year_2024_rank": 14,
    "trending_status": "rising",
    "search_volume": 7800,
    "regional_popularity": {
      "zurich": 10,
      "bern": 10,
      "aargau": 10,
      "st_gallen": 10
    }
  },
  {
    "name_en": "Maxime",
    "name_native": "Maxime",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Greatest",
    "meaning_native": "Größter",
    "starting_letter": "M",
    "pronunciation": "mak-SEEM",
    "origin": "Latin",
    "popularity_rank": 11,
    "popularity_change": "+2%",
    "year_2025_rank": 11,
    "year_2024_rank": 15,
    "trending_status": "rising",
    "search_volume": 7700,
    "regional_popularity": {
      "zurich": 11,
      "bern": 11,
      "aargau": 11,
      "st_gallen": 11
    }
  },
  {
    "name_en": "Samuel",
    "name_native": "Samuel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God has heard",
    "meaning_native": "Gott hat gehört",
    "starting_letter": "S",
    "pronunciation": "SAH-mu-el",
    "origin": "Hebrew",
    "popularity_rank": 12,
    "popularity_change": "+1%",
    "year_2025_rank": 12,
    "year_2024_rank": 15,
    "trending_status": "stable",
    "search_volume": 7500,
    "regional_popularity": {
      "zurich": 12,
      "bern": 12,
      "aargau": 12,
      "st_gallen": 12
    }
  },
  {
    "name_en": "Julian",
    "name_native": "Julian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Youthful",
    "meaning_native": "Jugendlich",
    "starting_letter": "J",
    "pronunciation": "YU-lee-ahn",
    "origin": "Latin",
    "popularity_rank": 13,
    "popularity_change": "+3%",
    "year_2025_rank": 13,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 7100,
    "regional_popularity": {
      "zurich": 13,
      "bern": 13,
      "aargau": 13,
      "st_gallen": 13
    }
  },
  {
    "name_en": "Raphael",
    "name_native": "Raphael",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God has healed",
    "meaning_native": "Gott hat geheilt",
    "starting_letter": "R",
    "pronunciation": "rah-fah-EL",
    "origin": "Hebrew",
    "popularity_rank": 14,
    "popularity_change": "+2%",
    "year_2025_rank": 14,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 6800,
    "regional_popularity": {
      "zurich": 14,
      "bern": 14,
      "aargau": 14,
      "st_gallen": 14
    }
  },
  {
    "name_en": "Alexander",
    "name_native": "Alexander",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Defender of men",
    "meaning_native": "Beschützer der Menschen",
    "starting_letter": "A",
    "pronunciation": "ah-lek-SAHN-der",
    "origin": "Greek",
    "popularity_rank": 15,
    "popularity_change": "+1%",
    "year_2025_rank": 15,
    "year_2024_rank": 16,
    "trending_status": "stable",
    "search_volume": 6500,
    "regional_popularity": {
      "zurich": 15,
      "bern": 15,
      "aargau": 15,
      "st_gallen": 15
    }
  },
  {
    "name_en": "Oscar",
    "name_native": "Oscar",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Divine spear",
    "meaning_native": "Göttlicher Speer",
    "starting_letter": "O",
    "pronunciation": "OS-kar",
    "origin": "Irish",
    "popularity_rank": 16,
    "popularity_change": "+4%",
    "year_2025_rank": 16,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 6200,
    "regional_popularity": {
      "zurich": 16,
      "bern": 16,
      "aargau": 16,
      "st_gallen": 16
    }
  },
  {
    "name_en": "Victor",
    "name_native": "Victor",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Conqueror",
    "meaning_native": "Eroberer",
    "starting_letter": "V",
    "pronunciation": "VIK-tor",
    "origin": "Latin",
    "popularity_rank": 17,
    "popularity_change": "+2%",
    "year_2025_rank": 17,
    "year_2024_rank": 19,
    "trending_status": "rising",
    "search_volume": 5900,
    "regional_popularity": {
      "zurich": 17,
      "bern": 17,
      "aargau": 17,
      "st_gallen": 17
    }
  },
  {
    "name_en": "Felix",
    "name_native": "Felix",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Happy, fortunate",
    "meaning_native": "Glücklich, erfolgreich",
    "starting_letter": "F",
    "pronunciation": "FEH-liks",
    "origin": "Latin",
    "popularity_rank": 18,
    "popularity_change": "+3%",
    "year_2025_rank": 18,
    "year_2024_rank": 21,
    "trending_status": "rising",
    "search_volume": 5600,
    "regional_popularity": {
      "zurich": 18,
      "bern": 18,
      "aargau": 18,
      "st_gallen": 18
    }
  },
  {
    "name_en": "Thomas",
    "name_native": "Thomas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Twin",
    "meaning_native": "Zwilling",
    "starting_letter": "T",
    "pronunciation": "TO-mas",
    "origin": "Aramaic",
    "popularity_rank": 19,
    "popularity_change": "+1%",
    "year_2025_rank": 19,
    "year_2024_rank": 20,
    "trending_status": "stable",
    "search_volume": 5400,
    "regional_popularity": {
      "zurich": 19,
      "bern": 19,
      "aargau": 19,
      "st_gallen": 19
    }
  },
  {
    "name_en": "Antoine",
    "name_native": "Antoine",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Priceless",
    "meaning_native": "Unbezahlbar",
    "starting_letter": "A",
    "pronunciation": "ahn-TWAHN",
    "origin": "Latin",
    "popularity_rank": 20,
    "popularity_change": "+2%",
    "year_2025_rank": 20,
    "year_2024_rank": 22,
    "trending_status": "rising",
    "search_volume": 5100,
    "regional_popularity": {
      "zurich": 20,
      "bern": 20,
      "aargau": 20,
      "st_gallen": 20
    }
  },
  {
    "name_en": "Mathis",
    "name_native": "Mathis",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "mah-TEES",
    "origin": "Hebrew",
    "popularity_rank": 21,
    "popularity_change": "+3%",
    "year_2025_rank": 21,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 4900,
    "regional_popularity": {
      "zurich": 21,
      "bern": 21,
      "aargau": 21,
      "st_gallen": 21
    }
  },
  {
    "name_en": "Paul",
    "name_native": "Paul",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Small, humble",
    "meaning_native": "Klein, bescheiden",
    "starting_letter": "P",
    "pronunciation": "PAUL",
    "origin": "Latin",
    "popularity_rank": 22,
    "popularity_change": "+1%",
    "year_2025_rank": 22,
    "year_2024_rank": 23,
    "trending_status": "stable",
    "search_volume": 4700,
    "regional_popularity": {
      "zurich": 22,
      "bern": 22,
      "aargau": 22,
      "st_gallen": 22
    }
  },
  {
    "name_en": "Simon",
    "name_native": "Simon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "He has heard",
    "meaning_native": "Er hat gehört",
    "starting_letter": "S",
    "pronunciation": "SEE-mohn",
    "origin": "Hebrew",
    "popularity_rank": 23,
    "popularity_change": "+2%",
    "year_2025_rank": 23,
    "year_2024_rank": 25,
    "trending_status": "rising",
    "search_volume": 4500,
    "regional_popularity": {
      "zurich": 23,
      "bern": 23,
      "aargau": 23,
      "st_gallen": 23
    }
  },
  {
    "name_en": "Vincent",
    "name_native": "Vincent",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Conquering",
    "meaning_native": "Erobernd",
    "starting_letter": "V",
    "pronunciation": "VIN-sent",
    "origin": "Latin",
    "popularity_rank": 24,
    "popularity_change": "+4%",
    "year_2025_rank": 24,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 4300,
    "regional_popularity": {
      "zurich": 24,
      "bern": 24,
      "aargau": 24,
      "st_gallen": 24
    }
  },
  {
    "name_en": "Benjamin",
    "name_native": "Benjamin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Son of the right hand",
    "meaning_native": "Sohn der rechten Hand",
    "starting_letter": "B",
    "pronunciation": "ben-yah-MEEN",
    "origin": "Hebrew",
    "popularity_rank": 25,
    "popularity_change": "+1%",
    "year_2025_rank": 25,
    "year_2024_rank": 26,
    "trending_status": "stable",
    "search_volume": 4100,
    "regional_popularity": {
      "zurich": 25,
      "bern": 25,
      "aargau": 25,
      "st_gallen": 25
    }
  },
  {
    "name_en": "Gabriel",
    "name_native": "Gabriel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my strength",
    "meaning_native": "Gott ist meine Stärke",
    "starting_letter": "G",
    "pronunciation": "gah-bree-EL",
    "origin": "Hebrew",
    "popularity_rank": 26,
    "popularity_change": "+3%",
    "year_2025_rank": 26,
    "year_2024_rank": 29,
    "trending_status": "rising",
    "search_volume": 3900,
    "regional_popularity": {
      "zurich": 26,
      "bern": 26,
      "aargau": 26,
      "st_gallen": 26
    }
  },
  {
    "name_en": "David",
    "name_native": "David",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Beloved",
    "meaning_native": "Geliebt",
    "starting_letter": "D",
    "pronunciation": "DAH-veet",
    "origin": "Hebrew",
    "popularity_rank": 27,
    "popularity_change": "+1%",
    "year_2025_rank": 27,
    "year_2024_rank": 28,
    "trending_status": "stable",
    "search_volume": 3800,
    "regional_popularity": {
      "zurich": 27,
      "bern": 27,
      "aargau": 27,
      "st_gallen": 27
    }
  },
  {
    "name_en": "Aaron",
    "name_native": "Aaron",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "High mountain",
    "meaning_native": "Hoher Berg",
    "starting_letter": "A",
    "pronunciation": "AH-ron",
    "origin": "Hebrew",
    "popularity_rank": 28,
    "popularity_change": "+2%",
    "year_2025_rank": 28,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 3600,
    "regional_popularity": {
      "zurich": 28,
      "bern": 28,
      "aargau": 28,
      "st_gallen": 28
    }
  },
  {
    "name_en": "Daniel",
    "name_native": "Daniel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my judge",
    "meaning_native": "Gott ist mein Richter",
    "starting_letter": "D",
    "pronunciation": "DAH-nee-el",
    "origin": "Hebrew",
    "popularity_rank": 29,
    "popularity_change": "+1%",
    "year_2025_rank": 29,
    "year_2024_rank": 30,
    "trending_status": "stable",
    "search_volume": 3500,
    "regional_popularity": {
      "zurich": 29,
      "bern": 29,
      "aargau": 29,
      "st_gallen": 29
    }
  },
  {
    "name_en": "Jonas",
    "name_native": "Jonas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dove",
    "meaning_native": "Taube",
    "starting_letter": "J",
    "pronunciation": "YO-nas",
    "origin": "Hebrew",
    "popularity_rank": 30,
    "popularity_change": "+3%",
    "year_2025_rank": 30,
    "year_2024_rank": 33,
    "trending_status": "rising",
    "search_volume": 3400,
    "regional_popularity": {
      "zurich": 30,
      "bern": 30,
      "aargau": 30,
      "st_gallen": 30
    }
  },
  {
    "name_en": "Noel",
    "name_native": "Noel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Christmas",
    "meaning_native": "Weihnachten",
    "starting_letter": "N",
    "pronunciation": "NO-el",
    "origin": "French",
    "popularity_rank": 31,
    "popularity_change": "+1%",
    "year_2025_rank": 31,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 3300,
    "regional_popularity": {
      "zurich": 31,
      "bern": 31,
      "aargau": 31,
      "st_gallen": 31
    }
  },
  {
    "name_en": "Mats",
    "name_native": "Mats",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "MATS",
    "origin": "Scandinavian",
    "popularity_rank": 32,
    "popularity_change": "+2%",
    "year_2025_rank": 32,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 3200,
    "regional_popularity": {
      "zurich": 32,
      "bern": 32,
      "aargau": 32,
      "st_gallen": 32
    }
  },
  {
    "name_en": "Levin",
    "name_native": "Levin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dear friend",
    "meaning_native": "Lieber Freund",
    "starting_letter": "L",
    "pronunciation": "LEH-vin",
    "origin": "Germanic",
    "popularity_rank": 33,
    "popularity_change": "+4%",
    "year_2025_rank": 33,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 3100,
    "regional_popularity": {
      "zurich": 33,
      "bern": 33,
      "aargau": 33,
      "st_gallen": 33
    }
  },
  {
    "name_en": "Milo",
    "name_native": "Milo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Soldier",
    "meaning_native": "Soldat",
    "starting_letter": "M",
    "pronunciation": "MEE-loh",
    "origin": "Germanic",
    "popularity_rank": 34,
    "popularity_change": "+3%",
    "year_2025_rank": 34,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 3000,
    "regional_popularity": {
      "zurich": 34,
      "bern": 34,
      "aargau": 34,
      "st_gallen": 34
    }
  },
  {
    "name_en": "Levi",
    "name_native": "Levi",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Joined, attached",
    "meaning_native": "Verbunden, angehängt",
    "starting_letter": "L",
    "pronunciation": "LEH-vee",
    "origin": "Hebrew",
    "popularity_rank": 35,
    "popularity_change": "+2%",
    "year_2025_rank": 35,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 2900,
    "regional_popularity": {
      "zurich": 35,
      "bern": 35,
      "aargau": 35,
      "st_gallen": 35
    }
  },
  {
    "name_en": "Adrian",
    "name_native": "Adrian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "From Hadria",
    "meaning_native": "Aus Hadria",
    "starting_letter": "A",
    "pronunciation": "AH-dree-ahn",
    "origin": "Latin",
    "popularity_rank": 36,
    "popularity_change": "+3%",
    "year_2025_rank": 36,
    "year_2024_rank": 44,
    "trending_status": "rising",
    "search_volume": 2800,
    "regional_popularity": {
      "zurich": 36,
      "bern": 36,
      "aargau": 36,
      "st_gallen": 36
    }
  },
  {
    "name_en": "Colin",
    "name_native": "Colin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Young creature",
    "meaning_native": "Junges Wesen",
    "starting_letter": "C",
    "pronunciation": "KOH-lin",
    "origin": "Gaelic",
    "popularity_rank": 37,
    "popularity_change": "+1%",
    "year_2025_rank": 37,
    "year_2024_rank": 43,
    "trending_status": "stable",
    "search_volume": 2700,
    "regional_popularity": {
      "zurich": 37,
      "bern": 37,
      "aargau": 37,
      "st_gallen": 37
    }
  },
  {
    "name_en": "Diego",
    "name_native": "Diego",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Supplanter",
    "meaning_native": "Verdränger",
    "starting_letter": "D",
    "pronunciation": "dee-EH-goh",
    "origin": "Spanish",
    "popularity_rank": 38,
    "popularity_change": "+4%",
    "year_2025_rank": 38,
    "year_2024_rank": 47,
    "trending_status": "rising",
    "search_volume": 2600,
    "regional_popularity": {
      "zurich": 38,
      "bern": 38,
      "aargau": 38,
      "st_gallen": 38
    }
  },
  {
    "name_en": "Erik",
    "name_native": "Erik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Eternal ruler",
    "meaning_native": "Ewiger Herrscher",
    "starting_letter": "E",
    "pronunciation": "EH-rik",
    "origin": "Norse",
    "popularity_rank": 39,
    "popularity_change": "+2%",
    "year_2025_rank": 39,
    "year_2024_rank": 46,
    "trending_status": "rising",
    "search_volume": 2500,
    "regional_popularity": {
      "zurich": 39,
      "bern": 39,
      "aargau": 39,
      "st_gallen": 39
    }
  },
  {
    "name_en": "Gian",
    "name_native": "Gian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "G",
    "pronunciation": "jee-AHN",
    "origin": "Italian",
    "popularity_rank": 40,
    "popularity_change": "+3%",
    "year_2025_rank": 40,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 2400,
    "regional_popularity": {
      "zurich": 40,
      "bern": 40,
      "aargau": 40,
      "st_gallen": 40
    }
  },
  {
    "name_en": "Henry",
    "name_native": "Henry",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Ruler of the home",
    "meaning_native": "Herrscher des Heims",
    "starting_letter": "H",
    "pronunciation": "HEN-ree",
    "origin": "Germanic",
    "popularity_rank": 41,
    "popularity_change": "+1%",
    "year_2025_rank": 41,
    "year_2024_rank": 47,
    "trending_status": "stable",
    "search_volume": 2300,
    "regional_popularity": {
      "zurich": 41,
      "bern": 41,
      "aargau": 41,
      "st_gallen": 41
    }
  },
  {
    "name_en": "Ivan",
    "name_native": "Ivan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "I",
    "pronunciation": "ee-VAHN",
    "origin": "Slavic",
    "popularity_rank": 42,
    "popularity_change": "+5%",
    "year_2025_rank": 42,
    "year_2024_rank": 52,
    "trending_status": "rising",
    "search_volume": 2200,
    "regional_popularity": {
      "zurich": 42,
      "bern": 42,
      "aargau": 42,
      "st_gallen": 42
    }
  },
  {
    "name_en": "Jonas",
    "name_native": "Jonas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dove",
    "meaning_native": "Taube",
    "starting_letter": "J",
    "pronunciation": "YO-nahs",
    "origin": "Hebrew",
    "popularity_rank": 43,
    "popularity_change": "+2%",
    "year_2025_rank": 43,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 2100,
    "regional_popularity": {
      "zurich": 43,
      "bern": 43,
      "aargau": 43,
      "st_gallen": 43
    }
  },
  {
    "name_en": "Kevin",
    "name_native": "Kevin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gentle birth",
    "meaning_native": "Sanfte Geburt",
    "starting_letter": "K",
    "pronunciation": "KEH-vin",
    "origin": "Irish",
    "popularity_rank": 44,
    "popularity_change": "+1%",
    "year_2025_rank": 44,
    "year_2024_rank": 50,
    "trending_status": "stable",
    "search_volume": 2000,
    "regional_popularity": {
      "zurich": 44,
      "bern": 44,
      "aargau": 44,
      "st_gallen": 44
    }
  },
  {
    "name_en": "Leon",
    "name_native": "Leon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lion",
    "meaning_native": "Löwe",
    "starting_letter": "L",
    "pronunciation": "LEH-on",
    "origin": "Greek",
    "popularity_rank": 45,
    "popularity_change": "+3%",
    "year_2025_rank": 45,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 1900,
    "regional_popularity": {
      "zurich": 45,
      "bern": 45,
      "aargau": 45,
      "st_gallen": 45
    }
  },
  {
    "name_en": "Noel",
    "name_native": "Noel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Christmas",
    "meaning_native": "Weihnachten",
    "starting_letter": "N",
    "pronunciation": "NO-el",
    "origin": "French",
    "popularity_rank": 46,
    "popularity_change": "+1%",
    "year_2025_rank": 46,
    "year_2024_rank": 54,
    "trending_status": "rising",
    "search_volume": 1800,
    "regional_popularity": {
      "zurich": 46,
      "bern": 46,
      "aargau": 46,
      "st_gallen": 46
    }
  },
  {
    "name_en": "Mats",
    "name_native": "Mats",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "MATS",
    "origin": "Scandinavian",
    "popularity_rank": 47,
    "popularity_change": "+2%",
    "year_2025_rank": 47,
    "year_2024_rank": 55,
    "trending_status": "rising",
    "search_volume": 1700,
    "regional_popularity": {
      "zurich": 47,
      "bern": 47,
      "aargau": 47,
      "st_gallen": 47
    }
  },
  {
    "name_en": "Levin",
    "name_native": "Levin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dear friend",
    "meaning_native": "Lieber Freund",
    "starting_letter": "L",
    "pronunciation": "LEH-vin",
    "origin": "Germanic",
    "popularity_rank": 48,
    "popularity_change": "+4%",
    "year_2025_rank": 48,
    "year_2024_rank": 56,
    "trending_status": "rising",
    "search_volume": 1600,
    "regional_popularity": {
      "zurich": 48,
      "bern": 48,
      "aargau": 48,
      "st_gallen": 48
    }
  },
  {
    "name_en": "Milo",
    "name_native": "Milo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Soldier",
    "meaning_native": "Soldat",
    "starting_letter": "M",
    "pronunciation": "MEE-loh",
    "origin": "Germanic",
    "popularity_rank": 49,
    "popularity_change": "+3%",
    "year_2025_rank": 49,
    "year_2024_rank": 57,
    "trending_status": "rising",
    "search_volume": 1500,
    "regional_popularity": {
      "zurich": 49,
      "bern": 49,
      "aargau": 49,
      "st_gallen": 49
    }
  },
  {
    "name_en": "Levi",
    "name_native": "Levi",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Joined, attached",
    "meaning_native": "Verbunden, angehängt",
    "starting_letter": "L",
    "pronunciation": "LEH-vee",
    "origin": "Hebrew",
    "popularity_rank": 50,
    "popularity_change": "+2%",
    "year_2025_rank": 50,
    "year_2024_rank": 58,
    "trending_status": "rising",
    "search_volume": 1400,
    "regional_popularity": {
      "zurich": 50,
      "bern": 50,
      "aargau": 50,
      "st_gallen": 50
    }
  },
  {
    "name_en": "Adrian",
    "name_native": "Adrian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "From Hadria",
    "meaning_native": "Aus Hadria",
    "starting_letter": "A",
    "pronunciation": "AH-dree-ahn",
    "origin": "Latin",
    "popularity_rank": 51,
    "popularity_change": "+3%",
    "year_2025_rank": 51,
    "year_2024_rank": 60,
    "trending_status": "rising",
    "search_volume": 1300,
    "regional_popularity": {
      "zurich": 51,
      "bern": 51,
      "aargau": 51,
      "st_gallen": 51
    }
  },
  {
    "name_en": "Colin",
    "name_native": "Colin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Young creature",
    "meaning_native": "Junges Wesen",
    "starting_letter": "C",
    "pronunciation": "KOH-lin",
    "origin": "Gaelic",
    "popularity_rank": 52,
    "popularity_change": "+1%",
    "year_2025_rank": 52,
    "year_2024_rank": 61,
    "trending_status": "stable",
    "search_volume": 1200,
    "regional_popularity": {
      "zurich": 52,
      "bern": 52,
      "aargau": 52,
      "st_gallen": 52
    }
  },
  {
    "name_en": "Diego",
    "name_native": "Diego",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Supplanter",
    "meaning_native": "Verdränger",
    "starting_letter": "D",
    "pronunciation": "dee-EH-goh",
    "origin": "Spanish",
    "popularity_rank": 53,
    "popularity_change": "+4%",
    "year_2025_rank": 53,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 1100,
    "regional_popularity": {
      "zurich": 53,
      "bern": 53,
      "aargau": 53,
      "st_gallen": 53
    }
  },
  {
    "name_en": "Erik",
    "name_native": "Erik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Eternal ruler",
    "meaning_native": "Ewiger Herrscher",
    "starting_letter": "E",
    "pronunciation": "EH-rik",
    "origin": "Norse",
    "popularity_rank": 54,
    "popularity_change": "+2%",
    "year_2025_rank": 54,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 1000,
    "regional_popularity": {
      "zurich": 54,
      "bern": 54,
      "aargau": 54,
      "st_gallen": 54
    }
  },
  {
    "name_en": "Gian",
    "name_native": "Gian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "G",
    "pronunciation": "jee-AHN",
    "origin": "Italian",
    "popularity_rank": 55,
    "popularity_change": "+3%",
    "year_2025_rank": 55,
    "year_2024_rank": 68,
    "trending_status": "rising",
    "search_volume": 900,
    "regional_popularity": {
      "zurich": 55,
      "bern": 55,
      "aargau": 55,
      "st_gallen": 55
    }
  },
  {
    "name_en": "Henry",
    "name_native": "Henry",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Ruler of the home",
    "meaning_native": "Herrscher des Heims",
    "starting_letter": "H",
    "pronunciation": "HEN-ree",
    "origin": "Germanic",
    "popularity_rank": 56,
    "popularity_change": "+1%",
    "year_2025_rank": 56,
    "year_2024_rank": 67,
    "trending_status": "stable",
    "search_volume": 800,
    "regional_popularity": {
      "zurich": 56,
      "bern": 56,
      "aargau": 56,
      "st_gallen": 56
    }
  },
  {
    "name_en": "Ivan",
    "name_native": "Ivan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "I",
    "pronunciation": "ee-VAHN",
    "origin": "Slavic",
    "popularity_rank": 57,
    "popularity_change": "+5%",
    "year_2025_rank": 57,
    "year_2024_rank": 72,
    "trending_status": "rising",
    "search_volume": 700,
    "regional_popularity": {
      "zurich": 57,
      "bern": 57,
      "aargau": 57,
      "st_gallen": 57
    }
  },
  {
    "name_en": "Jonas",
    "name_native": "Jonas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dove",
    "meaning_native": "Taube",
    "starting_letter": "J",
    "pronunciation": "YO-nahs",
    "origin": "Hebrew",
    "popularity_rank": 58,
    "popularity_change": "+2%",
    "year_2025_rank": 58,
    "year_2024_rank": 70,
    "trending_status": "rising",
    "search_volume": 600,
    "regional_popularity": {
      "zurich": 58,
      "bern": 58,
      "aargau": 58,
      "st_gallen": 58
    }
  },
  {
    "name_en": "Kevin",
    "name_native": "Kevin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gentle birth",
    "meaning_native": "Sanfte Geburt",
    "starting_letter": "K",
    "pronunciation": "KEH-vin",
    "origin": "Irish",
    "popularity_rank": 59,
    "popularity_change": "+1%",
    "year_2025_rank": 59,
    "year_2024_rank": 70,
    "trending_status": "stable",
    "search_volume": 500,
    "regional_popularity": {
      "zurich": 59,
      "bern": 59,
      "aargau": 59,
      "st_gallen": 59
    }
  },
  {
    "name_en": "Leon",
    "name_native": "Leon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lion",
    "meaning_native": "Löwe",
    "starting_letter": "L",
    "pronunciation": "LEH-on",
    "origin": "Greek",
    "popularity_rank": 60,
    "popularity_change": "+3%",
    "year_2025_rank": 60,
    "year_2024_rank": 73,
    "trending_status": "rising",
    "search_volume": 400,
    "regional_popularity": {
      "zurich": 60,
      "bern": 60,
      "aargau": 60,
      "st_gallen": 60
    }
  },
  {
    "name_en": "Nico",
    "name_native": "Nico",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Sieg des Volkes",
    "starting_letter": "N",
    "pronunciation": "NEE-ko",
    "origin": "Greek",
    "popularity_rank": 61,
    "popularity_change": "+1%",
    "year_2025_rank": 61,
    "year_2024_rank": 74,
    "trending_status": "rising",
    "search_volume": 390,
    "regional_popularity": { "zurich": 61, "bern": 61, "aargau": 61, "st_gallen": 61 }
  },
  {
    "name_en": "Lars",
    "name_native": "Lars",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Crowned with laurel",
    "meaning_native": "Gekrönt mit Lorbeer",
    "starting_letter": "L",
    "pronunciation": "LAHRS",
    "origin": "Scandinavian",
    "popularity_rank": 62,
    "popularity_change": "+2%",
    "year_2025_rank": 62,
    "year_2024_rank": 75,
    "trending_status": "rising",
    "search_volume": 380,
    "regional_popularity": { "zurich": 62, "bern": 62, "aargau": 62, "st_gallen": 62 }
  },
  {
    "name_en": "Jannis",
    "name_native": "Jannis",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YAH-nis",
    "origin": "Hebrew",
    "popularity_rank": 63,
    "popularity_change": "+1%",
    "year_2025_rank": 63,
    "year_2024_rank": 76,
    "trending_status": "rising",
    "search_volume": 370,
    "regional_popularity": { "zurich": 63, "bern": 63, "aargau": 63, "st_gallen": 63 }
  },
  {
    "name_en": "Silas",
    "name_native": "Silas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Man of the forest",
    "meaning_native": "Mann des Waldes",
    "starting_letter": "S",
    "pronunciation": "SEE-las",
    "origin": "Latin",
    "popularity_rank": 64,
    "popularity_change": "+2%",
    "year_2025_rank": 64,
    "year_2024_rank": 77,
    "trending_status": "rising",
    "search_volume": 360,
    "regional_popularity": { "zurich": 64, "bern": 64, "aargau": 64, "st_gallen": 64 }
  },
  {
    "name_en": "Lio",
    "name_native": "Lio",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lion",
    "meaning_native": "Löwe",
    "starting_letter": "L",
    "pronunciation": "LEE-oh",
    "origin": "Latin",
    "popularity_rank": 65,
    "popularity_change": "+1%",
    "year_2025_rank": 65,
    "year_2024_rank": 78,
    "trending_status": "rising",
    "search_volume": 350,
    "regional_popularity": { "zurich": 65, "bern": 65, "aargau": 65, "st_gallen": 65 }
  },
  {
    "name_en": "Mika",
    "name_native": "Mika",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Who is like God?",
    "meaning_native": "Wer ist wie Gott?",
    "starting_letter": "M",
    "pronunciation": "MEE-kah",
    "origin": "Hebrew",
    "popularity_rank": 66,
    "popularity_change": "+2%",
    "year_2025_rank": 66,
    "year_2024_rank": 79,
    "trending_status": "rising",
    "search_volume": 340,
    "regional_popularity": { "zurich": 66, "bern": 66, "aargau": 66, "st_gallen": 66 }
  },
  {
    "name_en": "Timon",
    "name_native": "Timon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honoring God",
    "meaning_native": "Gott ehrend",
    "starting_letter": "T",
    "pronunciation": "TEE-mon",
    "origin": "Greek",
    "popularity_rank": 67,
    "popularity_change": "+1%",
    "year_2025_rank": 67,
    "year_2024_rank": 80,
    "trending_status": "rising",
    "search_volume": 330,
    "regional_popularity": { "zurich": 67, "bern": 67, "aargau": 67, "st_gallen": 67 }
  },
  {
    "name_en": "Yannick",
    "name_native": "Yannick",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "Y",
    "pronunciation": "YAN-ik",
    "origin": "Breton",
    "popularity_rank": 68,
    "popularity_change": "+2%",
    "year_2025_rank": 68,
    "year_2024_rank": 81,
    "trending_status": "rising",
    "search_volume": 320,
    "regional_popularity": { "zurich": 68, "bern": 68, "aargau": 68, "st_gallen": 68 }
  },
  {
    "name_en": "Joel",
    "name_native": "Joel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Yahweh is God",
    "meaning_native": "Jahwe ist Gott",
    "starting_letter": "J",
    "pronunciation": "YO-el",
    "origin": "Hebrew",
    "popularity_rank": 69,
    "popularity_change": "+1%",
    "year_2025_rank": 69,
    "year_2024_rank": 82,
    "trending_status": "rising",
    "search_volume": 310,
    "regional_popularity": { "zurich": 69, "bern": 69, "aargau": 69, "st_gallen": 69 }
  },
  {
    "name_en": "Milan",
    "name_native": "Milan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gracious, dear",
    "meaning_native": "Gnädig, lieb",
    "starting_letter": "M",
    "pronunciation": "MEE-lan",
    "origin": "Slavic",
    "popularity_rank": 70,
    "popularity_change": "+2%",
    "year_2025_rank": 70,
    "year_2024_rank": 83,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": { "zurich": 70, "bern": 70, "aargau": 70, "st_gallen": 70 }
  },
  {
    "name_en": "Luan",
    "name_native": "Luan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lion",
    "meaning_native": "Löwe",
    "starting_letter": "L",
    "pronunciation": "LOO-ahn",
    "origin": "Albanian",
    "popularity_rank": 71,
    "popularity_change": "+1%",
    "year_2025_rank": 71,
    "year_2024_rank": 84,
    "trending_status": "rising",
    "search_volume": 290,
    "regional_popularity": { "zurich": 71, "bern": 71, "aargau": 71, "st_gallen": 71 }
  },
  {
    "name_en": "Noah",
    "name_native": "Noah",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rest, comfort",
    "meaning_native": "Ruhe, Trost",
    "starting_letter": "N",
    "pronunciation": "NO-ah",
    "origin": "Hebrew",
    "popularity_rank": 72,
    "popularity_change": "+2%",
    "year_2025_rank": 72,
    "year_2024_rank": 85,
    "trending_status": "rising",
    "search_volume": 280,
    "regional_popularity": { "zurich": 72, "bern": 72, "aargau": 72, "st_gallen": 72 }
  },
  {
    "name_en": "Janis",
    "name_native": "Janis",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YAH-nis",
    "origin": "Hebrew",
    "popularity_rank": 73,
    "popularity_change": "+1%",
    "year_2025_rank": 73,
    "year_2024_rank": 86,
    "trending_status": "rising",
    "search_volume": 270,
    "regional_popularity": { "zurich": 73, "bern": 73, "aargau": 73, "st_gallen": 73 }
  },
  {
    "name_en": "Miro",
    "name_native": "Miro",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Peace, world",
    "meaning_native": "Frieden, Welt",
    "starting_letter": "M",
    "pronunciation": "MEE-ro",
    "origin": "Slavic",
    "popularity_rank": 74,
    "popularity_change": "+2%",
    "year_2025_rank": 74,
    "year_2024_rank": 87,
    "trending_status": "rising",
    "search_volume": 260,
    "regional_popularity": { "zurich": 74, "bern": 74, "aargau": 74, "st_gallen": 74 }
  },
  {
    "name_en": "Nils",
    "name_native": "Nils",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Sieg des Volkes",
    "starting_letter": "N",
    "pronunciation": "NEELS",
    "origin": "Scandinavian",
    "popularity_rank": 75,
    "popularity_change": "+1%",
    "year_2025_rank": 75,
    "year_2024_rank": 88,
    "trending_status": "rising",
    "search_volume": 250,
    "regional_popularity": { "zurich": 75, "bern": 75, "aargau": 75, "st_gallen": 75 }
  },
  {
    "name_en": "Yves",
    "name_native": "Yves",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Yew wood",
    "meaning_native": "Eibenholz",
    "starting_letter": "Y",
    "pronunciation": "EEV",
    "origin": "French",
    "popularity_rank": 76,
    "popularity_change": "+2%",
    "year_2025_rank": 76,
    "year_2024_rank": 89,
    "trending_status": "rising",
    "search_volume": 240,
    "regional_popularity": { "zurich": 76, "bern": 76, "aargau": 76, "st_gallen": 76 }
  },
  {
    "name_en": "Oskar",
    "name_native": "Oskar",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God spear",
    "meaning_native": "Gottes Speer",
    "starting_letter": "O",
    "pronunciation": "OS-kar",
    "origin": "Germanic",
    "popularity_rank": 77,
    "popularity_change": "+1%",
    "year_2025_rank": 77,
    "year_2024_rank": 90,
    "trending_status": "rising",
    "search_volume": 230,
    "regional_popularity": { "zurich": 77, "bern": 77, "aargau": 77, "st_gallen": 77 }
  },
  {
    "name_en": "Rico",
    "name_native": "Rico",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Brave ruler",
    "meaning_native": "Mutiger Herrscher",
    "starting_letter": "R",
    "pronunciation": "REE-ko",
    "origin": "Spanish",
    "popularity_rank": 78,
    "popularity_change": "+2%",
    "year_2025_rank": 78,
    "year_2024_rank": 91,
    "trending_status": "rising",
    "search_volume": 220,
    "regional_popularity": { "zurich": 78, "bern": 78, "aargau": 78, "st_gallen": 78 }
  },
  {
    "name_en": "Sandro",
    "name_native": "Sandro",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Defender of mankind",
    "meaning_native": "Verteidiger der Menschheit",
    "starting_letter": "S",
    "pronunciation": "SAN-dro",
    "origin": "Italian",
    "popularity_rank": 79,
    "popularity_change": "+1%",
    "year_2025_rank": 79,
    "year_2024_rank": 92,
    "trending_status": "rising",
    "search_volume": 210,
    "regional_popularity": { "zurich": 79, "bern": 79, "aargau": 79, "st_gallen": 79 }
  },
  {
    "name_en": "Timon",
    "name_native": "Timon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honoring God",
    "meaning_native": "Gott ehrend",
    "starting_letter": "T",
    "pronunciation": "TEE-mon",
    "origin": "Greek",
    "popularity_rank": 80,
    "popularity_change": "+2%",
    "year_2025_rank": 80,
    "year_2024_rank": 93,
    "trending_status": "rising",
    "search_volume": 200,
    "regional_popularity": { "zurich": 80, "bern": 80, "aargau": 80, "st_gallen": 80 }
  },
  {
    "name_en": "Bastian",
    "name_native": "Bastian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Venerable",
    "meaning_native": "Ehrwürdig",
    "starting_letter": "B",
    "pronunciation": "BAS-tee-an",
    "origin": "Greek",
    "popularity_rank": 81,
    "popularity_change": "+3%",
    "year_2025_rank": 81,
    "year_2024_rank": 84,
    "trending_status": "rising",
    "search_volume": 420,
    "regional_popularity": {
      "zurich": 81,
      "bern": 81,
      "basel": 81,
      "geneva": 81
    }
  },
  {
    "name_en": "Cedric",
    "name_native": "Cedric",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bounty",
    "meaning_native": "Fülle",
    "starting_letter": "C",
    "pronunciation": "SED-rik",
    "origin": "Celtic",
    "popularity_rank": 82,
    "popularity_change": "+5%",
    "year_2025_rank": 82,
    "year_2024_rank": 87,
    "trending_status": "rising",
    "search_volume": 380,
    "regional_popularity": {
      "zurich": 82,
      "bern": 82,
      "basel": 82,
      "geneva": 82
    }
  },
  {
    "name_en": "Dominik",
    "name_native": "Dominik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Belonging to the Lord",
    "meaning_native": "Dem Herrn gehörend",
    "starting_letter": "D",
    "pronunciation": "do-MEE-nik",
    "origin": "Latin",
    "popularity_rank": 83,
    "popularity_change": "+2%",
    "year_2025_rank": 83,
    "year_2024_rank": 85,
    "trending_status": "stable",
    "search_volume": 340,
    "regional_popularity": {
      "zurich": 83,
      "bern": 83,
      "basel": 83,
      "geneva": 83
    }
  },
  {
    "name_en": "Elias",
    "name_native": "Elias",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "My God is Yahweh",
    "meaning_native": "Mein Gott ist Yahweh",
    "starting_letter": "E",
    "pronunciation": "ee-LEE-as",
    "origin": "Hebrew",
    "popularity_rank": 84,
    "popularity_change": "+4%",
    "year_2025_rank": 84,
    "year_2024_rank": 88,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": {
      "zurich": 84,
      "bern": 84,
      "basel": 84,
      "geneva": 84
    }
  },
  {
    "name_en": "Fabian",
    "name_native": "Fabian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bean grower",
    "meaning_native": "Bohnenzüchter",
    "starting_letter": "F",
    "pronunciation": "FAH-bee-an",
    "origin": "Latin",
    "popularity_rank": 85,
    "popularity_change": "+6%",
    "year_2025_rank": 85,
    "year_2024_rank": 91,
    "trending_status": "rising",
    "search_volume": 260,
    "regional_popularity": {
      "zurich": 85,
      "bern": 85,
      "basel": 85,
      "geneva": 85
    }
  },
  {
    "name_en": "Gabriel",
    "name_native": "Gabriel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my strength",
    "meaning_native": "Gott ist meine Stärke",
    "starting_letter": "G",
    "pronunciation": "GAH-bree-el",
    "origin": "Hebrew",
    "popularity_rank": 86,
    "popularity_change": "+3%",
    "year_2025_rank": 86,
    "year_2024_rank": 89,
    "trending_status": "rising",
    "search_volume": 220,
    "regional_popularity": {
      "zurich": 86,
      "bern": 86,
      "basel": 86,
      "geneva": 86
    }
  },
  {
    "name_en": "Hendrik",
    "name_native": "Hendrik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Ruler of the home",
    "meaning_native": "Herrscher des Hauses",
    "starting_letter": "H",
    "pronunciation": "HEN-drik",
    "origin": "Germanic",
    "popularity_rank": 87,
    "popularity_change": "+5%",
    "year_2025_rank": 87,
    "year_2024_rank": 92,
    "trending_status": "rising",
    "search_volume": 180,
    "regional_popularity": {
      "zurich": 87,
      "bern": 87,
      "basel": 87,
      "geneva": 87
    }
  },
  {
    "name_en": "Ivan",
    "name_native": "Ivan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "I",
    "pronunciation": "ee-van",
    "origin": "Slavic",
    "popularity_rank": 88,
    "popularity_change": "+2%",
    "year_2025_rank": 88,
    "year_2024_rank": 90,
    "trending_status": "stable",
    "search_volume": 140,
    "regional_popularity": {
      "zurich": 88,
      "bern": 88,
      "basel": 88,
      "geneva": 88
    }
  },
  {
    "name_en": "Jasper",
    "name_native": "Jasper",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bringer of treasure",
    "meaning_native": "Bringer des Schatzes",
    "starting_letter": "J",
    "pronunciation": "YAS-per",
    "origin": "Persian",
    "popularity_rank": 89,
    "popularity_change": "+4%",
    "year_2025_rank": 89,
    "year_2024_rank": 93,
    "trending_status": "rising",
    "search_volume": 100,
    "regional_popularity": {
      "zurich": 89,
      "bern": 89,
      "basel": 89,
      "geneva": 89
    }
  },
  {
    "name_en": "Kilian",
    "name_native": "Kilian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Church",
    "meaning_native": "Kirche",
    "starting_letter": "K",
    "pronunciation": "KEE-lee-an",
    "origin": "Irish",
    "popularity_rank": 90,
    "popularity_change": "+1%",
    "year_2025_rank": 90,
    "year_2024_rank": 91,
    "trending_status": "stable",
    "search_volume": 60,
    "regional_popularity": {
      "zurich": 90,
      "bern": 90,
      "basel": 90,
      "geneva": 90
    }
  },
  {
    "name_en": "Levi",
    "name_native": "Levi",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Joined",
    "meaning_native": "Verbunden",
    "starting_letter": "L",
    "pronunciation": "LAY-vee",
    "origin": "Hebrew",
    "popularity_rank": 91,
    "popularity_change": "+3%",
    "year_2025_rank": 91,
    "year_2024_rank": 94,
    "trending_status": "rising",
    "search_volume": 20,
    "regional_popularity": {
      "zurich": 91,
      "bern": 91,
      "basel": 91,
      "geneva": 91
    }
  },
  {
    "name_en": "Milan",
    "name_native": "Milan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gracious",
    "meaning_native": "Gnädig",
    "starting_letter": "M",
    "pronunciation": "MEE-lan",
    "origin": "Slavic",
    "popularity_rank": 92,
    "popularity_change": "+5%",
    "year_2025_rank": 92,
    "year_2024_rank": 97,
    "trending_status": "rising",
    "search_volume": 15,
    "regional_popularity": {
      "zurich": 92,
      "bern": 92,
      "basel": 92,
      "geneva": 92
    }
  },
  {
    "name_en": "Nico",
    "name_native": "Nico",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Sieg des Volkes",
    "starting_letter": "N",
    "pronunciation": "NEE-ko",
    "origin": "Greek",
    "popularity_rank": 93,
    "popularity_change": "+2%",
    "year_2025_rank": 93,
    "year_2024_rank": 95,
    "trending_status": "stable",
    "search_volume": 10,
    "regional_popularity": {
      "zurich": 93,
      "bern": 93,
      "basel": 93,
      "geneva": 93
    }
  },
  {
    "name_en": "Owen",
    "name_native": "Owen",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Young warrior",
    "meaning_native": "Junger Krieger",
    "starting_letter": "O",
    "pronunciation": "OH-en",
    "origin": "Welsh",
    "popularity_rank": 94,
    "popularity_change": "+4%",
    "year_2025_rank": 94,
    "year_2024_rank": 98,
    "trending_status": "rising",
    "search_volume": 5,
    "regional_popularity": {
      "zurich": 94,
      "bern": 94,
      "basel": 94,
      "geneva": 94
    }
  }
]

export default SwissGermanBoyNames;
