import type { NameData } from "@/types/name-data";

export const SwissFrenchGirlNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Whole, universal",
    "meaning_native": "Entière, universelle",
    "starting_letter": "E",
    "pronunciation": "eh-MAH",
    "origin": "Germanic",
    "popularity_rank": 1,
    "popularity_change": "+1%",
    "year_2025_rank": 1,
    "year_2024_rank": 2,
    "trending_status": "stable",
    "search_volume": 9200,
    "regional_popularity": {
      "geneva": 1,
      "vaud": 1,
      "valais": 1,
      "neuchatel": 1
    }
  },
  {
    "name_en": "Alice",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Noble, kind",
    "meaning_native": "Noble, bienveillante",
    "starting_letter": "A",
    "pronunciation": "ah-LEES",
    "origin": "Germanic",
    "popularity_rank": 2,
    "popularity_change": "+2%",
    "year_2025_rank": 2,
    "year_2024_rank": 4,
    "trending_status": "rising",
    "search_volume": 8600,
    "regional_popularity": {
      "geneva": 2,
      "vaud": 2,
      "valais": 2,
      "neuchatel": 2
    }
  },
  {
    "name_en": "Chloe",
    "name_native": "Chloé",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Green shoot",
    "meaning_native": "Jeune pousse verte",
    "starting_letter": "C",
    "pronunciation": "klo-EH",
    "origin": "Greek",
    "popularity_rank": 3,
    "popularity_change": "+1%",
    "year_2025_rank": 3,
    "year_2024_rank": 4,
    "trending_status": "stable",
    "search_volume": 7800,
    "regional_popularity": {
      "geneva": 3,
      "vaud": 3,
      "valais": 3,
      "neuchatel": 3
    }
  },
  {
    "name_en": "Louise",
    "name_native": "Louise",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Famous warrior",
    "meaning_native": "Guerrière célèbre",
    "starting_letter": "L",
    "pronunciation": "loo-EEZ",
    "origin": "Germanic",
    "popularity_rank": 4,
    "popularity_change": "+2%",
    "year_2025_rank": 4,
    "year_2024_rank": 6,
    "trending_status": "rising",
    "search_volume": 7200,
    "regional_popularity": {
      "geneva": 4,
      "vaud": 4,
      "valais": 4,
      "neuchatel": 4
    }
  },
  {
    "name_en": "Lina",
    "name_native": "Lina",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Tender, delicate",
    "meaning_native": "Tendre, délicate",
    "starting_letter": "L",
    "pronunciation": "lee-NAH",
    "origin": "Arabic",
    "popularity_rank": 5,
    "popularity_change": "+3%",
    "year_2025_rank": 5,
    "year_2024_rank": 8,
    "trending_status": "rising",
    "search_volume": 6400,
    "regional_popularity": {
      "geneva": 5,
      "vaud": 5,
      "valais": 5,
      "neuchatel": 5
    }
  },
  {
    "name_en": "Jade",
    "name_native": "Jade",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Jade (the stone)",
    "meaning_native": "Jade (la pierre)",
    "starting_letter": "J",
    "pronunciation": "ZHAD",
    "origin": "Spanish",
    "popularity_rank": 6,
    "popularity_change": "+2%",
    "year_2025_rank": 6,
    "year_2024_rank": 8,
    "trending_status": "rising",
    "search_volume": 6100,
    "regional_popularity": {
      "geneva": 6,
      "vaud": 6,
      "valais": 6,
      "neuchatel": 6
    }
  },
  {
    "name_en": "Rose",
    "name_native": "Rose",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Rose flower",
    "meaning_native": "Fleur de rose",
    "starting_letter": "R",
    "pronunciation": "ROZE",
    "origin": "Latin",
    "popularity_rank": 7,
    "popularity_change": "+1%",
    "year_2025_rank": 7,
    "year_2024_rank": 8,
    "trending_status": "stable",
    "search_volume": 5800,
    "regional_popularity": {
      "geneva": 7,
      "vaud": 7,
      "valais": 7,
      "neuchatel": 7
    }
  },
  {
    "name_en": "Mia",
    "name_native": "Mia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Mine, beloved",
    "meaning_native": "Mienne, bien-aimée",
    "starting_letter": "M",
    "pronunciation": "MEE-ah",
    "origin": "Italian",
    "popularity_rank": 8,
    "popularity_change": "+3%",
    "year_2025_rank": 8,
    "year_2024_rank": 11,
    "trending_status": "rising",
    "search_volume": 5500,
    "regional_popularity": {
      "geneva": 8,
      "vaud": 8,
      "valais": 8,
      "neuchatel": 8
    }
  },
  {
    "name_en": "Camille",
    "name_native": "Camille",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Attendant at a religious ceremony",
    "meaning_native": "Servante à la cérémonie religieuse",
    "starting_letter": "C",
    "pronunciation": "kah-MEE",
    "origin": "Latin",
    "popularity_rank": 9,
    "popularity_change": "+1%",
    "year_2025_rank": 9,
    "year_2024_rank": 13,
    "trending_status": "rising",
    "search_volume": 5000,
    "regional_popularity": {
      "geneva": 9,
      "vaud": 9,
      "valais": 9,
      "neuchatel": 9
    }
  },
  {
    "name_en": "Lea",
    "name_native": "Léa",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Weary",
    "meaning_native": "Fatiguée",
    "starting_letter": "L",
    "pronunciation": "LEH-ah",
    "origin": "Hebrew",
    "popularity_rank": 10,
    "popularity_change": "+1%",
    "year_2025_rank": 10,
    "year_2024_rank": 11,
    "trending_status": "stable",
    "search_volume": 4900,
    "regional_popularity": {
      "geneva": 10,
      "vaud": 10,
      "valais": 10,
      "neuchatel": 10
    }
  },
  {
    "name_en": "Sofia",
    "name_native": "Sofia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Wisdom",
    "meaning_native": "Sagesse",
    "starting_letter": "S",
    "pronunciation": "so-FEE-ah",
    "origin": "Greek",
    "popularity_rank": 11,
    "popularity_change": "+4%",
    "year_2025_rank": 11,
    "year_2024_rank": 15,
    "trending_status": "rising",
    "search_volume": 4800,
    "regional_popularity": {
      "geneva": 11,
      "vaud": 11,
      "valais": 11,
      "neuchatel": 11
    }
  },
  {
    "name_en": "Eva",
    "name_native": "Eva",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Life",
    "meaning_native": "Vie",
    "starting_letter": "E",
    "pronunciation": "eh-VAH",
    "origin": "Hebrew",
    "popularity_rank": 12,
    "popularity_change": "+2%",
    "year_2025_rank": 12,
    "year_2024_rank": 14,
    "trending_status": "rising",
    "search_volume": 4300,
    "regional_popularity": {
      "geneva": 12,
      "vaud": 12,
      "valais": 12,
      "neuchatel": 12
    }
  },
  {
    "name_en": "Clara",
    "name_native": "Clara",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bright, clear",
    "meaning_native": "Lumineuse, claire",
    "starting_letter": "C",
    "pronunciation": "klah-RAH",
    "origin": "Latin",
    "popularity_rank": 13,
    "popularity_change": "+1%",
    "year_2025_rank": 13,
    "year_2024_rank": 14,
    "trending_status": "stable",
    "search_volume": 4100,
    "regional_popularity": {
      "geneva": 13,
      "vaud": 13,
      "valais": 13,
      "neuchatel": 13
    }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoé",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Life",
    "meaning_native": "Vie",
    "starting_letter": "Z",
    "pronunciation": "zo-EH",
    "origin": "Greek",
    "popularity_rank": 14,
    "popularity_change": "+3%",
    "year_2025_rank": 14,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 3800,
    "regional_popularity": {
      "geneva": 14,
      "vaud": 14,
      "valais": 14,
      "neuchatel": 14
    }
  },
  {
    "name_en": "Charlotte",
    "name_native": "Charlotte",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Free woman",
    "meaning_native": "Femme libre",
    "starting_letter": "C",
    "pronunciation": "shar-LOT",
    "origin": "Germanic",
    "popularity_rank": 15,
    "popularity_change": "+2%",
    "year_2025_rank": 15,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 3600,
    "regional_popularity": {
      "geneva": 15,
      "vaud": 15,
      "valais": 15,
      "neuchatel": 15
    }
  },
  {
    "name_en": "Lily",
    "name_native": "Lily",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Lily flower",
    "meaning_native": "Fleur de lys",
    "starting_letter": "L",
    "pronunciation": "LEE-lee",
    "origin": "Latin",
    "popularity_rank": 16,
    "popularity_change": "+1%",
    "year_2025_rank": 16,
    "year_2024_rank": 17,
    "trending_status": "stable",
    "search_volume": 3400,
    "regional_popularity": {
      "geneva": 16,
      "vaud": 16,
      "valais": 16,
      "neuchatel": 16
    }
  },
  {
    "name_en": "Elena",
    "name_native": "Elena",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bright light",
    "meaning_native": "Lumière vive",
    "starting_letter": "E",
    "pronunciation": "eh-LEH-nah",
    "origin": "Greek",
    "popularity_rank": 17,
    "popularity_change": "+4%",
    "year_2025_rank": 17,
    "year_2024_rank": 21,
    "trending_status": "rising",
    "search_volume": 3200,
    "regional_popularity": {
      "geneva": 17,
      "vaud": 17,
      "valais": 17,
      "neuchatel": 17
    }
  },
  {
    "name_en": "Maya",
    "name_native": "Maya",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Illusion, dream",
    "meaning_native": "Illusion, rêve",
    "starting_letter": "M",
    "pronunciation": "mah-YAH",
    "origin": "Sanskrit",
    "popularity_rank": 18,
    "popularity_change": "+2%",
    "year_2025_rank": 18,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 3000,
    "regional_popularity": {
      "geneva": 18,
      "vaud": 18,
      "valais": 18,
      "neuchatel": 18
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amélie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Work, industrious",
    "meaning_native": "Travail, industrieuse",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 19,
    "popularity_change": "+1%",
    "year_2025_rank": 19,
    "year_2024_rank": 20,
    "trending_status": "stable",
    "search_volume": 2800,
    "regional_popularity": {
      "geneva": 19,
      "vaud": 19,
      "valais": 19,
      "neuchatel": 19
    }
  },
  {
    "name_en": "Emilie",
    "name_native": "Émilie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Rival",
    "meaning_native": "Rivale",
    "starting_letter": "E",
    "pronunciation": "eh-mee-LEE",
    "origin": "Latin",
    "popularity_rank": 20,
    "popularity_change": "+3%",
    "year_2025_rank": 20,
    "year_2024_rank": 23,
    "trending_status": "rising",
    "search_volume": 2600,
    "regional_popularity": {
      "geneva": 20,
      "vaud": 20,
      "valais": 20,
      "neuchatel": 20
    }
  },
  {
    "name_en": "Sarah",
    "name_native": "Sarah",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Princess",
    "meaning_native": "Princesse",
    "starting_letter": "S",
    "pronunciation": "sah-RAH",
    "origin": "Hebrew",
    "popularity_rank": 21,
    "popularity_change": "+1%",
    "year_2025_rank": 21,
    "year_2024_rank": 22,
    "trending_status": "stable",
    "search_volume": 2500,
    "regional_popularity": {
      "geneva": 21,
      "vaud": 21,
      "valais": 21,
      "neuchatel": 21
    }
  },
  {
    "name_en": "Julia",
    "name_native": "Julia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Youthful",
    "meaning_native": "Juvénile",
    "starting_letter": "J",
    "pronunciation": "zhü-LEE-ah",
    "origin": "Latin",
    "popularity_rank": 22,
    "popularity_change": "+2%",
    "year_2025_rank": 22,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 2300,
    "regional_popularity": {
      "geneva": 22,
      "vaud": 22,
      "valais": 22,
      "neuchatel": 22
    }
  },
  {
    "name_en": "Victoria",
    "name_native": "Victoria",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Victory",
    "meaning_native": "Victoire",
    "starting_letter": "V",
    "pronunciation": "vik-to-REE-ah",
    "origin": "Latin",
    "popularity_rank": 23,
    "popularity_change": "+1%",
    "year_2025_rank": 23,
    "year_2024_rank": 24,
    "trending_status": "stable",
    "search_volume": 2200,
    "regional_popularity": {
      "geneva": 23,
      "vaud": 23,
      "valais": 23,
      "neuchatel": 23
    }
  },
  {
    "name_en": "Lola",
    "name_native": "Lola",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Sorrows",
    "meaning_native": "Chagrins",
    "starting_letter": "L",
    "pronunciation": "lo-LAH",
    "origin": "Spanish",
    "popularity_rank": 24,
    "popularity_change": "+4%",
    "year_2025_rank": 24,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 2100,
    "regional_popularity": {
      "geneva": 24,
      "vaud": 24,
      "valais": 24,
      "neuchatel": 24
    }
  },
  {
    "name_en": "Anna",
    "name_native": "Anna",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Grace, favor",
    "meaning_native": "Grâce, faveur",
    "starting_letter": "A",
    "pronunciation": "ah-NAH",
    "origin": "Hebrew",
    "popularity_rank": 25,
    "popularity_change": "+1%",
    "year_2025_rank": 25,
    "year_2024_rank": 26,
    "trending_status": "stable",
    "search_volume": 1900,
    "regional_popularity": {
      "geneva": 25,
      "vaud": 25,
      "valais": 25,
      "neuchatel": 25
    }
  },
  {
    "name_en": "Manon",
    "name_native": "Manon",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bitter, beloved",
    "meaning_native": "Amère, bien-aimée",
    "starting_letter": "M",
    "pronunciation": "mah-NON",
    "origin": "Hebrew",
    "popularity_rank": 26,
    "popularity_change": "+2%",
    "year_2025_rank": 26,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 1800,
    "regional_popularity": {
      "geneva": 26,
      "vaud": 26,
      "valais": 26,
      "neuchatel": 26
    }
  },
  {
    "name_en": "Ines",
    "name_native": "Inès",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pure, chaste",
    "meaning_native": "Pure, chaste",
    "starting_letter": "I",
    "pronunciation": "ee-NESS",
    "origin": "Greek",
    "popularity_rank": 27,
    "popularity_change": "+3%",
    "year_2025_rank": 27,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 1700,
    "regional_popularity": {
      "geneva": 27,
      "vaud": 27,
      "valais": 27,
      "neuchatel": 27
    }
  },
  {
    "name_en": "Margot",
    "name_native": "Margot",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "M",
    "pronunciation": "mar-GO",
    "origin": "Greek",
    "popularity_rank": 28,
    "popularity_change": "+1%",
    "year_2025_rank": 28,
    "year_2024_rank": 29,
    "trending_status": "stable",
    "search_volume": 1600,
    "regional_popularity": {
      "geneva": 28,
      "vaud": 28,
      "valais": 28,
      "neuchatel": 28
    }
  },
  {
    "name_en": "Nora",
    "name_native": "Nora",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Honor",
    "meaning_native": "Honneur",
    "starting_letter": "N",
    "pronunciation": "no-RAH",
    "origin": "Irish",
    "popularity_rank": 29,
    "popularity_change": "+2%",
    "year_2025_rank": 29,
    "year_2024_rank": 31,
    "trending_status": "rising",
    "search_volume": 1500,
    "regional_popularity": {
      "geneva": 29,
      "vaud": 29,
      "valais": 29,
      "neuchatel": 29
    }
  },
  {
    "name_en": "Laura",
    "name_native": "Laura",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Laurel tree",
    "meaning_native": "Laurier",
    "starting_letter": "L",
    "pronunciation": "loh-RAH",
    "origin": "Latin",
    "popularity_rank": 30,
    "popularity_change": "+1%",
    "year_2025_rank": 30,
    "year_2024_rank": 31,
    "trending_status": "stable",
    "search_volume": 1400,
    "regional_popularity": {
      "geneva": 30,
      "vaud": 30,
      "valais": 30,
      "neuchatel": 30
    }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoé",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Life",
    "meaning_native": "Vie",
    "starting_letter": "Z",
    "pronunciation": "zo-EH",
    "origin": "Greek",
    "popularity_rank": 31,
    "popularity_change": "+3%",
    "year_2025_rank": 31,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 1300,
    "regional_popularity": {
      "geneva": 31,
      "vaud": 31,
      "valais": 31,
      "neuchatel": 31
    }
  },
  {
    "name_en": "Camille",
    "name_native": "Camille",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Perfect",
    "meaning_native": "Parfaite",
    "starting_letter": "C",
    "pronunciation": "kah-MEEL",
    "origin": "Latin",
    "popularity_rank": 32,
    "popularity_change": "+2%",
    "year_2025_rank": 32,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 1200,
    "regional_popularity": {
      "geneva": 32,
      "vaud": 32,
      "valais": 32,
      "neuchatel": 32
    }
  },
  {
    "name_en": "Juliette",
    "name_native": "Juliette",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Youthful",
    "meaning_native": "Jeune",
    "starting_letter": "J",
    "pronunciation": "zhoo-lee-ET",
    "origin": "Latin",
    "popularity_rank": 33,
    "popularity_change": "+4%",
    "year_2025_rank": 33,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 1100,
    "regional_popularity": {
      "geneva": 33,
      "vaud": 33,
      "valais": 33,
      "neuchatel": 33
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amélie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Hardworking",
    "meaning_native": "Travailleuse",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 34,
    "popularity_change": "+1%",
    "year_2025_rank": 34,
    "year_2024_rank": 35,
    "trending_status": "stable",
    "search_volume": 1000,
    "regional_popularity": {
      "geneva": 34,
      "vaud": 34,
      "valais": 34,
      "neuchatel": 34
    }
  },
  {
    "name_en": "Chloe",
    "name_native": "Chloé",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Green shoot",
    "meaning_native": "Jeune pousse verte",
    "starting_letter": "C",
    "pronunciation": "klo-EH",
    "origin": "Greek",
    "popularity_rank": 35,
    "popularity_change": "+3%",
    "year_2025_rank": 35,
    "year_2024_rank": 38,
    "trending_status": "rising",
    "search_volume": 900,
    "regional_popularity": {
      "geneva": 35,
      "vaud": 35,
      "valais": 35,
      "neuchatel": 35
    }
  },
  {
    "name_en": "Oceane",
    "name_native": "Océane",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Ocean",
    "meaning_native": "Océan",
    "starting_letter": "O",
    "pronunciation": "oh-seh-AHN",
    "origin": "Greek",
    "popularity_rank": 36,
    "popularity_change": "+5%",
    "year_2025_rank": 36,
    "year_2024_rank": 41,
    "trending_status": "rising",
    "search_volume": 800,
    "regional_popularity": {
      "geneva": 36,
      "vaud": 36,
      "valais": 36,
      "neuchatel": 36
    }
  },
  {
    "name_en": "Leonie",
    "name_native": "Léonie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Lioness",
    "meaning_native": "Lionne",
    "starting_letter": "L",
    "pronunciation": "leh-oh-NEE",
    "origin": "Latin",
    "popularity_rank": 37,
    "popularity_change": "+2%",
    "year_2025_rank": 37,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 700,
    "regional_popularity": {
      "geneva": 37,
      "vaud": 37,
      "valais": 37,
      "neuchatel": 37
    }
  },
  {
    "name_en": "Pauline",
    "name_native": "Pauline",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Small",
    "meaning_native": "Petite",
    "starting_letter": "P",
    "pronunciation": "poh-LEEN",
    "origin": "Latin",
    "popularity_rank": 38,
    "popularity_change": "+1%",
    "year_2025_rank": 38,
    "year_2024_rank": 39,
    "trending_status": "stable",
    "search_volume": 600,
    "regional_popularity": {
      "geneva": 38,
      "vaud": 38,
      "valais": 38,
      "neuchatel": 38
    }
  },
  {
    "name_en": "Clemence",
    "name_native": "Clémence",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Mercy",
    "meaning_native": "Miséricorde",
    "starting_letter": "C",
    "pronunciation": "kleh-MAHNSS",
    "origin": "Latin",
    "popularity_rank": 39,
    "popularity_change": "+3%",
    "year_2025_rank": 39,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 500,
    "regional_popularity": {
      "geneva": 39,
      "vaud": 39,
      "valais": 39,
      "neuchatel": 39
    }
  },
  {
    "name_en": "Valentine",
    "name_native": "Valentine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Forte, en bonne santé",
    "starting_letter": "V",
    "pronunciation": "vah-lahn-TEEN",
    "origin": "Latin",
    "popularity_rank": 40,
    "popularity_change": "+4%",
    "year_2025_rank": 40,
    "year_2024_rank": 44,
    "trending_status": "rising",
    "search_volume": 400,
    "regional_popularity": {
      "geneva": 40,
      "vaud": 40,
      "valais": 40,
      "neuchatel": 40
    }
  },
  {
    "name_en": "Aurelie",
    "name_native": "Aurélie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Golden",
    "meaning_native": "Dorée",
    "starting_letter": "A",
    "pronunciation": "oh-reh-LEE",
    "origin": "Latin",
    "popularity_rank": 41,
    "popularity_change": "+2%",
    "year_2025_rank": 41,
    "year_2024_rank": 43,
    "trending_status": "rising",
    "search_volume": 380,
    "regional_popularity": {
      "geneva": 41,
      "vaud": 41,
      "valais": 41,
      "neuchatel": 41
    }
  },
  {
    "name_en": "Camille",
    "name_native": "Camille",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Perfect",
    "meaning_native": "Parfaite",
    "starting_letter": "C",
    "pronunciation": "kah-MEEL",
    "origin": "Latin",
    "popularity_rank": 42,
    "popularity_change": "+3%",
    "year_2025_rank": 42,
    "year_2024_rank": 45,
    "trending_status": "rising",
    "search_volume": 360,
    "regional_popularity": {
      "geneva": 42,
      "vaud": 42,
      "valais": 42,
      "neuchatel": 42
    }
  },
  {
    "name_en": "Delphine",
    "name_native": "Delphine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Dolphin",
    "meaning_native": "Dauphin",
    "starting_letter": "D",
    "pronunciation": "del-FEEN",
    "origin": "Greek",
    "popularity_rank": 43,
    "popularity_change": "+1%",
    "year_2025_rank": 43,
    "year_2024_rank": 44,
    "trending_status": "stable",
    "search_volume": 340,
    "regional_popularity": {
      "geneva": 43,
      "vaud": 43,
      "valais": 43,
      "neuchatel": 43
    }
  },
  {
    "name_en": "Eloise",
    "name_native": "Éloïse",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Healthy, wide",
    "meaning_native": "Saine, large",
    "starting_letter": "E",
    "pronunciation": "eh-loh-EEZ",
    "origin": "Germanic",
    "popularity_rank": 44,
    "popularity_change": "+4%",
    "year_2025_rank": 44,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 320,
    "regional_popularity": {
      "geneva": 44,
      "vaud": 44,
      "valais": 44,
      "neuchatel": 44
    }
  },
  {
    "name_en": "Flavie",
    "name_native": "Flavie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Blonde",
    "meaning_native": "Blonde",
    "starting_letter": "F",
    "pronunciation": "flah-VEE",
    "origin": "Latin",
    "popularity_rank": 45,
    "popularity_change": "+2%",
    "year_2025_rank": 45,
    "year_2024_rank": 47,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": {
      "geneva": 45,
      "vaud": 45,
      "valais": 45,
      "neuchatel": 45
    }
  },
  {
    "name_en": "Gabrielle",
    "name_native": "Gabrielle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is my strength",
    "meaning_native": "Dieu est ma force",
    "starting_letter": "G",
    "pronunciation": "gah-bree-EL",
    "origin": "Hebrew",
    "popularity_rank": 46,
    "popularity_change": "+1%",
    "year_2025_rank": 46,
    "year_2024_rank": 47,
    "trending_status": "stable",
    "search_volume": 280,
    "regional_popularity": {
      "geneva": 46,
      "vaud": 46,
      "valais": 46,
      "neuchatel": 46
    }
  },
  {
    "name_en": "Helene",
    "name_native": "Hélène",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bright light",
    "meaning_native": "Lumière brillante",
    "starting_letter": "H",
    "pronunciation": "eh-LEN",
    "origin": "Greek",
    "popularity_rank": 47,
    "popularity_change": "+3%",
    "year_2025_rank": 47,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 260,
    "regional_popularity": {
      "geneva": 47,
      "vaud": 47,
      "valais": 47,
      "neuchatel": 47
    }
  },
  {
    "name_en": "Isabelle",
    "name_native": "Isabelle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is my oath",
    "meaning_native": "Dieu est mon serment",
    "starting_letter": "I",
    "pronunciation": "ee-zah-BEL",
    "origin": "Hebrew",
    "popularity_rank": 48,
    "popularity_change": "+2%",
    "year_2025_rank": 48,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 240,
    "regional_popularity": {
      "geneva": 48,
      "vaud": 48,
      "valais": 48,
      "neuchatel": 48
    }
  },
  {
    "name_en": "Justine",
    "name_native": "Justine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Just, righteous",
    "meaning_native": "Juste, droite",
    "starting_letter": "J",
    "pronunciation": "zhuu-STEEN",
    "origin": "Latin",
    "popularity_rank": 49,
    "popularity_change": "+5%",
    "year_2025_rank": 49,
    "year_2024_rank": 54,
    "trending_status": "rising",
    "search_volume": 220,
    "regional_popularity": {
      "geneva": 49,
      "vaud": 49,
      "valais": 49,
      "neuchatel": 49
    }
  },
  {
    "name_en": "Karine",
    "name_native": "Karine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pure",
    "meaning_native": "Pure",
    "starting_letter": "K",
    "pronunciation": "kah-REEN",
    "origin": "Greek",
    "popularity_rank": 50,
    "popularity_change": "+1%",
    "year_2025_rank": 50,
    "year_2024_rank": 51,
    "trending_status": "stable",
    "search_volume": 200,
    "regional_popularity": {
      "geneva": 50,
      "vaud": 50,
      "valais": 50,
      "neuchatel": 50
    }
  },
  {
    "name_en": "Léna",
    "name_native": "Léna",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Light",
    "meaning_native": "Lumière",
    "starting_letter": "L",
    "pronunciation": "LAY-nah",
    "origin": "Greek",
    "popularity_rank": 12,
    "popularity_change": "+1%",
    "year_2025_rank": 12,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 4700,
    "regional_popularity": { "geneva": 12, "vaud": 12, "valais": 12, "neuchatel": 12 }
  },
  {
    "name_en": "Anaëlle",
    "name_native": "Anaëlle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Grace",
    "meaning_native": "Grâce",
    "starting_letter": "A",
    "pronunciation": "ah-nah-EL",
    "origin": "Hebrew",
    "popularity_rank": 13,
    "popularity_change": "+2%",
    "year_2025_rank": 13,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 4600,
    "regional_popularity": { "geneva": 13, "vaud": 13, "valais": 13, "neuchatel": 13 }
  },
  {
    "name_en": "Maëlle",
    "name_native": "Maëlle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Chief, princess",
    "meaning_native": "Cheffe, princesse",
    "starting_letter": "M",
    "pronunciation": "mah-EL",
    "origin": "Breton",
    "popularity_rank": 14,
    "popularity_change": "+1%",
    "year_2025_rank": 14,
    "year_2024_rank": 18,
    "trending_status": "rising",
    "search_volume": 4500,
    "regional_popularity": { "geneva": 14, "vaud": 14, "valais": 14, "neuchatel": 14 }
  },
  {
    "name_en": "Solène",
    "name_native": "Solène",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Solemn",
    "meaning_native": "Solennel",
    "starting_letter": "S",
    "pronunciation": "so-LEHN",
    "origin": "Latin",
    "popularity_rank": 15,
    "popularity_change": "+2%",
    "year_2025_rank": 15,
    "year_2024_rank": 19,
    "trending_status": "rising",
    "search_volume": 4400,
    "regional_popularity": { "geneva": 15, "vaud": 15, "valais": 15, "neuchatel": 15 }
  },
  {
    "name_en": "Élise",
    "name_native": "Élise",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Consecrated to God",
    "meaning_native": "Consacrée à Dieu",
    "starting_letter": "E",
    "pronunciation": "ay-LEEZ",
    "origin": "Hebrew",
    "popularity_rank": 16,
    "popularity_change": "+1%",
    "year_2025_rank": 16,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 4300,
    "regional_popularity": { "geneva": 16, "vaud": 16, "valais": 16, "neuchatel": 16 }
  },
  {
    "name_en": "Mélina",
    "name_native": "Mélina",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Honey",
    "meaning_native": "Miel",
    "starting_letter": "M",
    "pronunciation": "meh-LEE-nah",
    "origin": "Greek",
    "popularity_rank": 17,
    "popularity_change": "+2%",
    "year_2025_rank": 17,
    "year_2024_rank": 21,
    "trending_status": "rising",
    "search_volume": 4200,
    "regional_popularity": { "geneva": 17, "vaud": 17, "valais": 17, "neuchatel": 17 }
  },
  {
    "name_en": "Amandine",
    "name_native": "Amandine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Lovable",
    "meaning_native": "Aimable",
    "starting_letter": "A",
    "pronunciation": "ah-mahn-DEEN",
    "origin": "Latin",
    "popularity_rank": 18,
    "popularity_change": "+1%",
    "year_2025_rank": 18,
    "year_2024_rank": 22,
    "trending_status": "rising",
    "search_volume": 4100,
    "regional_popularity": { "geneva": 18, "vaud": 18, "valais": 18, "neuchatel": 18 }
  },
  {
    "name_en": "Louna",
    "name_native": "Louna",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Moon",
    "meaning_native": "Lune",
    "starting_letter": "L",
    "pronunciation": "LOO-nah",
    "origin": "Greek",
    "popularity_rank": 19,
    "popularity_change": "+2%",
    "year_2025_rank": 19,
    "year_2024_rank": 23,
    "trending_status": "rising",
    "search_volume": 4000,
    "regional_popularity": { "geneva": 19, "vaud": 19, "valais": 19, "neuchatel": 19 }
  },
  {
    "name_en": "Capucine",
    "name_native": "Capucine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Nasturtium (flower)",
    "meaning_native": "Capucine (fleur)",
    "starting_letter": "C",
    "pronunciation": "ka-pyu-SEEN",
    "origin": "Latin",
    "popularity_rank": 20,
    "popularity_change": "+1%",
    "year_2025_rank": 20,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 3900,
    "regional_popularity": { "geneva": 20, "vaud": 20, "valais": 20, "neuchatel": 20 }
  },
  {
    "name_en": "Léane",
    "name_native": "Léane",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Graceful",
    "meaning_native": "Gracieuse",
    "starting_letter": "L",
    "pronunciation": "LAY-ahn",
    "origin": "Hebrew",
    "popularity_rank": 21,
    "popularity_change": "+2%",
    "year_2025_rank": 21,
    "year_2024_rank": 25,
    "trending_status": "rising",
    "search_volume": 3800,
    "regional_popularity": { "geneva": 21, "vaud": 21, "valais": 21, "neuchatel": 21 }
  },
  {
    "name_en": "Ambre",
    "name_native": "Ambre",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Amber",
    "meaning_native": "Ambre",
    "starting_letter": "A",
    "pronunciation": "AHM-bruh",
    "origin": "Arabic",
    "popularity_rank": 22,
    "popularity_change": "+1%",
    "year_2025_rank": 22,
    "year_2024_rank": 26,
    "trending_status": "rising",
    "search_volume": 3700,
    "regional_popularity": { "geneva": 22, "vaud": 22, "valais": 22, "neuchatel": 22 }
  },
  {
    "name_en": "Inès",
    "name_native": "Inès",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Chaste, pure",
    "meaning_native": "Chaste, pure",
    "starting_letter": "I",
    "pronunciation": "ee-NESS",
    "origin": "Greek",
    "popularity_rank": 23,
    "popularity_change": "+2%",
    "year_2025_rank": 23,
    "year_2024_rank": 27,
    "trending_status": "rising",
    "search_volume": 3600,
    "regional_popularity": { "geneva": 23, "vaud": 23, "valais": 23, "neuchatel": 23 }
  },
  {
    "name_en": "Anaïs",
    "name_native": "Anaïs",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Grace",
    "meaning_native": "Grâce",
    "starting_letter": "A",
    "pronunciation": "ah-nah-EES",
    "origin": "Hebrew",
    "popularity_rank": 24,
    "popularity_change": "+1%",
    "year_2025_rank": 24,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 3500,
    "regional_popularity": { "geneva": 24, "vaud": 24, "valais": 24, "neuchatel": 24 }
  },
  {
    "name_en": "Salomé",
    "name_native": "Salomé",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Peace",
    "meaning_native": "Paix",
    "starting_letter": "S",
    "pronunciation": "sa-lo-MAY",
    "origin": "Hebrew",
    "popularity_rank": 25,
    "popularity_change": "+2%",
    "year_2025_rank": 25,
    "year_2024_rank": 29,
    "trending_status": "rising",
    "search_volume": 3400,
    "regional_popularity": { "geneva": 25, "vaud": 25, "valais": 25, "neuchatel": 25 }
  },
  {
    "name_en": "Livia",
    "name_native": "Livia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Blue, envious",
    "meaning_native": "Bleue, envieuse",
    "starting_letter": "L",
    "pronunciation": "LEE-vee-ah",
    "origin": "Latin",
    "popularity_rank": 27,
    "popularity_change": "+2%",
    "year_2025_rank": 27,
    "year_2024_rank": 31,
    "trending_status": "rising",
    "search_volume": 3200,
    "regional_popularity": { "geneva": 27, "vaud": 27, "valais": 27, "neuchatel": 27 }
  },
  {
    "name_en": "Noémie",
    "name_native": "Noémie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pleasantness",
    "meaning_native": "Agréable",
    "starting_letter": "N",
    "pronunciation": "no-eh-MEE",
    "origin": "Hebrew",
    "popularity_rank": 28,
    "popularity_change": "+1%",
    "year_2025_rank": 28,
    "year_2024_rank": 32,
    "trending_status": "rising",
    "search_volume": 3100,
    "regional_popularity": { "geneva": 28, "vaud": 28, "valais": 28, "neuchatel": 28 }
  },
  {
    "name_en": "Soline",
    "name_native": "Soline",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Sunshine",
    "meaning_native": "Rayon de soleil",
    "starting_letter": "S",
    "pronunciation": "so-LEEN",
    "origin": "Latin",
    "popularity_rank": 29,
    "popularity_change": "+2%",
    "year_2025_rank": 29,
    "year_2024_rank": 33,
    "trending_status": "rising",
    "search_volume": 3000,
    "regional_popularity": { "geneva": 29, "vaud": 29, "valais": 29, "neuchatel": 29 }
  },
  {
    "name_en": "Maëlys",
    "name_native": "Maëlys",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Chief, princess",
    "meaning_native": "Cheffe, princesse",
    "starting_letter": "M",
    "pronunciation": "mah-EL-iss",
    "origin": "Breton",
    "popularity_rank": 30,
    "popularity_change": "+1%",
    "year_2025_rank": 30,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 2900,
    "regional_popularity": { "geneva": 30, "vaud": 30, "valais": 30, "neuchatel": 30 }
  },
  {
    "name_en": "Thaïs",
    "name_native": "Thaïs",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Beloved",
    "meaning_native": "Bien-aimée",
    "starting_letter": "T",
    "pronunciation": "ta-EES",
    "origin": "Greek",
    "popularity_rank": 31,
    "popularity_change": "+2%",
    "year_2025_rank": 31,
    "year_2024_rank": 35,
    "trending_status": "rising",
    "search_volume": 2800,
    "regional_popularity": { "geneva": 31, "vaud": 31, "valais": 31, "neuchatel": 31 }
  },
  {
    "name_en": "Mila",
    "name_native": "Mila",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Gracious, dear",
    "meaning_native": "Gracieuse, chère",
    "starting_letter": "M",
    "pronunciation": "MEE-lah",
    "origin": "Slavic",
    "popularity_rank": 32,
    "popularity_change": "+1%",
    "year_2025_rank": 32,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 2700,
    "regional_popularity": { "geneva": 32, "vaud": 32, "valais": 32, "neuchatel": 32 }
  },
  {
    "name_en": "Océane",
    "name_native": "Océane",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Ocean",
    "meaning_native": "Océan",
    "starting_letter": "O",
    "pronunciation": "oh-SAY-ahn",
    "origin": "Greek",
    "popularity_rank": 33,
    "popularity_change": "+2%",
    "year_2025_rank": 33,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 2600,
    "regional_popularity": { "geneva": 33, "vaud": 33, "valais": 33, "neuchatel": 33 }
  },
  {
    "name_en": "Céleste",
    "name_native": "Céleste",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Heavenly",
    "meaning_native": "Céleste",
    "starting_letter": "C",
    "pronunciation": "say-LEST",
    "origin": "Latin",
    "popularity_rank": 34,
    "popularity_change": "+3%",
    "year_2025_rank": 34,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 2500,
    "regional_popularity": { "geneva": 34, "vaud": 34, "valais": 34, "neuchatel": 34 }
  },
  {
    "name_en": "Adèle",
    "name_native": "Adèle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Noble",
    "meaning_native": "Noble",
    "starting_letter": "A",
    "pronunciation": "ah-DELL",
    "origin": "Germanic",
    "popularity_rank": 35,
    "popularity_change": "+2%",
    "year_2025_rank": 35,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 2400,
    "regional_popularity": { "geneva": 35, "vaud": 35, "valais": 35, "neuchatel": 35 }
  },
  {
    "name_en": "Capucine",
    "name_native": "Capucine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Nasturtium flower",
    "meaning_native": "Fleur de capucine",
    "starting_letter": "C",
    "pronunciation": "ka-poo-SEEN",
    "origin": "Latin",
    "popularity_rank": 36,
    "popularity_change": "+4%",
    "year_2025_rank": 36,
    "year_2024_rank": 40,
    "trending_status": "rising",
    "search_volume": 2300,
    "regional_popularity": { "geneva": 36, "vaud": 36, "valais": 36, "neuchatel": 36 }
  },
  {
    "name_en": "Constance",
    "name_native": "Constance",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Steadfast",
    "meaning_native": "Constante",
    "starting_letter": "C",
    "pronunciation": "kon-STAHNSS",
    "origin": "Latin",
    "popularity_rank": 37,
    "popularity_change": "+1%",
    "year_2025_rank": 37,
    "year_2024_rank": 38,
    "trending_status": "stable",
    "search_volume": 2200,
    "regional_popularity": { "geneva": 37, "vaud": 37, "valais": 37, "neuchatel": 37 }
  },
  {
    "name_en": "Margot",
    "name_native": "Margot",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "M",
    "pronunciation": "mar-GOH",
    "origin": "Greek",
    "popularity_rank": 38,
    "popularity_change": "+3%",
    "year_2025_rank": 38,
    "year_2024_rank": 41,
    "trending_status": "rising",
    "search_volume": 2100,
    "regional_popularity": { "geneva": 38, "vaud": 38, "valais": 38, "neuchatel": 38 }
  },
  {
    "name_en": "Héloïse",
    "name_native": "Héloïse",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Healthy, wide",
    "meaning_native": "Saine, large",
    "starting_letter": "H",
    "pronunciation": "ay-lo-EEZ",
    "origin": "Germanic",
    "popularity_rank": 39,
    "popularity_change": "+2%",
    "year_2025_rank": 39,
    "year_2024_rank": 41,
    "trending_status": "rising",
    "search_volume": 2000,
    "regional_popularity": { "geneva": 39, "vaud": 39, "valais": 39, "neuchatel": 39 }
  },
  {
    "name_en": "Inès",
    "name_native": "Inès",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pure, holy",
    "meaning_native": "Pure, sainte",
    "starting_letter": "I",
    "pronunciation": "ee-NESS",
    "origin": "Greek",
    "popularity_rank": 40,
    "popularity_change": "+3%",
    "year_2025_rank": 40,
    "year_2024_rank": 43,
    "trending_status": "rising",
    "search_volume": 1900,
    "regional_popularity": { "geneva": 40, "vaud": 40, "valais": 40, "neuchatel": 40 }
  },
  {
    "name_en": "Jade",
    "name_native": "Jade",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Jade stone",
    "meaning_native": "Pierre de jade",
    "starting_letter": "J",
    "pronunciation": "ZHAD",
    "origin": "Spanish",
    "popularity_rank": 41,
    "popularity_change": "+1%",
    "year_2025_rank": 41,
    "year_2024_rank": 42,
    "trending_status": "stable",
    "search_volume": 1800,
    "regional_popularity": { "geneva": 41, "vaud": 41, "valais": 41, "neuchatel": 41 }
  },
  {
    "name_en": "Kenza",
    "name_native": "Kenza",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Treasure",
    "meaning_native": "Trésor",
    "starting_letter": "K",
    "pronunciation": "ken-ZAH",
    "origin": "Arabic",
    "popularity_rank": 42,
    "popularity_change": "+4%",
    "year_2025_rank": 42,
    "year_2024_rank": 46,
    "trending_status": "rising",
    "search_volume": 1700,
    "regional_popularity": { "geneva": 42, "vaud": 42, "valais": 42, "neuchatel": 42 }
  },
  {
    "name_en": "Léa",
    "name_native": "Léa",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Weary",
    "meaning_native": "Fatiguée",
    "starting_letter": "L",
    "pronunciation": "LAY-ah",
    "origin": "Hebrew",
    "popularity_rank": 43,
    "popularity_change": "+2%",
    "year_2025_rank": 43,
    "year_2024_rank": 45,
    "trending_status": "rising",
    "search_volume": 1600,
    "regional_popularity": { "geneva": 43, "vaud": 43, "valais": 43, "neuchatel": 43 }
  },
  {
    "name_en": "Manon",
    "name_native": "Manon",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bitter, beloved",
    "meaning_native": "Amère, bien-aimée",
    "starting_letter": "M",
    "pronunciation": "mah-NON",
    "origin": "Hebrew",
    "popularity_rank": 44,
    "popularity_change": "+3%",
    "year_2025_rank": 44,
    "year_2024_rank": 47,
    "trending_status": "rising",
    "search_volume": 1500,
    "regional_popularity": { "geneva": 44, "vaud": 44, "valais": 44, "neuchatel": 44 }
  },
  {
    "name_en": "Nina",
    "name_native": "Nina",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Little girl",
    "meaning_native": "Petite fille",
    "starting_letter": "N",
    "pronunciation": "NEE-nah",
    "origin": "Spanish",
    "popularity_rank": 45,
    "popularity_change": "+1%",
    "year_2025_rank": 45,
    "year_2024_rank": 46,
    "trending_status": "stable",
    "search_volume": 1400,
    "regional_popularity": { "geneva": 45, "vaud": 45, "valais": 45, "neuchatel": 45 }
  },
  {
    "name_en": "Olivia",
    "name_native": "Olivia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Olive tree",
    "meaning_native": "Olivier",
    "starting_letter": "O",
    "pronunciation": "oh-lee-VEE-ah",
    "origin": "Latin",
    "popularity_rank": 46,
    "popularity_change": "+2%",
    "year_2025_rank": 46,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 1300,
    "regional_popularity": { "geneva": 46, "vaud": 46, "valais": 46, "neuchatel": 46 }
  },
  {
    "name_en": "Pauline",
    "name_native": "Pauline",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Small, humble",
    "meaning_native": "Petite, humble",
    "starting_letter": "P",
    "pronunciation": "poh-LEEN",
    "origin": "Latin",
    "popularity_rank": 47,
    "popularity_change": "+3%",
    "year_2025_rank": 47,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 1200,
    "regional_popularity": { "geneva": 47, "vaud": 47, "valais": 47, "neuchatel": 47 }
  },
  {
    "name_en": "Quitterie",
    "name_native": "Quitterie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Peace",
    "meaning_native": "Paix",
    "starting_letter": "Q",
    "pronunciation": "kee-teh-REE",
    "origin": "Latin",
    "popularity_rank": 48,
    "popularity_change": "+4%",
    "year_2025_rank": 48,
    "year_2024_rank": 52,
    "trending_status": "rising",
    "search_volume": 1100,
    "regional_popularity": { "geneva": 48, "vaud": 48, "valais": 48, "neuchatel": 48 }
  },
  {
    "name_en": "Romane",
    "name_native": "Romane",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Roman",
    "meaning_native": "Romaine",
    "starting_letter": "R",
    "pronunciation": "roh-MAHN",
    "origin": "Latin",
    "popularity_rank": 49,
    "popularity_change": "+2%",
    "year_2025_rank": 49,
    "year_2024_rank": 51,
    "trending_status": "rising",
    "search_volume": 1000,
    "regional_popularity": { "geneva": 49, "vaud": 49, "valais": 49, "neuchatel": 49 }
  },
  {
    "name_en": "Sofia",
    "name_native": "Sofia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Wisdom",
    "meaning_native": "Sagesse",
    "starting_letter": "S",
    "pronunciation": "so-FEE-ah",
    "origin": "Greek",
    "popularity_rank": 50,
    "popularity_change": "+1%",
    "year_2025_rank": 50,
    "year_2024_rank": 51,
    "trending_status": "stable",
    "search_volume": 900,
    "regional_popularity": { "geneva": 50, "vaud": 50, "valais": 50, "neuchatel": 50 }
  },
  {
    "name_en": "Théa",
    "name_native": "Théa",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Goddess",
    "meaning_native": "Déesse",
    "starting_letter": "T",
    "pronunciation": "tay-AH",
    "origin": "Greek",
    "popularity_rank": 51,
    "popularity_change": "+3%",
    "year_2025_rank": 51,
    "year_2024_rank": 54,
    "trending_status": "rising",
    "search_volume": 800,
    "regional_popularity": { "geneva": 51, "vaud": 51, "valais": 51, "neuchatel": 51 }
  },
  {
    "name_en": "Uma",
    "name_native": "Uma",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Flax",
    "meaning_native": "Lin",
    "starting_letter": "U",
    "pronunciation": "OO-mah",
    "origin": "Sanskrit",
    "popularity_rank": 52,
    "popularity_change": "+4%",
    "year_2025_rank": 52,
    "year_2024_rank": 56,
    "trending_status": "rising",
    "search_volume": 700,
    "regional_popularity": { "geneva": 52, "vaud": 52, "valais": 52, "neuchatel": 52 }
  },
  {
    "name_en": "Valentine",
    "name_native": "Valentine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Forte, saine",
    "starting_letter": "V",
    "pronunciation": "vah-len-TEEN",
    "origin": "Latin",
    "popularity_rank": 53,
    "popularity_change": "+2%",
    "year_2025_rank": 53,
    "year_2024_rank": 55,
    "trending_status": "rising",
    "search_volume": 600,
    "regional_popularity": { "geneva": 53, "vaud": 53, "valais": 53, "neuchatel": 53 }
  },
  {
    "name_en": "Wendy",
    "name_native": "Wendy",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "White, blessed",
    "meaning_native": "Blanche, bénie",
    "starting_letter": "W",
    "pronunciation": "wen-DEE",
    "origin": "English",
    "popularity_rank": 54,
    "popularity_change": "+3%",
    "year_2025_rank": 54,
    "year_2024_rank": 57,
    "trending_status": "rising",
    "search_volume": 500,
    "regional_popularity": { "geneva": 54, "vaud": 54, "valais": 54, "neuchatel": 54 }
  },
  {
    "name_en": "Xavière",
    "name_native": "Xavière",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "New house",
    "meaning_native": "Nouvelle maison",
    "starting_letter": "X",
    "pronunciation": "zah-vee-YEHR",
    "origin": "Basque",
    "popularity_rank": 55,
    "popularity_change": "+4%",
    "year_2025_rank": 55,
    "year_2024_rank": 59,
    "trending_status": "rising",
    "search_volume": 400,
    "regional_popularity": { "geneva": 55, "vaud": 55, "valais": 55, "neuchatel": 55 }
  },
  {
    "name_en": "Yasmine",
    "name_native": "Yasmine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Jasmine flower",
    "meaning_native": "Fleur de jasmin",
    "starting_letter": "Y",
    "pronunciation": "yahs-MEEN",
    "origin": "Persian",
    "popularity_rank": 56,
    "popularity_change": "+2%",
    "year_2025_rank": 56,
    "year_2024_rank": 58,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": { "geneva": 56, "vaud": 56, "valais": 56, "neuchatel": 56 }
  },
  {
    "name_en": "Zoé",
    "name_native": "Zoé",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Life",
    "meaning_native": "Vie",
    "starting_letter": "Z",
    "pronunciation": "zoh-EH",
    "origin": "Greek",
    "popularity_rank": 57,
    "popularity_change": "+1%",
    "year_2025_rank": 57,
    "year_2024_rank": 58,
    "trending_status": "stable",
    "search_volume": 200,
    "regional_popularity": { "geneva": 57, "vaud": 57, "valais": 57, "neuchatel": 57 }
  },
  {
    "name_en": "Aurélie",
    "name_native": "Aurélie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Golden",
    "meaning_native": "Dorée",
    "starting_letter": "A",
    "pronunciation": "oh-ray-LEE",
    "origin": "Latin",
    "popularity_rank": 58,
    "popularity_change": "+3%",
    "year_2025_rank": 58,
    "year_2024_rank": 61,
    "trending_status": "rising",
    "search_volume": 150,
    "regional_popularity": { "geneva": 58, "vaud": 58, "valais": 58, "neuchatel": 58 }
  },
  {
    "name_en": "Bérénice",
    "name_native": "Bérénice",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bringer of victory",
    "meaning_native": "Porteuse de victoire",
    "starting_letter": "B",
    "pronunciation": "bay-ray-NEESS",
    "origin": "Greek",
    "popularity_rank": 59,
    "popularity_change": "+2%",
    "year_2025_rank": 59,
    "year_2024_rank": 61,
    "trending_status": "rising",
    "search_volume": 100,
    "regional_popularity": { "geneva": 59, "vaud": 59, "valais": 59, "neuchatel": 59 }
  },
  {
    "name_en": "Clémence",
    "name_native": "Clémence",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Mercy",
    "meaning_native": "Clémence",
    "starting_letter": "C",
    "pronunciation": "klay-MAHNSS",
    "origin": "Latin",
    "popularity_rank": 60,
    "popularity_change": "+4%",
    "year_2025_rank": 60,
    "year_2024_rank": 64,
    "trending_status": "rising",
    "search_volume": 80,
    "regional_popularity": { "geneva": 60, "vaud": 60, "valais": 60, "neuchatel": 60 }
  },
  {
    "name_en": "Delphine",
    "name_native": "Delphine",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Dolphin",
    "meaning_native": "Dauphin",
    "starting_letter": "D",
    "pronunciation": "del-FEEN",
    "origin": "Greek",
    "popularity_rank": 61,
    "popularity_change": "+1%",
    "year_2025_rank": 61,
    "year_2024_rank": 62,
    "trending_status": "stable",
    "search_volume": 60,
    "regional_popularity": { "geneva": 61, "vaud": 61, "valais": 61, "neuchatel": 61 }
  },
  {
    "name_en": "Élodie",
    "name_native": "Élodie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Foreign riches",
    "meaning_native": "Richesses étrangères",
    "starting_letter": "É",
    "pronunciation": "ay-lo-DEE",
    "origin": "Germanic",
    "popularity_rank": 62,
    "popularity_change": "+3%",
    "year_2025_rank": 62,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 40,
    "regional_popularity": { "geneva": 62, "vaud": 62, "valais": 62, "neuchatel": 62 }
  },
  {
    "name_en": "Florence",
    "name_native": "Florence",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Flourishing",
    "meaning_native": "Florissante",
    "starting_letter": "F",
    "pronunciation": "flo-RAHNSS",
    "origin": "Latin",
    "popularity_rank": 63,
    "popularity_change": "+2%",
    "year_2025_rank": 63,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 20,
    "regional_popularity": { "geneva": 63, "vaud": 63, "valais": 63, "neuchatel": 63 }
  },
  {
    "name_en": "Gabrielle",
    "name_native": "Gabrielle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is my strength",
    "meaning_native": "Dieu est ma force",
    "starting_letter": "G",
    "pronunciation": "gah-bree-ELL",
    "origin": "Hebrew",
    "popularity_rank": 64,
    "popularity_change": "+1%",
    "year_2025_rank": 64,
    "year_2024_rank": 65,
    "trending_status": "stable",
    "search_volume": 10,
    "regional_popularity": { "geneva": 64, "vaud": 64, "valais": 64, "neuchatel": 64 }
  },
  {
    "name_en": "Bella",
    "name_native": "Bella",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Beautiful",
    "meaning_native": "Belle",
    "starting_letter": "B",
    "pronunciation": "bel-LAH",
    "origin": "Italian",
    "popularity_rank": 103,
    "popularity_change": "+3%",
    "year_2025_rank": 103,
    "year_2024_rank": 106,
    "trending_status": "rising",
    "search_volume": 420,
    "regional_popularity": {
      "geneva": 103,
      "vaud": 103,
      "valais": 103,
      "neuchatel": 103
    }
  },
  {
    "name_en": "Celine",
    "name_native": "Céline",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Heavenly",
    "meaning_native": "Céleste",
    "starting_letter": "C",
    "pronunciation": "say-LEEN",
    "origin": "Latin",
    "popularity_rank": 104,
    "popularity_change": "+5%",
    "year_2025_rank": 104,
    "year_2024_rank": 109,
    "trending_status": "rising",
    "search_volume": 380,
    "regional_popularity": {
      "geneva": 104,
      "vaud": 104,
      "valais": 104,
      "neuchatel": 104
    }
  },
  {
    "name_en": "Diana",
    "name_native": "Diana",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Divine",
    "meaning_native": "Divine",
    "starting_letter": "D",
    "pronunciation": "dee-AH-nah",
    "origin": "Latin",
    "popularity_rank": 105,
    "popularity_change": "+2%",
    "year_2025_rank": 105,
    "year_2024_rank": 107,
    "trending_status": "stable",
    "search_volume": 340,
    "regional_popularity": {
      "geneva": 105,
      "vaud": 105,
      "valais": 105,
      "neuchatel": 105
    }
  },
  {
    "name_en": "Eva",
    "name_native": "Eva",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Life",
    "meaning_native": "Vie",
    "starting_letter": "E",
    "pronunciation": "ay-VAH",
    "origin": "Hebrew",
    "popularity_rank": 106,
    "popularity_change": "+4%",
    "year_2025_rank": 106,
    "year_2024_rank": 110,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": {
      "geneva": 106,
      "vaud": 106,
      "valais": 106,
      "neuchatel": 106
    }
  },
  {
    "name_en": "Fiona",
    "name_native": "Fiona",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Fair, white",
    "meaning_native": "Blonde, blanche",
    "starting_letter": "F",
    "pronunciation": "fee-OH-nah",
    "origin": "Celtic",
    "popularity_rank": 107,
    "popularity_change": "+6%",
    "year_2025_rank": 107,
    "year_2024_rank": 113,
    "trending_status": "rising",
    "search_volume": 260,
    "regional_popularity": {
      "geneva": 107,
      "vaud": 107,
      "valais": 107,
      "neuchatel": 107
    }
  },
  {
    "name_en": "Greta",
    "name_native": "Greta",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "G",
    "pronunciation": "greh-TAH",
    "origin": "Greek",
    "popularity_rank": 108,
    "popularity_change": "+3%",
    "year_2025_rank": 108,
    "year_2024_rank": 111,
    "trending_status": "rising",
    "search_volume": 220,
    "regional_popularity": {
      "geneva": 108,
      "vaud": 108,
      "valais": 108,
      "neuchatel": 108
    }
  },
  {
    "name_en": "Hanna",
    "name_native": "Hanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Grace",
    "meaning_native": "Grâce",
    "starting_letter": "H",
    "pronunciation": "han-NAH",
    "origin": "Hebrew",
    "popularity_rank": 109,
    "popularity_change": "+5%",
    "year_2025_rank": 109,
    "year_2024_rank": 114,
    "trending_status": "rising",
    "search_volume": 180,
    "regional_popularity": {
      "geneva": 109,
      "vaud": 109,
      "valais": 109,
      "neuchatel": 109
    }
  },
  {
    "name_en": "Iris",
    "name_native": "Iris",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Rainbow",
    "meaning_native": "Arc-en-ciel",
    "starting_letter": "I",
    "pronunciation": "ee-REES",
    "origin": "Greek",
    "popularity_rank": 110,
    "popularity_change": "+2%",
    "year_2025_rank": 110,
    "year_2024_rank": 112,
    "trending_status": "stable",
    "search_volume": 140,
    "regional_popularity": {
      "geneva": 110,
      "vaud": 110,
      "valais": 110,
      "neuchatel": 110
    }
  },
  {
    "name_en": "Jasmin",
    "name_native": "Jasmin",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Jasmine flower",
    "meaning_native": "Fleur de jasmin",
    "starting_letter": "J",
    "pronunciation": "zhas-MEEN",
    "origin": "Persian",
    "popularity_rank": 111,
    "popularity_change": "+4%",
    "year_2025_rank": 111,
    "year_2024_rank": 115,
    "trending_status": "rising",
    "search_volume": 100,
    "regional_popularity": {
      "geneva": 111,
      "vaud": 111,
      "valais": 111,
      "neuchatel": 111
    }
  },
  {
    "name_en": "Kira",
    "name_native": "Kira",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Lady, ruler",
    "meaning_native": "Dame, souveraine",
    "starting_letter": "K",
    "pronunciation": "kee-RAH",
    "origin": "Persian",
    "popularity_rank": 112,
    "popularity_change": "+1%",
    "year_2025_rank": 112,
    "year_2024_rank": 113,
    "trending_status": "stable",
    "search_volume": 60,
    "regional_popularity": {
      "geneva": 112,
      "vaud": 112,
      "valais": 112,
      "neuchatel": 112
    }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Protection",
    "meaning_native": "Protection",
    "starting_letter": "L",
    "pronunciation": "lah-RAH",
    "origin": "Latin",
    "popularity_rank": 113,
    "popularity_change": "+3%",
    "year_2025_rank": 113,
    "year_2024_rank": 116,
    "trending_status": "rising",
    "search_volume": 20,
    "regional_popularity": {
      "geneva": 113,
      "vaud": 113,
      "valais": 113,
      "neuchatel": 113
    }
  },
  {
    "name_en": "Maya",
    "name_native": "Maya",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Water",
    "meaning_native": "Eau",
    "starting_letter": "M",
    "pronunciation": "mah-YAH",
    "origin": "Hebrew",
    "popularity_rank": 114,
    "popularity_change": "+5%",
    "year_2025_rank": 114,
    "year_2024_rank": 119,
    "trending_status": "rising",
    "search_volume": 15,
    "regional_popularity": {
      "geneva": 114,
      "vaud": 114,
      "valais": 114,
      "neuchatel": 114
    }
  },
  {
    "name_en": "Nina",
    "name_native": "Nina",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Little girl",
    "meaning_native": "Petite fille",
    "starting_letter": "N",
    "pronunciation": "nee-NAH",
    "origin": "Spanish",
    "popularity_rank": 115,
    "popularity_change": "+2%",
    "year_2025_rank": 115,
    "year_2024_rank": 117,
    "trending_status": "stable",
    "search_volume": 10,
    "regional_popularity": {
      "geneva": 115,
      "vaud": 115,
      "valais": 115,
      "neuchatel": 115
    }
  },
  {
    "name_en": "Olivia",
    "name_native": "Olivia",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Olive tree",
    "meaning_native": "Olivier",
    "starting_letter": "O",
    "pronunciation": "oh-lee-VEE-ah",
    "origin": "Latin",
    "popularity_rank": 116,
    "popularity_change": "+4%",
    "year_2025_rank": 116,
    "year_2024_rank": 120,
    "trending_status": "rising",
    "search_volume": 5,
    "regional_popularity": {
      "geneva": 116,
      "vaud": 116,
      "valais": 116,
      "neuchatel": 116
    }
  }
]

export default SwissFrenchGirlNames;
