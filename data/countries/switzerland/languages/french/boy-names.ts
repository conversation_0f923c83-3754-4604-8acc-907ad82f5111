import type { NameData } from "@/types/name-data";

export const SwissFrenchBoyNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is my strength",
    "meaning_native": "Dieu est ma force",
    "starting_letter": "G",
    "pronunciation": "gah-bree-EL",
    "origin": "Hebrew",
    "popularity_rank": 1,
    "popularity_change": "+1%",
    "year_2025_rank": 1,
    "year_2024_rank": 2,
    "trending_status": "stable",
    "search_volume": 8400,
    "regional_popularity": {
      "geneva": 1,
      "vaud": 2,
      "valais": 1,
      "neuchatel": 2
    }
  },
  {
    "name_en": "Arthur",
    "name_native": "Arthur",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bear, strong",
    "meaning_native": "Ours, fort",
    "starting_letter": "A",
    "pronunciation": "ar-TUUR",
    "origin": "Celtic",
    "popularity_rank": 2,
    "popularity_change": "+2%",
    "year_2025_rank": 2,
    "year_2024_rank": 4,
    "trending_status": "rising",
    "search_volume": 7600,
    "regional_popularity": {
      "geneva": 2,
      "vaud": 1,
      "valais": 2,
      "neuchatel": 1
    }
  },
  {
    "name_en": "Leo",
    "name_native": "Léo",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Lion",
    "meaning_native": "Lion",
    "starting_letter": "L",
    "pronunciation": "LAY-oh",
    "origin": "Latin",
    "popularity_rank": 17,
    "popularity_change": "+2%",
    "year_2025_rank": 17,
    "year_2024_rank": 21,
    "trending_status": "rising",
    "search_volume": 7200,
    "regional_popularity": {
      "geneva": 17,
      "vaud": 17,
      "valais": 17,
      "neuchatel": 17
    }
  },
  {
    "name_en": "Louis",
    "name_native": "Louis",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Famous warrior",
    "meaning_native": "Guerrier célèbre",
    "starting_letter": "L",
    "pronunciation": "loo-EE",
    "origin": "Germanic",
    "popularity_rank": 4,
    "popularity_change": "+3%",
    "year_2025_rank": 4,
    "year_2024_rank": 7,
    "trending_status": "rising",
    "search_volume": 6200,
    "regional_popularity": {
      "geneva": 4,
      "vaud": 4,
      "valais": 4,
      "neuchatel": 4
    }
  },
  {
    "name_en": "Hugo",
    "name_native": "Hugo",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Mind, intellect",
    "meaning_native": "Esprit, intellect",
    "starting_letter": "H",
    "pronunciation": "UU-go",
    "origin": "Germanic",
    "popularity_rank": 5,
    "popularity_change": "+1%",
    "year_2025_rank": 5,
    "year_2024_rank": 6,
    "trending_status": "stable",
    "search_volume": 5400,
    "regional_popularity": {
      "geneva": 5,
      "vaud": 5,
      "valais": 5,
      "neuchatel": 5
    }
  },
  {
    "name_en": "Noah",
    "name_native": "Noah",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Rest, comfort",
    "meaning_native": "Repos, réconfort",
    "starting_letter": "N",
    "pronunciation": "no-AH",
    "origin": "Hebrew",
    "popularity_rank": 6,
    "popularity_change": "+4%",
    "year_2025_rank": 6,
    "year_2024_rank": 10,
    "trending_status": "rising",
    "search_volume": 5100,
    "regional_popularity": {
      "geneva": 6,
      "vaud": 6,
      "valais": 6,
      "neuchatel": 6
    }
  },
  {
    "name_en": "Liam",
    "name_native": "Liam",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Strong-willed warrior",
    "meaning_native": "Guerrier déterminé",
    "starting_letter": "L",
    "pronunciation": "LEE-am",
    "origin": "Irish",
    "popularity_rank": 7,
    "popularity_change": "+2%",
    "year_2025_rank": 7,
    "year_2024_rank": 9,
    "trending_status": "rising",
    "search_volume": 4800,
    "regional_popularity": {
      "geneva": 7,
      "vaud": 7,
      "valais": 7,
      "neuchatel": 7
    }
  },
  {
    "name_en": "Ethan",
    "name_native": "Ethan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Firm, strong",
    "meaning_native": "Ferme, fort",
    "starting_letter": "E",
    "pronunciation": "eh-TAHN",
    "origin": "Hebrew",
    "popularity_rank": 8,
    "popularity_change": "+1%",
    "year_2025_rank": 8,
    "year_2024_rank": 9,
    "trending_status": "stable",
    "search_volume": 4500,
    "regional_popularity": {
      "geneva": 8,
      "vaud": 8,
      "valais": 8,
      "neuchatel": 8
    }
  },
  {
    "name_en": "Nathan",
    "name_native": "Nathan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "He gave",
    "meaning_native": "Il a donné",
    "starting_letter": "N",
    "pronunciation": "nah-TAHN",
    "origin": "Hebrew",
    "popularity_rank": 9,
    "popularity_change": "+1%",
    "year_2025_rank": 9,
    "year_2024_rank": 12,
    "trending_status": "rising",
    "search_volume": 8000,
    "regional_popularity": {
      "geneva": 9,
      "vaud": 9,
      "valais": 9,
      "neuchatel": 9
    }
  },
  {
    "name_en": "Lucas",
    "name_native": "Lucas",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Light-giving",
    "meaning_native": "Porteur de lumière",
    "starting_letter": "L",
    "pronunciation": "lü-KAH",
    "origin": "Greek",
    "popularity_rank": 10,
    "popularity_change": "+1%",
    "year_2025_rank": 10,
    "year_2024_rank": 11,
    "trending_status": "stable",
    "search_volume": 3900,
    "regional_popularity": {
      "geneva": 10,
      "vaud": 10,
      "valais": 10,
      "neuchatel": 10
    }
  },
  {
    "name_en": "Adam",
    "name_native": "Adam",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Man, earth",
    "meaning_native": "Homme, terre",
    "starting_letter": "A",
    "pronunciation": "ah-DAHN",
    "origin": "Hebrew",
    "popularity_rank": 11,
    "popularity_change": "+2%",
    "year_2025_rank": 11,
    "year_2024_rank": 13,
    "trending_status": "rising",
    "search_volume": 3700,
    "regional_popularity": {
      "geneva": 11,
      "vaud": 11,
      "valais": 11,
      "neuchatel": 11
    }
  },
  {
    "name_en": "Theo",
    "name_native": "Théo",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God's gift",
    "meaning_native": "Don de Dieu",
    "starting_letter": "T",
    "pronunciation": "teh-OH",
    "origin": "Greek",
    "popularity_rank": 12,
    "popularity_change": "+4%",
    "year_2025_rank": 12,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 7700,
    "regional_popularity": {
      "geneva": 12,
      "vaud": 12,
      "valais": 12,
      "neuchatel": 12
    }
  },
  {
    "name_en": "Maxime",
    "name_native": "Maxime",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Greatest",
    "meaning_native": "Le plus grand",
    "starting_letter": "M",
    "pronunciation": "mak-SEEM",
    "origin": "Latin",
    "popularity_rank": 13,
    "popularity_change": "+1%",
    "year_2025_rank": 13,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 7600,
    "regional_popularity": {
      "geneva": 13,
      "vaud": 13,
      "valais": 13,
      "neuchatel": 13
    }
  },
  {
    "name_en": "Samuel",
    "name_native": "Samuel",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God has heard",
    "meaning_native": "Dieu a entendu",
    "starting_letter": "S",
    "pronunciation": "sah-mü-EL",
    "origin": "Hebrew",
    "popularity_rank": 14,
    "popularity_change": "+2%",
    "year_2025_rank": 14,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 3100,
    "regional_popularity": {
      "geneva": 14,
      "vaud": 14,
      "valais": 14,
      "neuchatel": 14
    }
  },
  {
    "name_en": "Julien",
    "name_native": "Julien",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Youthful",
    "meaning_native": "Juvénile",
    "starting_letter": "J",
    "pronunciation": "zhü-lee-AHN",
    "origin": "Latin",
    "popularity_rank": 15,
    "popularity_change": "+1%",
    "year_2025_rank": 15,
    "year_2024_rank": 16,
    "trending_status": "stable",
    "search_volume": 2900,
    "regional_popularity": {
      "geneva": 15,
      "vaud": 15,
      "valais": 15,
      "neuchatel": 15
    }
  },
  {
    "name_en": "Raphael",
    "name_native": "Raphaël",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God has healed",
    "meaning_native": "Dieu a guéri",
    "starting_letter": "R",
    "pronunciation": "rah-fah-EL",
    "origin": "Hebrew",
    "popularity_rank": 16,
    "popularity_change": "+3%",
    "year_2025_rank": 16,
    "year_2024_rank": 19,
    "trending_status": "rising",
    "search_volume": 2700,
    "regional_popularity": {
      "geneva": 16,
      "vaud": 16,
      "valais": 16,
      "neuchatel": 16
    }
  },
  {
    "name_en": "Alexandre",
    "name_native": "Alexandre",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Defender of mankind",
    "meaning_native": "Défenseur de l'humanité",
    "starting_letter": "A",
    "pronunciation": "ah-lek-SAHNDR",
    "origin": "Greek",
    "popularity_rank": 17,
    "popularity_change": "+1%",
    "year_2025_rank": 17,
    "year_2024_rank": 18,
    "trending_status": "stable",
    "search_volume": 2600,
    "regional_popularity": {
      "geneva": 17,
      "vaud": 17,
      "valais": 17,
      "neuchatel": 17
    }
  },
  {
    "name_en": "Oscar",
    "name_native": "Oscar",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Divine spear",
    "meaning_native": "Lance divine",
    "starting_letter": "O",
    "pronunciation": "oss-KAHR",
    "origin": "Irish",
    "popularity_rank": 18,
    "popularity_change": "+4%",
    "year_2025_rank": 18,
    "year_2024_rank": 22,
    "trending_status": "rising",
    "search_volume": 2400,
    "regional_popularity": {
      "geneva": 18,
      "vaud": 18,
      "valais": 18,
      "neuchatel": 18
    }
  },
  {
    "name_en": "Victor",
    "name_native": "Victor",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Conqueror",
    "meaning_native": "Conquérant",
    "starting_letter": "V",
    "pronunciation": "vik-TORR",
    "origin": "Latin",
    "popularity_rank": 19,
    "popularity_change": "+2%",
    "year_2025_rank": 19,
    "year_2024_rank": 21,
    "trending_status": "rising",
    "search_volume": 2300,
    "regional_popularity": {
      "geneva": 19,
      "vaud": 19,
      "valais": 19,
      "neuchatel": 19
    }
  },
  {
    "name_en": "Felix",
    "name_native": "Félix",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Happy, lucky",
    "meaning_native": "Heureux, chanceux",
    "starting_letter": "F",
    "pronunciation": "feh-LEEKS",
    "origin": "Latin",
    "popularity_rank": 20,
    "popularity_change": "+1%",
    "year_2025_rank": 20,
    "year_2024_rank": 21,
    "trending_status": "stable",
    "search_volume": 2200,
    "regional_popularity": {
      "geneva": 20,
      "vaud": 20,
      "valais": 20,
      "neuchatel": 20
    }
  },
  {
    "name_en": "Thomas",
    "name_native": "Thomas",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Twin",
    "meaning_native": "Jumeau",
    "starting_letter": "T",
    "pronunciation": "toh-MAH",
    "origin": "Aramaic",
    "popularity_rank": 21,
    "popularity_change": "+1%",
    "year_2025_rank": 21,
    "year_2024_rank": 22,
    "trending_status": "stable",
    "search_volume": 2100,
    "regional_popularity": {
      "geneva": 21,
      "vaud": 21,
      "valais": 21,
      "neuchatel": 21
    }
  },
  {
    "name_en": "Antoine",
    "name_native": "Antoine",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Priceless",
    "meaning_native": "Inestimable",
    "starting_letter": "A",
    "pronunciation": "ahn-TWAHN",
    "origin": "Latin",
    "popularity_rank": 22,
    "popularity_change": "+2%",
    "year_2025_rank": 22,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 2000,
    "regional_popularity": {
      "geneva": 22,
      "vaud": 22,
      "valais": 22,
      "neuchatel": 22
    }
  },
  {
    "name_en": "Mathis",
    "name_native": "Mathis",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Gift of God",
    "meaning_native": "Don de Dieu",
    "starting_letter": "M",
    "pronunciation": "mah-TEES",
    "origin": "Hebrew",
    "popularity_rank": 23,
    "popularity_change": "+3%",
    "year_2025_rank": 23,
    "year_2024_rank": 26,
    "trending_status": "rising",
    "search_volume": 1900,
    "regional_popularity": {
      "geneva": 23,
      "vaud": 23,
      "valais": 23,
      "neuchatel": 23
    }
  },
  {
    "name_en": "Paul",
    "name_native": "Paul",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Small, humble",
    "meaning_native": "Petit, humble",
    "starting_letter": "P",
    "pronunciation": "POWL",
    "origin": "Latin",
    "popularity_rank": 24,
    "popularity_change": "+1%",
    "year_2025_rank": 24,
    "year_2024_rank": 25,
    "trending_status": "stable",
    "search_volume": 1800,
    "regional_popularity": {
      "geneva": 24,
      "vaud": 24,
      "valais": 24,
      "neuchatel": 24
    }
  },
  {
    "name_en": "Simon",
    "name_native": "Simon",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Listener",
    "meaning_native": "Celui qui écoute",
    "starting_letter": "S",
    "pronunciation": "see-MOHN",
    "origin": "Hebrew",
    "popularity_rank": 25,
    "popularity_change": "+2%",
    "year_2025_rank": 25,
    "year_2024_rank": 27,
    "trending_status": "rising",
    "search_volume": 1700,
    "regional_popularity": {
      "geneva": 25,
      "vaud": 25,
      "valais": 25,
      "neuchatel": 25
    }
  },
  {
    "name_en": "Vincent",
    "name_native": "Vincent",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Conquering",
    "meaning_native": "Conquérant",
    "starting_letter": "V",
    "pronunciation": "van-SAHN",
    "origin": "Latin",
    "popularity_rank": 26,
    "popularity_change": "+1%",
    "year_2025_rank": 26,
    "year_2024_rank": 27,
    "trending_status": "stable",
    "search_volume": 1600,
    "regional_popularity": {
      "geneva": 26,
      "vaud": 26,
      "valais": 26,
      "neuchatel": 26
    }
  },
  {
    "name_en": "Benjamin",
    "name_native": "Benjamin",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Son of the right hand",
    "meaning_native": "Fils de la main droite",
    "starting_letter": "B",
    "pronunciation": "ben-zhah-MAHN",
    "origin": "Hebrew",
    "popularity_rank": 27,
    "popularity_change": "+3%",
    "year_2025_rank": 27,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 1500,
    "regional_popularity": {
      "geneva": 27,
      "vaud": 27,
      "valais": 27,
      "neuchatel": 27
    }
  },
  {
    "name_en": "Matteo",
    "name_native": "Matteo",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Gift of God",
    "meaning_native": "Don de Dieu",
    "starting_letter": "M",
    "pronunciation": "mah-teh-OH",
    "origin": "Italian",
    "popularity_rank": 28,
    "popularity_change": "+4%",
    "year_2025_rank": 28,
    "year_2024_rank": 32,
    "trending_status": "rising",
    "search_volume": 1400,
    "regional_popularity": {
      "geneva": 28,
      "vaud": 28,
      "valais": 28,
      "neuchatel": 28
    }
  },
  {
    "name_en": "David",
    "name_native": "David",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Beloved",
    "meaning_native": "Bien-aimé",
    "starting_letter": "D",
    "pronunciation": "dah-VEED",
    "origin": "Hebrew",
    "popularity_rank": 29,
    "popularity_change": "+1%",
    "year_2025_rank": 29,
    "year_2024_rank": 30,
    "trending_status": "stable",
    "search_volume": 1300,
    "regional_popularity": {
      "geneva": 29,
      "vaud": 29,
      "valais": 29,
      "neuchatel": 29
    }
  },
  {
    "name_en": "Elias",
    "name_native": "Elias",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "The Lord is my God",
    "meaning_native": "Le Seigneur est mon Dieu",
    "starting_letter": "E",
    "pronunciation": "eh-LEE-ahs",
    "origin": "Hebrew",
    "popularity_rank": 30,
    "popularity_change": "+2%",
    "year_2025_rank": 30,
    "year_2024_rank": 32,
    "trending_status": "rising",
    "search_volume": 1200,
    "regional_popularity": {
      "geneva": 30,
      "vaud": 30,
      "valais": 30,
      "neuchatel": 30
    }
  },
  {
    "name_en": "Gabriel",
    "name_native": "Gabriel",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is my strength",
    "meaning_native": "Dieu est ma force",
    "starting_letter": "G",
    "pronunciation": "gah-bree-EL",
    "origin": "Hebrew",
    "popularity_rank": 31,
    "popularity_change": "+3%",
    "year_2025_rank": 31,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 1100,
    "regional_popularity": {
      "geneva": 31,
      "vaud": 31,
      "valais": 31,
      "neuchatel": 31
    }
  },
  {
    "name_en": "Samuel",
    "name_native": "Samuel",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God has heard",
    "meaning_native": "Dieu a entendu",
    "starting_letter": "S",
    "pronunciation": "sah-muu-EL",
    "origin": "Hebrew",
    "popularity_rank": 32,
    "popularity_change": "+2%",
    "year_2025_rank": 32,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 1000,
    "regional_popularity": {
      "geneva": 32,
      "vaud": 32,
      "valais": 32,
      "neuchatel": 32
    }
  },
  {
    "name_en": "Raphael",
    "name_native": "Raphaël",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God has healed",
    "meaning_native": "Dieu a guéri",
    "starting_letter": "R",
    "pronunciation": "rah-fah-EL",
    "origin": "Hebrew",
    "popularity_rank": 33,
    "popularity_change": "+4%",
    "year_2025_rank": 33,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 900,
    "regional_popularity": {
      "geneva": 33,
      "vaud": 33,
      "valais": 33,
      "neuchatel": 33
    }
  },
  {
    "name_en": "Liam",
    "name_native": "Liam",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Strong-willed warrior",
    "meaning_native": "Guerrier déterminé",
    "starting_letter": "L",
    "pronunciation": "lee-AHM",
    "origin": "Irish",
    "popularity_rank": 34,
    "popularity_change": "+5%",
    "year_2025_rank": 34,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 800,
    "regional_popularity": {
      "geneva": 34,
      "vaud": 34,
      "valais": 34,
      "neuchatel": 34
    }
  },
  {
    "name_en": "Aaron",
    "name_native": "Aaron",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "High mountain",
    "meaning_native": "Haute montagne",
    "starting_letter": "A",
    "pronunciation": "ah-ROHN",
    "origin": "Hebrew",
    "popularity_rank": 35,
    "popularity_change": "+2%",
    "year_2025_rank": 35,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 700,
    "regional_popularity": {
      "geneva": 35,
      "vaud": 35,
      "valais": 35,
      "neuchatel": 35
    }
  },
  {
    "name_en": "Nolan",
    "name_native": "Nolan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Noble, famous",
    "meaning_native": "Noble, célèbre",
    "starting_letter": "N",
    "pronunciation": "NO-lan",
    "origin": "Irish",
    "popularity_rank": 30,
    "popularity_change": "+1%",
    "year_2025_rank": 30,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 5900,
    "regional_popularity": { "geneva": 30, "vaud": 30, "valais": 30, "neuchatel": 30 }
  },
  {
    "name_en": "Swan",
    "name_native": "Swan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Swan (the bird)",
    "meaning_native": "Cygne",
    "starting_letter": "S",
    "pronunciation": "SWAN",
    "origin": "English",
    "popularity_rank": 31,
    "popularity_change": "+2%",
    "year_2025_rank": 31,
    "year_2024_rank": 35,
    "trending_status": "rising",
    "search_volume": 5800,
    "regional_popularity": { "geneva": 31, "vaud": 31, "valais": 31, "neuchatel": 31 }
  },
  {
    "name_en": "Sacha",
    "name_native": "Sacha",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Defender of mankind",
    "meaning_native": "Défenseur de l'humanité",
    "starting_letter": "S",
    "pronunciation": "SAH-sha",
    "origin": "Greek",
    "popularity_rank": 32,
    "popularity_change": "+1%",
    "year_2025_rank": 32,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 5700,
    "regional_popularity": { "geneva": 32, "vaud": 32, "valais": 32, "neuchatel": 32 }
  },
  {
    "name_en": "Titouan",
    "name_native": "Titouan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Highly praiseworthy",
    "meaning_native": "Très louable",
    "starting_letter": "T",
    "pronunciation": "tee-TWAN",
    "origin": "Latin",
    "popularity_rank": 33,
    "popularity_change": "+2%",
    "year_2025_rank": 33,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 5600,
    "regional_popularity": { "geneva": 33, "vaud": 33, "valais": 33, "neuchatel": 33 }
  },
  {
    "name_en": "Bastien",
    "name_native": "Bastien",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Venerable",
    "meaning_native": "Vénérable",
    "starting_letter": "B",
    "pronunciation": "bas-TYEN",
    "origin": "Greek",
    "popularity_rank": 54,
    "popularity_change": "+3%",
    "year_2025_rank": 54,
    "year_2024_rank": 57,
    "trending_status": "rising",
    "search_volume": 420,
    "regional_popularity": {
      "geneva": 54,
      "vaud": 54,
      "valais": 54,
      "neuchatel": 54
    }
  },
  {
    "name_en": "Cedric",
    "name_native": "Cedric",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bounty",
    "meaning_native": "Bonté",
    "starting_letter": "C",
    "pronunciation": "say-DREEK",
    "origin": "Celtic",
    "popularity_rank": 55,
    "popularity_change": "+5%",
    "year_2025_rank": 55,
    "year_2024_rank": 60,
    "trending_status": "rising",
    "search_volume": 380,
    "regional_popularity": {
      "geneva": 55,
      "vaud": 55,
      "valais": 55,
      "neuchatel": 55
    }
  },
  {
    "name_en": "Damien",
    "name_native": "Damien",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "To tame",
    "meaning_native": "Dompter",
    "starting_letter": "D",
    "pronunciation": "da-MYEN",
    "origin": "Greek",
    "popularity_rank": 56,
    "popularity_change": "+2%",
    "year_2025_rank": 56,
    "year_2024_rank": 58,
    "trending_status": "stable",
    "search_volume": 340,
    "regional_popularity": {
      "geneva": 56,
      "vaud": 56,
      "valais": 56,
      "neuchatel": 56
    }
  },
  {
    "name_en": "Elias",
    "name_native": "Elias",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "My God is Yahweh",
    "meaning_native": "Mon Dieu est Yahweh",
    "starting_letter": "E",
    "pronunciation": "ay-LEE-as",
    "origin": "Hebrew",
    "popularity_rank": 57,
    "popularity_change": "+4%",
    "year_2025_rank": 57,
    "year_2024_rank": 61,
    "trending_status": "rising",
    "search_volume": 300,
    "regional_popularity": {
      "geneva": 57,
      "vaud": 57,
      "valais": 57,
      "neuchatel": 57
    }
  },
  {
    "name_en": "Fabien",
    "name_native": "Fabien",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bean grower",
    "meaning_native": "Cultivateur de haricots",
    "starting_letter": "F",
    "pronunciation": "fa-BYEN",
    "origin": "Latin",
    "popularity_rank": 58,
    "popularity_change": "+6%",
    "year_2025_rank": 58,
    "year_2024_rank": 64,
    "trending_status": "rising",
    "search_volume": 260,
    "regional_popularity": {
      "geneva": 58,
      "vaud": 58,
      "valais": 58,
      "neuchatel": 58
    }
  },
  {
    "name_en": "Gabriel",
    "name_native": "Gabriel",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is my strength",
    "meaning_native": "Dieu est ma force",
    "starting_letter": "G",
    "pronunciation": "ga-bree-EL",
    "origin": "Hebrew",
    "popularity_rank": 59,
    "popularity_change": "+3%",
    "year_2025_rank": 59,
    "year_2024_rank": 62,
    "trending_status": "rising",
    "search_volume": 220,
    "regional_popularity": {
      "geneva": 59,
      "vaud": 59,
      "valais": 59,
      "neuchatel": 59
    }
  },
  {
    "name_en": "Hendrik",
    "name_native": "Hendrik",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Ruler of the home",
    "meaning_native": "Souverain de la maison",
    "starting_letter": "H",
    "pronunciation": "hen-DREEK",
    "origin": "Germanic",
    "popularity_rank": 60,
    "popularity_change": "+5%",
    "year_2025_rank": 60,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 180,
    "regional_popularity": {
      "geneva": 60,
      "vaud": 60,
      "valais": 60,
      "neuchatel": 60
    }
  },
  {
    "name_en": "Ivan",
    "name_native": "Ivan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "God is gracious",
    "meaning_native": "Dieu est gracieux",
    "starting_letter": "I",
    "pronunciation": "ee-VAN",
    "origin": "Slavic",
    "popularity_rank": 61,
    "popularity_change": "+2%",
    "year_2025_rank": 61,
    "year_2024_rank": 63,
    "trending_status": "stable",
    "search_volume": 140,
    "regional_popularity": {
      "geneva": 61,
      "vaud": 61,
      "valais": 61,
      "neuchatel": 61
    }
  },
  {
    "name_en": "Jasper",
    "name_native": "Jasper",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Bringer of treasure",
    "meaning_native": "Porteur de trésor",
    "starting_letter": "J",
    "pronunciation": "zhas-PER",
    "origin": "Persian",
    "popularity_rank": 62,
    "popularity_change": "+4%",
    "year_2025_rank": 62,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 100,
    "regional_popularity": {
      "geneva": 62,
      "vaud": 62,
      "valais": 62,
      "neuchatel": 62
    }
  },
  {
    "name_en": "Kilian",
    "name_native": "Kilian",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Church",
    "meaning_native": "Église",
    "starting_letter": "K",
    "pronunciation": "kee-lee-AN",
    "origin": "Irish",
    "popularity_rank": 63,
    "popularity_change": "+1%",
    "year_2025_rank": 63,
    "year_2024_rank": 64,
    "trending_status": "stable",
    "search_volume": 60,
    "regional_popularity": {
      "geneva": 63,
      "vaud": 63,
      "valais": 63,
      "neuchatel": 63
    }
  },
  {
    "name_en": "Levi",
    "name_native": "Levi",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Joined",
    "meaning_native": "Rejoint",
    "starting_letter": "L",
    "pronunciation": "lay-VEE",
    "origin": "Hebrew",
    "popularity_rank": 64,
    "popularity_change": "+3%",
    "year_2025_rank": 64,
    "year_2024_rank": 67,
    "trending_status": "rising",
    "search_volume": 20,
    "regional_popularity": {
      "geneva": 64,
      "vaud": 64,
      "valais": 64,
      "neuchatel": 64
    }
  },
  {
    "name_en": "Milan",
    "name_native": "Milan",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Gracious",
    "meaning_native": "Gracieux",
    "starting_letter": "M",
    "pronunciation": "mee-LAN",
    "origin": "Slavic",
    "popularity_rank": 65,
    "popularity_change": "+5%",
    "year_2025_rank": 65,
    "year_2024_rank": 70,
    "trending_status": "rising",
    "search_volume": 15,
    "regional_popularity": {
      "geneva": 65,
      "vaud": 65,
      "valais": 65,
      "neuchatel": 65
    }
  },
  {
    "name_en": "Nico",
    "name_native": "Nico",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Victory of the people",
    "meaning_native": "Victoire du peuple",
    "starting_letter": "N",
    "pronunciation": "nee-KO",
    "origin": "Greek",
    "popularity_rank": 66,
    "popularity_change": "+2%",
    "year_2025_rank": 66,
    "year_2024_rank": 68,
    "trending_status": "stable",
    "search_volume": 10,
    "regional_popularity": {
      "geneva": 66,
      "vaud": 66,
      "valais": 66,
      "neuchatel": 66
    }
  },
  {
    "name_en": "Owen",
    "name_native": "Owen",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Young warrior",
    "meaning_native": "Jeune guerrier",
    "starting_letter": "O",
    "pronunciation": "o-EN",
    "origin": "Welsh",
    "popularity_rank": 67,
    "popularity_change": "+4%",
    "year_2025_rank": 67,
    "year_2024_rank": 71,
    "trending_status": "rising",
    "search_volume": 5,
    "regional_popularity": {
      "geneva": 67,
      "vaud": 67,
      "valais": 67,
      "neuchatel": 67
    }
  }
]

export default SwissFrenchBoyNames;
