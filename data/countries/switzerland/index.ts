import { SwissGermanBoyNames } from './languages/german/boy-names'
import { SwissGermanGirlNames } from './languages/german/girl-names'
import { SwissFrenchBoyNames } from './languages/french/boy-names'
import { SwissFrenchGirlNames } from './languages/french/girl-names'
import type { NameData } from '@/types/name-data'

export const SwitzerlandNames: NameData[] = [
  ...SwissGermanBoyNames,
  ...SwissGermanGirlNames,
  ...SwissFrenchBoyNames,
  ...SwissFrenchGirlNames
]

export const SwitzerlandBoyNames: NameData[] = [...SwissGermanBoyNames, ...SwissFrenchBoyNames]
export const SwitzerlandGirlNames: NameData[] = [...SwissGermanGirlNames, ...SwissFrenchGirlNames]

export const getSwitzerlandNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'german':
      return [...SwissGermanBoyNames, ...SwissGermanGirlNames]
    case 'french':
      return [...SwissFrenchBoyNames, ...SwissFrenchGirlNames]
    default:
      return []
  }
}

export const getSwitzerlandNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'german':
      return gender.toLowerCase() === 'boy' ? SwissGermanBoyNames : SwissGermanGirlNames
    case 'french':
      return gender.toLowerCase() === 'boy' ? SwissFrenchBoyNames : SwissFrenchGirlNames
    default:
      return []
  }
}
