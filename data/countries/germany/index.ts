import { GermanBoyNames } from './languages/german/boy-names'
import { GermanGirlNames } from './languages/german/girl-names'
import type { NameData } from '@/types/name-data'

export const GermanyNames: NameData[] = [
  ...GermanBoyNames,
  ...GermanGirlNames
]

export const GermanyBoyNames: NameData[] = GermanBoyNames
export const GermanyGirlNames: NameData[] = GermanGirlNames

export const getGermanyNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'german':
      return GermanyNames
    default:
      return []
  }
}

export const getGermanyNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'german':
      return gender.toLowerCase() === 'boy' ? GermanyBoyNames : GermanyGirlNames
    default:
      return []
  }
}
