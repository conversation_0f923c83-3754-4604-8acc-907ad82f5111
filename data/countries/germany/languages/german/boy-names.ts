import type { NameData } from "@/types/name-data";

export const GermanBoyNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rest, comfort",
    "meaning_native": "<PERSON>uh<PERSON>, <PERSON>rost",
    "starting_letter": "N",
    "pronunciation": "NO-ah",
    "origin": "Hebrew",
    "popularity_rank": 1,
    "popularity_change": "+2%",
    "year_2025_rank": 1,
    "year_2024_rank": 3,
    "trending_status": "rising",
    "search_volume": 125000,
    "regional_popularity": {
      "bavaria": 1,
      "north_rhine_westphalia": 2,
      "baden_wurttemberg": 1,
      "berlin": 3
    }
  },
  {
    "name_en": "<PERSON>",
    "name_native": "Matteo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "<PERSON><PERSON><PERSON><PERSON>",
    "starting_letter": "M",
    "pronunciation": "mat-TEH-oh",
    "origin": "Italian",
    "popularity_rank": 2,
    "popularity_change": "+8%",
    "year_2025_rank": 2,
    "year_2024_rank": 10,
    "trending_status": "rising",
    "search_volume": 98500,
    "regional_popularity": {
      "bavaria": 3,
      "north_rhine_westphalia": 1,
      "baden_wurttemberg": 2,
      "berlin": 1
    }
  },
  {
    "name_en": "Elias",
    "name_native": "Elias",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "My God is Yahweh",
    "meaning_native": "Mein Gott ist Jahwe",
    "starting_letter": "E",
    "pronunciation": "eh-LEE-as",
    "origin": "Hebrew",
    "popularity_rank": 3,
    "popularity_change": "+1%",
    "year_2025_rank": 3,
    "year_2024_rank": 4,
    "trending_status": "stable",
    "search_volume": 87300,
    "regional_popularity": {
      "bavaria": 2,
      "north_rhine_westphalia": 4,
      "baden_wurttemberg": 3,
      "berlin": 2
    }
  },
  {
    "name_en": "Finn",
    "name_native": "Finn",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Fair, white",
    "meaning_native": "Hell, weiß",
    "starting_letter": "F",
    "pronunciation": "FIN",
    "origin": "Irish",
    "popularity_rank": 4,
    "popularity_change": "+3%",
    "year_2025_rank": 4,
    "year_2024_rank": 7,
    "trending_status": "rising",
    "search_volume": 76800,
    "regional_popularity": {
      "bavaria": 5,
      "north_rhine_westphalia": 3,
      "baden_wurttemberg": 4,
      "berlin": 4
    }
  },
  {
    "name_en": "Leon",
    "name_native": "Leon",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lion",
    "meaning_native": "Löwe",
    "starting_letter": "L",
    "pronunciation": "LEH-on",
    "origin": "Greek",
    "popularity_rank": 5,
    "popularity_change": "-1%",
    "year_2025_rank": 5,
    "year_2024_rank": 4,
    "trending_status": "stable",
    "search_volume": 89400,
    "regional_popularity": {
      "bavaria": 4,
      "north_rhine_westphalia": 5,
      "baden_wurttemberg": 5,
      "berlin": 6
    }
  },
  {
    "name_en": "Paul",
    "name_native": "Paul",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Small, humble",
    "meaning_native": "Klein, bescheiden",
    "starting_letter": "P",
    "pronunciation": "POWL",
    "origin": "Latin",
    "popularity_rank": 6,
    "popularity_change": "+4%",
    "year_2025_rank": 6,
    "year_2024_rank": 10,
    "trending_status": "rising",
    "search_volume": 67200,
    "regional_popularity": {
      "bavaria": 7,
      "north_rhine_westphalia": 6,
      "baden_wurttemberg": 6,
      "berlin": 5
    }
  },
  {
    "name_en": "Emil",
    "name_native": "Emil",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rival, eager",
    "meaning_native": "Eifrig, wetteifern",
    "starting_letter": "E",
    "pronunciation": "eh-MEEL",
    "origin": "Latin",
    "popularity_rank": 7,
    "popularity_change": "+6%",
    "year_2025_rank": 7,
    "year_2024_rank": 13,
    "trending_status": "rising",
    "search_volume": 54300,
    "regional_popularity": {
      "bavaria": 6,
      "north_rhine_westphalia": 8,
      "baden_wurttemberg": 7,
      "berlin": 7
    }
  },
  {
    "name_en": "Henry",
    "name_native": "Henri",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Home ruler",
    "meaning_native": "Hausherr",
    "starting_letter": "H",
    "pronunciation": "hen-REE",
    "origin": "Germanic",
    "popularity_rank": 8,
    "popularity_change": "+5%",
    "year_2025_rank": 8,
    "year_2024_rank": 13,
    "trending_status": "rising",
    "search_volume": 48700,
    "regional_popularity": {
      "bavaria": 9,
      "north_rhine_westphalia": 7,
      "baden_wurttemberg": 8,
      "berlin": 8
    }
  },
  {
    "name_en": "Louis",
    "name_native": "Louis",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Famous warrior",
    "meaning_native": "Berühmter Krieger",
    "starting_letter": "L",
    "pronunciation": "loo-EE",
    "origin": "French",
    "popularity_rank": 9,
    "popularity_change": "+2%",
    "year_2025_rank": 9,
    "year_2024_rank": 11,
    "trending_status": "rising",
    "search_volume": 43600,
    "regional_popularity": {
      "bavaria": 8,
      "north_rhine_westphalia": 9,
      "baden_wurttemberg": 9,
      "berlin": 9
    }
  },
  {
    "name_en": "Anton",
    "name_native": "Anton",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Priceless, flourishing",
    "meaning_native": "Unbezahlbar, blühend",
    "starting_letter": "A",
    "pronunciation": "AN-ton",
    "origin": "Latin",
    "popularity_rank": 10,
    "popularity_change": "+7%",
    "year_2025_rank": 10,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 39800,
    "regional_popularity": {
      "bavaria": 10,
      "north_rhine_westphalia": 10,
      "baden_wurttemberg": 10,
      "berlin": 10
    }
  },
  {
    "name_en": "Liam",
    "name_native": "Liam",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong-willed warrior",
    "meaning_native": "Willensstarker Krieger",
    "starting_letter": "L",
    "pronunciation": "LEE-am",
    "origin": "Irish",
    "popularity_rank": 11,
    "popularity_change": "+9%",
    "year_2025_rank": 11,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 36200,
    "regional_popularity": {
      "bavaria": 11,
      "north_rhine_westphalia": 11,
      "baden_wurttemberg": 11,
      "berlin": 11
    }
  },
  {
    "name_en": "Theo",
    "name_native": "Theo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God",
    "meaning_native": "Gott",
    "starting_letter": "T",
    "pronunciation": "TEH-oh",
    "origin": "Greek",
    "popularity_rank": 12,
    "popularity_change": "+4%",
    "year_2025_rank": 12,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 34800,
    "regional_popularity": {
      "bavaria": 12,
      "north_rhine_westphalia": 12,
      "baden_wurttemberg": 12,
      "berlin": 12
    }
  },
  {
    "name_en": "Ben",
    "name_native": "Ben",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Son of the right hand",
    "meaning_native": "Sohn der rechten Hand",
    "starting_letter": "B",
    "pronunciation": "BEN",
    "origin": "Hebrew",
    "popularity_rank": 13,
    "popularity_change": "+3%",
    "year_2025_rank": 13,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 33600,
    "regional_popularity": {
      "bavaria": 13,
      "north_rhine_westphalia": 13,
      "baden_wurttemberg": 13,
      "berlin": 13
    }
  },
  {
    "name_en": "Jonas",
    "name_native": "Jonas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dove",
    "meaning_native": "Taube",
    "starting_letter": "J",
    "pronunciation": "YO-nas",
    "origin": "Hebrew",
    "popularity_rank": 14,
    "popularity_change": "+2%",
    "year_2025_rank": 14,
    "year_2024_rank": 16,
    "trending_status": "stable",
    "search_volume": 32400,
    "regional_popularity": {
      "bavaria": 14,
      "north_rhine_westphalia": 14,
      "baden_wurttemberg": 14,
      "berlin": 14
    }
  },
  {
    "name_en": "Felix",
    "name_native": "Felix",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Happy, lucky",
    "meaning_native": "Glücklich, Glück",
    "starting_letter": "F",
    "pronunciation": "FEH-liks",
    "origin": "Latin",
    "popularity_rank": 15,
    "popularity_change": "+5%",
    "year_2025_rank": 15,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 31200,
    "regional_popularity": {
      "bavaria": 15,
      "north_rhine_westphalia": 15,
      "baden_wurttemberg": 15,
      "berlin": 15
    }
  },
  {
    "name_en": "Maximilian",
    "name_native": "Maximilian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Greatest",
    "meaning_native": "Der Größte",
    "starting_letter": "M",
    "pronunciation": "mak-see-MEE-lee-an",
    "origin": "Latin",
    "popularity_rank": 16,
    "popularity_change": "+1%",
    "year_2025_rank": 16,
    "year_2024_rank": 17,
    "trending_status": "stable",
    "search_volume": 29900,
    "regional_popularity": {
      "bavaria": 16,
      "north_rhine_westphalia": 16,
      "baden_wurttemberg": 16,
      "berlin": 16
    }
  },
  {
    "name_en": "Alexander",
    "name_native": "Alexander",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Defender of mankind",
    "meaning_native": "Beschützer der Menschen",
    "starting_letter": "A",
    "pronunciation": "ah-lek-SAHN-der",
    "origin": "Greek",
    "popularity_rank": 17,
    "popularity_change": "+3%",
    "year_2025_rank": 17,
    "year_2024_rank": 20,
    "trending_status": "rising",
    "search_volume": 28700,
    "regional_popularity": {
      "bavaria": 17,
      "north_rhine_westphalia": 17,
      "baden_wurttemberg": 17,
      "berlin": 17
    }
  },
  {
    "name_en": "David",
    "name_native": "David",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Beloved",
    "meaning_native": "Geliebt",
    "starting_letter": "D",
    "pronunciation": "DAH-veet",
    "origin": "Hebrew",
    "popularity_rank": 18,
    "popularity_change": "+2%",
    "year_2025_rank": 18,
    "year_2024_rank": 20,
    "trending_status": "stable",
    "search_volume": 27500,
    "regional_popularity": {
      "bavaria": 18,
      "north_rhine_westphalia": 18,
      "baden_wurttemberg": 18,
      "berlin": 18
    }
  },
  {
    "name_en": "Oskar",
    "name_native": "Oskar",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Divine spear",
    "meaning_native": "Göttlicher Speer",
    "starting_letter": "O",
    "pronunciation": "OS-kar",
    "origin": "Irish",
    "popularity_rank": 19,
    "popularity_change": "+6%",
    "year_2025_rank": 19,
    "year_2024_rank": 25,
    "trending_status": "rising",
    "search_volume": 26300,
    "regional_popularity": {
      "bavaria": 19,
      "north_rhine_westphalia": 19,
      "baden_wurttemberg": 19,
      "berlin": 19
    }
  },
  {
    "name_en": "Jakob",
    "name_native": "Jakob",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Supplanter",
    "meaning_native": "Verdränger",
    "starting_letter": "J",
    "pronunciation": "YAH-kop",
    "origin": "Hebrew",
    "popularity_rank": 20,
    "popularity_change": "+4%",
    "year_2025_rank": 20,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 25100,
    "regional_popularity": {
      "bavaria": 20,
      "north_rhine_westphalia": 20,
      "baden_wurttemberg": 20,
      "berlin": 20
    }
  },
  {
    "name_en": "Samuel",
    "name_native": "Samuel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Name of God",
    "meaning_native": "Name Gottes",
    "starting_letter": "S",
    "pronunciation": "SAH-moo-el",
    "origin": "Hebrew",
    "popularity_rank": 21,
    "popularity_change": "+3%",
    "year_2025_rank": 21,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 24600,
    "regional_popularity": {
      "bavaria": 21,
      "north_rhine_westphalia": 21,
      "baden_wurttemberg": 21,
      "berlin": 21
    }
  },
  {
    "name_en": "Moritz",
    "name_native": "Moritz",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dark-skinned, Moorish",
    "meaning_native": "Dunkelhäutig, maurisch",
    "starting_letter": "M",
    "pronunciation": "MO-rits",
    "origin": "Latin",
    "popularity_rank": 22,
    "popularity_change": "+5%",
    "year_2025_rank": 22,
    "year_2024_rank": 27,
    "trending_status": "rising",
    "search_volume": 23200,
    "regional_popularity": {
      "bavaria": 22,
      "north_rhine_westphalia": 22,
      "baden_wurttemberg": 22,
      "berlin": 22
    }
  },
  {
    "name_en": "Luca",
    "name_native": "Luca",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Light",
    "meaning_native": "Licht",
    "starting_letter": "L",
    "pronunciation": "LOO-ka",
    "origin": "Italian",
    "popularity_rank": 23,
    "popularity_change": "+7%",
    "year_2025_rank": 23,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 22800,
    "regional_popularity": {
      "bavaria": 23,
      "north_rhine_westphalia": 23,
      "baden_wurttemberg": 23,
      "berlin": 23
    }
  },
  {
    "name_en": "Julius",
    "name_native": "Julius",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Youthful",
    "meaning_native": "Jugendlich",
    "starting_letter": "J",
    "pronunciation": "YOO-lee-oos",
    "origin": "Latin",
    "popularity_rank": 24,
    "popularity_change": "+2%",
    "year_2025_rank": 24,
    "year_2024_rank": 26,
    "trending_status": "stable",
    "search_volume": 21400,
    "regional_popularity": {
      "bavaria": 24,
      "north_rhine_westphalia": 24,
      "baden_wurttemberg": 24,
      "berlin": 24
    }
  },
  {
    "name_en": "Adrian",
    "name_native": "Adrian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dark, rich",
    "meaning_native": "Dunkel, reich",
    "starting_letter": "A",
    "pronunciation": "AH-dree-an",
    "origin": "Latin",
    "popularity_rank": 25,
    "popularity_change": "+8%",
    "year_2025_rank": 25,
    "year_2024_rank": 33,
    "trending_status": "rising",
    "search_volume": 20100,
    "regional_popularity": {
      "bavaria": 25,
      "north_rhine_westphalia": 25,
      "baden_wurttemberg": 25,
      "berlin": 25
    }
  },
  {
    "name_en": "Vincent",
    "name_native": "Vincent",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Conquering",
    "meaning_native": "Erobernd",
    "starting_letter": "V",
    "pronunciation": "VIN-tsent",
    "origin": "Latin",
    "popularity_rank": 26,
    "popularity_change": "+4%",
    "year_2025_rank": 26,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 19700,
    "regional_popularity": {
      "bavaria": 26,
      "north_rhine_westphalia": 26,
      "baden_wurttemberg": 26,
      "berlin": 26
    }
  },
  {
    "name_en": "Lukas",
    "name_native": "Lukas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Light",
    "meaning_native": "Licht",
    "starting_letter": "L",
    "pronunciation": "LOO-kas",
    "origin": "Greek",
    "popularity_rank": 27,
    "popularity_change": "+1%",
    "year_2025_rank": 27,
    "year_2024_rank": 28,
    "trending_status": "stable",
    "search_volume": 20300,
    "regional_popularity": {
      "bavaria": 27,
      "north_rhine_westphalia": 27,
      "baden_wurttemberg": 27,
      "berlin": 27
    }
  },
  {
    "name_en": "Jannik",
    "name_native": "Jannik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YAN-nik",
    "origin": "Hebrew",
    "popularity_rank": 28,
    "popularity_change": "+6%",
    "year_2025_rank": 28,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 18400,
    "regional_popularity": {
      "bavaria": 28,
      "north_rhine_westphalia": 28,
      "baden_wurttemberg": 28,
      "berlin": 28
    }
  },
  {
    "name_en": "Constantin",
    "name_native": "Constantin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Constant, steadfast",
    "meaning_native": "Beständig, standhaft",
    "starting_letter": "C",
    "pronunciation": "kon-stan-TEEN",
    "origin": "Latin",
    "popularity_rank": 29,
    "popularity_change": "+9%",
    "year_2025_rank": 29,
    "year_2024_rank": 38,
    "trending_status": "rising",
    "search_volume": 17100,
    "regional_popularity": {
      "bavaria": 29,
      "north_rhine_westphalia": 29,
      "baden_wurttemberg": 29,
      "berlin": 29
    }
  },
  {
    "name_en": "Gabriel",
    "name_native": "Gabriel",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my strength",
    "meaning_native": "Gott ist meine Stärke",
    "starting_letter": "G",
    "pronunciation": "GAH-bree-el",
    "origin": "Hebrew",
    "popularity_rank": 30,
    "popularity_change": "+3%",
    "year_2025_rank": 30,
    "year_2024_rank": 33,
    "trending_status": "rising",
    "search_volume": 18800,
    "regional_popularity": {
      "bavaria": 30,
      "north_rhine_westphalia": 30,
      "baden_wurttemberg": 30,
      "berlin": 30
    }
  },
  {
    "name_en": "Johann",
    "name_native": "Johann",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YO-han",
    "origin": "Hebrew",
    "popularity_rank": 31,
    "popularity_change": "+5%",
    "year_2025_rank": 31,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 16700,
    "regional_popularity": {
      "bavaria": 31,
      "north_rhine_westphalia": 31,
      "baden_wurttemberg": 31,
      "berlin": 31
    }
  },
  {
    "name_en": "Raphael",
    "name_native": "Raphael",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God has healed",
    "meaning_native": "Gott hat geheilt",
    "starting_letter": "R",
    "pronunciation": "rah-fah-EL",
    "origin": "Hebrew",
    "popularity_rank": 32,
    "popularity_change": "+7%",
    "year_2025_rank": 32,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 15900,
    "regional_popularity": {
      "bavaria": 32,
      "north_rhine_westphalia": 32,
      "baden_wurttemberg": 32,
      "berlin": 32
    }
  },
  {
    "name_en": "Benedikt",
    "name_native": "Benedikt",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Blessed",
    "meaning_native": "Gesegnet",
    "starting_letter": "B",
    "pronunciation": "BEH-neh-dikt",
    "origin": "Latin",
    "popularity_rank": 33,
    "popularity_change": "+4%",
    "year_2025_rank": 33,
    "year_2024_rank": 37,
    "trending_status": "rising",
    "search_volume": 16300,
    "regional_popularity": {
      "bavaria": 33,
      "north_rhine_westphalia": 33,
      "baden_wurttemberg": 33,
      "berlin": 33
    }
  },
  {
    "name_en": "Max",
    "name_native": "Max",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Greatest",
    "meaning_native": "Der Größte",
    "starting_letter": "M",
    "pronunciation": "MAKS",
    "origin": "Latin",
    "popularity_rank": 34,
    "popularity_change": "+2%",
    "year_2025_rank": 34,
    "year_2024_rank": 36,
    "trending_status": "stable",
    "search_volume": 15600,
    "regional_popularity": {
      "bavaria": 34,
      "north_rhine_westphalia": 34,
      "baden_wurttemberg": 34,
      "berlin": 34
    }
  },
  {
    "name_en": "Tim",
    "name_native": "Tim",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honoring God",
    "meaning_native": "Gott ehren",
    "starting_letter": "T",
    "pronunciation": "TEEM",
    "origin": "Greek",
    "popularity_rank": 35,
    "popularity_change": "+1%",
    "year_2025_rank": 35,
    "year_2024_rank": 36,
    "trending_status": "stable",
    "search_volume": 14900,
    "regional_popularity": {
      "bavaria": 35,
      "north_rhine_westphalia": 35,
      "baden_wurttemberg": 35,
      "berlin": 35
    }
  },
  {
    "name_en": "Florian",
    "name_native": "Florian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Flowering",
    "meaning_native": "Blühend",
    "starting_letter": "F",
    "pronunciation": "FLO-ree-an",
    "origin": "Latin",
    "popularity_rank": 36,
    "popularity_change": "+6%",
    "year_2025_rank": 36,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 14200,
    "regional_popularity": {
      "bavaria": 36,
      "north_rhine_westphalia": 36,
      "baden_wurttemberg": 36,
      "berlin": 36
    }
  },
  {
    "name_en": "Niklas",
    "name_native": "Niklas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Sieg des Volkes",
    "starting_letter": "N",
    "pronunciation": "NIK-las",
    "origin": "Greek",
    "popularity_rank": 37,
    "popularity_change": "+3%",
    "year_2025_rank": 37,
    "year_2024_rank": 40,
    "trending_status": "rising",
    "search_volume": 13800,
    "regional_popularity": {
      "bavaria": 37,
      "north_rhine_westphalia": 37,
      "baden_wurttemberg": 37,
      "berlin": 37
    }
  },
  {
    "name_en": "Sebastian",
    "name_native": "Sebastian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Venerable",
    "meaning_native": "Ehrwürdig",
    "starting_letter": "S",
    "pronunciation": "zeh-BASS-tee-an",
    "origin": "Greek",
    "popularity_rank": 38,
    "popularity_change": "+2%",
    "year_2025_rank": 38,
    "year_2024_rank": 40,
    "trending_status": "stable",
    "search_volume": 14500,
    "regional_popularity": {
      "bavaria": 38,
      "north_rhine_westphalia": 38,
      "baden_wurttemberg": 38,
      "berlin": 38
    }
  },
  {
    "name_en": "Milan",
    "name_native": "Milan",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gracious, dear",
    "meaning_native": "Gnädig, lieb",
    "starting_letter": "M",
    "pronunciation": "MEE-lan",
    "origin": "Slavic",
    "popularity_rank": 39,
    "popularity_change": "+8%",
    "year_2025_rank": 39,
    "year_2024_rank": 47,
    "trending_status": "rising",
    "search_volume": 12900,
    "regional_popularity": {
      "bavaria": 39,
      "north_rhine_westphalia": 39,
      "baden_wurttemberg": 39,
      "berlin": 39
    }
  },
  {
    "name_en": "Ole",
    "name_native": "Ole",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Ancestor's relic",
    "meaning_native": "Relikt der Ahnen",
    "starting_letter": "O",
    "pronunciation": "OH-leh",
    "origin": "Norse",
    "popularity_rank": 40,
    "popularity_change": "+5%",
    "year_2025_rank": 40,
    "year_2024_rank": 45,
    "trending_status": "rising",
    "search_volume": 13400,
    "regional_popularity": {
      "bavaria": 40,
      "north_rhine_westphalia": 40,
      "baden_wurttemberg": 40,
      "berlin": 40
    }
  },
  {
    "name_en": "Mika",
    "name_native": "Mika",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Who is like God",
    "meaning_native": "Wer ist wie Gott",
    "starting_letter": "M",
    "pronunciation": "MEE-ka",
    "origin": "Hebrew",
    "popularity_rank": 41,
    "popularity_change": "+7%",
    "year_2025_rank": 41,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 12100,
    "regional_popularity": {
      "bavaria": 41,
      "north_rhine_westphalia": 41,
      "baden_wurttemberg": 41,
      "berlin": 41
    }
  },
  {
    "name_en": "Jonah",
    "name_native": "Jona",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dove",
    "meaning_native": "Taube",
    "starting_letter": "J",
    "pronunciation": "YO-na",
    "origin": "Hebrew",
    "popularity_rank": 42,
    "popularity_change": "+4%",
    "year_2025_rank": 42,
    "year_2024_rank": 46,
    "trending_status": "rising",
    "search_volume": 11700,
    "regional_popularity": {
      "bavaria": 42,
      "north_rhine_westphalia": 42,
      "baden_wurttemberg": 42,
      "berlin": 42
    }
  },
  {
    "name_en": "Carl",
    "name_native": "Carl",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Free man",
    "meaning_native": "Freier Mann",
    "starting_letter": "C",
    "pronunciation": "KARL",
    "origin": "Germanic",
    "popularity_rank": 43,
    "popularity_change": "+6%",
    "year_2025_rank": 43,
    "year_2024_rank": 49,
    "trending_status": "rising",
    "search_volume": 11300,
    "regional_popularity": {
      "bavaria": 43,
      "north_rhine_westphalia": 43,
      "baden_wurttemberg": 43,
      "berlin": 43
    }
  },
  {
    "name_en": "Leonard",
    "name_native": "Leonard",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Brave lion",
    "meaning_native": "Tapferer Löwe",
    "starting_letter": "L",
    "pronunciation": "LEH-o-nart",
    "origin": "Germanic",
    "popularity_rank": 44,
    "popularity_change": "+9%",
    "year_2025_rank": 44,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 10600,
    "regional_popularity": {
      "bavaria": 44,
      "north_rhine_westphalia": 44,
      "baden_wurttemberg": 44,
      "berlin": 44
    }
  },
  {
    "name_en": "Mattis",
    "name_native": "Mattis",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "MAT-tis",
    "origin": "Hebrew",
    "popularity_rank": 45,
    "popularity_change": "+3%",
    "year_2025_rank": 45,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 11000,
    "regional_popularity": {
      "bavaria": 45,
      "north_rhine_westphalia": 45,
      "baden_wurttemberg": 45,
      "berlin": 45
    }
  },
  {
    "name_en": "Nils",
    "name_native": "Nils",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Sieg des Volkes",
    "starting_letter": "N",
    "pronunciation": "NILS",
    "origin": "Scandinavian",
    "popularity_rank": 46,
    "popularity_change": "+5%",
    "year_2025_rank": 46,
    "year_2024_rank": 51,
    "trending_status": "rising",
    "search_volume": 10200,
    "regional_popularity": {
      "bavaria": 46,
      "north_rhine_westphalia": 46,
      "baden_wurttemberg": 46,
      "berlin": 46
    }
  },
  {
    "name_en": "Robin",
    "name_native": "Robin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright fame",
    "meaning_native": "Heller Ruhm",
    "starting_letter": "R",
    "pronunciation": "RO-bin",
    "origin": "Germanic",
    "popularity_rank": 47,
    "popularity_change": "+2%",
    "year_2025_rank": 47,
    "year_2024_rank": 49,
    "trending_status": "stable",
    "search_volume": 10700,
    "regional_popularity": {
      "bavaria": 47,
      "north_rhine_westphalia": 47,
      "baden_wurttemberg": 47,
      "berlin": 47
    }
  },
  {
    "name_en": "Jannis",
    "name_native": "Jannis",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YAN-nis",
    "origin": "Greek",
    "popularity_rank": 48,
    "popularity_change": "+7%",
    "year_2025_rank": 48,
    "year_2024_rank": 55,
    "trending_status": "rising",
    "search_volume": 9800,
    "regional_popularity": {
      "bavaria": 48,
      "north_rhine_westphalia": 48,
      "baden_wurttemberg": 48,
      "berlin": 48
    }
  },
  {
    "name_en": "Arthur",
    "name_native": "Arthur",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bear, strong",
    "meaning_native": "Bär, stark",
    "starting_letter": "A",
    "pronunciation": "AR-toor",
    "origin": "Celtic",
    "popularity_rank": 49,
    "popularity_change": "+4%",
    "year_2025_rank": 49,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 9400,
    "regional_popularity": {
      "bavaria": 49,
      "north_rhine_westphalia": 49,
      "baden_wurttemberg": 49,
      "berlin": 49
    }
  },
  {
    "name_en": "Caspar",
    "name_native": "Caspar",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Treasure keeper",
    "meaning_native": "Schatzbewahrer",
    "starting_letter": "C",
    "pronunciation": "KAS-par",
    "origin": "Persian",
    "popularity_rank": 50,
    "popularity_change": "+8%",
    "year_2025_rank": 50,
    "year_2024_rank": 58,
    "trending_status": "rising",
    "search_volume": 8900,
    "regional_popularity": {
      "bavaria": 50,
      "north_rhine_westphalia": 50,
      "baden_wurttemberg": 50,
      "berlin": 50
    }
  },
  {
    "name_en": "Linus",
    "name_native": "Linus",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Flax",
    "meaning_native": "Lein",
    "starting_letter": "L",
    "pronunciation": "LEE-nus",
    "origin": "Greek",
    "popularity_rank": 51,
    "popularity_change": "+6%",
    "year_2025_rank": 51,
    "year_2024_rank": 57,
    "trending_status": "rising",
    "search_volume": 8200,
    "regional_popularity": {
      "bavaria": 51,
      "north_rhine_westphalia": 51,
      "baden_wurttemberg": 51,
      "berlin": 51
    }
  },
  {
    "name_en": "Kilian",
    "name_native": "Kilian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Church",
    "meaning_native": "Kirche",
    "starting_letter": "K",
    "pronunciation": "KEE-lee-ahn",
    "origin": "Irish",
    "popularity_rank": 52,
    "popularity_change": "+4%",
    "year_2025_rank": 52,
    "year_2024_rank": 56,
    "trending_status": "rising",
    "search_volume": 7500,
    "regional_popularity": {
      "bavaria": 52,
      "north_rhine_westphalia": 52,
      "baden_wurttemberg": 52,
      "berlin": 52
    }
  },
  {
    "name_en": "Mats",
    "name_native": "Mats",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "MAHTS",
    "origin": "Hebrew",
    "popularity_rank": 53,
    "popularity_change": "+2%",
    "year_2025_rank": 53,
    "year_2024_rank": 55,
    "trending_status": "rising",
    "search_volume": 6800,
    "regional_popularity": {
      "bavaria": 53,
      "north_rhine_westphalia": 53,
      "baden_wurttemberg": 53,
      "berlin": 53
    }
  },
  {
    "name_en": "Timo",
    "name_native": "Timo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honoring God",
    "meaning_native": "Gott ehren",
    "starting_letter": "T",
    "pronunciation": "TEE-moh",
    "origin": "Greek",
    "popularity_rank": 54,
    "popularity_change": "+3%",
    "year_2025_rank": 54,
    "year_2024_rank": 57,
    "trending_status": "rising",
    "search_volume": 6100,
    "regional_popularity": {
      "bavaria": 54,
      "north_rhine_westphalia": 54,
      "baden_wurttemberg": 54,
      "berlin": 54
    }
  },
  {
    "name_en": "Vincent",
    "name_native": "Vincent",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Conquering",
    "meaning_native": "Sieger",
    "starting_letter": "V",
    "pronunciation": "VIN-tsent",
    "origin": "Latin",
    "popularity_rank": 55,
    "popularity_change": "+5%",
    "year_2025_rank": 55,
    "year_2024_rank": 60,
    "trending_status": "rising",
    "search_volume": 5400,
    "regional_popularity": {
      "bavaria": 55,
      "north_rhine_westphalia": 55,
      "baden_wurttemberg": 55,
      "berlin": 55
    }
  },
  {
    "name_en": "Niklas",
    "name_native": "Niklas",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Sieg des Volkes",
    "starting_letter": "N",
    "pronunciation": "NEEK-lahs",
    "origin": "Greek",
    "popularity_rank": 56,
    "popularity_change": "+3%",
    "year_2025_rank": 56,
    "year_2024_rank": 59,
    "trending_status": "rising",
    "search_volume": 4700,
    "regional_popularity": {
      "bavaria": 56,
      "north_rhine_westphalia": 56,
      "baden_wurttemberg": 56,
      "berlin": 56
    }
  },
  {
    "name_en": "Liam",
    "name_native": "Liam",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong-willed warrior",
    "meaning_native": "Starker Krieger",
    "starting_letter": "L",
    "pronunciation": "LEE-ahm",
    "origin": "Irish",
    "popularity_rank": 57,
    "popularity_change": "+6%",
    "year_2025_rank": 57,
    "year_2024_rank": 63,
    "trending_status": "rising",
    "search_volume": 4000,
    "regional_popularity": {
      "bavaria": 57,
      "north_rhine_westphalia": 57,
      "baden_wurttemberg": 57,
      "berlin": 57
    }
  },
  {
    "name_en": "Oskar",
    "name_native": "Oskar",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Divine spear",
    "meaning_native": "Göttlicher Speer",
    "starting_letter": "O",
    "pronunciation": "OS-kar",
    "origin": "Germanic",
    "popularity_rank": 58,
    "popularity_change": "+4%",
    "year_2025_rank": 58,
    "year_2024_rank": 62,
    "trending_status": "rising",
    "search_volume": 3300,
    "regional_popularity": {
      "bavaria": 58,
      "north_rhine_westphalia": 58,
      "baden_wurttemberg": 58,
      "berlin": 58
    }
  },
  {
    "name_en": "Matteo",
    "name_native": "Matteo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "mat-TEH-oh",
    "origin": "Hebrew",
    "popularity_rank": 59,
    "popularity_change": "+7%",
    "year_2025_rank": 59,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 2600,
    "regional_popularity": {
      "bavaria": 59,
      "north_rhine_westphalia": 59,
      "baden_wurttemberg": 59,
      "berlin": 59
    }
  },
  {
    "name_en": "Theo",
    "name_native": "Theo",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God's gift",
    "meaning_native": "Gottes Geschenk",
    "starting_letter": "T",
    "pronunciation": "TEH-oh",
    "origin": "Greek",
    "popularity_rank": 60,
    "popularity_change": "+2%",
    "year_2025_rank": 60,
    "year_2024_rank": 62,
    "trending_status": "rising",
    "search_volume": 1900,
    "regional_popularity": {
      "bavaria": 60,
      "north_rhine_westphalia": 60,
      "baden_wurttemberg": 60,
      "berlin": 60
    }
  },
  {
    "name_en": "Finn",
    "name_native": "Finn",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Fair, white",
    "meaning_native": "Hell, weiß",
    "starting_letter": "F",
    "pronunciation": "FIN",
    "origin": "Irish",
    "popularity_rank": 61,
    "popularity_change": "+5%",
    "year_2025_rank": 61,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 1200,
    "regional_popularity": {
      "bavaria": 61,
      "north_rhine_westphalia": 61,
      "baden_wurttemberg": 61,
      "berlin": 61
    }
  },
  {
    "name_en": "Lennard",
    "name_native": "Lennard",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Brave lion",
    "meaning_native": "Tapferer Löwe",
    "starting_letter": "L",
    "pronunciation": "LEN-nart",
    "origin": "Germanic",
    "popularity_rank": 62,
    "popularity_change": "+3%",
    "year_2025_rank": 62,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 500,
    "regional_popularity": {
      "bavaria": 62,
      "north_rhine_westphalia": 62,
      "baden_wurttemberg": 62,
      "berlin": 62
    }
  },
  {
    "name_en": "Jannik",
    "name_native": "Jannik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YAN-nik",
    "origin": "Hebrew",
    "popularity_rank": 63,
    "popularity_change": "+4%",
    "year_2025_rank": 63,
    "year_2024_rank": 67,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 63,
      "north_rhine_westphalia": 63,
      "baden_wurttemberg": 63,
      "berlin": 63
    }
  },
  {
    "name_en": "Moritz",
    "name_native": "Moritz",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dark-skinned",
    "meaning_native": "Dunkelhäutig",
    "starting_letter": "M",
    "pronunciation": "MO-rits",
    "origin": "Latin",
    "popularity_rank": 64,
    "popularity_change": "+2%",
    "year_2025_rank": 64,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 64,
      "north_rhine_westphalia": 64,
      "baden_wurttemberg": 64,
      "berlin": 64
    }
  },
  {
    "name_en": "Hannes",
    "name_native": "Hannes",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "H",
    "pronunciation": "HAN-nes",
    "origin": "Hebrew",
    "popularity_rank": 65,
    "popularity_change": "+6%",
    "year_2025_rank": 65,
    "year_2024_rank": 71,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 65,
      "north_rhine_westphalia": 65,
      "baden_wurttemberg": 65,
      "berlin": 65
    }
  },
  {
    "name_en": "Matthias",
    "name_native": "Matthias",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "M",
    "pronunciation": "mah-TEE-ahs",
    "origin": "Hebrew",
    "popularity_rank": 66,
    "popularity_change": "+3%",
    "year_2025_rank": 66,
    "year_2024_rank": 69,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 66,
      "north_rhine_westphalia": 66,
      "baden_wurttemberg": 66,
      "berlin": 66
    }
  },
  {
    "name_en": "Fabian",
    "name_native": "Fabian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bean grower",
    "meaning_native": "Bohnenzüchter",
    "starting_letter": "F",
    "pronunciation": "FAH-bee-ahn",
    "origin": "Latin",
    "popularity_rank": 67,
    "popularity_change": "+2%",
    "year_2025_rank": 67,
    "year_2024_rank": 69,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 67,
      "north_rhine_westphalia": 67,
      "baden_wurttemberg": 67,
      "berlin": 67
    }
  },
  {
    "name_en": "Tobias",
    "name_native": "Tobias",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is good",
    "meaning_native": "Gott ist gut",
    "starting_letter": "T",
    "pronunciation": "to-BEE-ahs",
    "origin": "Hebrew",
    "popularity_rank": 68,
    "popularity_change": "+4%",
    "year_2025_rank": 68,
    "year_2024_rank": 72,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 68,
      "north_rhine_westphalia": 68,
      "baden_wurttemberg": 68,
      "berlin": 68
    }
  },
  {
    "name_en": "Sebastian",
    "name_native": "Sebastian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Venerable",
    "meaning_native": "Ehrwürdig",
    "starting_letter": "S",
    "pronunciation": "se-BAS-tee-ahn",
    "origin": "Greek",
    "popularity_rank": 69,
    "popularity_change": "+1%",
    "year_2025_rank": 69,
    "year_2024_rank": 70,
    "trending_status": "stable",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 69,
      "north_rhine_westphalia": 69,
      "baden_wurttemberg": 69,
      "berlin": 69
    }
  },
  {
    "name_en": "Florian",
    "name_native": "Florian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Flowering",
    "meaning_native": "Blühend",
    "starting_letter": "F",
    "pronunciation": "FLO-ree-ahn",
    "origin": "Latin",
    "popularity_rank": 70,
    "popularity_change": "+2%",
    "year_2025_rank": 70,
    "year_2024_rank": 72,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 70,
      "north_rhine_westphalia": 70,
      "baden_wurttemberg": 70,
      "berlin": 70
    }
  },
  {
    "name_en": "Dominik",
    "name_native": "Dominik",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Belonging to the Lord",
    "meaning_native": "Dem Herrn gehörend",
    "starting_letter": "D",
    "pronunciation": "do-mee-NEEK",
    "origin": "Latin",
    "popularity_rank": 71,
    "popularity_change": "+3%",
    "year_2025_rank": 71,
    "year_2024_rank": 74,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 71,
      "north_rhine_westphalia": 71,
      "baden_wurttemberg": 71,
      "berlin": 71
    }
  },
  {
    "name_en": "Philipp",
    "name_native": "Philipp",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lover of horses",
    "meaning_native": "Pferdeliebhaber",
    "starting_letter": "P",
    "pronunciation": "FEE-lip",
    "origin": "Greek",
    "popularity_rank": 72,
    "popularity_change": "+1%",
    "year_2025_rank": 72,
    "year_2024_rank": 73,
    "trending_status": "stable",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 72,
      "north_rhine_westphalia": 72,
      "baden_wurttemberg": 72,
      "berlin": 72
    }
  },
  {
    "name_en": "Moritz",
    "name_native": "Moritz",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dark-skinned",
    "meaning_native": "Dunkelhäutig",
    "starting_letter": "M",
    "pronunciation": "MO-rits",
    "origin": "Latin",
    "popularity_rank": 73,
    "popularity_change": "+2%",
    "year_2025_rank": 73,
    "year_2024_rank": 75,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 73,
      "north_rhine_westphalia": 73,
      "baden_wurttemberg": 73,
      "berlin": 73
    }
  },
  {
    "name_en": "Adrian",
    "name_native": "Adrian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "From Hadria",
    "meaning_native": "Aus Hadria",
    "starting_letter": "A",
    "pronunciation": "AH-dree-ahn",
    "origin": "Latin",
    "popularity_rank": 74,
    "popularity_change": "+4%",
    "year_2025_rank": 74,
    "year_2024_rank": 78,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 74,
      "north_rhine_westphalia": 74,
      "baden_wurttemberg": 74,
      "berlin": 74
    }
  },
  {
    "name_en": "Maximilian",
    "name_native": "Maximilian",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Greatest",
    "meaning_native": "Größter",
    "starting_letter": "M",
    "pronunciation": "mak-see-mee-lee-AHN",
    "origin": "Latin",
    "popularity_rank": 75,
    "popularity_change": "+3%",
    "year_2025_rank": 75,
    "year_2024_rank": 78,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 75,
      "north_rhine_westphalia": 75,
      "baden_wurttemberg": 75,
      "berlin": 75
    }
  },
  {
    "name_en": "Johannes",
    "name_native": "Johannes",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "yo-HAH-nes",
    "origin": "Hebrew",
    "popularity_rank": 76,
    "popularity_change": "+2%",
    "year_2025_rank": 76,
    "year_2024_rank": 78,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 76,
      "north_rhine_westphalia": 76,
      "baden_wurttemberg": 76,
      "berlin": 76
    }
  },
  {
    "name_en": "Constantin",
    "name_native": "Constantin",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Constant",
    "meaning_native": "Beständig",
    "starting_letter": "C",
    "pronunciation": "kon-stan-TEEN",
    "origin": "Latin",
    "popularity_rank": 77,
    "popularity_change": "+4%",
    "year_2025_rank": 77,
    "year_2024_rank": 81,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 77,
      "north_rhine_westphalia": 77,
      "baden_wurttemberg": 77,
      "berlin": 77
    }
  },
  {
    "name_en": "Benedikt",
    "name_native": "Benedikt",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Blessed",
    "meaning_native": "Gesegnet",
    "starting_letter": "B",
    "pronunciation": "be-ne-DIKT",
    "origin": "Latin",
    "popularity_rank": 78,
    "popularity_change": "+3%",
    "year_2025_rank": 78,
    "year_2024_rank": 81,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 78,
      "north_rhine_westphalia": 78,
      "baden_wurttemberg": 78,
      "berlin": 78
    }
  },
  {
    "name_en": "Vincent",
    "name_native": "Vincent",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Conquering",
    "meaning_native": "Siegend",
    "starting_letter": "V",
    "pronunciation": "VIN-tsent",
    "origin": "Latin",
    "popularity_rank": 79,
    "popularity_change": "+5%",
    "year_2025_rank": 79,
    "year_2024_rank": 84,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 79,
      "north_rhine_westphalia": 79,
      "baden_wurttemberg": 79,
      "berlin": 79
    }
  },
  {
    "name_en": "Clemens",
    "name_native": "Clemens",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Merciful",
    "meaning_native": "Barmherzig",
    "starting_letter": "C",
    "pronunciation": "KLAY-mens",
    "origin": "Latin",
    "popularity_rank": 80,
    "popularity_change": "+2%",
    "year_2025_rank": 80,
    "year_2024_rank": 82,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 80,
      "north_rhine_westphalia": 80,
      "baden_wurttemberg": 80,
      "berlin": 80
    }
  }
]

export default GermanBoyNames;
