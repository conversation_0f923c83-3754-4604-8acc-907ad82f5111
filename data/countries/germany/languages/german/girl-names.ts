import type { NameData } from "@/types/name-data";

export const GermanGirlNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rival, eager",
    "meaning_native": "Eif<PERSON>, wetteifern",
    "starting_letter": "E",
    "pronunciation": "eh-MEE-lee-ah",
    "origin": "Latin",
    "popularity_rank": 1,
    "popularity_change": "+3%",
    "year_2025_rank": 1,
    "year_2024_rank": 4,
    "trending_status": "rising",
    "search_volume": 112000,
    "regional_popularity": {
      "bavaria": 1,
      "north_rhine_westphalia": 2,
      "baden_wurttemberg": 1,
      "berlin": 1
    }
  },
  {
    "name_en": "<PERSON>",
    "name_native": "Hannah",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace, favor",
    "meaning_native": "<PERSON>nade, Gunst",
    "starting_letter": "H",
    "pronunciation": "HAN-nah",
    "origin": "Hebrew",
    "popularity_rank": 2,
    "popularity_change": "+1%",
    "year_2025_rank": 2,
    "year_2024_rank": 3,
    "trending_status": "stable",
    "search_volume": 98600,
    "regional_popularity": {
      "bavaria": 2,
      "north_rhine_westphalia": 1,
      "baden_wurttemberg": 2,
      "berlin": 3
    }
  },
  {
    "name_en": "Emma",
    "name_native": "Emma",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Whole, universal",
    "meaning_native": "Ganz, universal",
    "starting_letter": "E",
    "pronunciation": "EM-mah",
    "origin": "Germanic",
    "popularity_rank": 3,
    "popularity_change": "-1%",
    "year_2025_rank": 3,
    "year_2024_rank": 2,
    "trending_status": "stable",
    "search_volume": 104500,
    "regional_popularity": {
      "bavaria": 3,
      "north_rhine_westphalia": 3,
      "baden_wurttemberg": 3,
      "berlin": 2
    }
  },
  {
    "name_en": "Sophia",
    "name_native": "Sophia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Wisdom",
    "meaning_native": "Weisheit",
    "starting_letter": "S",
    "pronunciation": "so-FEE-ah",
    "origin": "Greek",
    "popularity_rank": 4,
    "popularity_change": "+2%",
    "year_2025_rank": 4,
    "year_2024_rank": 6,
    "trending_status": "rising",
    "search_volume": 87400,
    "regional_popularity": {
      "bavaria": 4,
      "north_rhine_westphalia": 4,
      "baden_wurttemberg": 4,
      "berlin": 4
    }
  },
  {
    "name_en": "Lina",
    "name_native": "Lina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Tender, delicate",
    "meaning_native": "Zart, zärtlich",
    "starting_letter": "L",
    "pronunciation": "LEE-nah",
    "origin": "Arabic",
    "popularity_rank": 5,
    "popularity_change": "+4%",
    "year_2025_rank": 5,
    "year_2024_rank": 9,
    "trending_status": "rising",
    "search_volume": 73200,
    "regional_popularity": {
      "bavaria": 6,
      "north_rhine_westphalia": 5,
      "baden_wurttemberg": 5,
      "berlin": 5
    }
  },
  {
    "name_en": "Mia",
    "name_native": "Mia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Mine, beloved",
    "meaning_native": "Mein, geliebt",
    "starting_letter": "M",
    "pronunciation": "MEE-ah",
    "origin": "Italian",
    "popularity_rank": 6,
    "popularity_change": "-2%",
    "year_2025_rank": 6,
    "year_2024_rank": 4,
    "trending_status": "stable",
    "search_volume": 91300,
    "regional_popularity": {
      "bavaria": 5,
      "north_rhine_westphalia": 6,
      "baden_wurttemberg": 6,
      "berlin": 6
    }
  },
  {
    "name_en": "Clara",
    "name_native": "Clara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright, clear",
    "meaning_native": "Hell, klar",
    "starting_letter": "C",
    "pronunciation": "KLAH-rah",
    "origin": "Latin",
    "popularity_rank": 7,
    "popularity_change": "+3%",
    "year_2025_rank": 7,
    "year_2024_rank": 10,
    "trending_status": "rising",
    "search_volume": 65800,
    "regional_popularity": {
      "bavaria": 7,
      "north_rhine_westphalia": 7,
      "baden_wurttemberg": 7,
      "berlin": 8
    }
  },
  {
    "name_en": "Lea",
    "name_native": "Lea",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Weary, meadow",
    "meaning_native": "Müde, Wiese",
    "starting_letter": "L",
    "pronunciation": "LEH-ah",
    "origin": "Hebrew",
    "popularity_rank": 8,
    "popularity_change": "+1%",
    "year_2025_rank": 8,
    "year_2024_rank": 9,
    "trending_status": "stable",
    "search_volume": 58400,
    "regional_popularity": {
      "bavaria": 8,
      "north_rhine_westphalia": 8,
      "baden_wurttemberg": 8,
      "berlin": 7
    }
  },
  {
    "name_en": "Leni",
    "name_native": "Leni",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "L",
    "pronunciation": "LEH-nee",
    "origin": "German",
    "popularity_rank": 9,
    "popularity_change": "+6%",
    "year_2025_rank": 9,
    "year_2024_rank": 15,
    "trending_status": "rising",
    "search_volume": 47300,
    "regional_popularity": {
      "bavaria": 9,
      "north_rhine_westphalia": 10,
      "baden_wurttemberg": 9,
      "berlin": 9
    }
  },
  {
    "name_en": "Ella",
    "name_native": "Ella",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "All, completely",
    "meaning_native": "Alle, vollständig",
    "starting_letter": "E",
    "pronunciation": "EL-lah",
    "origin": "Germanic",
    "popularity_rank": 10,
    "popularity_change": "+2%",
    "year_2025_rank": 10,
    "year_2024_rank": 12,
    "trending_status": "rising",
    "search_volume": 52100,
    "regional_popularity": {
      "bavaria": 10,
      "north_rhine_westphalia": 9,
      "baden_wurttemberg": 10,
      "berlin": 10
    }
  },
  {
    "name_en": "Anna",
    "name_native": "Anna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace, favor",
    "meaning_native": "Gnade, Gunst",
    "starting_letter": "A",
    "pronunciation": "AH-nah",
    "origin": "Hebrew",
    "popularity_rank": 11,
    "popularity_change": "+1%",
    "year_2025_rank": 11,
    "year_2024_rank": 12,
    "trending_status": "stable",
    "search_volume": 48700,
    "regional_popularity": {
      "bavaria": 11,
      "north_rhine_westphalia": 11,
      "baden_wurttemberg": 11,
      "berlin": 11
    }
  },
  {
    "name_en": "Lia",
    "name_native": "Lia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Weary, meadow",
    "meaning_native": "Müde, Wiese",
    "starting_letter": "L",
    "pronunciation": "LEE-ah",
    "origin": "Hebrew",
    "popularity_rank": 12,
    "popularity_change": "+5%",
    "year_2025_rank": 12,
    "year_2024_rank": 17,
    "trending_status": "rising",
    "search_volume": 42300,
    "regional_popularity": {
      "bavaria": 12,
      "north_rhine_westphalia": 12,
      "baden_wurttemberg": 12,
      "berlin": 12
    }
  },
  {
    "name_en": "Maja",
    "name_native": "Maja",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Great, mother",
    "meaning_native": "Groß, Mutter",
    "starting_letter": "M",
    "pronunciation": "MAH-yah",
    "origin": "Sanskrit",
    "popularity_rank": 13,
    "popularity_change": "+3%",
    "year_2025_rank": 13,
    "year_2024_rank": 16,
    "trending_status": "rising",
    "search_volume": 39600,
    "regional_popularity": {
      "bavaria": 13,
      "north_rhine_westphalia": 13,
      "baden_wurttemberg": 13,
      "berlin": 13
    }
  },
  {
    "name_en": "Ida",
    "name_native": "Ida",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Industrious, work",
    "meaning_native": "Fleißig, Arbeit",
    "starting_letter": "I",
    "pronunciation": "EE-dah",
    "origin": "Germanic",
    "popularity_rank": 14,
    "popularity_change": "+4%",
    "year_2025_rank": 14,
    "year_2024_rank": 18,
    "trending_status": "rising",
    "search_volume": 37200,
    "regional_popularity": {
      "bavaria": 14,
      "north_rhine_westphalia": 14,
      "baden_wurttemberg": 14,
      "berlin": 14
    }
  },
  {
    "name_en": "Nora",
    "name_native": "Nora",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honor, light",
    "meaning_native": "Ehre, Licht",
    "starting_letter": "N",
    "pronunciation": "NO-rah",
    "origin": "Irish",
    "popularity_rank": 15,
    "popularity_change": "+2%",
    "year_2025_rank": 15,
    "year_2024_rank": 17,
    "trending_status": "stable",
    "search_volume": 38900,
    "regional_popularity": {
      "bavaria": 15,
      "north_rhine_westphalia": 15,
      "baden_wurttemberg": 15,
      "berlin": 15
    }
  },
  {
    "name_en": "Josephine",
    "name_native": "Josephine",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God will add",
    "meaning_native": "Gott wird hinzufügen",
    "starting_letter": "J",
    "pronunciation": "yo-zeh-FEE-neh",
    "origin": "Hebrew",
    "popularity_rank": 16,
    "popularity_change": "+6%",
    "year_2025_rank": 16,
    "year_2024_rank": 22,
    "trending_status": "rising",
    "search_volume": 34500,
    "regional_popularity": {
      "bavaria": 16,
      "north_rhine_westphalia": 16,
      "baden_wurttemberg": 16,
      "berlin": 16
    }
  },
  {
    "name_en": "Greta",
    "name_native": "Greta",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "G",
    "pronunciation": "GREH-tah",
    "origin": "Germanic",
    "popularity_rank": 17,
    "popularity_change": "+7%",
    "year_2025_rank": 17,
    "year_2024_rank": 24,
    "trending_status": "rising",
    "search_volume": 32100,
    "regional_popularity": {
      "bavaria": 17,
      "north_rhine_westphalia": 17,
      "baden_wurttemberg": 17,
      "berlin": 17
    }
  },
  {
    "name_en": "Frieda",
    "name_native": "Frieda",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Peace",
    "meaning_native": "Frieden",
    "starting_letter": "F",
    "pronunciation": "FREE-dah",
    "origin": "Germanic",
    "popularity_rank": 18,
    "popularity_change": "+5%",
    "year_2025_rank": 18,
    "year_2024_rank": 23,
    "trending_status": "rising",
    "search_volume": 29800,
    "regional_popularity": {
      "bavaria": 18,
      "north_rhine_westphalia": 18,
      "baden_wurttemberg": 18,
      "berlin": 18
    }
  },
  {
    "name_en": "Lotte",
    "name_native": "Lotte",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Free man",
    "meaning_native": "Freier Mann",
    "starting_letter": "L",
    "pronunciation": "LOT-teh",
    "origin": "Germanic",
    "popularity_rank": 19,
    "popularity_change": "+8%",
    "year_2025_rank": 19,
    "year_2024_rank": 27,
    "trending_status": "rising",
    "search_volume": 27400,
    "regional_popularity": {
      "bavaria": 19,
      "north_rhine_westphalia": 19,
      "baden_wurttemberg": 19,
      "berlin": 19
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amelie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Industrious, eager",
    "meaning_native": "Fleißig, eifrig",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 20,
    "popularity_change": "+3%",
    "year_2025_rank": 20,
    "year_2024_rank": 23,
    "trending_status": "rising",
    "search_volume": 30600,
    "regional_popularity": {
      "bavaria": 20,
      "north_rhine_westphalia": 20,
      "baden_wurttemberg": 20,
      "berlin": 20
    }
  },
  {
    "name_en": "Luna",
    "name_native": "Luna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Moon",
    "meaning_native": "Mond",
    "starting_letter": "L",
    "pronunciation": "LOO-nah",
    "origin": "Latin",
    "popularity_rank": 21,
    "popularity_change": "+9%",
    "year_2025_rank": 21,
    "year_2024_rank": 30,
    "trending_status": "rising",
    "search_volume": 25800,
    "regional_popularity": {
      "bavaria": 21,
      "north_rhine_westphalia": 21,
      "baden_wurttemberg": 21,
      "berlin": 21
    }
  },
  {
    "name_en": "Mathilda",
    "name_native": "Mathilda",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Mighty in battle",
    "meaning_native": "Mächtig im Kampf",
    "starting_letter": "M",
    "pronunciation": "mah-TIL-dah",
    "origin": "Germanic",
    "popularity_rank": 22,
    "popularity_change": "+4%",
    "year_2025_rank": 22,
    "year_2024_rank": 26,
    "trending_status": "rising",
    "search_volume": 24700,
    "regional_popularity": {
      "bavaria": 22,
      "north_rhine_westphalia": 22,
      "baden_wurttemberg": 22,
      "berlin": 22
    }
  },
  {
    "name_en": "Charlotte",
    "name_native": "Charlotte",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Free man",
    "meaning_native": "Freier Mann",
    "starting_letter": "C",
    "pronunciation": "shar-LOT-teh",
    "origin": "Germanic",
    "popularity_rank": 23,
    "popularity_change": "+2%",
    "year_2025_rank": 23,
    "year_2024_rank": 25,
    "trending_status": "stable",
    "search_volume": 26300,
    "regional_popularity": {
      "bavaria": 23,
      "north_rhine_westphalia": 23,
      "baden_wurttemberg": 23,
      "berlin": 23
    }
  },
  {
    "name_en": "Marie",
    "name_native": "Marie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bitter, beloved",
    "meaning_native": "Bitter, geliebt",
    "starting_letter": "M",
    "pronunciation": "mah-REE",
    "origin": "Hebrew",
    "popularity_rank": 24,
    "popularity_change": "+1%",
    "year_2025_rank": 24,
    "year_2024_rank": 25,
    "trending_status": "stable",
    "search_volume": 23900,
    "regional_popularity": {
      "bavaria": 24,
      "north_rhine_westphalia": 24,
      "baden_wurttemberg": 24,
      "berlin": 24
    }
  },
  {
    "name_en": "Paula",
    "name_native": "Paula",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Small",
    "meaning_native": "Klein",
    "starting_letter": "P",
    "pronunciation": "PAU-lah",
    "origin": "Latin",
    "popularity_rank": 25,
    "popularity_change": "+3%",
    "year_2025_rank": 25,
    "year_2024_rank": 28,
    "trending_status": "rising",
    "search_volume": 22500,
    "regional_popularity": {
      "bavaria": 25,
      "north_rhine_westphalia": 25,
      "baden_wurttemberg": 25,
      "berlin": 25
    }
  },
  {
    "name_en": "Elisa",
    "name_native": "Elisa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my oath",
    "meaning_native": "Gott ist mein Eid",
    "starting_letter": "E",
    "pronunciation": "eh-LEE-zah",
    "origin": "Hebrew",
    "popularity_rank": 26,
    "popularity_change": "+5%",
    "year_2025_rank": 26,
    "year_2024_rank": 31,
    "trending_status": "rising",
    "search_volume": 21200,
    "regional_popularity": {
      "bavaria": 26,
      "north_rhine_westphalia": 26,
      "baden_wurttemberg": 26,
      "berlin": 26
    }
  },
  {
    "name_en": "Helena",
    "name_native": "Helena",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "H",
    "pronunciation": "heh-LEH-nah",
    "origin": "Greek",
    "popularity_rank": 27,
    "popularity_change": "+6%",
    "year_2025_rank": 27,
    "year_2024_rank": 33,
    "trending_status": "rising",
    "search_volume": 19800,
    "regional_popularity": {
      "bavaria": 27,
      "north_rhine_westphalia": 27,
      "baden_wurttemberg": 27,
      "berlin": 27
    }
  },
  {
    "name_en": "Laura",
    "name_native": "Laura",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Laurel, victory",
    "meaning_native": "Lorbeer, Sieg",
    "starting_letter": "L",
    "pronunciation": "LAU-rah",
    "origin": "Latin",
    "popularity_rank": 28,
    "popularity_change": "+1%",
    "year_2025_rank": 28,
    "year_2024_rank": 29,
    "trending_status": "stable",
    "search_volume": 20700,
    "regional_popularity": {
      "bavaria": 28,
      "north_rhine_westphalia": 28,
      "baden_wurttemberg": 28,
      "berlin": 28
    }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoe",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "Z",
    "pronunciation": "TSO-eh",
    "origin": "Greek",
    "popularity_rank": 29,
    "popularity_change": "+7%",
    "year_2025_rank": 29,
    "year_2024_rank": 36,
    "trending_status": "rising",
    "search_volume": 18400,
    "regional_popularity": {
      "bavaria": 29,
      "north_rhine_westphalia": 29,
      "baden_wurttemberg": 29,
      "berlin": 29
    }
  },
  {
    "name_en": "Marlene",
    "name_native": "Marlene",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bitter grace",
    "meaning_native": "Bittere Gnade",
    "starting_letter": "M",
    "pronunciation": "mar-LEH-neh",
    "origin": "Germanic",
    "popularity_rank": 30,
    "popularity_change": "+4%",
    "year_2025_rank": 30,
    "year_2024_rank": 34,
    "trending_status": "rising",
    "search_volume": 19100,
    "regional_popularity": {
      "bavaria": 30,
      "north_rhine_westphalia": 30,
      "baden_wurttemberg": 30,
      "berlin": 30
    }
  },
  {
    "name_en": "Fiona",
    "name_native": "Fiona",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Fair, white",
    "meaning_native": "Schön, weiß",
    "starting_letter": "F",
    "pronunciation": "fee-O-nah",
    "origin": "Irish",
    "popularity_rank": 31,
    "popularity_change": "+8%",
    "year_2025_rank": 31,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 17200,
    "regional_popularity": {
      "bavaria": 31,
      "north_rhine_westphalia": 31,
      "baden_wurttemberg": 31,
      "berlin": 31
    }
  },
  {
    "name_en": "Luisa",
    "name_native": "Luisa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Famous warrior",
    "meaning_native": "Berühmte Kriegerin",
    "starting_letter": "L",
    "pronunciation": "loo-EE-zah",
    "origin": "Germanic",
    "popularity_rank": 32,
    "popularity_change": "+3%",
    "year_2025_rank": 32,
    "year_2024_rank": 35,
    "trending_status": "rising",
    "search_volume": 18600,
    "regional_popularity": {
      "bavaria": 32,
      "north_rhine_westphalia": 32,
      "baden_wurttemberg": 32,
      "berlin": 32
    }
  },
  {
    "name_en": "Johanna",
    "name_native": "Johanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "yo-HAN-nah",
    "origin": "Hebrew",
    "popularity_rank": 33,
    "popularity_change": "+2%",
    "year_2025_rank": 33,
    "year_2024_rank": 35,
    "trending_status": "stable",
    "search_volume": 17900,
    "regional_popularity": {
      "bavaria": 33,
      "north_rhine_westphalia": 33,
      "baden_wurttemberg": 33,
      "berlin": 33
    }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Cheerful, protection",
    "meaning_native": "Fröhlich, Schutz",
    "starting_letter": "L",
    "pronunciation": "LAH-rah",
    "origin": "Latin",
    "popularity_rank": 34,
    "popularity_change": "+1%",
    "year_2025_rank": 34,
    "year_2024_rank": 35,
    "trending_status": "stable",
    "search_volume": 16800,
    "regional_popularity": {
      "bavaria": 34,
      "north_rhine_westphalia": 34,
      "baden_wurttemberg": 34,
      "berlin": 34
    }
  },
  {
    "name_en": "Antonia",
    "name_native": "Antonia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Praiseworthy",
    "meaning_native": "Lobenswert",
    "starting_letter": "A",
    "pronunciation": "an-TO-nee-ah",
    "origin": "Latin",
    "popularity_rank": 35,
    "popularity_change": "+5%",
    "year_2025_rank": 35,
    "year_2024_rank": 40,
    "trending_status": "rising",
    "search_volume": 15400,
    "regional_popularity": {
      "bavaria": 35,
      "north_rhine_westphalia": 35,
      "baden_wurttemberg": 35,
      "berlin": 35
    }
  },
  {
    "name_en": "Theresa",
    "name_native": "Theresa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Harvester",
    "meaning_native": "Ernterin",
    "starting_letter": "T",
    "pronunciation": "teh-REH-zah",
    "origin": "Greek",
    "popularity_rank": 36,
    "popularity_change": "+3%",
    "year_2025_rank": 36,
    "year_2024_rank": 39,
    "trending_status": "rising",
    "search_volume": 16100,
    "regional_popularity": {
      "bavaria": 36,
      "north_rhine_westphalia": 36,
      "baden_wurttemberg": 36,
      "berlin": 36
    }
  },
  {
    "name_en": "Victoria",
    "name_native": "Victoria",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory",
    "meaning_native": "Sieg",
    "starting_letter": "V",
    "pronunciation": "vik-TO-ree-ah",
    "origin": "Latin",
    "popularity_rank": 37,
    "popularity_change": "+6%",
    "year_2025_rank": 37,
    "year_2024_rank": 43,
    "trending_status": "rising",
    "search_volume": 14700,
    "regional_popularity": {
      "bavaria": 37,
      "north_rhine_westphalia": 37,
      "baden_wurttemberg": 37,
      "berlin": 37
    }
  },
  {
    "name_en": "Carla",
    "name_native": "Carla",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Free man",
    "meaning_native": "Freier Mann",
    "starting_letter": "C",
    "pronunciation": "KAR-lah",
    "origin": "Germanic",
    "popularity_rank": 38,
    "popularity_change": "+4%",
    "year_2025_rank": 38,
    "year_2024_rank": 42,
    "trending_status": "rising",
    "search_volume": 15300,
    "regional_popularity": {
      "bavaria": 38,
      "north_rhine_westphalia": 38,
      "baden_wurttemberg": 38,
      "berlin": 38
    }
  },
  {
    "name_en": "Isabell",
    "name_native": "Isabell",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my oath",
    "meaning_native": "Gott ist mein Eid",
    "starting_letter": "I",
    "pronunciation": "ee-zah-BELL",
    "origin": "Hebrew",
    "popularity_rank": 39,
    "popularity_change": "+2%",
    "year_2025_rank": 39,
    "year_2024_rank": 41,
    "trending_status": "stable",
    "search_volume": 14900,
    "regional_popularity": {
      "bavaria": 39,
      "north_rhine_westphalia": 39,
      "baden_wurttemberg": 39,
      "berlin": 39
    }
  },
  {
    "name_en": "Emilie",
    "name_native": "Emilie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rival, eager",
    "meaning_native": "Rivale, eifrig",
    "starting_letter": "E",
    "pronunciation": "eh-mee-LEE",
    "origin": "Latin",
    "popularity_rank": 40,
    "popularity_change": "+7%",
    "year_2025_rank": 40,
    "year_2024_rank": 47,
    "trending_status": "rising",
    "search_volume": 13600,
    "regional_popularity": {
      "bavaria": 40,
      "north_rhine_westphalia": 40,
      "baden_wurttemberg": 40,
      "berlin": 40
    }
  },
  {
    "name_en": "Romy",
    "name_native": "Romy",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Dew of the sea",
    "meaning_native": "Tau des Meeres",
    "starting_letter": "R",
    "pronunciation": "RO-mee",
    "origin": "Latin",
    "popularity_rank": 41,
    "popularity_change": "+9%",
    "year_2025_rank": 41,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 12800,
    "regional_popularity": {
      "bavaria": 41,
      "north_rhine_westphalia": 41,
      "baden_wurttemberg": 41,
      "berlin": 41
    }
  },
  {
    "name_en": "Stella",
    "name_native": "Stella",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Star",
    "meaning_native": "Stern",
    "starting_letter": "S",
    "pronunciation": "SHTEL-lah",
    "origin": "Latin",
    "popularity_rank": 42,
    "popularity_change": "+5%",
    "year_2025_rank": 42,
    "year_2024_rank": 47,
    "trending_status": "rising",
    "search_volume": 13100,
    "regional_popularity": {
      "bavaria": 42,
      "north_rhine_westphalia": 42,
      "baden_wurttemberg": 42,
      "berlin": 42
    }
  },
  {
    "name_en": "Valentina",
    "name_native": "Valentina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Stark, gesund",
    "starting_letter": "V",
    "pronunciation": "vah-len-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 43,
    "popularity_change": "+6%",
    "year_2025_rank": 43,
    "year_2024_rank": 49,
    "trending_status": "rising",
    "search_volume": 12300,
    "regional_popularity": {
      "bavaria": 43,
      "north_rhine_westphalia": 43,
      "baden_wurttemberg": 43,
      "berlin": 43
    }
  },
  {
    "name_en": "Melina",
    "name_native": "Melina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Honey",
    "meaning_native": "Honig",
    "starting_letter": "M",
    "pronunciation": "meh-LEE-nah",
    "origin": "Greek",
    "popularity_rank": 44,
    "popularity_change": "+8%",
    "year_2025_rank": 44,
    "year_2024_rank": 52,
    "trending_status": "rising",
    "search_volume": 11700,
    "regional_popularity": {
      "bavaria": 44,
      "north_rhine_westphalia": 44,
      "baden_wurttemberg": 44,
      "berlin": 44
    }
  },
  {
    "name_en": "Leonie",
    "name_native": "Leonie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Lioness",
    "meaning_native": "Löwin",
    "starting_letter": "L",
    "pronunciation": "leh-O-nee",
    "origin": "Latin",
    "popularity_rank": 45,
    "popularity_change": "+3%",
    "year_2025_rank": 45,
    "year_2024_rank": 48,
    "trending_status": "rising",
    "search_volume": 12400,
    "regional_popularity": {
      "bavaria": 45,
      "north_rhine_westphalia": 45,
      "baden_wurttemberg": 45,
      "berlin": 45
    }
  },
  {
    "name_en": "Juliana",
    "name_native": "Juliana",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Youthful",
    "meaning_native": "Jugendlich",
    "starting_letter": "J",
    "pronunciation": "yoo-lee-AH-nah",
    "origin": "Latin",
    "popularity_rank": 46,
    "popularity_change": "+4%",
    "year_2025_rank": 46,
    "year_2024_rank": 50,
    "trending_status": "rising",
    "search_volume": 11200,
    "regional_popularity": {
      "bavaria": 46,
      "north_rhine_westphalia": 46,
      "baden_wurttemberg": 46,
      "berlin": 46
    }
  },
  {
    "name_en": "Anni",
    "name_native": "Anni",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace, favor",
    "meaning_native": "Gnade, Gunst",
    "starting_letter": "A",
    "pronunciation": "AH-nee",
    "origin": "Hebrew",
    "popularity_rank": 47,
    "popularity_change": "+7%",
    "year_2025_rank": 47,
    "year_2024_rank": 54,
    "trending_status": "rising",
    "search_volume": 10800,
    "regional_popularity": {
      "bavaria": 47,
      "north_rhine_westphalia": 47,
      "baden_wurttemberg": 47,
      "berlin": 47
    }
  },
  {
    "name_en": "Rosalie",
    "name_native": "Rosalie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Rose",
    "meaning_native": "Rose",
    "starting_letter": "R",
    "pronunciation": "ro-zah-LEE",
    "origin": "Latin",
    "popularity_rank": 48,
    "popularity_change": "+5%",
    "year_2025_rank": 48,
    "year_2024_rank": 53,
    "trending_status": "rising",
    "search_volume": 10400,
    "regional_popularity": {
      "bavaria": 48,
      "north_rhine_westphalia": 48,
      "baden_wurttemberg": 48,
      "berlin": 48
    }
  },
  {
    "name_en": "Helene",
    "name_native": "Helene",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "H",
    "pronunciation": "heh-LEH-neh",
    "origin": "Greek",
    "popularity_rank": 49,
    "popularity_change": "+2%",
    "year_2025_rank": 49,
    "year_2024_rank": 51,
    "trending_status": "stable",
    "search_volume": 10900,
    "regional_popularity": {
      "bavaria": 49,
      "north_rhine_westphalia": 49,
      "baden_wurttemberg": 49,
      "berlin": 49
    }
  },
  {
    "name_en": "Thea",
    "name_native": "Thea",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Goddess, divine",
    "meaning_native": "Göttin, göttlich",
    "starting_letter": "T",
    "pronunciation": "TEH-ah",
    "origin": "Greek",
    "popularity_rank": 50,
    "popularity_change": "+9%",
    "year_2025_rank": 50,
    "year_2024_rank": 59,
    "trending_status": "rising",
    "search_volume": 9600,
    "regional_popularity": {
      "bavaria": 50,
      "north_rhine_westphalia": 50,
      "baden_wurttemberg": 50,
      "berlin": 50
    }
  },
  {
    "name_en": "Lina",
    "name_native": "Lina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Light",
    "meaning_native": "Licht",
    "starting_letter": "L",
    "pronunciation": "LEE-nah",
    "origin": "Greek",
    "popularity_rank": 51,
    "popularity_change": "+4%",
    "year_2025_rank": 51,
    "year_2024_rank": 55,
    "trending_status": "rising",
    "search_volume": 8900,
    "regional_popularity": {
      "bavaria": 51,
      "north_rhine_westphalia": 51,
      "baden_wurttemberg": 51,
      "berlin": 51
    }
  },
  {
    "name_en": "Nora",
    "name_native": "Nora",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Light",
    "meaning_native": "Licht",
    "starting_letter": "N",
    "pronunciation": "NOH-rah",
    "origin": "Arabic",
    "popularity_rank": 52,
    "popularity_change": "+2%",
    "year_2025_rank": 52,
    "year_2024_rank": 54,
    "trending_status": "rising",
    "search_volume": 8200,
    "regional_popularity": {
      "bavaria": 52,
      "north_rhine_westphalia": 52,
      "baden_wurttemberg": 52,
      "berlin": 52
    }
  },
  {
    "name_en": "Mila",
    "name_native": "Mila",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gracious, dear",
    "meaning_native": "Gnädig, lieb",
    "starting_letter": "M",
    "pronunciation": "MEE-lah",
    "origin": "Slavic",
    "popularity_rank": 53,
    "popularity_change": "+3%",
    "year_2025_rank": 53,
    "year_2024_rank": 56,
    "trending_status": "rising",
    "search_volume": 7500,
    "regional_popularity": {
      "bavaria": 53,
      "north_rhine_westphalia": 53,
      "baden_wurttemberg": 53,
      "berlin": 53
    }
  },
  {
    "name_en": "Sofia",
    "name_native": "Sofia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Wisdom",
    "meaning_native": "Weisheit",
    "starting_letter": "S",
    "pronunciation": "ZOH-fee-ah",
    "origin": "Greek",
    "popularity_rank": 54,
    "popularity_change": "+1%",
    "year_2025_rank": 54,
    "year_2024_rank": 55,
    "trending_status": "stable",
    "search_volume": 6800,
    "regional_popularity": {
      "bavaria": 54,
      "north_rhine_westphalia": 54,
      "baden_wurttemberg": 54,
      "berlin": 54
    }
  },
  {
    "name_en": "Isabella",
    "name_native": "Isabella",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is my oath",
    "meaning_native": "Gott ist mein Eid",
    "starting_letter": "I",
    "pronunciation": "ee-zah-BEL-lah",
    "origin": "Hebrew",
    "popularity_rank": 55,
    "popularity_change": "+5%",
    "year_2025_rank": 55,
    "year_2024_rank": 60,
    "trending_status": "rising",
    "search_volume": 6100,
    "regional_popularity": {
      "bavaria": 55,
      "north_rhine_westphalia": 55,
      "baden_wurttemberg": 55,
      "berlin": 55
    }
  },
  {
    "name_en": "Zoe",
    "name_native": "Zoe",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Life",
    "meaning_native": "Leben",
    "starting_letter": "Z",
    "pronunciation": "ZOH-eh",
    "origin": "Greek",
    "popularity_rank": 56,
    "popularity_change": "+3%",
    "year_2025_rank": 56,
    "year_2024_rank": 59,
    "trending_status": "rising",
    "search_volume": 5400,
    "regional_popularity": {
      "bavaria": 56,
      "north_rhine_westphalia": 56,
      "baden_wurttemberg": 56,
      "berlin": 56
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amelie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Work",
    "meaning_native": "Arbeit",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 57,
    "popularity_change": "+4%",
    "year_2025_rank": 57,
    "year_2024_rank": 61,
    "trending_status": "rising",
    "search_volume": 4700,
    "regional_popularity": {
      "bavaria": 57,
      "north_rhine_westphalia": 57,
      "baden_wurttemberg": 57,
      "berlin": 57
    }
  },
  {
    "name_en": "Leni",
    "name_native": "Leni",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bright light",
    "meaning_native": "Helles Licht",
    "starting_letter": "L",
    "pronunciation": "LEH-nee",
    "origin": "Greek",
    "popularity_rank": 58,
    "popularity_change": "+6%",
    "year_2025_rank": 58,
    "year_2024_rank": 64,
    "trending_status": "rising",
    "search_volume": 4000,
    "regional_popularity": {
      "bavaria": 58,
      "north_rhine_westphalia": 58,
      "baden_wurttemberg": 58,
      "berlin": 58
    }
  },
  {
    "name_en": "Frieda",
    "name_native": "Frieda",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Peace",
    "meaning_native": "Frieden",
    "starting_letter": "F",
    "pronunciation": "FREE-dah",
    "origin": "Germanic",
    "popularity_rank": 59,
    "popularity_change": "+7%",
    "year_2025_rank": 59,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 3300,
    "regional_popularity": {
      "bavaria": 59,
      "north_rhine_westphalia": 59,
      "baden_wurttemberg": 59,
      "berlin": 59
    }
  },
  {
    "name_en": "Matilda",
    "name_native": "Matilda",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Mighty in battle",
    "meaning_native": "Mächtig im Kampf",
    "starting_letter": "M",
    "pronunciation": "mah-TIL-dah",
    "origin": "Germanic",
    "popularity_rank": 60,
    "popularity_change": "+2%",
    "year_2025_rank": 60,
    "year_2024_rank": 62,
    "trending_status": "rising",
    "search_volume": 2600,
    "regional_popularity": {
      "bavaria": 60,
      "north_rhine_westphalia": 60,
      "baden_wurttemberg": 60,
      "berlin": 60
    }
  },
  {
    "name_en": "Pia",
    "name_native": "Pia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pious",
    "meaning_native": "Fromm",
    "starting_letter": "P",
    "pronunciation": "PEE-ah",
    "origin": "Latin",
    "popularity_rank": 61,
    "popularity_change": "+5%",
    "year_2025_rank": 61,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 1900,
    "regional_popularity": {
      "bavaria": 61,
      "north_rhine_westphalia": 61,
      "baden_wurttemberg": 61,
      "berlin": 61
    }
  },
  {
    "name_en": "Anni",
    "name_native": "Anni",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Grace",
    "meaning_native": "Gnade",
    "starting_letter": "A",
    "pronunciation": "AH-nee",
    "origin": "Hebrew",
    "popularity_rank": 62,
    "popularity_change": "+3%",
    "year_2025_rank": 62,
    "year_2024_rank": 65,
    "trending_status": "rising",
    "search_volume": 1200,
    "regional_popularity": {
      "bavaria": 62,
      "north_rhine_westphalia": 62,
      "baden_wurttemberg": 62,
      "berlin": 62
    }
  },
  {
    "name_en": "Valentina",
    "name_native": "Valentina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Stark, gesund",
    "starting_letter": "V",
    "pronunciation": "vah-len-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 63,
    "popularity_change": "+4%",
    "year_2025_rank": 63,
    "year_2024_rank": 67,
    "trending_status": "rising",
    "search_volume": 500,
    "regional_popularity": {
      "bavaria": 63,
      "north_rhine_westphalia": 63,
      "baden_wurttemberg": 63,
      "berlin": 63
    }
  },
  {
    "name_en": "Greta",
    "name_native": "Greta",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pearl",
    "meaning_native": "Perle",
    "starting_letter": "G",
    "pronunciation": "GREH-tah",
    "origin": "Greek",
    "popularity_rank": 64,
    "popularity_change": "+2%",
    "year_2025_rank": 64,
    "year_2024_rank": 66,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 64,
      "north_rhine_westphalia": 64,
      "baden_wurttemberg": 64,
      "berlin": 64
    }
  },
  {
    "name_en": "Ida",
    "name_native": "Ida",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Hardworking",
    "meaning_native": "Fleißig",
    "starting_letter": "I",
    "pronunciation": "EE-dah",
    "origin": "Germanic",
    "popularity_rank": 65,
    "popularity_change": "+6%",
    "year_2025_rank": 65,
    "year_2024_rank": 71,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 65,
      "north_rhine_westphalia": 65,
      "baden_wurttemberg": 65,
      "berlin": 65
    }
  },
  {
    "name_en": "Katharina",
    "name_native": "Katharina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pure",
    "meaning_native": "Rein",
    "starting_letter": "K",
    "pronunciation": "kah-tah-REE-nah",
    "origin": "Greek",
    "popularity_rank": 66,
    "popularity_change": "+2%",
    "year_2025_rank": 66,
    "year_2024_rank": 68,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 66,
      "north_rhine_westphalia": 66,
      "baden_wurttemberg": 66,
      "berlin": 66
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amelie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Work",
    "meaning_native": "Arbeit",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 67,
    "popularity_change": "+3%",
    "year_2025_rank": 67,
    "year_2024_rank": 70,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 67,
      "north_rhine_westphalia": 67,
      "baden_wurttemberg": 67,
      "berlin": 67
    }
  },
  {
    "name_en": "Lara",
    "name_native": "Lara",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Citadel",
    "meaning_native": "Zitadelle",
    "starting_letter": "L",
    "pronunciation": "LAH-ra",
    "origin": "Latin",
    "popularity_rank": 68,
    "popularity_change": "+4%",
    "year_2025_rank": 68,
    "year_2024_rank": 72,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 68,
      "north_rhine_westphalia": 68,
      "baden_wurttemberg": 68,
      "berlin": 68
    }
  },
  {
    "name_en": "Johanna",
    "name_native": "Johanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "yo-HAH-nah",
    "origin": "Hebrew",
    "popularity_rank": 69,
    "popularity_change": "+1%",
    "year_2025_rank": 69,
    "year_2024_rank": 70,
    "trending_status": "stable",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 69,
      "north_rhine_westphalia": 69,
      "baden_wurttemberg": 69,
      "berlin": 69
    }
  },
  {
    "name_en": "Antonia",
    "name_native": "Antonia",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Priceless",
    "meaning_native": "Unbezahlbar",
    "starting_letter": "A",
    "pronunciation": "an-TOH-nee-ah",
    "origin": "Latin",
    "popularity_rank": 70,
    "popularity_change": "+2%",
    "year_2025_rank": 70,
    "year_2024_rank": 72,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 70,
      "north_rhine_westphalia": 70,
      "baden_wurttemberg": 70,
      "berlin": 70
    }
  },
  {
    "name_en": "Theresa",
    "name_native": "Theresa",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Harvester",
    "meaning_native": "Ernterin",
    "starting_letter": "T",
    "pronunciation": "teh-REH-zah",
    "origin": "Greek",
    "popularity_rank": 71,
    "popularity_change": "+3%",
    "year_2025_rank": 71,
    "year_2024_rank": 74,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 71,
      "north_rhine_westphalia": 71,
      "baden_wurttemberg": 71,
      "berlin": 71
    }
  },
  {
    "name_en": "Paulina",
    "name_native": "Paulina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Small",
    "meaning_native": "Klein",
    "starting_letter": "P",
    "pronunciation": "pow-LEE-nah",
    "origin": "Latin",
    "popularity_rank": 72,
    "popularity_change": "+5%",
    "year_2025_rank": 72,
    "year_2024_rank": 77,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 72,
      "north_rhine_westphalia": 72,
      "baden_wurttemberg": 72,
      "berlin": 72
    }
  },
  {
    "name_en": "Carla",
    "name_native": "Carla",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Free woman",
    "meaning_native": "Freie Frau",
    "starting_letter": "C",
    "pronunciation": "KAR-la",
    "origin": "Germanic",
    "popularity_rank": 73,
    "popularity_change": "+2%",
    "year_2025_rank": 73,
    "year_2024_rank": 75,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 73,
      "north_rhine_westphalia": 73,
      "baden_wurttemberg": 73,
      "berlin": 73
    }
  },
  {
    "name_en": "Marlene",
    "name_native": "Marlene",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Bitter grace",
    "meaning_native": "Bittere Gnade",
    "starting_letter": "M",
    "pronunciation": "mar-LEH-neh",
    "origin": "Hebrew",
    "popularity_rank": 74,
    "popularity_change": "+1%",
    "year_2025_rank": 74,
    "year_2024_rank": 75,
    "trending_status": "stable",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 74,
      "north_rhine_westphalia": 74,
      "baden_wurttemberg": 74,
      "berlin": 74
    }
  },
  {
    "name_en": "Franziska",
    "name_native": "Franziska",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Free",
    "meaning_native": "Frei",
    "starting_letter": "F",
    "pronunciation": "fran-TSEES-kah",
    "origin": "Latin",
    "popularity_rank": 75,
    "popularity_change": "+4%",
    "year_2025_rank": 75,
    "year_2024_rank": 79,
    "trending_status": "rising",
    "search_volume": 0,
    "regional_popularity": {
      "bavaria": 75,
      "north_rhine_westphalia": 75,
      "baden_wurttemberg": 75,
      "berlin": 75
    }
  },
  {
    "name_en": "Katharina",
    "name_native": "Katharina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Pure",
    "meaning_native": "Rein",
    "starting_letter": "K",
    "pronunciation": "kah-tah-REE-nah",
    "origin": "Greek",
    "popularity_rank": 76,
    "popularity_change": "+3%",
    "year_2025_rank": 76,
    "year_2024_rank": 79,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 76,
      "north_rhine_westphalia": 76,
      "baden_wurttemberg": 76,
      "berlin": 76
    }
  },
  {
    "name_en": "Valentina",
    "name_native": "Valentina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Strong",
    "meaning_native": "Stark",
    "starting_letter": "V",
    "pronunciation": "vah-len-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 77,
    "popularity_change": "+5%",
    "year_2025_rank": 77,
    "year_2024_rank": 82,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 77,
      "north_rhine_westphalia": 77,
      "baden_wurttemberg": 77,
      "berlin": 77
    }
  },
  {
    "name_en": "Amelie",
    "name_native": "Amelie",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Work",
    "meaning_native": "Arbeit",
    "starting_letter": "A",
    "pronunciation": "ah-meh-LEE",
    "origin": "Germanic",
    "popularity_rank": 78,
    "popularity_change": "+2%",
    "year_2025_rank": 78,
    "year_2024_rank": 80,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 78,
      "north_rhine_westphalia": 78,
      "baden_wurttemberg": 78,
      "berlin": 78
    }
  },
  {
    "name_en": "Sabrina",
    "name_native": "Sabrina",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "From the river Severn",
    "meaning_native": "Vom Fluss Severn",
    "starting_letter": "S",
    "pronunciation": "za-BREE-nah",
    "origin": "Celtic",
    "popularity_rank": 79,
    "popularity_change": "+4%",
    "year_2025_rank": 79,
    "year_2024_rank": 83,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 79,
      "north_rhine_westphalia": 79,
      "baden_wurttemberg": 79,
      "berlin": 79
    }
  },
  {
    "name_en": "Dorothea",
    "name_native": "Dorothea",
    "gender": "girl",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Gift of God",
    "meaning_native": "Geschenk Gottes",
    "starting_letter": "D",
    "pronunciation": "doh-ro-TEH-ah",
    "origin": "Greek",
    "popularity_rank": 80,
    "popularity_change": "+3%",
    "year_2025_rank": 80,
    "year_2024_rank": 83,
    "trending_status": "rising",
    "search_volume": 1,
    "regional_popularity": {
      "bavaria": 80,
      "north_rhine_westphalia": 80,
      "baden_wurttemberg": 80,
      "berlin": 80
    }
  }
]

export default GermanGirlNames;
