import { BelgianDutchBoyNames } from './languages/dutch/boy-names'
import { BelgianDutchGirlNames } from './languages/dutch/girl-names'
import { BelgianFrenchBoyNames } from './languages/french/boy-names'
import { BelgianFrenchGirlNames } from './languages/french/girl-names'
import type { NameData } from '@/types/name-data'

export const BelgiumNames: NameData[] = [
  ...BelgianDutchBoyNames,
  ...BelgianDutchGirlNames,
  ...BelgianFrenchBoyNames,
  ...BelgianFrenchGirlNames
]

export const BelgiumBoyNames: NameData[] = [...BelgianDutchBoyNames, ...BelgianFrenchBoyNames]
export const BelgiumGirlNames: NameData[] = [...BelgianDutchGirlNames, ...BelgianFrenchGirlNames]

export const getBelgiumNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'dutch':
      return [...BelgianDutchBoyNames, ...BelgianDutchGirlNames]
    case 'french':
      return [...BelgianFrenchBoyNames, ...BelgianFrenchGirlNames]
    default:
      return []
  }
}

export const getBelgiumNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'dutch':
      return gender.toLowerCase() === 'boy' ? BelgianDutchBoyNames : BelgianDutchGirlNames
    case 'french':
      return gender.toLowerCase() === 'boy' ? BelgianFrenchBoyNames : BelgianFrenchGirlNames
    default:
      return []
  }
}
