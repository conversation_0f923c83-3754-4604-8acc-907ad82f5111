import { DutchBoyNames } from './languages/dutch/boy-names'
import { DutchGirlNames } from './languages/dutch/girl-names'
import type { NameData } from '@/types/name-data'

export const NetherlandsNames: NameData[] = [
  ...DutchBoyNames,
  ...DutchGirlNames
]

export const NetherlandsBoyNames: NameData[] = DutchBoyNames
export const NetherlandsGirlNames: NameData[] = DutchGirlNames

export const getNetherlandsNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'dutch':
      return NetherlandsNames
    default:
      return []
  }
}

export const getNetherlandsNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'dutch':
      return gender.toLowerCase() === 'boy' ? NetherlandsBoyNames : NetherlandsGirlNames
    default:
      return []
  }
}
