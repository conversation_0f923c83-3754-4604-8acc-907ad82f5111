import { AustrianBoyNames } from './languages/german/boy-names'
import { AustrianGirlNames } from './languages/german/girl-names'
import type { NameData } from '@/types/name-data'

export const AustriaNames: NameData[] = [
  ...AustrianBoyNames,
  ...AustrianGirlNames
]

export const AustriaBoyNames: NameData[] = AustrianBoyNames
export const AustriaGirlNames: NameData[] = AustrianGirlNames

export const getAustriaNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'german':
      return AustriaNames
    default:
      return []
  }
}

export const getAustriaNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'german':
      return gender.toLowerCase() === 'boy' ? AustriaBoyNames : AustriaGirlNames
    default:
      return []
  }
}
