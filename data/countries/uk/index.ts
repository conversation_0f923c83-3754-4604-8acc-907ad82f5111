import { EnglishBoyNames } from './languages/english/boy-names'
import { EnglishGirlNames } from './languages/english/girl-names'
import type { NameData } from '@/types/name-data'

export const UKNames: NameData[] = [
  ...EnglishBoyNames,
  ...EnglishGirlNames
]

export const UKBoyNames: NameData[] = EnglishBoyNames
export const UKGirlNames: NameData[] = EnglishGirlNames

export const getUKNamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'english':
      return UKNames
    default:
      return []
  }
}

export const getUKNamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'english':
      return gender.toLowerCase() === 'boy' ? UKBoyNames : UKGirlNames
    default:
      return []
  }
}
