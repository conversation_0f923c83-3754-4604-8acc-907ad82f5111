import { EnglishBoyNames } from './languages/english/boy-names'
import { EnglishGirlNames } from './languages/english/girl-names'
import type { NameData } from '@/types/name-data'

export const USANames: NameData[] = [
  ...EnglishBoyNames,
  ...EnglishGirlNames
]

export const USABoyNames: NameData[] = EnglishBoyNames
export const USAGirlNames: NameData[] = EnglishGirlNames

export const getUSANamesByLanguage = (language: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'english':
      return USANames
    default:
      return []
  }
}

export const getUSANamesByLanguageAndGender = (language: string, gender: string): NameData[] => {
  switch (language.toLowerCase()) {
    case 'english':
      return gender.toLowerCase() === 'boy' ? USABoyNames : USAGirlNames
    default:
      return []
  }
} 