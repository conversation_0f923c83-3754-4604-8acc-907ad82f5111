import type { NameData } from "@/types/name-data"

export const ChristianBoyNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Supplanter",
    "meaning_native": "Supplanter",
    "starting_letter": "J",
    "pronunciation": "<PERSON>",
    "origin": "Hebrew"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is gracious",
    "meaning_native": "God is gracious",
    "starting_letter": "J",
    "pronunciation": "<PERSON>",
    "origin": "Hebrew"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Who is like God",
    "meaning_native": "Who is like <PERSON>",
    "starting_letter": "<PERSON>",
    "pronunciation": "<PERSON>",
    "origin": "Hebrew"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "<PERSON>oved",
    "meaning_native": "Beloved",
    "starting_letter": "D",
    "pronunciation": "David",
    "origin": "Hebrew"
  },
  {
    "name_en": "Daniel",
    "name_native": "Daniel",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is my judge",
    "meaning_native": "God is my judge",
    "starting_letter": "D",
    "pronunciation": "Daniel",
    "origin": "Hebrew"
  },
  {
    "name_en": "Matthew",
    "name_native": "Matthew",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Gift of God",
    "meaning_native": "Gift of God",
    "starting_letter": "M",
    "pronunciation": "Matthew",
    "origin": "Hebrew"
  },
  {
    "name_en": "Christopher",
    "name_native": "Christopher",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Bearer of Christ",
    "meaning_native": "Bearer of Christ",
    "starting_letter": "C",
    "pronunciation": "Christopher",
    "origin": "Hebrew"
  },
  {
    "name_en": "Andrew",
    "name_native": "Andrew",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Manly, brave",
    "meaning_native": "Manly, brave",
    "starting_letter": "A",
    "pronunciation": "Andrew",
    "origin": "Hebrew"
  },
  {
    "name_en": "Joshua",
    "name_native": "Joshua",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is salvation",
    "meaning_native": "God is salvation",
    "starting_letter": "J",
    "pronunciation": "Joshua",
    "origin": "Hebrew"
  },
  {
    "name_en": "Ethan",
    "name_native": "Ethan",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Strong, firm",
    "meaning_native": "Strong, firm",
    "starting_letter": "E",
    "pronunciation": "Ethan",
    "origin": "Hebrew"
  },
  {
    "name_en": "Benjamin",
    "name_native": "Benjamin",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Son of the right hand",
    "meaning_native": "Son of the right hand",
    "starting_letter": "B",
    "pronunciation": "Benjamin",
    "origin": "Hebrew"
  },
  {
    "name_en": "Samuel",
    "name_native": "Samuel",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Heard by God",
    "meaning_native": "Heard by God",
    "starting_letter": "S",
    "pronunciation": "Samuel",
    "origin": "Hebrew"
  },
  {
    "name_en": "Noah",
    "name_native": "Noah",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Rest, comfort",
    "meaning_native": "Rest, comfort",
    "starting_letter": "N",
    "pronunciation": "Noah",
    "origin": "Hebrew"
  },
  {
    "name_en": "Elijah",
    "name_native": "Elijah",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "My God is Yahweh",
    "meaning_native": "My God is Yahweh",
    "starting_letter": "E",
    "pronunciation": "Elijah",
    "origin": "Hebrew"
  },
  {
    "name_en": "Caleb",
    "name_native": "Caleb",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Faithful, devoted",
    "meaning_native": "Faithful, devoted",
    "starting_letter": "C",
    "pronunciation": "Caleb",
    "origin": "Hebrew"
  },
  {
    "name_en": "Isaac",
    "name_native": "Isaac",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "He will laugh",
    "meaning_native": "He will laugh",
    "starting_letter": "I",
    "pronunciation": "Isaac",
    "origin": "Hebrew"
  },
  {
    "name_en": "Nathan",
    "name_native": "Nathan",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "He gave",
    "meaning_native": "He gave",
    "starting_letter": "N",
    "pronunciation": "Nathan",
    "origin": "Hebrew"
  },
  {
    "name_en": "Aaron",
    "name_native": "Aaron",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Exalted, strong",
    "meaning_native": "Exalted, strong",
    "starting_letter": "A",
    "pronunciation": "Aaron",
    "origin": "Hebrew"
  },
  {
    "name_en": "Timothy",
    "name_native": "Timothy",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Honoring God",
    "meaning_native": "Honoring God",
    "starting_letter": "T",
    "pronunciation": "Timothy",
    "origin": "Hebrew"
  },
  {
    "name_en": "Paul",
    "name_native": "Paul",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Small, humble",
    "meaning_native": "Small, humble",
    "starting_letter": "P",
    "pronunciation": "Paul",
    "origin": "Hebrew"
  },
  {
    "name_en": "Mark",
    "name_native": "Mark",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Warlike",
    "meaning_native": "Warlike",
    "starting_letter": "M",
    "pronunciation": "Mark",
    "origin": "Hebrew"
  },
  {
    "name_en": "Luke",
    "name_native": "Luke",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Light giving",
    "meaning_native": "Light giving",
    "starting_letter": "L",
    "pronunciation": "Luke",
    "origin": "Hebrew"
  },
  {
    "name_en": "Peter",
    "name_native": "Peter",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Rock, stone",
    "meaning_native": "Rock, stone",
    "starting_letter": "P",
    "pronunciation": "Peter",
    "origin": "Hebrew"
  },
  {
    "name_en": "Thomas",
    "name_native": "Thomas",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Twin",
    "meaning_native": "Twin",
    "starting_letter": "T",
    "pronunciation": "Thomas",
    "origin": "Hebrew"
  },
  {
    "name_en": "Philip",
    "name_native": "Philip",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Lover of horses",
    "meaning_native": "Lover of horses",
    "starting_letter": "P",
    "pronunciation": "Philip",
    "origin": "Hebrew"
  },
  {
    "name_en": "Stephen",
    "name_native": "Stephen",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Crown, wreath",
    "meaning_native": "Crown, wreath",
    "starting_letter": "S",
    "pronunciation": "Stephen",
    "origin": "Hebrew"
  },
  {
    "name_en": "Alexander",
    "name_native": "Alexander",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Defender of mankind",
    "meaning_native": "Protector of people",
    "starting_letter": "A",
    "pronunciation": "al-ig-ZAN-der",
    "origin": "Greek",
    "popularity_rank": 28,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 45200
  },
  {
    "name_en": "Sebastian",
    "name_native": "Sebastian",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Venerable, revered",
    "meaning_native": "Respected one",
    "starting_letter": "S",
    "pronunciation": "se-BAS-chan",
    "origin": "Latin",
    "popularity_rank": 29,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 42800
  },
  {
    "name_en": "Gabriel",
    "name_native": "Gabriel",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is my strength",
    "meaning_native": "Strength of God",
    "starting_letter": "G",
    "pronunciation": "GAY-bree-el",
    "origin": "Hebrew",
    "popularity_rank": 30,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 41500
  },
  {
    "name_en": "Theodore",
    "name_native": "Theodore",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Gift of God",
    "meaning_native": "God's gift",
    "starting_letter": "T",
    "pronunciation": "THEE-o-dor",
    "origin": "Greek",
    "popularity_rank": 31,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 38900
  },
  {
    "name_en": "Jonathan",
    "name_native": "Jonathan",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God has given",
    "meaning_native": "Gift from God",
    "starting_letter": "J",
    "pronunciation": "JON-a-than",
    "origin": "Hebrew",
    "popularity_rank": 32,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 37600
  },
  {
    "name_en": "Nicholas",
    "name_native": "Nicholas",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Victory of the people",
    "meaning_native": "People's champion",
    "starting_letter": "N",
    "pronunciation": "NIK-o-las",
    "origin": "Greek",
    "popularity_rank": 33,
    "popularity_change": "0%",
    "trending_status": "stable",
    "search_volume": 36200
  },
  {
    "name_en": "Jeremiah",
    "name_native": "Jeremiah",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God will uplift",
    "meaning_native": "Exalted by God",
    "starting_letter": "J",
    "pronunciation": "jer-e-MY-ah",
    "origin": "Hebrew",
    "popularity_rank": 34,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 34800
  },
  {
    "name_en": "Emmanuel",
    "name_native": "Emmanuel",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is with us",
    "meaning_native": "God among us",
    "starting_letter": "E",
    "pronunciation": "e-MAN-yoo-el",
    "origin": "Hebrew",
    "popularity_rank": 35,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 33400
  },
  {
    "name_en": "Ezekiel",
    "name_native": "Ezekiel",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God strengthens",
    "meaning_native": "Strengthened by God",
    "starting_letter": "E",
    "pronunciation": "e-ZEEK-ee-el",
    "origin": "Hebrew",
    "popularity_rank": 36,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 31900
  },
  {
    "name_en": "Zachary",
    "name_native": "Zachary",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God remembers",
    "meaning_native": "Remembered by God",
    "starting_letter": "Z",
    "pronunciation": "ZAK-a-ree",
    "origin": "Hebrew",
    "popularity_rank": 37,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 30500
  },
  {
    "name_en": "Micah",
    "name_native": "Micah",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Who is like God",
    "meaning_native": "Like unto God",
    "starting_letter": "M",
    "pronunciation": "MY-kah",
    "origin": "Hebrew",
    "popularity_rank": 38,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 29100
  },
  {
    "name_en": "Josiah",
    "name_native": "Josiah",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God supports",
    "meaning_native": "Supported by God",
    "starting_letter": "J",
    "pronunciation": "jo-SY-ah",
    "origin": "Hebrew",
    "popularity_rank": 39,
    "popularity_change": "+9%",
    "trending_status": "rising",
    "search_volume": 27800
  },
  {
    "name_en": "Constantine",
    "name_native": "Constantine",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Steadfast, constant",
    "meaning_native": "Unwavering",
    "starting_letter": "C",
    "pronunciation": "KON-stan-teen",
    "origin": "Latin",
    "popularity_rank": 40,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 26400
  },
  {
    "name_en": "Raphael",
    "name_native": "Raphael",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God has healed",
    "meaning_native": "Healed by God",
    "starting_letter": "R",
    "pronunciation": "RAFF-ay-el",
    "origin": "Hebrew",
    "popularity_rank": 41,
    "popularity_change": "+11%",
    "trending_status": "rising",
    "search_volume": 25100
  },
  {
    "name_en": "Bartholomew",
    "name_native": "Bartholomew",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Son of Tolmai",
    "meaning_native": "Son of furrows",
    "starting_letter": "B",
    "pronunciation": "bar-THOL-o-mew",
    "origin": "Aramaic",
    "popularity_rank": 42,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 23700
  },
  {
    "name_en": "Matthias",
    "name_native": "Matthias",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Gift of God",
    "meaning_native": "God's present",
    "starting_letter": "M",
    "pronunciation": "ma-THY-as",
    "origin": "Hebrew",
    "popularity_rank": 43,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 22300
  },
  {
    "name_en": "Simeon",
    "name_native": "Simeon",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God has heard",
    "meaning_native": "Heard by God",
    "starting_letter": "S",
    "pronunciation": "SIM-ee-on",
    "origin": "Hebrew",
    "popularity_rank": 44,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 20900
  },
  {
    "name_en": "Barnabas",
    "name_native": "Barnabas",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Son of encouragement",
    "meaning_native": "Encouraging son",
    "starting_letter": "B",
    "pronunciation": "BAR-na-bas",
    "origin": "Aramaic",
    "popularity_rank": 45,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 19500
  },
  {
    "name_en": "Cornelius",
    "name_native": "Cornelius",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Horn",
    "meaning_native": "Of the horn",
    "starting_letter": "C",
    "pronunciation": "kor-NEEL-yus",
    "origin": "Latin",
    "popularity_rank": 46,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 18200
  },
  {
    "name_en": "Thaddeus",
    "name_native": "Thaddeus",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Courageous heart",
    "meaning_native": "Brave hearted",
    "starting_letter": "T",
    "pronunciation": "THAD-ee-us",
    "origin": "Aramaic",
    "popularity_rank": 47,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 16800
  },
  {
    "name_en": "Lazarus",
    "name_native": "Lazarus",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God has helped",
    "meaning_native": "Helped by God",
    "starting_letter": "L",
    "pronunciation": "LAZ-a-rus",
    "origin": "Hebrew",
    "popularity_rank": 48,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 15400
  },
  {
    "name_en": "Tobias",
    "name_native": "Tobias",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is good",
    "meaning_native": "Goodness of God",
    "starting_letter": "T",
    "pronunciation": "to-BY-as",
    "origin": "Hebrew",
    "popularity_rank": 49,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 14100
  },
  {
    "name_en": "Jedidiah",
    "name_native": "Jedidiah",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Beloved of God",
    "meaning_native": "God's beloved",
    "starting_letter": "J",
    "pronunciation": "jed-i-DY-ah",
    "origin": "Hebrew",
    "popularity_rank": 50,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 12700
  },
  {
    "name_en": "Amadeus",
    "name_native": "Amadeus",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Love of God",
    "meaning_native": "God's beloved",
    "starting_letter": "A",
    "pronunciation": "ah-ma-DAY-us",
    "origin": "Latin",
    "popularity_rank": 51,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 11300
  },
  {
    "name_en": "Maximilian",
    "name_native": "Maximilian",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Greatest",
    "meaning_native": "The greatest one",
    "starting_letter": "M",
    "pronunciation": "mak-si-MIL-yan",
    "origin": "Latin",
    "popularity_rank": 52,
    "popularity_change": "+10%",
    "trending_status": "rising",
    "search_volume": 10800
  },
  {
    "name_en": "Augustine",
    "name_native": "Augustine",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Magnificent, venerable",
    "meaning_native": "Majestic",
    "starting_letter": "A",
    "pronunciation": "AW-guh-steen",
    "origin": "Latin",
    "popularity_rank": 53,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 9400
  },
  {
    "name_en": "Valentino",
    "name_native": "Valentino",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Valiant",
    "starting_letter": "V",
    "pronunciation": "val-en-TEE-no",
    "origin": "Latin",
    "popularity_rank": 54,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 8900
  },
  {
    "name_en": "Dominic",
    "name_native": "Dominic",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Belonging to God",
    "meaning_native": "Of the Lord",
    "starting_letter": "D",
    "pronunciation": "DOM-i-nik",
    "origin": "Latin",
    "popularity_rank": 55,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 8500
  },
  {
    "name_en": "Francis",
    "name_native": "Francis",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Frenchman, free one",
    "meaning_native": "Free man",
    "starting_letter": "F",
    "pronunciation": "FRAN-sis",
    "origin": "Latin",
    "popularity_rank": 56,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 8100
  },
  {
    "name_en": "Benedict",
    "name_native": "Benedict",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Blessed",
    "meaning_native": "Blessed one",
    "starting_letter": "B",
    "pronunciation": "BEN-ə-dikt",
    "origin": "Latin",
    "popularity_rank": 57,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 7700
  },
  {
    "name_en": "Vincent",
    "name_native": "Vincent",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Conquering",
    "meaning_native": "Victorious",
    "starting_letter": "V",
    "pronunciation": "VIN-sent",
    "origin": "Latin",
    "popularity_rank": 58,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 7300
  },
  {
    "name_en": "Adrian",
    "name_native": "Adrian",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Dark one, rich",
    "meaning_native": "From Hadria",
    "starting_letter": "A",
    "pronunciation": "AY-dree-an",
    "origin": "Latin",
    "popularity_rank": 59,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 6900
  },
  {
    "name_en": "Julian",
    "name_native": "Julian",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Youthful",
    "meaning_native": "Young at heart",
    "starting_letter": "J",
    "pronunciation": "JOO-lee-an",
    "origin": "Latin",
    "popularity_rank": 60,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 6500
  },
  {
    "name_en": "Marcus",
    "name_native": "Marcus",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Warlike",
    "meaning_native": "Of Mars",
    "starting_letter": "M",
    "pronunciation": "MAR-kus",
    "origin": "Latin",
    "popularity_rank": 61,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 6100
  },
  {
    "name_en": "Leonard",
    "name_native": "Leonard",
    "gender": "boy",
    "religion": "Christian",
    "language": "Germanic",
    "meaning_en": "Brave lion",
    "meaning_native": "Lion-hearted",
    "starting_letter": "L",
    "pronunciation": "LEE-ə-nard",
    "origin": "Germanic",
    "popularity_rank": 62,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 5700
  },
  {
    "name_en": "Bernard",
    "name_native": "Bernard",
    "gender": "boy",
    "religion": "Christian",
    "language": "Germanic",
    "meaning_en": "Brave bear",
    "meaning_native": "Strong as a bear",
    "starting_letter": "B",
    "pronunciation": "ber-NARD",
    "origin": "Germanic",
    "popularity_rank": 63,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 5300
  },
  {
    "name_en": "Frederick",
    "name_native": "Frederick",
    "gender": "boy",
    "religion": "Christian",
    "language": "Germanic",
    "meaning_en": "Peaceful ruler",
    "meaning_native": "Peaceful king",
    "starting_letter": "F",
    "pronunciation": "FRED-ər-ik",
    "origin": "Germanic",
    "popularity_rank": 64,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 4900
  },
  {
    "name_en": "Edmund",
    "name_native": "Edmund",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Fortunate protector",
    "meaning_native": "Rich guardian",
    "starting_letter": "E",
    "pronunciation": "ED-mənd",
    "origin": "Old English",
    "popularity_rank": 65,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 4500
  },
  {
    "name_en": "Alfred",
    "name_native": "Alfred",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Wise counselor",
    "meaning_native": "Elf counsel",
    "starting_letter": "A",
    "pronunciation": "AL-fred",
    "origin": "Old English",
    "popularity_rank": 66,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 4100
  },
  {
    "name_en": "Edgar",
    "name_native": "Edgar",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Wealthy spear",
    "meaning_native": "Rich warrior",
    "starting_letter": "E",
    "pronunciation": "ED-gar",
    "origin": "Old English",
    "popularity_rank": 67,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 3700
  },
  {
    "name_en": "Harold",
    "name_native": "Harold",
    "gender": "boy",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Army ruler",
    "meaning_native": "Military commander",
    "starting_letter": "H",
    "pronunciation": "HAR-əld",
    "origin": "Old English",
    "popularity_rank": 68,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 3300
  },
  {
    "name_en": "Albert",
    "name_native": "Albert",
    "gender": "boy",
    "religion": "Christian",
    "language": "Germanic",
    "meaning_en": "Noble, bright",
    "meaning_native": "Bright nobility",
    "starting_letter": "A",
    "pronunciation": "AL-bert",
    "origin": "Germanic",
    "popularity_rank": 69,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 2900
  },
  {
    "name_en": "Victor",
    "name_native": "Victor",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Conqueror",
    "meaning_native": "Victorious one",
    "starting_letter": "V",
    "pronunciation": "VIK-tor",
    "origin": "Latin",
    "popularity_rank": 70,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 2500
  },
  {
    "name_en": "Gregory",
    "name_native": "Gregory",
    "gender": "boy",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Watchful, vigilant",
    "meaning_native": "Alert guardian",
    "starting_letter": "G",
    "pronunciation": "GREG-ə-ree",
    "origin": "Greek",
    "popularity_rank": 71,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2100
  },
  {
    "name_en": "Ignatius",
    "name_native": "Ignatius",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Fiery",
    "meaning_native": "Ardent one",
    "starting_letter": "I",
    "pronunciation": "ig-NAY-shus",
    "origin": "Latin",
    "popularity_rank": 72,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 1800
  },
  {
    "name_en": "Ambrose",
    "name_native": "Ambrose",
    "gender": "boy",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Immortal",
    "meaning_native": "Divine one",
    "starting_letter": "A",
    "pronunciation": "AM-brohz",
    "origin": "Greek",
    "popularity_rank": 73,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 1500
  },
  {
    "name_en": "Clement",
    "name_native": "Clement",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Merciful, gentle",
    "meaning_native": "Kind one",
    "starting_letter": "C",
    "pronunciation": "KLEM-ənt",
    "origin": "Latin",
    "popularity_rank": 74,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 1200
  },
  {
    "name_en": "Basil",
    "name_native": "Basil",
    "gender": "boy",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Royal, kingly",
    "meaning_native": "King",
    "starting_letter": "B",
    "pronunciation": "BAZ-əl",
    "origin": "Greek",
    "popularity_rank": 75,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 1100
  },
  {
    "name_en": "Cyrus",
    "name_native": "Cyrus",
    "gender": "boy",
    "religion": "Christian",
    "language": "Persian",
    "meaning_en": "Sun, throne",
    "meaning_native": "Like the sun",
    "starting_letter": "C",
    "pronunciation": "SY-rəs",
    "origin": "Persian",
    "popularity_rank": 76,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 1000
  },
  {
    "name_en": "Felix",
    "name_native": "Felix",
    "gender": "boy",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Happy, fortunate",
    "meaning_native": "Lucky one",
    "starting_letter": "F",
    "pronunciation": "FEE-liks",
    "origin": "Latin",
    "popularity_rank": 77,
    "popularity_change": "+9%",
    "trending_status": "rising",
    "search_volume": 950
  },
  {
    "name_en": "Jerome",
    "name_native": "Jerome",
    "gender": "boy",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Sacred name",
    "meaning_native": "Holy name",
    "starting_letter": "J",
    "pronunciation": "jə-ROHM",
    "origin": "Greek",
    "popularity_rank": 78,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 900
  },
  {
    "name_en": "Damian",
    "name_native": "Damian",
    "gender": "boy",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "To tame, subdue",
    "meaning_native": "Gentle spirit",
    "starting_letter": "D",
    "pronunciation": "DAY-mee-ən",
    "origin": "Greek",
    "popularity_rank": 79,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 850
  },
  {
    "name_en": "Demetrius",
    "name_native": "Demetrius",
    "gender": "boy",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Earth-lover",
    "meaning_native": "Of Demeter",
    "starting_letter": "D",
    "pronunciation": "də-MEE-tree-əs",
    "origin": "Greek",
    "popularity_rank": 80,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 800
  },
  {
    "name_en": "Emmanuel",
    "name_native": "Emmanuele",
    "gender": "boy",
    "religion": "Christian",
    "language": "Italian",
    "meaning_en": "God is with us",
    "meaning_native": "Dio è con noi",
    "starting_letter": "E",
    "pronunciation": "em-man-WEH-leh",
    "origin": "Hebrew",
    "popularity_rank": 81,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 750
  },
  {
    "name_en": "Lorenzo",
    "name_native": "Lorenzo",
    "gender": "boy",
    "religion": "Christian",
    "language": "Italian",
    "meaning_en": "From Laurentum",
    "meaning_native": "Crowned with laurel",
    "starting_letter": "L",
    "pronunciation": "lo-REN-zo",
    "origin": "Latin",
    "popularity_rank": 82,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 700
  },
  {
    "name_en": "Antonio",
    "name_native": "Antonio",
    "gender": "boy",
    "religion": "Christian",
    "language": "Italian",
    "meaning_en": "Priceless one",
    "meaning_native": "Beyond praise",
    "starting_letter": "A",
    "pronunciation": "an-TOH-nee-oh",
    "origin": "Latin",
    "popularity_rank": 83,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 650
  },
  {
    "name_en": "Ricardo",
    "name_native": "Ricardo",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Brave ruler",
    "meaning_native": "Rey valiente",
    "starting_letter": "R",
    "pronunciation": "ree-KAR-do",
    "origin": "Germanic",
    "popularity_rank": 84,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 600
  },
  {
    "name_en": "Francisco",
    "name_native": "Francisco",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Free man",
    "meaning_native": "Hombre libre",
    "starting_letter": "F",
    "pronunciation": "fran-THEES-ko",
    "origin": "Latin",
    "popularity_rank": 85,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 550
  },
  {
    "name_en": "Salvador",
    "name_native": "Salvador",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Savior",
    "meaning_native": "Salvador",
    "starting_letter": "S",
    "pronunciation": "sal-va-DOR",
    "origin": "Latin",
    "popularity_rank": 86,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 500
  },
  {
    "name_en": "Fernando",
    "name_native": "Fernando",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Bold voyager",
    "meaning_native": "Viajero audaz",
    "starting_letter": "F",
    "pronunciation": "fer-NAN-do",
    "origin": "Germanic",
    "popularity_rank": 87,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 450
  },
  {
    "name_en": "Eduardo",
    "name_native": "Eduardo",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Wealthy guardian",
    "meaning_native": "Guardián próspero",
    "starting_letter": "E",
    "pronunciation": "eh-DWAR-do",
    "origin": "Old English",
    "popularity_rank": 88,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 400
  },
  {
    "name_en": "Pierre",
    "name_native": "Pierre",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Stone, rock",
    "meaning_native": "Pierre",
    "starting_letter": "P",
    "pronunciation": "pee-AIR",
    "origin": "Greek",
    "popularity_rank": 89,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 380
  },
  {
    "name_en": "Henri",
    "name_native": "Henri",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Ruler of the home",
    "meaning_native": "Maître de maison",
    "starting_letter": "H",
    "pronunciation": "ahn-REE",
    "origin": "Germanic",
    "popularity_rank": 90,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 360
  },
  {
    "name_en": "André",
    "name_native": "André",
    "gender": "boy",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Manly, brave",
    "meaning_native": "Homme courageux",
    "starting_letter": "A",
    "pronunciation": "ahn-DREH",
    "origin": "Greek",
    "popularity_rank": 91,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 340
  },
  {
    "name_en": "Klaus",
    "name_native": "Klaus",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Victory of the people",
    "meaning_native": "Volkssieg",
    "starting_letter": "K",
    "pronunciation": "KLOWS",
    "origin": "Greek",
    "popularity_rank": 92,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 320
  },
  {
    "name_en": "Wilhelm",
    "name_native": "Wilhelm",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Resolute protector",
    "meaning_native": "Entschlossener Beschützer",
    "starting_letter": "W",
    "pronunciation": "VIL-helm",
    "origin": "Germanic",
    "popularity_rank": 93,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 300
  },
  {
    "name_en": "Heinrich",
    "name_native": "Heinrich",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Ruler of the home",
    "meaning_native": "Hausherr",
    "starting_letter": "H",
    "pronunciation": "HINE-rikh",
    "origin": "Germanic",
    "popularity_rank": 94,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 280
  },
  {
    "name_en": "Johann",
    "name_native": "Johann",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "God is gracious",
    "meaning_native": "Gott ist gnädig",
    "starting_letter": "J",
    "pronunciation": "YO-hahn",
    "origin": "Hebrew",
    "popularity_rank": 95,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 260
  },
  {
    "name_en": "Georg",
    "name_native": "Georg",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Farmer, earth worker",
    "meaning_native": "Landarbeiter",
    "starting_letter": "G",
    "pronunciation": "GAY-org",
    "origin": "Greek",
    "popularity_rank": 96,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 240
  },
  {
    "name_en": "Wolfgang",
    "name_native": "Wolfgang",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Wolf path",
    "meaning_native": "Wolfspfad",
    "starting_letter": "W",
    "pronunciation": "VOLF-gang",
    "origin": "Germanic",
    "popularity_rank": 97,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 220
  },
  {
    "name_en": "Ludwig",
    "name_native": "Ludwig",
    "gender": "boy",
    "religion": "Christian",
    "language": "German",
    "meaning_en": "Famous warrior",
    "meaning_native": "Berühmter Krieger",
    "starting_letter": "L",
    "pronunciation": "LOOD-vig",
    "origin": "Germanic",
    "popularity_rank": 98,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 200
  },
  {
    "name_en": "Dimitri",
    "name_native": "Dimitri",
    "gender": "boy",
    "religion": "Christian",
    "language": "Russian",
    "meaning_en": "Earth-lover",
    "meaning_native": "Любящий землю",
    "starting_letter": "D",
    "pronunciation": "di-MEE-tree",
    "origin": "Greek",
    "popularity_rank": 99,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 190
  },
  {
    "name_en": "Alexei",
    "name_native": "Alexei",
    "gender": "boy",
    "religion": "Christian",
    "language": "Russian",
    "meaning_en": "Defender",
    "meaning_native": "Защитник",
    "starting_letter": "A",
    "pronunciation": "ah-LEK-say",
    "origin": "Greek",
    "popularity_rank": 100,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 180
  },
  {
    "name_en": "Sergei",
    "name_native": "Sergei",
    "gender": "boy",
    "religion": "Christian",
    "language": "Russian",
    "meaning_en": "Servant",
    "meaning_native": "Слуга",
    "starting_letter": "S",
    "pronunciation": "ser-GAY",
    "origin": "Latin",
    "popularity_rank": 101,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 170
  },
  {
    "name_en": "Mikhail",
    "name_native": "Mikhail",
    "gender": "boy",
    "religion": "Christian",
    "language": "Russian",
    "meaning_en": "Who is like God",
    "meaning_native": "Кто как Бог",
    "starting_letter": "M",
    "pronunciation": "mee-khah-EEL",
    "origin": "Hebrew",
    "popularity_rank": 102,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 160
  },
  {
    "name_en": "Pavel",
    "name_native": "Pavel",
    "gender": "boy",
    "religion": "Christian",
    "language": "Russian",
    "meaning_en": "Small, humble",
    "meaning_native": "Маленький, скромный",
    "starting_letter": "P",
    "pronunciation": "PAH-vel",
    "origin": "Latin",
    "popularity_rank": 103,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 150
  },
  {
    "name_en": "Stanislav",
    "name_native": "Stanislav",
    "gender": "boy",
    "religion": "Christian",
    "language": "Slavic",
    "meaning_en": "Becoming glory",
    "meaning_native": "Становящийся славным",
    "starting_letter": "S",
    "pronunciation": "STAN-is-lahf",
    "origin": "Slavic",
    "popularity_rank": 104,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 140
  },
  {
    "name_en": "Vladimir",
    "name_native": "Vladimir",
    "gender": "boy",
    "religion": "Christian",
    "language": "Slavic",
    "meaning_en": "Ruling with greatness",
    "meaning_native": "Властвующий с величием",
    "starting_letter": "V",
    "pronunciation": "vlah-DEE-meer",
    "origin": "Slavic",
    "popularity_rank": 105,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 130
  },
  {
    "name_en": "Bojan",
    "name_native": "Bojan",
    "gender": "boy",
    "religion": "Christian",
    "language": "Slavic",
    "meaning_en": "Battle",
    "meaning_native": "Битва",
    "starting_letter": "B",
    "pronunciation": "BO-yahn",
    "origin": "Slavic",
    "popularity_rank": 106,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 120
  },
  {
    "name_en": "Kristjan",
    "name_native": "Kristjan",
    "gender": "boy",
    "religion": "Christian",
    "language": "Estonian",
    "meaning_en": "Christian, follower of Christ",
    "meaning_native": "Kristlane",
    "starting_letter": "K",
    "pronunciation": "KRIS-tyahn",
    "origin": "Latin",
    "popularity_rank": 107,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 110
  },
  {
    "name_en": "Mateo",
    "name_native": "Mateo",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Gift of God",
    "meaning_native": "Regalo de Dios",
    "starting_letter": "M",
    "pronunciation": "mah-TEH-oh",
    "origin": "Hebrew",
    "popularity_rank": 108,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 100
  },
  {
    "name_en": "Carlos",
    "name_native": "Carlos",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Free man",
    "meaning_native": "Hombre libre",
    "starting_letter": "C",
    "pronunciation": "KAR-lohs",
    "origin": "Germanic",
    "popularity_rank": 109,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 95
  },
  {
    "name_en": "Alejandro",
    "name_native": "Alejandro",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Defender of mankind",
    "meaning_native": "Defensor de la humanidad",
    "starting_letter": "A",
    "pronunciation": "ah-leh-HAHN-dro",
    "origin": "Greek",
    "popularity_rank": 110,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 90
  },
  {
    "name_en": "José",
    "name_native": "José",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "God will increase",
    "meaning_native": "Dios aumentará",
    "starting_letter": "J",
    "pronunciation": "ho-SEH",
    "origin": "Hebrew",
    "popularity_rank": 111,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 85
  },
  {
    "name_en": "Miguel",
    "name_native": "Miguel",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Who is like God",
    "meaning_native": "Quien como Dios",
    "starting_letter": "M",
    "pronunciation": "mee-GEHL",
    "origin": "Hebrew",
    "popularity_rank": 112,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 80
  },
  {
    "name_en": "Rafael",
    "name_native": "Rafael",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "God has healed",
    "meaning_native": "Dios ha sanado",
    "starting_letter": "R",
    "pronunciation": "rah-fah-EHL",
    "origin": "Hebrew",
    "popularity_rank": 113,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 75
  },
  {
    "name_en": "Andrés",
    "name_native": "Andrés",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Manly, brave",
    "meaning_native": "Varonil, valiente",
    "starting_letter": "A",
    "pronunciation": "ahn-DREHS",
    "origin": "Greek",
    "popularity_rank": 114,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 70
  },
  {
    "name_en": "Diego",
    "name_native": "Diego",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Supplanter",
    "meaning_native": "Suplantador",
    "starting_letter": "D",
    "pronunciation": "dee-EH-go",
    "origin": "Hebrew",
    "popularity_rank": 115,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 65
  },
  {
    "name_en": "Martín",
    "name_native": "Martín",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Warlike",
    "meaning_native": "Guerrero",
    "starting_letter": "M",
    "pronunciation": "mar-TEEN",
    "origin": "Latin",
    "popularity_rank": 116,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 60
  },
  {
    "name_en": "Sebastián",
    "name_native": "Sebastián",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Venerable, revered",
    "meaning_native": "Venerable, respetado",
    "starting_letter": "S",
    "pronunciation": "seh-bahs-tee-AHN",
    "origin": "Latin",
    "popularity_rank": 117,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 55
  },
  {
    "name_en": "Ángel",
    "name_native": "Ángel",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Angel, messenger",
    "meaning_native": "Ángel, mensajero",
    "starting_letter": "A",
    "pronunciation": "AHN-hehl",
    "origin": "Greek",
    "popularity_rank": 118,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 50
  },
  {
    "name_en": "Emilio",
    "name_native": "Emilio",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Rival",
    "meaning_native": "Rival",
    "starting_letter": "E",
    "pronunciation": "eh-MEE-lee-oh",
    "origin": "Latin",
    "popularity_rank": 119,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 45
  },
  {
    "name_en": "Arturo",
    "name_native": "Arturo",
    "gender": "boy",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Bear, strong",
    "meaning_native": "Oso, fuerte",
    "starting_letter": "A",
    "pronunciation": "ar-TOO-ro",
    "origin": "Celtic",
    "popularity_rank": 120,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 40
  }
]
export default ChristianBoyNames
