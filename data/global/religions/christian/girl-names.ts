import type { NameData } from "@/types/name-data"

export const ChristianGirlNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Beloved, drop of the sea",
    "meaning_native": "Beloved, drop of the sea",
    "starting_letter": "M",
    "pronunciation": "<PERSON>",
    "origin": "Hebrew"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Princess",
    "meaning_native": "Princess",
    "starting_letter": "S",
    "pronunciation": "<PERSON>",
    "origin": "Hebrew"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is my oath",
    "meaning_native": "God is my oath",
    "starting_letter": "E",
    "pronunciation": "<PERSON>",
    "origin": "Hebrew"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "<PERSON>",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "<PERSON>, favor",
    "meaning_native": "<PERSON>, favor",
    "starting_letter": "G",
    "pronunciation": "Grace",
    "origin": "Hebrew"
  },
  {
    "name_en": "Faith",
    "name_native": "Faith",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Faith, trust",
    "meaning_native": "Faith, trust",
    "starting_letter": "F",
    "pronunciation": "Faith",
    "origin": "Hebrew"
  },
  {
    "name_en": "Hope",
    "name_native": "Hope",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Hope, expectation",
    "meaning_native": "Hope, expectation",
    "starting_letter": "H",
    "pronunciation": "Hope",
    "origin": "Hebrew"
  },
  {
    "name_en": "Charity",
    "name_native": "Charity",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Charity, love",
    "meaning_native": "Charity, love",
    "starting_letter": "C",
    "pronunciation": "Charity",
    "origin": "Hebrew"
  },
  {
    "name_en": "Joy",
    "name_native": "Joy",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Joy, happiness",
    "meaning_native": "Joy, happiness",
    "starting_letter": "J",
    "pronunciation": "Joy",
    "origin": "Hebrew"
  },
  {
    "name_en": "Peace",
    "name_native": "Peace",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Peace, tranquility",
    "meaning_native": "Peace, tranquility",
    "starting_letter": "P",
    "pronunciation": "Peace",
    "origin": "Hebrew"
  },
  {
    "name_en": "Patience",
    "name_native": "Patience",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Patience, endurance",
    "meaning_native": "Patience, endurance",
    "starting_letter": "P",
    "pronunciation": "Patience",
    "origin": "Hebrew"
  },
  {
    "name_en": "Mercy",
    "name_native": "Mercy",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Mercy, compassion",
    "meaning_native": "Mercy, compassion",
    "starting_letter": "M",
    "pronunciation": "Mercy",
    "origin": "Hebrew"
  },
  {
    "name_en": "Prudence",
    "name_native": "Prudence",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Prudence, wisdom",
    "meaning_native": "Prudence, wisdom",
    "starting_letter": "P",
    "pronunciation": "Prudence",
    "origin": "Hebrew"
  },
  {
    "name_en": "Temperance",
    "name_native": "Temperance",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Temperance, moderation",
    "meaning_native": "Temperance, moderation",
    "starting_letter": "T",
    "pronunciation": "Temperance",
    "origin": "Hebrew"
  },
  {
    "name_en": "Chastity",
    "name_native": "Chastity",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Chastity, purity",
    "meaning_native": "Chastity, purity",
    "starting_letter": "C",
    "pronunciation": "Chastity",
    "origin": "Hebrew"
  },
  {
    "name_en": "Humility",
    "name_native": "Humility",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Humility, modesty",
    "meaning_native": "Humility, modesty",
    "starting_letter": "H",
    "pronunciation": "Humility",
    "origin": "Hebrew"
  },
  {
    "name_en": "Diligence",
    "name_native": "Diligence",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Diligence, hard work",
    "meaning_native": "Diligence, hard work",
    "starting_letter": "D",
    "pronunciation": "Diligence",
    "origin": "Hebrew"
  },
  {
    "name_en": "Kindness",
    "name_native": "Kindness",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Kindness, gentleness",
    "meaning_native": "Kindness, gentleness",
    "starting_letter": "K",
    "pronunciation": "Kindness",
    "origin": "Hebrew"
  },
  {
    "name_en": "Goodness",
    "name_native": "Goodness",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Goodness, virtue",
    "meaning_native": "Goodness, virtue",
    "starting_letter": "G",
    "pronunciation": "Goodness",
    "origin": "Hebrew"
  },
  {
    "name_en": "Gentleness",
    "name_native": "Gentleness",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Gentleness, meekness",
    "meaning_native": "Gentleness, meekness",
    "starting_letter": "G",
    "pronunciation": "Gentleness",
    "origin": "Hebrew"
  },
  {
    "name_en": "Self-control",
    "name_native": "Self-control",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Self-control, discipline",
    "meaning_native": "Self-control, discipline",
    "starting_letter": "S",
    "pronunciation": "Self-control",
    "origin": "Hebrew"
  },
  {
    "name_en": "Wisdom",
    "name_native": "Wisdom",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Wisdom, knowledge",
    "meaning_native": "Wisdom, knowledge",
    "starting_letter": "W",
    "pronunciation": "Wisdom",
    "origin": "Hebrew"
  },
  {
    "name_en": "Understanding",
    "name_native": "Understanding",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Understanding, comprehension",
    "meaning_native": "Understanding, comprehension",
    "starting_letter": "U",
    "pronunciation": "Understanding",
    "origin": "Hebrew"
  },
  {
    "name_en": "Counsel",
    "name_native": "Counsel",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Counsel, advice",
    "meaning_native": "Counsel, advice",
    "starting_letter": "C",
    "pronunciation": "Counsel",
    "origin": "Hebrew"
  },
  {
    "name_en": "Fortitude",
    "name_native": "Fortitude",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Fortitude, courage",
    "meaning_native": "Fortitude, courage",
    "starting_letter": "F",
    "pronunciation": "Fortitude",
    "origin": "Hebrew"
  },
  {
    "name_en": "Knowledge",
    "name_native": "Knowledge",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Knowledge, learning",
    "meaning_native": "Knowledge, learning",
    "starting_letter": "K",
    "pronunciation": "Knowledge",
    "origin": "Hebrew"
  },
  {
    "name_en": "Piety",
    "name_native": "Piety",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Piety, devotion",
    "meaning_native": "Piety, devotion",
    "starting_letter": "P",
    "pronunciation": "Piety",
    "origin": "Hebrew"
  },
  {
    "name_en": "Fear of the Lord",
    "name_native": "Fear of the Lord",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Fear of the Lord, reverence",
    "meaning_native": "Fear of the Lord, reverence",
    "starting_letter": "F",
    "pronunciation": "Fear of the Lord",
    "origin": "Hebrew"
  },
  {
    "name_en": "Isabella",
    "name_native": "Isabella",
    "gender": "girl",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Devoted to God",
    "meaning_native": "Consagrada a Dios",
    "starting_letter": "I",
    "pronunciation": "ee-sa-BEH-lah",
    "origin": "Hebrew",
    "popularity_rank": 28,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 67800
  },
  {
    "name_en": "Sophia",
    "name_native": "Sophia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Wisdom",
    "meaning_native": "Σοφία",
    "starting_letter": "S",
    "pronunciation": "so-FEE-ah",
    "origin": "Greek",
    "popularity_rank": 29,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 64200
  },
  {
    "name_en": "Victoria",
    "name_native": "Victoria",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Victory",
    "meaning_native": "Victoria",
    "starting_letter": "V",
    "pronunciation": "vik-TOR-ee-ah",
    "origin": "Latin",
    "popularity_rank": 30,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 61500
  },
  {
    "name_en": "Anastasia",
    "name_native": "Anastasia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Resurrection",
    "meaning_native": "Ανάσταση",
    "starting_letter": "A",
    "pronunciation": "an-ah-STAH-see-ah",
    "origin": "Greek",
    "popularity_rank": 31,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 58900
  },
  {
    "name_en": "Gabriella",
    "name_native": "Gabriella",
    "gender": "girl",
    "religion": "Christian",
    "language": "Italian",
    "meaning_en": "God is my strength",
    "meaning_native": "Dio è la mia forza",
    "starting_letter": "G",
    "pronunciation": "gah-bree-EH-lah",
    "origin": "Hebrew",
    "popularity_rank": 32,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 56300
  },
  {
    "name_en": "Catherine",
    "name_native": "Catherine",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "Pure",
    "meaning_native": "Pure one",
    "starting_letter": "C",
    "pronunciation": "KATH-rin",
    "origin": "Greek",
    "popularity_rank": 33,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 53700
  },
  {
    "name_en": "Natalie",
    "name_native": "Natalie",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Christmas Day",
    "meaning_native": "Jour de Noël",
    "starting_letter": "N",
    "pronunciation": "NAT-ah-lee",
    "origin": "Latin",
    "popularity_rank": 34,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 51200
  },
  {
    "name_en": "Evangeline",
    "name_native": "Evangeline",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bearer of good news",
    "meaning_native": "Ευαγγέλιο",
    "starting_letter": "E",
    "pronunciation": "ee-VAN-jah-leen",
    "origin": "Greek",
    "popularity_rank": 35,
    "popularity_change": "+9%",
    "trending_status": "rising",
    "search_volume": 48600
  },
  {
    "name_en": "Seraphina",
    "name_native": "Seraphina",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Burning one, fiery",
    "meaning_native": "שרפה",
    "starting_letter": "S",
    "pronunciation": "ser-ah-FEE-nah",
    "origin": "Hebrew",
    "popularity_rank": 36,
    "popularity_change": "+11%",
    "trending_status": "rising",
    "search_volume": 46100
  },
  {
    "name_en": "Celeste",
    "name_native": "Celeste",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Heavenly",
    "meaning_native": "Caelestis",
    "starting_letter": "C",
    "pronunciation": "se-LEST",
    "origin": "Latin",
    "popularity_rank": 37,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 43500
  },
  {
    "name_en": "Angelica",
    "name_native": "Angelica",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Angelic",
    "meaning_native": "Like an angel",
    "starting_letter": "A",
    "pronunciation": "an-JEL-i-kah",
    "origin": "Latin",
    "popularity_rank": 38,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 41000
  },
  {
    "name_en": "Beatrice",
    "name_native": "Beatrice",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "She who brings happiness",
    "meaning_native": "Bringer of joy",
    "starting_letter": "B",
    "pronunciation": "BEE-ah-triss",
    "origin": "Latin",
    "popularity_rank": 39,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 38500
  },
  {
    "name_en": "Constance",
    "name_native": "Constance",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Constant, steadfast",
    "meaning_native": "Unwavering",
    "starting_letter": "C",
    "pronunciation": "KON-stans",
    "origin": "Latin",
    "popularity_rank": 40,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 36200
  },
  {
    "name_en": "Veronica",
    "name_native": "Veronica",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "True image",
    "meaning_native": "Vera icon",
    "starting_letter": "V",
    "pronunciation": "ver-ON-i-kah",
    "origin": "Latin",
    "popularity_rank": 41,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 33800
  },
  {
    "name_en": "Theresa",
    "name_native": "Theresa",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "To harvest",
    "meaning_native": "Harvester",
    "starting_letter": "T",
    "pronunciation": "teh-REE-sah",
    "origin": "Greek",
    "popularity_rank": 42,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 31400
  },
  {
    "name_en": "Magdalena",
    "name_native": "Magdalena",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "From Magdala",
    "meaning_native": "Tower",
    "starting_letter": "M",
    "pronunciation": "mag-dah-LEH-nah",
    "origin": "Hebrew",
    "popularity_rank": 43,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 29100
  },
  {
    "name_en": "Cecilia",
    "name_native": "Cecilia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Blind to one's own beauty",
    "meaning_native": "Musical",
    "starting_letter": "C",
    "pronunciation": "se-SEEL-yah",
    "origin": "Latin",
    "popularity_rank": 44,
    "popularity_change": "+8%",
    "trending_status": "rising",
    "search_volume": 26700
  },
  {
    "name_en": "Juliana",
    "name_native": "Juliana",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Youthful",
    "meaning_native": "Young at heart",
    "starting_letter": "J",
    "pronunciation": "hoo-lee-AH-nah",
    "origin": "Latin",
    "popularity_rank": 45,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 24300
  },
  {
    "name_en": "Adriana",
    "name_native": "Adriana",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "From Hadria",
    "meaning_native": "Dark one",
    "starting_letter": "A",
    "pronunciation": "ah-dree-AH-nah",
    "origin": "Latin",
    "popularity_rank": 46,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 22000
  },
  {
    "name_en": "Christina",
    "name_native": "Christina",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Follower of Christ",
    "meaning_native": "Christian",
    "starting_letter": "C",
    "pronunciation": "kris-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 47,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 19800
  },
  {
    "name_en": "Daniela",
    "name_native": "Daniela",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "God is my judge",
    "meaning_native": "Judged by God",
    "starting_letter": "D",
    "pronunciation": "dan-ee-EH-lah",
    "origin": "Hebrew",
    "popularity_rank": 48,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 17500
  },
  {
    "name_en": "Valentina",
    "name_native": "Valentina",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Strong, healthy",
    "meaning_native": "Valiant",
    "starting_letter": "V",
    "pronunciation": "val-en-TEE-nah",
    "origin": "Latin",
    "popularity_rank": 49,
    "popularity_change": "+7%",
    "trending_status": "rising",
    "search_volume": 15200
  },
  {
    "name_en": "Francesca",
    "name_native": "Francesca",
    "gender": "girl",
    "religion": "Christian",
    "language": "Italian",
    "meaning_en": "Free one",
    "meaning_native": "Libera",
    "starting_letter": "F",
    "pronunciation": "fran-CHES-kah",
    "origin": "Latin",
    "popularity_rank": 50,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 13000
  },
  {
    "name_en": "Elena",
    "name_native": "Elena",
    "gender": "girl",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Bright light",
    "meaning_native": "Luz brillante",
    "starting_letter": "E",
    "pronunciation": "eh-LEH-nah",
    "origin": "Greek",
    "popularity_rank": 51,
    "popularity_change": "+6%",
    "trending_status": "rising",
    "search_volume": 11800
  },
  {
    "name_en": "Carmen",
    "name_native": "Carmen",
    "gender": "girl",
    "religion": "Christian",
    "language": "Spanish",
    "meaning_en": "Garden, orchard",
    "meaning_native": "Jardín",
    "starting_letter": "C",
    "pronunciation": "KAR-men",
    "origin": "Latin",
    "popularity_rank": 52,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 10600
  },
  {
    "name_en": "Gloria",
    "name_native": "Gloria",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Glory",
    "meaning_native": "Gloria",
    "starting_letter": "G",
    "pronunciation": "GLOR-ee-ah",
    "origin": "Latin",
    "popularity_rank": 53,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 9500
  },
  {
    "name_en": "Monica",
    "name_native": "Monica",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Advisor",
    "meaning_native": "Counselor",
    "starting_letter": "M",
    "pronunciation": "MON-i-kah",
    "origin": "Latin",
    "popularity_rank": 54,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 8400
  },
  {
    "name_en": "Angela",
    "name_native": "Angela",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Angel, messenger",
    "meaning_native": "Άγγελος",
    "starting_letter": "A",
    "pronunciation": "an-JEL-ah",
    "origin": "Greek",
    "popularity_rank": 55,
    "popularity_change": "+3%",
    "trending_status": "stable",
    "search_volume": 7300
  },
  {
    "name_en": "Patricia",
    "name_native": "Patricia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Noble",
    "meaning_native": "Patrician",
    "starting_letter": "P",
    "pronunciation": "pah-TREE-shah",
    "origin": "Latin",
    "popularity_rank": 56,
    "popularity_change": "+0%",
    "trending_status": "stable",
    "search_volume": 6200
  },
  {
    "name_en": "Rebecca",
    "name_native": "Rebecca",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "To bind",
    "meaning_native": "Captivating",
    "starting_letter": "R",
    "pronunciation": "re-BEK-ah",
    "origin": "Hebrew",
    "popularity_rank": 57,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 5100
  },
  {
    "name_en": "Rachel",
    "name_native": "Rachel",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Ewe",
    "meaning_native": "Gentle as a lamb",
    "starting_letter": "R",
    "pronunciation": "RAY-chel",
    "origin": "Hebrew",
    "popularity_rank": 58,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 4600
  },
  {
    "name_en": "Stephanie",
    "name_native": "Stephanie",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Crown",
    "meaning_native": "Στέφανος",
    "starting_letter": "S",
    "pronunciation": "STEF-ah-nee",
    "origin": "Greek",
    "popularity_rank": 59,
    "popularity_change": "+0%",
    "trending_status": "stable",
    "search_volume": 4100
  },
  {
    "name_en": "Michelle",
    "name_native": "Michelle",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Who is like God",
    "meaning_native": "Qui est comme Dieu",
    "starting_letter": "M",
    "pronunciation": "mi-SHELL",
    "origin": "Hebrew",
    "popularity_rank": 60,
    "popularity_change": "-1%",
    "trending_status": "stable",
    "search_volume": 3700
  },
  {
    "name_en": "Nicole",
    "name_native": "Nicole",
    "gender": "girl",
    "religion": "Christian",
    "language": "French",
    "meaning_en": "Victory of the people",
    "meaning_native": "Victoire du peuple",
    "starting_letter": "N",
    "pronunciation": "ni-KOHL",
    "origin": "Greek",
    "popularity_rank": 61,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 3300
  },
  {
    "name_en": "Amanda",
    "name_native": "Amanda",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Lovable",
    "meaning_native": "Worthy of love",
    "starting_letter": "A",
    "pronunciation": "ah-MAN-dah",
    "origin": "Latin",
    "popularity_rank": 62,
    "popularity_change": "+0%",
    "trending_status": "stable",
    "search_volume": 2900
  },
  {
    "name_en": "Jennifer",
    "name_native": "Jennifer",
    "gender": "girl",
    "religion": "Christian",
    "language": "Welsh",
    "meaning_en": "White wave",
    "meaning_native": "Fair phantom",
    "starting_letter": "J",
    "pronunciation": "JEN-i-fer",
    "origin": "Welsh",
    "popularity_rank": 63,
    "popularity_change": "-2%",
    "trending_status": "falling",
    "search_volume": 2500
  },
  {
    "name_en": "Lisa",
    "name_native": "Lisa",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "God is my oath",
    "meaning_native": "Pledged to God",
    "starting_letter": "L",
    "pronunciation": "LEE-sah",
    "origin": "Hebrew",
    "popularity_rank": 64,
    "popularity_change": "-1%",
    "trending_status": "stable",
    "search_volume": 2200
  },
  {
    "name_en": "Karen",
    "name_native": "Karen",
    "gender": "girl",
    "religion": "Christian",
    "language": "Danish",
    "meaning_en": "Pure",
    "meaning_native": "Ren",
    "starting_letter": "K",
    "pronunciation": "KAR-en",
    "origin": "Greek",
    "popularity_rank": 65,
    "popularity_change": "-3%",
    "trending_status": "falling",
    "search_volume": 1900
  },
  {
    "name_en": "Helen",
    "name_native": "Helen",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Light",
    "meaning_native": "Ἑλένη",
    "starting_letter": "H",
    "pronunciation": "HEL-en",
    "origin": "Greek",
    "popularity_rank": 66,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1600
  },
  {
    "name_en": "Diana",
    "name_native": "Diana",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Divine",
    "meaning_native": "Goddess",
    "starting_letter": "D",
    "pronunciation": "dee-AH-nah",
    "origin": "Latin",
    "popularity_rank": 67,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1400
  },
  {
    "name_en": "Ruth",
    "name_native": "Ruth",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Friend, companion",
    "meaning_native": "רות",
    "starting_letter": "R",
    "pronunciation": "ROOTH",
    "origin": "Hebrew",
    "popularity_rank": 68,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1200
  },
  {
    "name_en": "Anna",
    "name_native": "Anna",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Grace",
    "meaning_native": "חנה",
    "starting_letter": "A",
    "pronunciation": "AN-nah",
    "origin": "Hebrew",
    "popularity_rank": 69,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 1000
  },
  {
    "name_en": "Laura",
    "name_native": "Laura",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Laurel",
    "meaning_native": "Bay laurel",
    "starting_letter": "L",
    "pronunciation": "LAW-rah",
    "origin": "Latin",
    "popularity_rank": 70,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 900
  },
  {
    "name_en": "Kimberly",
    "name_native": "Kimberly",
    "gender": "girl",
    "religion": "Christian",
    "language": "English",
    "meaning_en": "From the wood of the royal forest",
    "meaning_native": "Royal meadow",
    "starting_letter": "K",
    "pronunciation": "KIM-ber-lee",
    "origin": "Old English",
    "popularity_rank": 71,
    "popularity_change": "-1%",
    "trending_status": "stable",
    "search_volume": 800
  },
  {
    "name_en": "Deborah",
    "name_native": "Deborah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Bee",
    "meaning_native": "דבורה",
    "starting_letter": "D",
    "pronunciation": "DEB-or-ah",
    "origin": "Hebrew",
    "popularity_rank": 72,
    "popularity_change": "+0%",
    "trending_status": "stable",
    "search_volume": 700
  },
  {
    "name_en": "Dorothy",
    "name_native": "Dorothy",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Gift of God",
    "meaning_native": "Δωροθέα",
    "starting_letter": "D",
    "pronunciation": "DOR-oh-thee",
    "origin": "Greek",
    "popularity_rank": 73,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 650
  },
  {
    "name_en": "Esther",
    "name_native": "Esther",
    "gender": "girl",
    "religion": "Christian",
    "language": "Persian",
    "meaning_en": "Star",
    "meaning_native": "ستاره",
    "starting_letter": "E",
    "pronunciation": "ES-ter",
    "origin": "Persian",
    "popularity_rank": 74,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 600
  },
  {
    "name_en": "Martha",
    "name_native": "Martha",
    "gender": "girl",
    "religion": "Christian",
    "language": "Aramaic",
    "meaning_en": "Lady, mistress",
    "meaning_native": "מרתא",
    "starting_letter": "M",
    "pronunciation": "MAR-thah",
    "origin": "Aramaic",
    "popularity_rank": 75,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 580
  },
  {
    "name_en": "Phoebe",
    "name_native": "Phoebe",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bright, pure",
    "meaning_native": "Φοίβη",
    "starting_letter": "P",
    "pronunciation": "FEE-bee",
    "origin": "Greek",
    "popularity_rank": 76,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 550
  },
  {
    "name_en": "Tabitha",
    "name_native": "Tabitha",
    "gender": "girl",
    "religion": "Christian",
    "language": "Aramaic",
    "meaning_en": "Gazelle",
    "meaning_native": "טביתא",
    "starting_letter": "T",
    "pronunciation": "TAB-i-thah",
    "origin": "Aramaic",
    "popularity_rank": 77,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 520
  },
  {
    "name_en": "Lydia",
    "name_native": "Lydia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "From Lydia",
    "meaning_native": "Λυδία",
    "starting_letter": "L",
    "pronunciation": "LID-ee-ah",
    "origin": "Greek",
    "popularity_rank": 78,
    "popularity_change": "+5%",
    "trending_status": "rising",
    "search_volume": 500
  },
  {
    "name_en": "Priscilla",
    "name_native": "Priscilla",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Ancient",
    "meaning_native": "Prisca",
    "starting_letter": "P",
    "pronunciation": "pri-SIL-ah",
    "origin": "Latin",
    "popularity_rank": 79,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 480
  },
  {
    "name_en": "Aquila",
    "name_native": "Aquila",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Eagle",
    "meaning_native": "Aquila",
    "starting_letter": "A",
    "pronunciation": "AK-wi-lah",
    "origin": "Latin",
    "popularity_rank": 80,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 460
  },
  {
    "name_en": "Junia",
    "name_native": "Junia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Born in June",
    "meaning_native": "Junia",
    "starting_letter": "J",
    "pronunciation": "JOO-nee-ah",
    "origin": "Latin",
    "popularity_rank": 81,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 440
  },
  {
    "name_en": "Chloe",
    "name_native": "Chloe",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Green shoot",
    "meaning_native": "Χλόη",
    "starting_letter": "C",
    "pronunciation": "KLOH-ee",
    "origin": "Greek",
    "popularity_rank": 82,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 420
  },
  {
    "name_en": "Damaris",
    "name_native": "Damaris",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Calf",
    "meaning_native": "Δάμαρις",
    "starting_letter": "D",
    "pronunciation": "DAM-ah-ris",
    "origin": "Greek",
    "popularity_rank": 83,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 400
  },
  {
    "name_en": "Eunice",
    "name_native": "Eunice",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Good victory",
    "meaning_native": "Εὐνίκη",
    "starting_letter": "E",
    "pronunciation": "YOO-nis",
    "origin": "Greek",
    "popularity_rank": 84,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 380
  },
  {
    "name_en": "Lois",
    "name_native": "Lois",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Better",
    "meaning_native": "Λωΐς",
    "starting_letter": "L",
    "pronunciation": "LOH-is",
    "origin": "Greek",
    "popularity_rank": 85,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 360
  },
  {
    "name_en": "Tryphena",
    "name_native": "Tryphena",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Delicate",
    "meaning_native": "Τρύφαινα",
    "starting_letter": "T",
    "pronunciation": "tri-FEE-nah",
    "origin": "Greek",
    "popularity_rank": 86,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 340
  },
  {
    "name_en": "Tryphosa",
    "name_native": "Tryphosa",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Luxurious",
    "meaning_native": "Τρυφῶσα",
    "starting_letter": "T",
    "pronunciation": "tri-FOH-sah",
    "origin": "Greek",
    "popularity_rank": 87,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 320
  },
  {
    "name_en": "Persis",
    "name_native": "Persis",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Persian woman",
    "meaning_native": "Περσίς",
    "starting_letter": "P",
    "pronunciation": "PER-sis",
    "origin": "Greek",
    "popularity_rank": 88,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 300
  },
  {
    "name_en": "Julia",
    "name_native": "Julia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Youthful",
    "meaning_native": "Julia",
    "starting_letter": "J",
    "pronunciation": "JOO-lee-ah",
    "origin": "Latin",
    "popularity_rank": 89,
    "popularity_change": "+4%",
    "trending_status": "rising",
    "search_volume": 280
  },
  {
    "name_en": "Nereus",
    "name_native": "Nereus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Water",
    "meaning_native": "Νηρεύς",
    "starting_letter": "N",
    "pronunciation": "NEER-ee-us",
    "origin": "Greek",
    "popularity_rank": 90,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 260
  },
  {
    "name_en": "Olympas",
    "name_native": "Olympas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Heavenly",
    "meaning_native": "Ὀλυμπᾶς",
    "starting_letter": "O",
    "pronunciation": "oh-LIM-pas",
    "origin": "Greek",
    "popularity_rank": 91,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 240
  },
  {
    "name_en": "Stachys",
    "name_native": "Stachys",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Ear of corn",
    "meaning_native": "Στάχυς",
    "starting_letter": "S",
    "pronunciation": "STAK-is",
    "origin": "Greek",
    "popularity_rank": 92,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 220
  },
  {
    "name_en": "Urban",
    "name_native": "Urban",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "City dweller",
    "meaning_native": "Urbanus",
    "starting_letter": "U",
    "pronunciation": "UR-ban",
    "origin": "Latin",
    "popularity_rank": 93,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 200
  },
  {
    "name_en": "Hermas",
    "name_native": "Hermas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Hermes",
    "meaning_native": "Ἑρμᾶς",
    "starting_letter": "H",
    "pronunciation": "HER-mas",
    "origin": "Greek",
    "popularity_rank": 94,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 180
  },
  {
    "name_en": "Patrobas",
    "name_native": "Patrobas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Father's life",
    "meaning_native": "Πατροβᾶς",
    "starting_letter": "P",
    "pronunciation": "PAT-ro-bas",
    "origin": "Greek",
    "popularity_rank": 95,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 160
  },
  {
    "name_en": "Hermes",
    "name_native": "Hermes",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Messenger",
    "meaning_native": "Ἑρμῆς",
    "starting_letter": "H",
    "pronunciation": "HER-meez",
    "origin": "Greek",
    "popularity_rank": 96,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 140
  },
  {
    "name_en": "Philologus",
    "name_native": "Philologus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Lover of words",
    "meaning_native": "Φιλόλογος",
    "starting_letter": "P",
    "pronunciation": "fi-LOL-o-gus",
    "origin": "Greek",
    "popularity_rank": 97,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 120
  },
  {
    "name_en": "Narcissus",
    "name_native": "Narcissus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Numbness",
    "meaning_native": "Νάρκισσος",
    "starting_letter": "N",
    "pronunciation": "nar-SIS-us",
    "origin": "Greek",
    "popularity_rank": 98,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 110
  },
  {
    "name_en": "Asyncritus",
    "name_native": "Asyncritus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Incomparable",
    "meaning_native": "Ἀσύγκριτος",
    "starting_letter": "A",
    "pronunciation": "ah-SIN-kri-tus",
    "origin": "Greek",
    "popularity_rank": 99,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 100
  },
  {
    "name_en": "Phlegon",
    "name_native": "Phlegon",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Burning",
    "meaning_native": "Φλέγων",
    "starting_letter": "P",
    "pronunciation": "FLEG-on",
    "origin": "Greek",
    "popularity_rank": 100,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 95
  },
  {
    "name_en": "Apelles",
    "name_native": "Apelles",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Without deceit",
    "meaning_native": "Ἀπελλῆς",
    "starting_letter": "A",
    "pronunciation": "ah-PEL-eez",
    "origin": "Greek",
    "popularity_rank": 101,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 90
  },
  {
    "name_en": "Aristobulus",
    "name_native": "Aristobulus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Best counselor",
    "meaning_native": "Ἀριστόβουλος",
    "starting_letter": "A",
    "pronunciation": "ah-ris-to-BOO-lus",
    "origin": "Greek",
    "popularity_rank": 102,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 85
  },
  {
    "name_en": "Herodion",
    "name_native": "Herodion",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Little Herod",
    "meaning_native": "Ἡρωδίων",
    "starting_letter": "H",
    "pronunciation": "he-ROH-dee-on",
    "origin": "Greek",
    "popularity_rank": 103,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 80
  },
  {
    "name_en": "Rufus",
    "name_native": "Rufus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Red-haired",
    "meaning_native": "Rufus",
    "starting_letter": "R",
    "pronunciation": "ROO-fus",
    "origin": "Latin",
    "popularity_rank": 104,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 75
  },
  {
    "name_en": "Quartus",
    "name_native": "Quartus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Fourth",
    "meaning_native": "Quartus",
    "starting_letter": "Q",
    "pronunciation": "KWAR-tus",
    "origin": "Latin",
    "popularity_rank": 105,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 70
  },
  {
    "name_en": "Sosthenes",
    "name_native": "Sosthenes",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Safe strength",
    "meaning_native": "Σωσθένης",
    "starting_letter": "S",
    "pronunciation": "sos-THEE-neez",
    "origin": "Greek",
    "popularity_rank": 106,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 65
  },
  {
    "name_en": "Tertius",
    "name_native": "Tertius",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Third",
    "meaning_native": "Tertius",
    "starting_letter": "T",
    "pronunciation": "TER-shee-us",
    "origin": "Latin",
    "popularity_rank": 107,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 60
  },
  {
    "name_en": "Urban",
    "name_native": "Urban",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "City dweller",
    "meaning_native": "Urbanus",
    "starting_letter": "U",
    "pronunciation": "UR-ban",
    "origin": "Latin",
    "popularity_rank": 108,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 55
  },
  {
    "name_en": "Vitus",
    "name_native": "Vitus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Life",
    "meaning_native": "Vitus",
    "starting_letter": "V",
    "pronunciation": "VEE-tus",
    "origin": "Latin",
    "popularity_rank": 109,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 50
  },
  {
    "name_en": "Xenophon",
    "name_native": "Xenophon",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Foreign voice",
    "meaning_native": "Ξενοφῶν",
    "starting_letter": "X",
    "pronunciation": "ZEN-oh-fon",
    "origin": "Greek",
    "popularity_rank": 110,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 45
  },
  {
    "name_en": "Zenas",
    "name_native": "Zenas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Gift of Zeus",
    "meaning_native": "Ζηνᾶς",
    "starting_letter": "Z",
    "pronunciation": "ZEE-nas",
    "origin": "Greek",
    "popularity_rank": 111,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 40
  },
  {
    "name_en": "Ampliatus",
    "name_native": "Ampliatus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Enlarged",
    "meaning_native": "Ampliatus",
    "starting_letter": "A",
    "pronunciation": "am-plee-AH-tus",
    "origin": "Latin",
    "popularity_rank": 112,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 35
  },
  {
    "name_en": "Andronicus",
    "name_native": "Andronicus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Victory of man",
    "meaning_native": "Ἀνδρόνικος",
    "starting_letter": "A",
    "pronunciation": "an-DRON-i-kus",
    "origin": "Greek",
    "popularity_rank": 113,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 30
  },
  {
    "name_en": "Archippus",
    "name_native": "Archippus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Master of horses",
    "meaning_native": "Ἄρχιππος",
    "starting_letter": "A",
    "pronunciation": "ar-KIP-us",
    "origin": "Greek",
    "popularity_rank": 114,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 25
  },
  {
    "name_en": "Demas",
    "name_native": "Demas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Popular",
    "meaning_native": "Δημᾶς",
    "starting_letter": "D",
    "pronunciation": "DEE-mas",
    "origin": "Greek",
    "popularity_rank": 115,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 20
  },
  {
    "name_en": "Epaphras",
    "name_native": "Epaphras",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Handsome",
    "meaning_native": "Ἐπαφρᾶς",
    "starting_letter": "E",
    "pronunciation": "EP-ah-fras",
    "origin": "Greek",
    "popularity_rank": 116,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 15
  },
  {
    "name_en": "Epaphroditus",
    "name_native": "Epaphroditus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Lovely",
    "meaning_native": "Ἐπαφρόδιτος",
    "starting_letter": "E",
    "pronunciation": "ep-ah-fro-DEE-tus",
    "origin": "Greek",
    "popularity_rank": 117,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 10
  },
  {
    "name_en": "Erastus",
    "name_native": "Erastus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Beloved",
    "meaning_native": "Ἔραστος",
    "starting_letter": "E",
    "pronunciation": "e-RAS-tus",
    "origin": "Greek",
    "popularity_rank": 118,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 5
  },
  {
    "name_en": "Gaius",
    "name_native": "Gaius",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Rejoice",
    "meaning_native": "Gaius",
    "starting_letter": "G",
    "pronunciation": "GAY-us",
    "origin": "Latin",
    "popularity_rank": 119,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 3
  },
  {
    "name_en": "Linus",
    "name_native": "Linus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Flax",
    "meaning_native": "Λίνος",
    "starting_letter": "L",
    "pronunciation": "LY-nus",
    "origin": "Greek",
    "popularity_rank": 120,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 2
  },
  {
    "name_en": "Onesimus",
    "name_native": "Onesimus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Useful",
    "meaning_native": "Ὀνήσιμος",
    "starting_letter": "O",
    "pronunciation": "oh-NES-i-mus",
    "origin": "Greek",
    "popularity_rank": 121,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Philemon",
    "name_native": "Philemon",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Affectionate",
    "meaning_native": "Φιλήμων",
    "starting_letter": "P",
    "pronunciation": "fi-LEE-mon",
    "origin": "Greek",
    "popularity_rank": 122,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Silas",
    "name_native": "Silas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Forest",
    "meaning_native": "Silas",
    "starting_letter": "S",
    "pronunciation": "SY-las",
    "origin": "Latin",
    "popularity_rank": 123,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Timothy",
    "name_native": "Timothy",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Honoring God",
    "meaning_native": "Τιμόθεος",
    "starting_letter": "T",
    "pronunciation": "TIM-oh-thee",
    "origin": "Greek",
    "popularity_rank": 124,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Titus",
    "name_native": "Titus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Title of honor",
    "meaning_native": "Titus",
    "starting_letter": "T",
    "pronunciation": "TY-tus",
    "origin": "Latin",
    "popularity_rank": 125,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Tychicus",
    "name_native": "Tychicus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Fortunate",
    "meaning_native": "Τυχικός",
    "starting_letter": "T",
    "pronunciation": "TIK-i-kus",
    "origin": "Greek",
    "popularity_rank": 126,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Zenas",
    "name_native": "Zenas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Gift of Zeus",
    "meaning_native": "Ζηνᾶς",
    "starting_letter": "Z",
    "pronunciation": "ZEE-nas",
    "origin": "Greek",
    "popularity_rank": 127,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Apollos",
    "name_native": "Apollos",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Destroyer",
    "meaning_native": "Ἀπολλῶς",
    "starting_letter": "A",
    "pronunciation": "ah-POL-os",
    "origin": "Greek",
    "popularity_rank": 128,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Barnabas",
    "name_native": "Barnabas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Aramaic",
    "meaning_en": "Son of encouragement",
    "meaning_native": "ברנבא",
    "starting_letter": "B",
    "pronunciation": "BAR-nah-bas",
    "origin": "Aramaic",
    "popularity_rank": 129,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Clement",
    "name_native": "Clement",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Merciful",
    "meaning_native": "Clemens",
    "starting_letter": "C",
    "pronunciation": "KLEM-ent",
    "origin": "Latin",
    "popularity_rank": 130,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Crispus",
    "name_native": "Crispus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Curly-haired",
    "meaning_native": "Crispus",
    "starting_letter": "C",
    "pronunciation": "KRIS-pus",
    "origin": "Latin",
    "popularity_rank": 131,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Dionysius",
    "name_native": "Dionysius",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Follower of Dionysus",
    "meaning_native": "Διονύσιος",
    "starting_letter": "D",
    "pronunciation": "dy-oh-NIS-ee-us",
    "origin": "Greek",
    "popularity_rank": 132,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Eubulus",
    "name_native": "Eubulus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Good counsel",
    "meaning_native": "Εὔβουλος",
    "starting_letter": "E",
    "pronunciation": "yoo-BOO-lus",
    "origin": "Greek",
    "popularity_rank": 133,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Fortunatus",
    "name_native": "Fortunatus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Fortunate",
    "meaning_native": "Fortunatus",
    "starting_letter": "F",
    "pronunciation": "for-too-NAH-tus",
    "origin": "Latin",
    "popularity_rank": 134,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Gaius",
    "name_native": "Gaius",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Rejoice",
    "meaning_native": "Gaius",
    "starting_letter": "G",
    "pronunciation": "GAY-us",
    "origin": "Latin",
    "popularity_rank": 135,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Hermogenes",
    "name_native": "Hermogenes",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Born of Hermes",
    "meaning_native": "Ἑρμογένης",
    "starting_letter": "H",
    "pronunciation": "her-MOJ-e-neez",
    "origin": "Greek",
    "popularity_rank": 136,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Justus",
    "name_native": "Justus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Just",
    "meaning_native": "Justus",
    "starting_letter": "J",
    "pronunciation": "JUS-tus",
    "origin": "Latin",
    "popularity_rank": 137,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Lucius",
    "name_native": "Lucius",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Light",
    "meaning_native": "Lucius",
    "starting_letter": "L",
    "pronunciation": "LOO-shee-us",
    "origin": "Latin",
    "popularity_rank": 138,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Marcus",
    "name_native": "Marcus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Warlike",
    "meaning_native": "Marcus",
    "starting_letter": "M",
    "pronunciation": "MAR-kus",
    "origin": "Latin",
    "popularity_rank": 139,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Nicanor",
    "name_native": "Nicanor",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Victory",
    "meaning_native": "Νικάνωρ",
    "starting_letter": "N",
    "pronunciation": "ni-KAN-or",
    "origin": "Greek",
    "popularity_rank": 140,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Onesiphorus",
    "name_native": "Onesiphorus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bringing profit",
    "meaning_native": "Ὀνησίφορος",
    "starting_letter": "O",
    "pronunciation": "oh-nes-IF-or-us",
    "origin": "Greek",
    "popularity_rank": 141,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Pudens",
    "name_native": "Pudens",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Modest",
    "meaning_native": "Pudens",
    "starting_letter": "P",
    "pronunciation": "POO-dens",
    "origin": "Latin",
    "popularity_rank": 142,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Quartus",
    "name_native": "Quartus",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Fourth",
    "meaning_native": "Quartus",
    "starting_letter": "Q",
    "pronunciation": "KWAR-tus",
    "origin": "Latin",
    "popularity_rank": 143,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Prisca",
    "name_native": "Prisca",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Ancient",
    "meaning_native": "Prisca",
    "starting_letter": "P",
    "pronunciation": "PRIS-ka",
    "origin": "Latin",
    "popularity_rank": 144,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Evodia",
    "name_native": "Evodia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Good journey",
    "meaning_native": "Ευοδία",
    "starting_letter": "E",
    "pronunciation": "eh-VOH-dee-ah",
    "origin": "Greek",
    "popularity_rank": 145,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Syntyche",
    "name_native": "Syntyche",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Fortunate",
    "meaning_native": "Συντύχη",
    "starting_letter": "S",
    "pronunciation": "sin-TIH-kee",
    "origin": "Greek",
    "popularity_rank": 146,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Apphia",
    "name_native": "Apphia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Fruitful",
    "meaning_native": "Ἀπφία",
    "starting_letter": "A",
    "pronunciation": "AP-fee-ah",
    "origin": "Greek",
    "popularity_rank": 147,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Tryphaena",
    "name_native": "Tryphaena",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Delicate",
    "meaning_native": "Τρύφαινα",
    "starting_letter": "T",
    "pronunciation": "trih-FEE-nah",
    "origin": "Greek",
    "popularity_rank": 148,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Tryphosa",
    "name_native": "Tryphosa",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Luxurious",
    "meaning_native": "Τρυφῶσα",
    "starting_letter": "T",
    "pronunciation": "trih-FOH-sah",
    "origin": "Greek",
    "popularity_rank": 149,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Persis",
    "name_native": "Persis",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Persian woman",
    "meaning_native": "Περσίς",
    "starting_letter": "P",
    "pronunciation": "PER-sis",
    "origin": "Greek",
    "popularity_rank": 150,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Claudia",
    "name_native": "Claudia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Lame",
    "meaning_native": "Claudia",
    "starting_letter": "C",
    "pronunciation": "CLAW-dee-ah",
    "origin": "Latin",
    "popularity_rank": 151,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Phoebe",
    "name_native": "Phoebe",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bright, pure",
    "meaning_native": "Φοίβη",
    "starting_letter": "P",
    "pronunciation": "FEE-bee",
    "origin": "Greek",
    "popularity_rank": 152,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Junia",
    "name_native": "Junia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Born in June",
    "meaning_native": "Junia",
    "starting_letter": "J",
    "pronunciation": "JOO-nee-ah",
    "origin": "Latin",
    "popularity_rank": 153,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Olympas",
    "name_native": "Olympas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Heavenly",
    "meaning_native": "Ὀλυμπᾶς",
    "starting_letter": "O",
    "pronunciation": "oh-LIM-pas",
    "origin": "Greek",
    "popularity_rank": 154,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Nympha",
    "name_native": "Nympha",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bride",
    "meaning_native": "Νύμφη",
    "starting_letter": "N",
    "pronunciation": "NIM-fah",
    "origin": "Greek",
    "popularity_rank": 155,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Damaris",
    "name_native": "Damaris",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Calf",
    "meaning_native": "Δάμαρις",
    "starting_letter": "D",
    "pronunciation": "DAM-ah-ris",
    "origin": "Greek",
    "popularity_rank": 156,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Lois",
    "name_native": "Lois",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Better",
    "meaning_native": "Λωΐς",
    "starting_letter": "L",
    "pronunciation": "LOH-is",
    "origin": "Greek",
    "popularity_rank": 157,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Eunice",
    "name_native": "Eunice",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Good victory",
    "meaning_native": "Εὐνίκη",
    "starting_letter": "E",
    "pronunciation": "YOO-nis",
    "origin": "Greek",
    "popularity_rank": 158,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Rhoda",
    "name_native": "Rhoda",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Rose",
    "meaning_native": "Ῥόδη",
    "starting_letter": "R",
    "pronunciation": "ROH-dah",
    "origin": "Greek",
    "popularity_rank": 159,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Tabitha",
    "name_native": "Tabitha",
    "gender": "girl",
    "religion": "Christian",
    "language": "Aramaic",
    "meaning_en": "Gazelle",
    "meaning_native": "טביתא",
    "starting_letter": "T",
    "pronunciation": "TAB-i-thah",
    "origin": "Aramaic",
    "popularity_rank": 160,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Susanna",
    "name_native": "Susanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Lily",
    "meaning_native": "שׁוֹשַׁנָּה",
    "starting_letter": "S",
    "pronunciation": "soo-ZAN-ah",
    "origin": "Hebrew",
    "popularity_rank": 161,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Salome",
    "name_native": "Salome",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Peace",
    "meaning_native": "שָׁלוֹם",
    "starting_letter": "S",
    "pronunciation": "sah-LOH-may",
    "origin": "Hebrew",
    "popularity_rank": 162,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Joanna",
    "name_native": "Joanna",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "God is gracious",
    "meaning_native": "יוֹחָנָה",
    "starting_letter": "J",
    "pronunciation": "jo-AN-nah",
    "origin": "Hebrew",
    "popularity_rank": 163,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mariam",
    "name_native": "Mariam",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Wished-for child",
    "meaning_native": "מרים",
    "starting_letter": "M",
    "pronunciation": "mah-ree-AM",
    "origin": "Hebrew",
    "popularity_rank": 164,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Magdalene",
    "name_native": "Magdalene",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Of Magdala",
    "meaning_native": "מגדלנה",
    "starting_letter": "M",
    "pronunciation": "MAG-da-leen",
    "origin": "Hebrew",
    "popularity_rank": 165,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Martha",
    "name_native": "Martha",
    "gender": "girl",
    "religion": "Christian",
    "language": "Aramaic",
    "meaning_en": "Lady, mistress",
    "meaning_native": "מרתא",
    "starting_letter": "M",
    "pronunciation": "MAR-thah",
    "origin": "Aramaic",
    "popularity_rank": 166,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mary Magdalene",
    "name_native": "Mary Magdalene",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Mary of Magdala",
    "meaning_native": "מרים המגדלית",
    "starting_letter": "M",
    "pronunciation": "MAIR-ee MAG-da-leen",
    "origin": "Hebrew",
    "popularity_rank": 167,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Elizabeth",
    "name_native": "Elizabeth",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "God is my oath",
    "meaning_native": "אֱלִישֶׁבַע",
    "starting_letter": "E",
    "pronunciation": "ee-LIZ-ah-beth",
    "origin": "Hebrew",
    "popularity_rank": 168,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Abigail",
    "name_native": "Abigail",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Father's joy",
    "meaning_native": "אֲבִיגַיִל",
    "starting_letter": "A",
    "pronunciation": "AB-ih-gail",
    "origin": "Hebrew",
    "popularity_rank": 169,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Adina",
    "name_native": "Adina",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Delicate, gentle",
    "meaning_native": "עדינה",
    "starting_letter": "A",
    "pronunciation": "ah-DEE-nah",
    "origin": "Hebrew",
    "popularity_rank": 170,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Alethea",
    "name_native": "Alethea",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Truth",
    "meaning_native": "Αλήθεια",
    "starting_letter": "A",
    "pronunciation": "ah-LEE-thee-ah",
    "origin": "Greek",
    "popularity_rank": 171,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Anastasia",
    "name_native": "Anastasia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Resurrection",
    "meaning_native": "Αναστασία",
    "starting_letter": "A",
    "pronunciation": "ah-nah-STAH-see-ah",
    "origin": "Greek",
    "popularity_rank": 172,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Antonia",
    "name_native": "Antonia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Priceless",
    "meaning_native": "Antonia",
    "starting_letter": "A",
    "pronunciation": "an-TOH-nee-ah",
    "origin": "Latin",
    "popularity_rank": 173,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aquila",
    "name_native": "Aquila",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Eagle",
    "meaning_native": "Aquila",
    "starting_letter": "A",
    "pronunciation": "ah-KWEE-lah",
    "origin": "Latin",
    "popularity_rank": 174,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Bernice",
    "name_native": "Bernice",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bringer of victory",
    "meaning_native": "Βερενίκη",
    "starting_letter": "B",
    "pronunciation": "BUR-nis",
    "origin": "Greek",
    "popularity_rank": 175,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Candace",
    "name_native": "Candace",
    "gender": "girl",
    "religion": "Christian",
    "language": "Ethiopian",
    "meaning_en": "Queen mother",
    "meaning_native": "Kandake",
    "starting_letter": "C",
    "pronunciation": "KAN-diss",
    "origin": "Ethiopian",
    "popularity_rank": 176,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Claudine",
    "name_native": "Claudine",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Lame",
    "meaning_native": "Claudine",
    "starting_letter": "C",
    "pronunciation": "claw-DEEN",
    "origin": "Latin",
    "popularity_rank": 177,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Dorcas",
    "name_native": "Dorcas",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Gazelle",
    "meaning_native": "Δορκάς",
    "starting_letter": "D",
    "pronunciation": "DOR-kas",
    "origin": "Greek",
    "popularity_rank": 178,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Drusilla",
    "name_native": "Drusilla",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Strong",
    "meaning_native": "Drusilla",
    "starting_letter": "D",
    "pronunciation": "dru-SIL-ah",
    "origin": "Latin",
    "popularity_rank": 179,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Eunike",
    "name_native": "Eunike",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Good victory",
    "meaning_native": "Εὐνίκη",
    "starting_letter": "E",
    "pronunciation": "YOO-nee-keh",
    "origin": "Greek",
    "popularity_rank": 180,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Hannah",
    "name_native": "Hannah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Grace",
    "meaning_native": "חַנָּה",
    "starting_letter": "H",
    "pronunciation": "HAN-ah",
    "origin": "Hebrew",
    "popularity_rank": 181,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Helena",
    "name_native": "Helena",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Light",
    "meaning_native": "Ἑλένη",
    "starting_letter": "H",
    "pronunciation": "heh-LAY-nah",
    "origin": "Greek",
    "popularity_rank": 182,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Irene",
    "name_native": "Irene",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Peace",
    "meaning_native": "Ειρήνη",
    "starting_letter": "I",
    "pronunciation": "ee-RAY-nee",
    "origin": "Greek",
    "popularity_rank": 183,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Julia",
    "name_native": "Julia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Youthful",
    "meaning_native": "Julia",
    "starting_letter": "J",
    "pronunciation": "JOO-lee-ah",
    "origin": "Latin",
    "popularity_rank": 184,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Lydia",
    "name_native": "Lydia",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "From Lydia",
    "meaning_native": "Λυδία",
    "starting_letter": "L",
    "pronunciation": "LID-ee-ah",
    "origin": "Greek",
    "popularity_rank": 185,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Miriam",
    "name_native": "Miriam",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Wished-for child",
    "meaning_native": "מרים",
    "starting_letter": "M",
    "pronunciation": "MEER-ee-am",
    "origin": "Hebrew",
    "popularity_rank": 186,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naomi",
    "name_native": "Naomi",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Pleasantness",
    "meaning_native": "נָעֳמִי",
    "starting_letter": "N",
    "pronunciation": "nay-OH-mee",
    "origin": "Hebrew",
    "popularity_rank": 187,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Phoebe",
    "name_native": "Phoebe",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Bright, pure",
    "meaning_native": "Φοίβη",
    "starting_letter": "P",
    "pronunciation": "FEE-bee",
    "origin": "Greek",
    "popularity_rank": 188,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Priscilla",
    "name_native": "Priscilla",
    "gender": "girl",
    "religion": "Christian",
    "language": "Latin",
    "meaning_en": "Ancient",
    "meaning_native": "Prisca",
    "starting_letter": "P",
    "pronunciation": "pri-SIL-ah",
    "origin": "Latin",
    "popularity_rank": 189,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Rebecca",
    "name_native": "Rebecca",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "To tie, bind",
    "meaning_native": "רִבְקָה",
    "starting_letter": "R",
    "pronunciation": "reh-BEK-ah",
    "origin": "Hebrew",
    "popularity_rank": 190,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ruth",
    "name_native": "Ruth",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Friend, companion",
    "meaning_native": "רות",
    "starting_letter": "R",
    "pronunciation": "ROOTH",
    "origin": "Hebrew",
    "popularity_rank": 191,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Sapphira",
    "name_native": "Sapphira",
    "gender": "girl",
    "religion": "Christian",
    "language": "Greek",
    "meaning_en": "Sapphire",
    "meaning_native": "Σαπφείρα",
    "starting_letter": "S",
    "pronunciation": "sah-FEE-rah",
    "origin": "Greek",
    "popularity_rank": 192,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Tabitha",
    "name_native": "Tabitha",
    "gender": "girl",
    "religion": "Christian",
    "language": "Aramaic",
    "meaning_en": "Gazelle",
    "meaning_native": "טביתא",
    "starting_letter": "T",
    "pronunciation": "TAB-i-thah",
    "origin": "Aramaic",
    "popularity_rank": 193,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Zipporah",
    "name_native": "Zipporah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Bird",
    "meaning_native": "צִפּוֹרָה",
    "starting_letter": "Z",
    "pronunciation": "zi-POHR-ah",
    "origin": "Hebrew",
    "popularity_rank": 194,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Dinah",
    "name_native": "Dinah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Judged",
    "meaning_native": "דִּינָה",
    "starting_letter": "D",
    "pronunciation": "DEE-nah",
    "origin": "Hebrew",
    "popularity_rank": 195,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Jemima",
    "name_native": "Jemima",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Dove",
    "meaning_native": "יְמִימָה",
    "starting_letter": "J",
    "pronunciation": "jeh-MY-mah",
    "origin": "Hebrew",
    "popularity_rank": 196,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Keziah",
    "name_native": "Keziah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Cassia tree",
    "meaning_native": "קְצִיעָה",
    "starting_letter": "K",
    "pronunciation": "keh-ZY-ah",
    "origin": "Hebrew",
    "popularity_rank": 197,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Keren",
    "name_native": "Keren",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Horn, ray of light",
    "meaning_native": "קֶרֶן",
    "starting_letter": "K",
    "pronunciation": "KEH-ren",
    "origin": "Hebrew",
    "popularity_rank": 198,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Mahlah",
    "name_native": "Mahlah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Disease, weak",
    "meaning_native": "מַחְלָה",
    "starting_letter": "M",
    "pronunciation": "MAH-lah",
    "origin": "Hebrew",
    "popularity_rank": 199,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Noa",
    "name_native": "Noa",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Motion, movement",
    "meaning_native": "נֹעָה",
    "starting_letter": "N",
    "pronunciation": "NOH-ah",
    "origin": "Hebrew",
    "popularity_rank": 200,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Shelomith",
    "name_native": "Shelomith",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Peaceful",
    "meaning_native": "שְׁלוֹמִית",
    "starting_letter": "S",
    "pronunciation": "sheh-loh-MEET",
    "origin": "Hebrew",
    "popularity_rank": 201,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Tirzah",
    "name_native": "Tirzah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Delight, pleasantness",
    "meaning_native": "תִּרְצָה",
    "starting_letter": "T",
    "pronunciation": "TEER-zah",
    "origin": "Hebrew",
    "popularity_rank": 202,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zillah",
    "name_native": "Zillah",
    "gender": "girl",
    "religion": "Christian",
    "language": "Hebrew",
    "meaning_en": "Shade, shadow",
    "meaning_native": "צִלָּה",
    "starting_letter": "Z",
    "pronunciation": "ZIL-ah",
    "origin": "Hebrew",
    "popularity_rank": 203,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  }
]
export default ChristianGirlNames
