import type { NameData } from "@/types/name-data"

export const MuslimGirlNames: NameData[] = [
  {
    "name_en": "<PERSON><PERSON>",
    "name_native": "عائشة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living, prosperous",
    "meaning_native": "زندہ، خوشحال",
    "starting_letter": "A",
    "pronunciation": "<PERSON><PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "<PERSON><PERSON>",
    "name_native": "فاطمة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Daughter of the Prophet",
    "meaning_native": "پیغمبر کی بیٹی",
    "starting_letter": "F",
    "pronunciation": "<PERSON>ima",
    "origin": "Arabic"
  },
  {
    "name_en": "Hana",
    "name_native": "هناء",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Happiness, bliss",
    "meaning_native": "خوشی، مسرت",
    "starting_letter": "H",
    "pronunciation": "Hana",
    "origin": "Arabic"
  },
  {
    "name_en": "Layla",
    "name_native": "ليلى",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Night, dark beauty",
    "meaning_native": "رات، سیاہ خوبصورتی",
    "starting_letter": "L",
    "pronunciation": "Layla",
    "origin": "Arabic"
  },
  {
    "name_en": "Mariam",
    "name_native": "مريم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Virgin Mary, sea of bitterness",
    "meaning_native": "مریم، تلخی کا سمندر",
    "starting_letter": "M",
    "pronunciation": "Mariam",
    "origin": "Arabic"
  },
  {
    "name_en": "Noor",
    "name_native": "نور",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Light, illumination",
    "meaning_native": "روشنی، نور",
    "starting_letter": "N",
    "pronunciation": "Noor",
    "origin": "Arabic"
  },
  {
    "name_en": "Sara",
    "name_native": "سارة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Princess, noble lady",
    "meaning_native": "شہزادی، عالی خاتون",
    "starting_letter": "S",
    "pronunciation": "Sara",
    "origin": "Arabic"
  },
  {
    "name_en": "Zara",
    "name_native": "زارا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Princess, flower",
    "meaning_native": "شہزادی، پھول",
    "starting_letter": "Z",
    "pronunciation": "Zara",
    "origin": "Arabic"
  },
  {
    "name_en": "Amina",
    "name_native": "أمينة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Trustworthy, faithful",
    "meaning_native": "قابل اعتماد، وفادار",
    "starting_letter": "A",
    "pronunciation": "Amina",
    "origin": "Arabic"
  },
  {
    "name_en": "Bushra",
    "name_native": "بشرى",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Good news, glad tidings",
    "meaning_native": "خوشخبری، بشارت",
    "starting_letter": "B",
    "pronunciation": "Bushra",
    "origin": "Arabic"
  },
  {
    "name_en": "Dalia",
    "name_native": "داليا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Grape vine",
    "meaning_native": "انگور کی بیل",
    "starting_letter": "D",
    "pronunciation": "Dalia",
    "origin": "Arabic"
  },
  {
    "name_en": "Farah",
    "name_native": "فرح",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Joy, happiness",
    "meaning_native": "خوشی، مسرت",
    "starting_letter": "F",
    "pronunciation": "Farah",
    "origin": "Arabic"
  },
  {
    "name_en": "Ghazal",
    "name_native": "غزل",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gazelle, poetry",
    "meaning_native": "ہرن، غزل",
    "starting_letter": "G",
    "pronunciation": "Ghazal",
    "origin": "Arabic"
  },
  {
    "name_en": "Huda",
    "name_native": "هدى",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Guidance, right path",
    "meaning_native": "ہدایت، صحیح راہ",
    "starting_letter": "H",
    "pronunciation": "Huda",
    "origin": "Arabic"
  },
  {
    "name_en": "Inaya",
    "name_native": "عناية",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Care, concern",
    "meaning_native": "دیکھ بھال، فکر",
    "starting_letter": "I",
    "pronunciation": "Inaya",
    "origin": "Arabic"
  },
  {
    "name_en": "Jannah",
    "name_native": "جنة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Paradise, garden",
    "meaning_native": "جنت، باغ",
    "starting_letter": "J",
    "pronunciation": "Jannah",
    "origin": "Arabic"
  },
  {
    "name_en": "Khadija",
    "name_native": "خديجة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Premature child",
    "meaning_native": "پیش از وقت بچہ",
    "starting_letter": "K",
    "pronunciation": "Khadija",
    "origin": "Arabic"
  },
  {
    "name_en": "Lubna",
    "name_native": "لبنى",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Storax tree",
    "meaning_native": "ستورکس درخت",
    "starting_letter": "L",
    "pronunciation": "Lubna",
    "origin": "Arabic"
  },
  {
    "name_en": "Mariyah",
    "name_native": "ماریہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Bitter sea",
    "meaning_native": "تلخ سمندر",
    "starting_letter": "M",
    "pronunciation": "Mariyah",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadira",
    "name_native": "ندیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rare, precious",
    "meaning_native": "نایاب، قیمتی",
    "starting_letter": "N",
    "pronunciation": "Nadira",
    "origin": "Arabic"
  },
  {
    "name_en": "Omaima",
    "name_native": "امیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little mother",
    "meaning_native": "چھوٹی ماں",
    "starting_letter": "O",
    "pronunciation": "Omaima",
    "origin": "Arabic"
  },
  {
    "name_en": "Parveen",
    "name_native": "پروین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Pleiades, star cluster",
    "meaning_native": "پلیڈیز، ستاروں کا جھرمٹ",
    "starting_letter": "P",
    "pronunciation": "Parveen",
    "origin": "Persian"
  },
  {
    "name_en": "Qudsia",
    "name_native": "قدسیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Holy, sacred",
    "meaning_native": "مقدس، پاک",
    "starting_letter": "Q",
    "pronunciation": "Qudsia",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahima",
    "name_native": "رحیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Merciful, compassionate",
    "meaning_native": "رحم کرنے والی، مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahima",
    "origin": "Arabic"
  },
  {
    "name_en": "Sabira",
    "name_native": "صابرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient, enduring",
    "meaning_native": "صابر، بردبار",
    "starting_letter": "S",
    "pronunciation": "Sabira",
    "origin": "Arabic"
  },
  {
    "name_en": "Tahira",
    "name_native": "طاہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, chaste",
    "meaning_native": "پاک، پاکیزہ",
    "starting_letter": "T",
    "pronunciation": "Tahira",
    "origin": "Arabic"
  },
  {
    "name_en": "Uzma",
    "name_native": "عظمیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Greatest, most exalted",
    "meaning_native": "سب سے بڑی، سب سے بلند",
    "starting_letter": "U",
    "pronunciation": "Uzma",
    "origin": "Arabic"
  },
  {
    "name_en": "Varda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rose",
    "meaning_native": "گلاب",
    "starting_letter": "V",
    "pronunciation": "Varda",
    "origin": "Arabic"
  },
  {
    "name_en": "Warda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rose flower",
    "meaning_native": "گلاب کا پھول",
    "starting_letter": "W",
    "pronunciation": "Warda",
    "origin": "Arabic"
  },
  {
    "name_en": "Xara",
    "name_native": "زارا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Princess, noble lady",
    "meaning_native": "شہزادی، عالی خاتون",
    "starting_letter": "X",
    "pronunciation": "Xara",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasmin",
    "name_native": "یاسمین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Jasmine flower",
    "meaning_native": "چنبیلی کا پھول",
    "starting_letter": "Y",
    "pronunciation": "Yasmin",
    "origin": "Persian"
  },
  {
    "name_en": "Zahra",
    "name_native": "زہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Bright, shining",
    "meaning_native": "چمکدار، روشن",
    "starting_letter": "Z",
    "pronunciation": "Zahra",
    "origin": "Arabic"
  },
  {
    "name_en": "Aaliyah",
    "name_native": "عالیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Exalted, high",
    "meaning_native": "بلند، عالی",
    "starting_letter": "A",
    "pronunciation": "Aaliyah",
    "origin": "Arabic"
  },
  {
    "name_en": "Bilqis",
    "name_native": "بلقیس",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Queen of Sheba",
    "meaning_native": "ملکہ سبا",
    "starting_letter": "B",
    "pronunciation": "Bilqis",
    "origin": "Arabic"
  },
  {
    "name_en": "Dunya",
    "name_native": "دنیا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "World, universe",
    "meaning_native": "دنیا، کائنات",
    "starting_letter": "D",
    "pronunciation": "Dunya",
    "origin": "Arabic"
  },
  {
    "name_en": "Eman",
    "name_native": "ایمان",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith, belief",
    "meaning_native": "ایمان، عقیدہ",
    "starting_letter": "E",
    "pronunciation": "Eman",
    "origin": "Arabic"
  },
  {
    "name_en": "Firdaus",
    "name_native": "فردوس",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Paradise, garden",
    "meaning_native": "جنت، باغ",
    "starting_letter": "F",
    "pronunciation": "Firdaus",
    "origin": "Persian"
  },
  {
    "name_en": "Ghazala",
    "name_native": "غزالہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gazelle, deer",
    "meaning_native": "ہرن، غزال",
    "starting_letter": "G",
    "pronunciation": "Ghazala",
    "origin": "Arabic"
  },
  {
    "name_en": "Hadiya",
    "name_native": "ہدیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gift, present",
    "meaning_native": "تحفہ، ہدیہ",
    "starting_letter": "H",
    "pronunciation": "Hadiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Ibtisam",
    "name_native": "ابتسام",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Smile",
    "meaning_native": "مسکراہٹ",
    "starting_letter": "I",
    "pronunciation": "Ibtisam",
    "origin": "Arabic"
  },
  {
    "name_en": "Jannat",
    "name_native": "جنت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Paradise, heaven",
    "meaning_native": "جنت، بہشت",
    "starting_letter": "J",
    "pronunciation": "Jannat",
    "origin": "Arabic"
  },
  {
    "name_en": "Kareema",
    "name_native": "کریمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous, noble",
    "meaning_native": "سخی، عالی",
    "starting_letter": "K",
    "pronunciation": "Kareema",
    "origin": "Arabic"
  },
  {
    "name_en": "Lubna",
    "name_native": "لبنیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Storax tree, sweet",
    "meaning_native": "شیرین درخت، میٹھا",
    "starting_letter": "L",
    "pronunciation": "Lubna",
    "origin": "Arabic"
  },
  {
    "name_en": "Maimoona",
    "name_native": "میمونہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Blessed, fortunate",
    "meaning_native": "مبارک، خوش قسمت",
    "starting_letter": "M",
    "pronunciation": "Maimoona",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadira",
    "name_native": "ندیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rare, precious",
    "meaning_native": "نایاب، قیمتی",
    "starting_letter": "N",
    "pronunciation": "Nadira",
    "origin": "Arabic"
  },
  {
    "name_en": "Omaima",
    "name_native": "امیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little mother",
    "meaning_native": "چھوٹی ماں",
    "starting_letter": "O",
    "pronunciation": "Omaima",
    "origin": "Arabic"
  },
  {
    "name_en": "Parveen",
    "name_native": "پروین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Pleiades, star cluster",
    "meaning_native": "ستاروں کا جھرمٹ",
    "starting_letter": "P",
    "pronunciation": "Parveen",
    "origin": "Persian"
  },
  {
    "name_en": "Qudsia",
    "name_native": "قدسیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Holy, sacred",
    "meaning_native": "مقدس، پاک",
    "starting_letter": "Q",
    "pronunciation": "Qudsia",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahima",
    "name_native": "رحیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Merciful, compassionate",
    "meaning_native": "رحم والی، مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahima",
    "origin": "Arabic"
  },
  {
    "name_en": "Safiya",
    "name_native": "صفیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, sincere friend",
    "meaning_native": "پاک، مخلص دوست",
    "starting_letter": "S",
    "pronunciation": "Safiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Tasneem",
    "name_native": "تسنیم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fountain in paradise",
    "meaning_native": "جنت کا چشمہ",
    "starting_letter": "T",
    "pronunciation": "Tasneem",
    "origin": "Arabic"
  },
  {
    "name_en": "Umm Kulthum",
    "name_native": "ام کلثوم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mother of Kulthum",
    "meaning_native": "کلثوم کی ماں",
    "starting_letter": "U",
    "pronunciation": "Umm Kulthum",
    "origin": "Arabic"
  },
  {
    "name_en": "Vasila",
    "name_native": "وسیلة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Intercessor, mediator",
    "meaning_native": "شفاعت کرنے والی",
    "starting_letter": "V",
    "pronunciation": "Vasila",
    "origin": "Arabic"
  },
  {
    "name_en": "Wajida",
    "name_native": "واجدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Found, discovered",
    "meaning_native": "پائی گئی، دریافت شدہ",
    "starting_letter": "W",
    "pronunciation": "Wajida",
    "origin": "Arabic"
  },
  {
    "name_en": "Xalima",
    "name_native": "خلیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient, gentle",
    "meaning_native": "صابر، نرم",
    "starting_letter": "X",
    "pronunciation": "Xalima",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasira",
    "name_native": "یسیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Easy, simple",
    "meaning_native": "آسان، سادہ",
    "starting_letter": "Y",
    "pronunciation": "Yasira",
    "origin": "Arabic"
  },
  {
    "name_en": "Zainab",
    "name_native": "زینب",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fragrant tree, beauty",
    "meaning_native": "خوشبودار درخت، خوبصورتی",
    "starting_letter": "Z",
    "pronunciation": "Zainab",
    "origin": "Arabic"
  },
  {
    "name_en": "Abeer",
    "name_native": "عبیر",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fragrance, perfume",
    "meaning_native": "خوشبو، عطر",
    "starting_letter": "A",
    "pronunciation": "Abeer",
    "origin": "Arabic"
  },
  {
    "name_en": "Bushra",
    "name_native": "بشرى",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Good news, glad tidings",
    "meaning_native": "خوشخبری، بشارت",
    "starting_letter": "B",
    "pronunciation": "Bushra",
    "origin": "Arabic"
  },
  {
    "name_en": "Dua",
    "name_native": "دعا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Prayer, supplication",
    "meaning_native": "دعا، التجا",
    "starting_letter": "D",
    "pronunciation": "Dua",
    "origin": "Arabic"
  },
  {
    "name_en": "Eman",
    "name_native": "ایمان",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith, belief",
    "meaning_native": "ایمان، عقیدہ",
    "starting_letter": "E",
    "pronunciation": "Eman",
    "origin": "Arabic"
  },
  {
    "name_en": "Firdaus",
    "name_native": "فردوس",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Paradise, garden",
    "meaning_native": "جنت، باغ",
    "starting_letter": "F",
    "pronunciation": "Firdaus",
    "origin": "Persian"
  },
  {
    "name_en": "Ghazala",
    "name_native": "غزالہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gazelle, deer",
    "meaning_native": "ہرن، غزال",
    "starting_letter": "G",
    "pronunciation": "Ghazala",
    "origin": "Arabic"
  },
  {
    "name_en": "Hadiya",
    "name_native": "ہدیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gift, present",
    "meaning_native": "تحفہ، ہدیہ",
    "starting_letter": "H",
    "pronunciation": "Hadiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Ibtisam",
    "name_native": "ابتسام",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Smile",
    "meaning_native": "مسکراہٹ",
    "starting_letter": "I",
    "pronunciation": "Ibtisam",
    "origin": "Arabic"
  },
  {
    "name_en": "Jannat",
    "name_native": "جنت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Paradise, heaven",
    "meaning_native": "جنت، بہشت",
    "starting_letter": "J",
    "pronunciation": "Jannat",
    "origin": "Arabic"
  },
  {
    "name_en": "Kareema",
    "name_native": "کریمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous, noble",
    "meaning_native": "سخی، عالی",
    "starting_letter": "K",
    "pronunciation": "Kareema",
    "origin": "Arabic"
  },
  {
    "name_en": "Lubna",
    "name_native": "لبنیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Storax tree, sweet",
    "meaning_native": "شیرین درخت، میٹھا",
    "starting_letter": "L",
    "pronunciation": "Lubna",
    "origin": "Arabic"
  },
  {
    "name_en": "Maimoona",
    "name_native": "میمونہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Blessed, fortunate",
    "meaning_native": "مبارک، خوش قسمت",
    "starting_letter": "M",
    "pronunciation": "Maimoona",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadira",
    "name_native": "ندیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rare, precious",
    "meaning_native": "نایاب، قیمتی",
    "starting_letter": "N",
    "pronunciation": "Nadira",
    "origin": "Arabic"
  },
  {
    "name_en": "Omaima",
    "name_native": "امیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little mother",
    "meaning_native": "چھوٹی ماں",
    "starting_letter": "O",
    "pronunciation": "Omaima",
    "origin": "Arabic"
  },
  {
    "name_en": "Parveen",
    "name_native": "پروین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Pleiades, star cluster",
    "meaning_native": "ستاروں کا جھرمٹ",
    "starting_letter": "P",
    "pronunciation": "Parveen",
    "origin": "Persian"
  },
  {
    "name_en": "Qudsia",
    "name_native": "قدسیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Holy, sacred",
    "meaning_native": "مقدس، پاک",
    "starting_letter": "Q",
    "pronunciation": "Qudsia",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahima",
    "name_native": "رحیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Merciful, compassionate",
    "meaning_native": "رحم والی، مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahima",
    "origin": "Arabic"
  },
  {
    "name_en": "Safiya",
    "name_native": "صفیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, sincere friend",
    "meaning_native": "پاک، مخلص دوست",
    "starting_letter": "S",
    "pronunciation": "Safiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Tasneem",
    "name_native": "تسنیم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fountain in paradise",
    "meaning_native": "جنت کا چشمہ",
    "starting_letter": "T",
    "pronunciation": "Tasneem",
    "origin": "Arabic"
  },
  {
    "name_en": "Umm Kulthum",
    "name_native": "ام کلثوم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mother of Kulthum",
    "meaning_native": "کلثوم کی ماں",
    "starting_letter": "U",
    "pronunciation": "Umm Kulthum",
    "origin": "Arabic"
  },
  {
    "name_en": "Vasila",
    "name_native": "وسیلة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Intercessor, mediator",
    "meaning_native": "شفاعت کرنے والی",
    "starting_letter": "V",
    "pronunciation": "Vasila",
    "origin": "Arabic"
  },
  {
    "name_en": "Wajida",
    "name_native": "واجدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Found, discovered",
    "meaning_native": "پائی گئی، دریافت شدہ",
    "starting_letter": "W",
    "pronunciation": "Wajida",
    "origin": "Arabic"
  },
  {
    "name_en": "Xalima",
    "name_native": "خلیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient, gentle",
    "meaning_native": "صابر، نرم",
    "starting_letter": "X",
    "pronunciation": "Xalima",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasira",
    "name_native": "یسیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Easy, simple",
    "meaning_native": "آسان، سادہ",
    "starting_letter": "Y",
    "pronunciation": "Yasira",
    "origin": "Arabic"
  },
  {
    "name_en": "Zainab",
    "name_native": "زینب",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fragrant tree, beauty",
    "meaning_native": "خوشبودار درخت، خوبصورتی",
    "starting_letter": "Z",
    "pronunciation": "Zainab",
    "origin": "Arabic"
  },
  {
    "name_en": "Adila",
    "name_native": "عادلہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Just, fair",
    "meaning_native": "عادل، منصف",
    "starting_letter": "A",
    "pronunciation": "Adila",
    "origin": "Arabic"
  },
  {
    "name_en": "Badriya",
    "name_native": "بدریہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Full moon",
    "meaning_native": "پورا چاند",
    "starting_letter": "B",
    "pronunciation": "Badriya",
    "origin": "Arabic"
  },
  {
    "name_en": "Dilshad",
    "name_native": "دلشاد",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Happy heart",
    "meaning_native": "خوش دل",
    "starting_letter": "D",
    "pronunciation": "Dilshad",
    "origin": "Persian"
  },
  {
    "name_en": "Ehsan",
    "name_native": "احسان",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kindness, benevolence",
    "meaning_native": "احسان، مہربانی",
    "starting_letter": "E",
    "pronunciation": "Ehsan",
    "origin": "Arabic"
  },
  {
    "name_en": "Fawzia",
    "name_native": "فوزیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "F",
    "pronunciation": "Fawzia",
    "origin": "Arabic"
  },
  {
    "name_en": "Gulnaz",
    "name_native": "گلناز",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Graceful flower",
    "meaning_native": "خوبصورت پھول",
    "starting_letter": "G",
    "pronunciation": "Gulnaz",
    "origin": "Persian"
  },
  {
    "name_en": "Hafsa",
    "name_native": "حفصہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Young lioness",
    "meaning_native": "جوان شیرنی",
    "starting_letter": "H",
    "pronunciation": "Hafsa",
    "origin": "Arabic"
  },
  {
    "name_en": "Iffat",
    "name_native": "عفت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Chastity, modesty",
    "meaning_native": "پاکدامنی، حیا",
    "starting_letter": "I",
    "pronunciation": "Iffat",
    "origin": "Arabic"
  },
  {
    "name_en": "Javeria",
    "name_native": "جویریہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Small bird",
    "meaning_native": "چھوٹا پرندہ",
    "starting_letter": "J",
    "pronunciation": "Javeria",
    "origin": "Arabic"
  },
  {
    "name_en": "Khalida",
    "name_native": "خالدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Immortal, eternal",
    "meaning_native": "ہمیشہ رہنے والی",
    "starting_letter": "K",
    "pronunciation": "Khalida",
    "origin": "Arabic"
  },
  {
    "name_en": "Laila",
    "name_native": "لیلیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Night beauty",
    "meaning_native": "رات کی خوبصورتی",
    "starting_letter": "L",
    "pronunciation": "Laila",
    "origin": "Arabic"
  },
  {
    "name_en": "Mahira",
    "name_native": "ماہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Skilled, expert",
    "meaning_native": "ماہر، ہنرمند",
    "starting_letter": "M",
    "pronunciation": "Mahira",
    "origin": "Arabic"
  },
  {
    "name_en": "Nabila",
    "name_native": "نبیلہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Noble, honorable",
    "meaning_native": "عزت دار، شریف",
    "starting_letter": "N",
    "pronunciation": "Nabila",
    "origin": "Arabic"
  },
  {
    "name_en": "Omar",
    "name_native": "عمر",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived, flourishing",
    "meaning_native": "لمبی عمر والا",
    "starting_letter": "O",
    "pronunciation": "Omar",
    "origin": "Arabic"
  },
  {
    "name_en": "Parisa",
    "name_native": "پریسا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fairy-like, angelic",
    "meaning_native": "پری جیسی، فرشتہ صفت",
    "starting_letter": "P",
    "pronunciation": "Parisa",
    "origin": "Persian"
  },
  {
    "name_en": "Qamar",
    "name_native": "قمر",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Moon",
    "meaning_native": "چاند",
    "starting_letter": "Q",
    "pronunciation": "Qamar",
    "origin": "Arabic"
  },
  {
    "name_en": "Rashida",
    "name_native": "رشیدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided",
    "meaning_native": "صحیح راہ پر",
    "starting_letter": "R",
    "pronunciation": "Rashida",
    "origin": "Arabic"
  },
  {
    "name_en": "Sadiya",
    "name_native": "سعدیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fortunate, lucky",
    "meaning_native": "خوش قسمت، مبارک",
    "starting_letter": "S",
    "pronunciation": "Sadiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Tahira",
    "name_native": "طاہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, chaste",
    "meaning_native": "پاک، پاکیزہ",
    "starting_letter": "T",
    "pronunciation": "Tahira",
    "origin": "Arabic"
  },
  {
    "name_en": "Uzma",
    "name_native": "عظمیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Greatest, most exalted",
    "meaning_native": "سب سے بڑی، سب سے بلند",
    "starting_letter": "U",
    "pronunciation": "Uzma",
    "origin": "Arabic"
  },
  {
    "name_en": "Varda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rose",
    "meaning_native": "گلاب",
    "starting_letter": "V",
    "pronunciation": "Varda",
    "origin": "Arabic"
  },
  {
    "name_en": "Warda",
    "name_native": "وردہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rose flower",
    "meaning_native": "گلاب کا پھول",
    "starting_letter": "W",
    "pronunciation": "Warda",
    "origin": "Arabic"
  },
  {
    "name_en": "Xara",
    "name_native": "زارا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Princess, noble lady",
    "meaning_native": "شہزادی، عالی خاتون",
    "starting_letter": "X",
    "pronunciation": "Xara",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasmin",
    "name_native": "یاسمین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Jasmine flower",
    "meaning_native": "چنبیلی کا پھول",
    "starting_letter": "Y",
    "pronunciation": "Yasmin",
    "origin": "Persian"
  },
  {
    "name_en": "Zahra",
    "name_native": "زہرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Bright, shining",
    "meaning_native": "چمکدار، روشن",
    "starting_letter": "Z",
    "pronunciation": "Zahra",
    "origin": "Arabic"
  },
  {
    "name_en": "Aaliyah",
    "name_native": "عالیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Exalted, high",
    "meaning_native": "بلند، عالی",
    "starting_letter": "A",
    "pronunciation": "Aaliyah",
    "origin": "Arabic"
  },
  {
    "name_en": "Bilqis",
    "name_native": "بلقیس",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Queen of Sheba",
    "meaning_native": "ملکہ سبا",
    "starting_letter": "B",
    "pronunciation": "Bilqis",
    "origin": "Arabic"
  },
  {
    "name_en": "Dunya",
    "name_native": "دنیا",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "World, universe",
    "meaning_native": "دنیا، کائنات",
    "starting_letter": "D",
    "pronunciation": "Dunya",
    "origin": "Arabic"
  },
  {
    "name_en": "Eman",
    "name_native": "ایمان",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith, belief",
    "meaning_native": "ایمان، عقیدہ",
    "starting_letter": "E",
    "pronunciation": "Eman",
    "origin": "Arabic"
  },
  {
    "name_en": "Firdaus",
    "name_native": "فردوس",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Paradise, garden",
    "meaning_native": "جنت، باغ",
    "starting_letter": "F",
    "pronunciation": "Firdaus",
    "origin": "Persian"
  },
  {
    "name_en": "Ghazala",
    "name_native": "غزالہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gazelle, deer",
    "meaning_native": "ہرن، غزال",
    "starting_letter": "G",
    "pronunciation": "Ghazala",
    "origin": "Arabic"
  },
  {
    "name_en": "Hadiya",
    "name_native": "ہدیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gift, present",
    "meaning_native": "تحفہ، ہدیہ",
    "starting_letter": "H",
    "pronunciation": "Hadiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Ibtisam",
    "name_native": "ابتسام",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Smile",
    "meaning_native": "مسکراہٹ",
    "starting_letter": "I",
    "pronunciation": "Ibtisam",
    "origin": "Arabic"
  },
  {
    "name_en": "Jannat",
    "name_native": "جنت",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Paradise, heaven",
    "meaning_native": "جنت، بہشت",
    "starting_letter": "J",
    "pronunciation": "Jannat",
    "origin": "Arabic"
  },
  {
    "name_en": "Kareema",
    "name_native": "کریمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous, noble",
    "meaning_native": "سخی، عالی",
    "starting_letter": "K",
    "pronunciation": "Kareema",
    "origin": "Arabic"
  },
  {
    "name_en": "Lubna",
    "name_native": "لبنیٰ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Storax tree, sweet",
    "meaning_native": "شیرین درخت، میٹھا",
    "starting_letter": "L",
    "pronunciation": "Lubna",
    "origin": "Arabic"
  },
  {
    "name_en": "Maimoona",
    "name_native": "میمونہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Blessed, fortunate",
    "meaning_native": "مبارک، خوش قسمت",
    "starting_letter": "M",
    "pronunciation": "Maimoona",
    "origin": "Arabic"
  },
  {
    "name_en": "Nadira",
    "name_native": "ندیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rare, precious",
    "meaning_native": "نایاب، قیمتی",
    "starting_letter": "N",
    "pronunciation": "Nadira",
    "origin": "Arabic"
  },
  {
    "name_en": "Omaima",
    "name_native": "امیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little mother",
    "meaning_native": "چھوٹی ماں",
    "starting_letter": "O",
    "pronunciation": "Omaima",
    "origin": "Arabic"
  },
  {
    "name_en": "Parveen",
    "name_native": "پروین",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Pleiades, star cluster",
    "meaning_native": "ستاروں کا جھرمٹ",
    "starting_letter": "P",
    "pronunciation": "Parveen",
    "origin": "Persian"
  },
  {
    "name_en": "Qudsia",
    "name_native": "قدسیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Holy, sacred",
    "meaning_native": "مقدس، پاک",
    "starting_letter": "Q",
    "pronunciation": "Qudsia",
    "origin": "Arabic"
  },
  {
    "name_en": "Rahima",
    "name_native": "رحیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Merciful, compassionate",
    "meaning_native": "رحم والی، مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahima",
    "origin": "Arabic"
  },
  {
    "name_en": "Safiya",
    "name_native": "صفیہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, sincere friend",
    "meaning_native": "پاک، مخلص دوست",
    "starting_letter": "S",
    "pronunciation": "Safiya",
    "origin": "Arabic"
  },
  {
    "name_en": "Tasneem",
    "name_native": "تسنیم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fountain in paradise",
    "meaning_native": "جنت کا چشمہ",
    "starting_letter": "T",
    "pronunciation": "Tasneem",
    "origin": "Arabic"
  },
  {
    "name_en": "Umm Kulthum",
    "name_native": "ام کلثوم",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mother of Kulthum",
    "meaning_native": "کلثوم کی ماں",
    "starting_letter": "U",
    "pronunciation": "Umm Kulthum",
    "origin": "Arabic"
  },
  {
    "name_en": "Vasila",
    "name_native": "وسیلة",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Intercessor, mediator",
    "meaning_native": "شفاعت کرنے والی",
    "starting_letter": "V",
    "pronunciation": "Vasila",
    "origin": "Arabic"
  },
  {
    "name_en": "Wajida",
    "name_native": "واجدہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Found, discovered",
    "meaning_native": "پائی گئی، دریافت شدہ",
    "starting_letter": "W",
    "pronunciation": "Wajida",
    "origin": "Arabic"
  },
  {
    "name_en": "Xalima",
    "name_native": "خلیمہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient, gentle",
    "meaning_native": "صابر، نرم",
    "starting_letter": "X",
    "pronunciation": "Xalima",
    "origin": "Arabic"
  },
  {
    "name_en": "Yasira",
    "name_native": "یسیرہ",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Easy, simple",
    "meaning_native": "آسان، سادہ",
    "starting_letter": "Y",
    "pronunciation": "Yasira",
    "origin": "Arabic"
  },
  {
    "name_en": "Zainab",
    "name_native": "زینب",
    "gender": "girl",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fragrant tree, beauty",
    "meaning_native": "خوشبودار درخت، خوبصورتی",
    "starting_letter": "Z",
    "pronunciation": "Zainab",
    "origin": "Arabic"
  }
]
export default MuslimGirlNames
