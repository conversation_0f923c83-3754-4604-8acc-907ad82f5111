import type { NameData } from "@/types/name-data"

export const MuslimBoyNames: NameData[] = [
  {
    "name_en": "<PERSON>",
    "name_native": "أحمد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Most praised, commendable",
    "meaning_native": "سب سے زیادہ تعریف کے لائق",
    "starting_letter": "A",
    "pronunciation": "<PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "<PERSON>",
    "name_native": "علي",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "High, elevated, noble",
    "meaning_native": "بلند، عالی، شریف",
    "starting_letter": "A",
    "pronunciation": "Ali",
    "origin": "Arabic"
  },
  {
    "name_en": "Hassan",
    "name_native": "حسن",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Handsome, good, beautiful",
    "meaning_native": "خوبصورت، اچھا، حسین",
    "starting_letter": "H",
    "pronunciation": "<PERSON>",
    "origin": "Arabic"
  },
  {
    "name_en": "Ibrahim",
    "name_native": "إبراهيم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of many nations",
    "meaning_native": "بہت سی قوموں کے باپ",
    "starting_letter": "I",
    "pronunciation": "Ibrahim",
    "origin": "Arabic"
  },
  {
    "name_en": "Muhammad",
    "name_native": "محمد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiseworthy, praised",
    "meaning_native": "قابل تعریف، تعریف کیا گیا",
    "starting_letter": "M",
    "pronunciation": "Muhammad",
    "origin": "Arabic"
  },
  {
    "name_en": "Omar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived, flourishing",
    "meaning_native": "لمبی عمر، خوشحال",
    "starting_letter": "O",
    "pronunciation": "Omar",
    "origin": "Arabic"
  },
  {
    "name_en": "Yusuf",
    "name_native": "يوسف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "God will increase",
    "meaning_native": "خدا بڑھائے گا",
    "starting_letter": "Y",
    "pronunciation": "Yusuf",
    "origin": "Arabic"
  },
  {
    "name_en": "Zain",
    "name_native": "زين",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beauty, grace",
    "meaning_native": "خوبصورتی، فضل",
    "starting_letter": "Z",
    "pronunciation": "Zain",
    "origin": "Arabic"
  },
  {
    "name_en": "Amin",
    "name_native": "أمين",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Trustworthy, faithful",
    "meaning_native": "قابل اعتماد، وفادار",
    "starting_letter": "A",
    "pronunciation": "Amin",
    "origin": "Arabic"
  },
  {
    "name_en": "Bilal",
    "name_native": "بلال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Water, moisture",
    "meaning_native": "پانی، نمی",
    "starting_letter": "B",
    "pronunciation": "Bilal",
    "origin": "Arabic"
  },
  {
    "name_en": "Dawud",
    "name_native": "داود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved, David",
    "meaning_native": "پیارا، داؤد",
    "starting_letter": "D",
    "pronunciation": "Dawud",
    "origin": "Arabic"
  },
  {
    "name_en": "Fahad",
    "name_native": "فهد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard, panther",
    "meaning_native": "چیتا، تیندوا",
    "starting_letter": "F",
    "pronunciation": "Fahad",
    "origin": "Arabic"
  },
  {
    "name_en": "Ghazi",
    "name_native": "غازي",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior, conqueror",
    "meaning_native": "جنگجو، فاتح",
    "starting_letter": "G",
    "pronunciation": "Ghazi",
    "origin": "Arabic"
  },
  {
    "name_en": "Hamza",
    "name_native": "حمزة",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Lion, strong",
    "meaning_native": "شیر، مضبوط",
    "starting_letter": "H",
    "pronunciation": "Hamza",
    "origin": "Arabic"
  },
  {
    "name_en": "Idris",
    "name_native": "إدريس",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Interpreter, teacher",
    "meaning_native": "مترجم، استاد",
    "starting_letter": "I",
    "pronunciation": "Idris",
    "origin": "Arabic"
  },
  {
    "name_en": "Jabir",
    "name_native": "جابر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Comforter, consoler",
    "meaning_native": "تسلی دینے والا",
    "starting_letter": "J",
    "pronunciation": "Jabir",
    "origin": "Arabic"
  },
  {
    "name_en": "Khalid",
    "name_native": "خالد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Eternal, immortal",
    "meaning_native": "ہمیشہ رہنے والا",
    "starting_letter": "K",
    "pronunciation": "Khalid",
    "origin": "Arabic"
  },
  {
    "name_en": "Imran",
    "name_native": "عمران",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Prosperity, population",
    "meaning_native": "آبادی، خوشحالی",
    "starting_letter": "I",
    "pronunciation": "Imran",
    "origin": "Arabic",
    "popularity_rank": 27,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 3200
  },
  {
    "name_en": "Sami",
    "name_native": "سامي",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Exalted, sublime",
    "meaning_native": "بلند، عظیم",
    "starting_letter": "S",
    "pronunciation": "Sami",
    "origin": "Arabic",
    "popularity_rank": 28,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 3100
  },
  {
    "name_en": "Farhan",
    "name_native": "فرحان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Happy, joyful",
    "meaning_native": "خوش، مسرور",
    "starting_letter": "F",
    "pronunciation": "Farhan",
    "origin": "Arabic",
    "popularity_rank": 29,
    "popularity_change": "+3%",
    "trending_status": "rising",
    "search_volume": 3000
  },
  {
    "name_en": "Naveed",
    "name_native": "نوید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Good news, glad tidings",
    "meaning_native": "خوشخبری",
    "starting_letter": "N",
    "pronunciation": "Naveed",
    "origin": "Persian",
    "popularity_rank": 30,
    "popularity_change": "+2%",
    "trending_status": "stable",
    "search_volume": 2900
  },
  {
    "name_en": "Rashid",
    "name_native": "رشید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided, wise",
    "meaning_native": "ہدایت یافتہ، عقلمند",
    "starting_letter": "R",
    "pronunciation": "Rashid",
    "origin": "Arabic",
    "popularity_rank": 31,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2800
  },
  {
    "name_en": "Saad",
    "name_native": "سعد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Felicity, happiness",
    "meaning_native": "خوشی، سعادت",
    "starting_letter": "S",
    "pronunciation": "Saad",
    "origin": "Arabic",
    "popularity_rank": 32,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 2700
  },
  {
    "name_en": "Tariq",
    "name_native": "طارق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Morning star, night visitor",
    "meaning_native": "صبح کا ستارہ، رات کا مہمان",
    "starting_letter": "T",
    "pronunciation": "Tariq",
    "origin": "Arabic",
    "popularity_rank": 33,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2600
  },
  {
    "name_en": "Zubair",
    "name_native": "زبیر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Strong, powerful",
    "meaning_native": "طاقتور، مضبوط",
    "starting_letter": "Z",
    "pronunciation": "Zubair",
    "origin": "Arabic",
    "popularity_rank": 34,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 2500
  },
  {
    "name_en": "Qasim",
    "name_native": "قاسم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Distributor, divider",
    "meaning_native": "تقسیم کرنے والا",
    "starting_letter": "Q",
    "pronunciation": "Qasim",
    "origin": "Arabic",
    "popularity_rank": 35,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2400
  },
  {
    "name_en": "Rayyan",
    "name_native": "ریان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Luxuriant, plentiful",
    "meaning_native": "سرسبز، وافر",
    "starting_letter": "R",
    "pronunciation": "Rayyan",
    "origin": "Arabic",
    "popularity_rank": 36,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 2300
  },
  {
    "name_en": "Suleiman",
    "name_native": "سلیمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Man of peace",
    "meaning_native": "امن والا آدمی",
    "starting_letter": "S",
    "pronunciation": "Suleiman",
    "origin": "Arabic",
    "popularity_rank": 37,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2200
  },
  {
    "name_en": "Usman",
    "name_native": "عثمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "The chosen one, wise",
    "meaning_native": "منتخب، عقلمند",
    "starting_letter": "U",
    "pronunciation": "Usman",
    "origin": "Arabic",
    "popularity_rank": 38,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 2100
  },
  {
    "name_en": "Waleed",
    "name_native": "ولید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Newborn, child",
    "meaning_native": "نوزائیدہ، بچہ",
    "starting_letter": "W",
    "pronunciation": "Waleed",
    "origin": "Arabic",
    "popularity_rank": 39,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2000
  },
  {
    "name_en": "Yasir",
    "name_native": "یاسر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Wealthy, prosperous",
    "meaning_native": "دولت مند، خوشحال",
    "starting_letter": "Y",
    "pronunciation": "Yasir",
    "origin": "Arabic",
    "popularity_rank": 40,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1900
  },
  {
    "name_en": "Zayd",
    "name_native": "زيد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Growth, abundance",
    "meaning_native": "اضافہ، کثرت",
    "starting_letter": "Z",
    "pronunciation": "Zayd",
    "origin": "Arabic",
    "popularity_rank": 41,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1800
  },
  {
    "name_en": "Ayaan",
    "name_native": "عیان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gift of God",
    "meaning_native": "خدا کا تحفہ",
    "starting_letter": "A",
    "pronunciation": "Ayaan",
    "origin": "Arabic",
    "popularity_rank": 42,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1700
  },
  {
    "name_en": "Bashir",
    "name_native": "بشیر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Bringer of good news",
    "meaning_native": "خوشخبری دینے والا",
    "starting_letter": "B",
    "pronunciation": "Bashir",
    "origin": "Arabic",
    "popularity_rank": 43,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1600
  },
  {
    "name_en": "Danish",
    "name_native": "دانش",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Knowledge, wisdom",
    "meaning_native": "علم، حکمت",
    "starting_letter": "D",
    "pronunciation": "Danish",
    "origin": "Persian",
    "popularity_rank": 44,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1500
  },
  {
    "name_en": "Ehsan",
    "name_native": "احسان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfection, excellence",
    "meaning_native": "کمال، عمدگی",
    "starting_letter": "E",
    "pronunciation": "Ehsan",
    "origin": "Arabic",
    "popularity_rank": 45,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1400
  },
  {
    "name_en": "Faisal",
    "name_native": "فیصل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Decisive, judge",
    "meaning_native": "فیصلہ کرنے والا، منصف",
    "starting_letter": "F",
    "pronunciation": "Faisal",
    "origin": "Arabic",
    "popularity_rank": 46,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1300
  },
  {
    "name_en": "Ghani",
    "name_native": "غنی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rich, wealthy",
    "meaning_native": "مالدار، دولت مند",
    "starting_letter": "G",
    "pronunciation": "Ghani",
    "origin": "Arabic",
    "popularity_rank": 47,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1200
  },
  {
    "name_en": "Haroon",
    "name_native": "ہارون",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior lion",
    "meaning_native": "شیر جنگجو",
    "starting_letter": "H",
    "pronunciation": "Haroon",
    "origin": "Arabic",
    "popularity_rank": 48,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1100
  },
  {
    "name_en": "Ilyas",
    "name_native": "الیاس",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Elijah, the Lord is my God",
    "meaning_native": "ایلیا، خدا میرا خدا ہے",
    "starting_letter": "I",
    "pronunciation": "Ilyas",
    "origin": "Arabic",
    "popularity_rank": 49,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1000
  },
  {
    "name_en": "Junaid",
    "name_native": "جنید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Soldier, warrior",
    "meaning_native": "سپاہی، جنگجو",
    "starting_letter": "J",
    "pronunciation": "Junaid",
    "origin": "Arabic",
    "popularity_rank": 50,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 900
  },
  {
    "name_en": "Kamran",
    "name_native": "کامران",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Successful, fortunate",
    "meaning_native": "کامیاب، خوش قسمت",
    "starting_letter": "K",
    "pronunciation": "Kamran",
    "origin": "Persian",
    "popularity_rank": 51,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 850
  },
  {
    "name_en": "Layth",
    "name_native": "لیث",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Lion",
    "meaning_native": "شیر",
    "starting_letter": "L",
    "pronunciation": "Layth",
    "origin": "Arabic",
    "popularity_rank": 52,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 800
  },
  {
    "name_en": "Malik",
    "name_native": "مالک",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "King, owner",
    "meaning_native": "بادشاہ، مالک",
    "starting_letter": "M",
    "pronunciation": "Malik",
    "origin": "Arabic",
    "popularity_rank": 53,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 750
  },
  {
    "name_en": "Nabil",
    "name_native": "نبیل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Noble, honorable",
    "meaning_native": "شریف، قابل احترام",
    "starting_letter": "N",
    "pronunciation": "Nabil",
    "origin": "Arabic",
    "popularity_rank": 54,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 700
  },
  {
    "name_en": "Omar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived, flourishing",
    "meaning_native": "لمبی عمر، خوشحال",
    "starting_letter": "O",
    "pronunciation": "Omar",
    "origin": "Arabic",
    "popularity_rank": 55,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 650
  },
  {
    "name_en": "Parvez",
    "name_native": "پرویز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "P",
    "pronunciation": "Parvez",
    "origin": "Persian",
    "popularity_rank": 56,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 600
  },
  {
    "name_en": "Qadir",
    "name_native": "قادر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Capable, powerful",
    "meaning_native": "قابل، طاقتور",
    "starting_letter": "Q",
    "pronunciation": "Qadir",
    "origin": "Arabic",
    "popularity_rank": 57,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 550
  },
  {
    "name_en": "Rahim",
    "name_native": "رحیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Merciful, compassionate",
    "meaning_native": "رحم کرنے والا، مہربان",
    "starting_letter": "R",
    "pronunciation": "Rahim",
    "origin": "Arabic",
    "popularity_rank": 58,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 500
  },
  {
    "name_en": "Salim",
    "name_native": "سالم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Safe, secure",
    "meaning_native": "محفوظ، محفوظ",
    "starting_letter": "S",
    "pronunciation": "Salim",
    "origin": "Arabic",
    "popularity_rank": 59,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 450
  },
  {
    "name_en": "Tahir",
    "name_native": "طاہر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, clean",
    "meaning_native": "پاک، صاف",
    "starting_letter": "T",
    "pronunciation": "Tahir",
    "origin": "Arabic",
    "popularity_rank": 60,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 400
  },
  {
    "name_en": "Ubaid",
    "name_native": "عبید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا نوکر",
    "starting_letter": "U",
    "pronunciation": "Ubaid",
    "origin": "Arabic",
    "popularity_rank": 61,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 350
  },
  {
    "name_en": "Vahid",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "V",
    "pronunciation": "Vahid",
    "origin": "Arabic",
    "popularity_rank": 62,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 300
  },
  {
    "name_en": "Wahid",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "W",
    "pronunciation": "Wahid",
    "origin": "Arabic",
    "popularity_rank": 63,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 250
  },
  {
    "name_en": "Yahya",
    "name_native": "یحییٰ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "John, God is gracious",
    "meaning_native": "یحییٰ، خدا مہربان ہے",
    "starting_letter": "Y",
    "pronunciation": "Yahya",
    "origin": "Arabic",
    "popularity_rank": 64,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 200
  },
  {
    "name_en": "Zafar",
    "name_native": "ظفر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victory, triumph",
    "meaning_native": "فتح، کامیابی",
    "starting_letter": "Z",
    "pronunciation": "Zafar",
    "origin": "Arabic",
    "popularity_rank": 65,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 150
  },
  {
    "name_en": "Adil",
    "name_native": "عادل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Just, fair",
    "meaning_native": "عادل، منصف",
    "starting_letter": "A",
    "pronunciation": "Adil",
    "origin": "Arabic",
    "popularity_rank": 66,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 100
  },
  {
    "name_en": "Basit",
    "name_native": "باسط",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander, generous",
    "meaning_native": "وسعت دینے والا، سخی",
    "starting_letter": "B",
    "pronunciation": "Basit",
    "origin": "Arabic",
    "popularity_rank": 67,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 90
  },
  {
    "name_en": "Daud",
    "name_native": "داود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved, David",
    "meaning_native": "پیارا، داؤد",
    "starting_letter": "D",
    "pronunciation": "Daud",
    "origin": "Arabic",
    "popularity_rank": 68,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 80
  },
  {
    "name_en": "Eisa",
    "name_native": "عیسیٰ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Jesus, God saves",
    "meaning_native": "عیسیٰ، خدا بچاتا ہے",
    "starting_letter": "E",
    "pronunciation": "Eisa",
    "origin": "Arabic",
    "popularity_rank": 69,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 70
  },
  {
    "name_en": "Fazal",
    "name_native": "فضل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Grace, favor",
    "meaning_native": "فضل، احسان",
    "starting_letter": "F",
    "pronunciation": "Fazal",
    "origin": "Arabic",
    "popularity_rank": 70,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 60
  },
  {
    "name_en": "Ghalib",
    "name_native": "غالب",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Conqueror, victorious",
    "meaning_native": "فاتح، غالب",
    "starting_letter": "G",
    "pronunciation": "Ghalib",
    "origin": "Arabic",
    "popularity_rank": 71,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 50
  },
  {
    "name_en": "Hadi",
    "name_native": "ہارون",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Guide, leader",
    "meaning_native": "رہنما، قائد",
    "starting_letter": "H",
    "pronunciation": "Hadi",
    "origin": "Arabic",
    "popularity_rank": 72,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 40
  },
  {
    "name_en": "Iqbal",
    "name_native": "اقبال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Fortune, prosperity",
    "meaning_native": "قسمت، خوشحالی",
    "starting_letter": "I",
    "pronunciation": "Iqbal",
    "origin": "Arabic",
    "popularity_rank": 73,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 30
  },
  {
    "name_en": "Javed",
    "name_native": "جاوید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Eternal, immortal",
    "meaning_native": "ہمیشہ، لافانی",
    "starting_letter": "J",
    "pronunciation": "Javed",
    "origin": "Persian",
    "popularity_rank": 74,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 20
  },
  {
    "name_en": "Karim",
    "name_native": "کریم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous, noble",
    "meaning_native": "سخی، شریف",
    "starting_letter": "K",
    "pronunciation": "Karim",
    "origin": "Arabic",
    "popularity_rank": 75,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 10
  },
  {
    "name_en": "Latif",
    "name_native": "لطیف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle, kind",
    "meaning_native": "نرم، مہربان",
    "starting_letter": "L",
    "pronunciation": "Latif",
    "origin": "Arabic",
    "popularity_rank": 76,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 9
  },
  {
    "name_en": "Mahmood",
    "name_native": "محمود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiseworthy",
    "meaning_native": "قابل تعریف",
    "starting_letter": "M",
    "pronunciation": "Mahmood",
    "origin": "Arabic",
    "popularity_rank": 77,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 8
  },
  {
    "name_en": "Nadeem",
    "name_native": "ندیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "N",
    "pronunciation": "Nadeem",
    "origin": "Arabic",
    "popularity_rank": 78,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 7
  },
  {
    "name_en": "Obaid",
    "name_native": "عبید",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا نوکر",
    "starting_letter": "O",
    "pronunciation": "Obaid",
    "origin": "Arabic",
    "popularity_rank": 79,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 6
  },
  {
    "name_en": "Pervaiz",
    "name_native": "پرویز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "P",
    "pronunciation": "Pervaiz",
    "origin": "Persian",
    "popularity_rank": 80,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 5
  },
  {
    "name_en": "Qais",
    "name_native": "قیس",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Firm, determined",
    "meaning_native": "مضبوط، پختہ",
    "starting_letter": "Q",
    "pronunciation": "Qais",
    "origin": "Arabic",
    "popularity_rank": 81,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 4
  },
  {
    "name_en": "Rafiq",
    "name_native": "رفیق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "R",
    "pronunciation": "Rafiq",
    "origin": "Arabic",
    "popularity_rank": 82,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 3
  },
  {
    "name_en": "Sajid",
    "name_native": "ساجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "One who prostrates",
    "meaning_native": "سجدہ کرنے والا",
    "starting_letter": "S",
    "pronunciation": "Sajid",
    "origin": "Arabic",
    "popularity_rank": 83,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 2
  },
  {
    "name_en": "Talal",
    "name_native": "طلال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Nice, pleasant",
    "meaning_native": "اچھا، خوشگوار",
    "starting_letter": "T",
    "pronunciation": "Talal",
    "origin": "Arabic",
    "popularity_rank": 84,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Umar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived, flourishing",
    "meaning_native": "لمبی عمر، خوشحال",
    "starting_letter": "U",
    "pronunciation": "Umar",
    "origin": "Arabic",
    "popularity_rank": 85,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Waqar",
    "name_native": "وقار",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Dignity, majesty",
    "meaning_native": "عزت، شان",
    "starting_letter": "W",
    "pronunciation": "Waqar",
    "origin": "Arabic",
    "popularity_rank": 86,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Yasin",
    "name_native": "یاسین",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rich, wealthy",
    "meaning_native": "مالدار، دولت مند",
    "starting_letter": "Y",
    "pronunciation": "Yasin",
    "origin": "Arabic",
    "popularity_rank": 87,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Zahid",
    "name_native": "زاہد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Ascetic, pious",
    "meaning_native": "زاہد، پرہیزگار",
    "starting_letter": "Z",
    "pronunciation": "Zahid",
    "origin": "Arabic",
    "popularity_rank": 88,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Aamir",
    "name_native": "عامر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Prosperous, full of life",
    "meaning_native": "خوشحال، زندگی سے بھرپور",
    "starting_letter": "A",
    "pronunciation": "Aamir",
    "origin": "Arabic",
    "popularity_rank": 89,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Baqir",
    "name_native": "باقر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Learned, scholar",
    "meaning_native": "عالم، دانشور",
    "starting_letter": "B",
    "pronunciation": "Baqir",
    "origin": "Arabic",
    "popularity_rank": 90,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Dawood",
    "name_native": "داود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved, David",
    "meaning_native": "پیارا، داؤد",
    "starting_letter": "D",
    "pronunciation": "Dawood",
    "origin": "Arabic",
    "popularity_rank": 91,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanullah",
    "name_native": "احسان اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfection of God",
    "meaning_native": "خدا کی عمدگی",
    "starting_letter": "E",
    "pronunciation": "Ehsanullah",
    "origin": "Arabic",
    "popularity_rank": 92,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fazlullah",
    "name_native": "فضل اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Grace of God",
    "meaning_native": "خدا کا فضل",
    "starting_letter": "F",
    "pronunciation": "Fazlullah",
    "origin": "Arabic",
    "popularity_rank": 93,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghulam",
    "name_native": "غلام",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant, slave",
    "meaning_native": "نوکر، غلام",
    "starting_letter": "G",
    "pronunciation": "Ghulam",
    "origin": "Arabic",
    "popularity_rank": 94,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Hafiz",
    "name_native": "حافظ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Guardian, protector",
    "meaning_native": "محافظ، نگہبان",
    "starting_letter": "H",
    "pronunciation": "Hafiz",
    "origin": "Arabic",
    "popularity_rank": 95,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ishfaq",
    "name_native": "اشفاق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Compassion, mercy",
    "meaning_native": "رحم، مہربانی",
    "starting_letter": "I",
    "pronunciation": "Ishfaq",
    "origin": "Arabic",
    "popularity_rank": 96,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jalal",
    "name_native": "جلال",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Majesty, glory",
    "meaning_native": "شان، عظمت",
    "starting_letter": "J",
    "pronunciation": "Jalal",
    "origin": "Arabic",
    "popularity_rank": 97,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kashif",
    "name_native": "کاشف",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Revealer, discoverer",
    "meaning_native": "ظاہر کرنے والا، دریافت کرنے والا",
    "starting_letter": "K",
    "pronunciation": "Kashif",
    "origin": "Arabic",
    "popularity_rank": 98,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lutfi",
    "name_native": "لطفی",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kind, gentle",
    "meaning_native": "نرم، مہربان",
    "starting_letter": "L",
    "pronunciation": "Lutfi",
    "origin": "Arabic",
    "popularity_rank": 99,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoor",
    "name_native": "منصور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious, aided",
    "meaning_native": "فاتح، مددگار",
    "starting_letter": "M",
    "pronunciation": "Mansoor",
    "origin": "Arabic",
    "popularity_rank": 100,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Nabeel",
    "name_native": "نبيل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Noble, generous",
    "meaning_native": "شریف، سخی",
    "starting_letter": "N",
    "pronunciation": "Nabeel",
    "origin": "Arabic",
    "popularity_rank": 101,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaid",
    "name_native": "عبيد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا نوکر",
    "starting_letter": "O",
    "pronunciation": "Obaid",
    "origin": "Arabic",
    "popularity_rank": 102,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvez",
    "name_native": "پرویز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "P",
    "pronunciation": "Parvez",
    "origin": "Persian",
    "popularity_rank": 103,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qasim",
    "name_native": "قاسم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider, distributor",
    "meaning_native": "تقسیم کرنے والا",
    "starting_letter": "Q",
    "pronunciation": "Qasim",
    "origin": "Arabic",
    "popularity_rank": 104,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Rafiq",
    "name_native": "رفیق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Companion, friend",
    "meaning_native": "ساتھی، دوست",
    "starting_letter": "R",
    "pronunciation": "Rafiq",
    "origin": "Arabic",
    "popularity_rank": 105,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saeed",
    "name_native": "سعيد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Happy, fortunate",
    "meaning_native": "خوش، خوش قسمت",
    "starting_letter": "S",
    "pronunciation": "Saeed",
    "origin": "Arabic",
    "popularity_rank": 106,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Tahir",
    "name_native": "طاهر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, clean",
    "meaning_native": "پاک، صاف",
    "starting_letter": "T",
    "pronunciation": "Tahir",
    "origin": "Arabic",
    "popularity_rank": 107,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaid",
    "name_native": "عبيد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Little servant",
    "meaning_native": "چھوٹا نوکر",
    "starting_letter": "U",
    "pronunciation": "Ubaid",
    "origin": "Arabic",
    "popularity_rank": 108,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vahid",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "V",
    "pronunciation": "Vahid",
    "origin": "Arabic",
    "popularity_rank": 109,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahid",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "W",
    "pronunciation": "Wahid",
    "origin": "Arabic",
    "popularity_rank": 110,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xavier",
    "name_native": "زاویر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "New house, bright",
    "meaning_native": "نیا گھر، روشن",
    "starting_letter": "X",
    "pronunciation": "Xavier",
    "origin": "Arabic",
    "popularity_rank": 111,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahya",
    "name_native": "يحيى",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "God is gracious",
    "meaning_native": "خدا مہربان ہے",
    "starting_letter": "Y",
    "pronunciation": "Yahya",
    "origin": "Arabic",
    "popularity_rank": 112,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariya",
    "name_native": "زكريا",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "God remembers",
    "meaning_native": "خدا یاد رکھتا ہے",
    "starting_letter": "Z",
    "pronunciation": "Zakariya",
    "origin": "Arabic",
    "popularity_rank": 113,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aadil",
    "name_native": "عادل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Just, fair",
    "meaning_native": "عادل، منصف",
    "starting_letter": "A",
    "pronunciation": "Aadil",
    "origin": "Arabic",
    "popularity_rank": 114,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baqar",
    "name_native": "باقر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Learned, scholar",
    "meaning_native": "عالم، دانشور",
    "starting_letter": "B",
    "pronunciation": "Baqar",
    "origin": "Arabic",
    "popularity_rank": 115,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Cahil",
    "name_native": "جاهل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Ignorant, unaware",
    "meaning_native": "جاہل، بے خبر",
    "starting_letter": "C",
    "pronunciation": "Cahil",
    "origin": "Arabic",
    "popularity_rank": 116,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daani",
    "name_native": "داني",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Close, near",
    "meaning_native": "قریب، نزدیک",
    "starting_letter": "D",
    "pronunciation": "Daani",
    "origin": "Arabic",
    "popularity_rank": 117,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Eesa",
    "name_native": "عيسى",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Jesus, salvation",
    "meaning_native": "عیسیٰ، نجات",
    "starting_letter": "E",
    "pronunciation": "Eesa",
    "origin": "Arabic",
    "popularity_rank": 118,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaiz",
    "name_native": "فائز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "F",
    "pronunciation": "Faaiz",
    "origin": "Arabic",
    "popularity_rank": 119,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaazi",
    "name_native": "غازي",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior, conqueror",
    "meaning_native": "جنگجو، فاتح",
    "starting_letter": "G",
    "pronunciation": "Ghaazi",
    "origin": "Arabic",
    "popularity_rank": 120,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haani",
    "name_native": "هاني",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Happy, content",
    "meaning_native": "خوش، مطمئن",
    "starting_letter": "H",
    "pronunciation": "Haani",
    "origin": "Arabic",
    "popularity_rank": 121,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaad",
    "name_native": "عماد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pillar, support",
    "meaning_native": "ستون، سہارا",
    "starting_letter": "I",
    "pronunciation": "Imaad",
    "origin": "Arabic",
    "popularity_rank": 122,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaafer",
    "name_native": "جعفر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream, river",
    "meaning_native": "ندی، دریا",
    "starting_letter": "J",
    "pronunciation": "Jaafer",
    "origin": "Arabic",
    "popularity_rank": 123,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamil",
    "name_native": "كامل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect, complete",
    "meaning_native": "مکمل، پورا",
    "starting_letter": "K",
    "pronunciation": "Kaamil",
    "origin": "Arabic",
    "popularity_rank": 124,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiq",
    "name_native": "لائق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy, deserving",
    "meaning_native": "قابل، مستحق",
    "starting_letter": "L",
    "pronunciation": "Laaiq",
    "origin": "Arabic",
    "popularity_rank": 125,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maaz",
    "name_native": "ماز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Protected, safe",
    "meaning_native": "محفوظ، محفوظ",
    "starting_letter": "M",
    "pronunciation": "Maaz",
    "origin": "Arabic",
    "popularity_rank": 126,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naadir",
    "name_native": "نادر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rare, precious",
    "meaning_native": "نایاب، قیمتی",
    "starting_letter": "N",
    "pronunciation": "Naadir",
    "origin": "Arabic",
    "popularity_rank": 127,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived, flourishing",
    "meaning_native": "لمبی عمر، خوشحال",
    "starting_letter": "O",
    "pronunciation": "Omar",
    "origin": "Arabic",
    "popularity_rank": 128,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pasha",
    "name_native": "پاشا",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Turkish",
    "meaning_en": "Lord, master",
    "meaning_native": "مالک، آقا",
    "starting_letter": "P",
    "pronunciation": "Pasha",
    "origin": "Turkish",
    "popularity_rank": 129,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadir",
    "name_native": "قادر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful, capable",
    "meaning_native": "طاقتور، قابل",
    "starting_letter": "Q",
    "pronunciation": "Qaadir",
    "origin": "Arabic",
    "popularity_rank": 130,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raafi",
    "name_native": "رافع",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Exalted, elevated",
    "meaning_native": "بلند، عالی",
    "starting_letter": "R",
    "pronunciation": "Raafi",
    "origin": "Arabic",
    "popularity_rank": 131,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabir",
    "name_native": "صابر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient, enduring",
    "meaning_native": "صابر، بردبار",
    "starting_letter": "S",
    "pronunciation": "Saabir",
    "origin": "Arabic",
    "popularity_rank": 132,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahir",
    "name_native": "طاهر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure, clean",
    "meaning_native": "پاک، صاف",
    "starting_letter": "T",
    "pronunciation": "Taahir",
    "origin": "Arabic",
    "popularity_rank": 133,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Umar",
    "name_native": "عمر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived, flourishing",
    "meaning_native": "لمبی عمر، خوشحال",
    "starting_letter": "U",
    "pronunciation": "Umar",
    "origin": "Arabic",
    "popularity_rank": 134,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheed",
    "name_native": "واحد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique, one",
    "meaning_native": "منفرد، ایک",
    "starting_letter": "V",
    "pronunciation": "Vaheed",
    "origin": "Arabic",
    "popularity_rank": 135,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Waseem",
    "name_native": "وسیم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Handsome, beautiful",
    "meaning_native": "خوبصورت، حسین",
    "starting_letter": "W",
    "pronunciation": "Waseem",
    "origin": "Arabic",
    "popularity_rank": 136,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheer",
    "name_native": "ظاہر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Apparent, manifest",
    "meaning_native": "ظاہر، واضح",
    "starting_letter": "X",
    "pronunciation": "Xaheer",
    "origin": "Arabic",
    "popularity_rank": 137,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yaseen",
    "name_native": "يٰسٓ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Chapter of Quran",
    "meaning_native": "قرآن کی سورت",
    "starting_letter": "Y",
    "pronunciation": "Yaseen",
    "origin": "Arabic",
    "popularity_rank": 138,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zaahir",
    "name_native": "ظاہر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Apparent, manifest",
    "meaning_native": "ظاہر، واضح",
    "starting_letter": "Z",
    "pronunciation": "Zaahir",
    "origin": "Arabic",
    "popularity_rank": 139,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabid",
    "name_native": "عابد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper, devotee",
    "meaning_native": "عابد، بندہ",
    "starting_letter": "A",
    "pronunciation": "Aabid",
    "origin": "Arabic",
    "popularity_rank": 140,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasit",
    "name_native": "باسط",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander, spreader",
    "meaning_native": "پھیلانے والا",
    "starting_letter": "B",
    "pronunciation": "Baasit",
    "origin": "Arabic",
    "popularity_rank": 141,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasim",
    "name_native": "قاسم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider, distributor",
    "meaning_native": "تقسیم کرنے والا",
    "starting_letter": "C",
    "pronunciation": "Caasim",
    "origin": "Arabic",
    "popularity_rank": 142,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawud",
    "name_native": "داود",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved, David",
    "meaning_native": "پیارا، داؤد",
    "starting_letter": "D",
    "pronunciation": "Daawud",
    "origin": "Arabic",
    "popularity_rank": 143,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaan",
    "name_native": "احسان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kindness, favor",
    "meaning_native": "احسان، مہربانی",
    "starting_letter": "E",
    "pronunciation": "Ehsaan",
    "origin": "Arabic",
    "popularity_rank": 144,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaiz",
    "name_native": "فائز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "F",
    "pronunciation": "Faaiz",
    "origin": "Arabic",
    "popularity_rank": 145,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaazi",
    "name_native": "غازي",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior, conqueror",
    "meaning_native": "جنگجو، فاتح",
    "starting_letter": "G",
    "pronunciation": "Ghaazi",
    "origin": "Arabic",
    "popularity_rank": 146,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamid",
    "name_native": "حامد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser, thankful",
    "meaning_native": "حمد کرنے والا، شکرگزار",
    "starting_letter": "H",
    "pronunciation": "Haamid",
    "origin": "Arabic",
    "popularity_rank": 147,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaan",
    "name_native": "ایمان",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith, belief",
    "meaning_native": "ایمان، عقیدہ",
    "starting_letter": "I",
    "pronunciation": "Imaan",
    "origin": "Arabic",
    "popularity_rank": 148,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaafer",
    "name_native": "جعفر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream, river",
    "meaning_native": "ندی، دریا",
    "starting_letter": "J",
    "pronunciation": "Jaafer",
    "origin": "Arabic",
    "popularity_rank": 149,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamil",
    "name_native": "كامل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect, complete",
    "meaning_native": "مکمل، پورا",
    "starting_letter": "K",
    "pronunciation": "Kaamil",
    "origin": "Arabic",
    "popularity_rank": 150,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiq",
    "name_native": "لائق",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy, deserving",
    "meaning_native": "قابل، مستحق",
    "starting_letter": "L",
    "pronunciation": "Laaiq",
    "origin": "Arabic",
    "popularity_rank": 151,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajid",
    "name_native": "ماجد",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Noble, glorious",
    "meaning_native": "شریف، عظیم",
    "starting_letter": "M",
    "pronunciation": "Maajid",
    "origin": "Arabic",
    "popularity_rank": 152,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasir",
    "name_native": "ناصر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper, supporter",
    "meaning_native": "مددگار، حامی",
    "starting_letter": "N",
    "pronunciation": "Naasir",
    "origin": "Arabic",
    "popularity_rank": 153,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidullah",
    "name_native": "عبید اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of God",
    "meaning_native": "خدا کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidullah",
    "origin": "Arabic",
    "popularity_rank": 154,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaiz",
    "name_native": "پرویز",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious, successful",
    "meaning_native": "فاتح، کامیاب",
    "starting_letter": "P",
    "pronunciation": "Pervaiz",
    "origin": "Persian",
    "popularity_rank": 155,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasim",
    "name_native": "قاسم",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider, distributor",
    "meaning_native": "تقسیم کرنے والا",
    "starting_letter": "Q",
    "pronunciation": "Qaasim",
    "origin": "Arabic",
    "popularity_rank": 156,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahil",
    "name_native": "راحل",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler, journeyer",
    "meaning_native": "مسافر، راہی",
    "starting_letter": "R",
    "pronunciation": "Raahil",
    "origin": "Arabic",
    "popularity_rank": 157,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabir",
    "name_native": "صابر",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient, enduring",
    "meaning_native": "صابر، بردبار",
    "starting_letter": "S",
    "pronunciation": "Saabir",
    "origin": "Arabic",
    "popularity_rank": 158,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalib",
    "name_native": "طالب",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker, student",
    "meaning_native": "طلبگار، طالب علم",
    "starting_letter": "T",
    "pronunciation": "Taalib",
    "origin": "Arabic",
    "popularity_rank": 159,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidullah",
    "name_native": "عبید اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of God",
    "meaning_native": "خدا کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidullah",
    "origin": "Arabic",
    "popularity_rank": 160,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedullah",
    "name_native": "واحد اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique one of God",
    "meaning_native": "خدا کا واحد",
    "starting_letter": "V",
    "pronunciation": "Vaheedullah",
    "origin": "Arabic",
    "popularity_rank": 161,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidullah",
    "name_native": "واحد اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique one of God",
    "meaning_native": "خدا کا واحد",
    "starting_letter": "W",
    "pronunciation": "Wahidullah",
    "origin": "Arabic",
    "popularity_rank": 162,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerullah",
    "name_native": "ظاہر اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifestation of God",
    "meaning_native": "خدا کی ظاہریت",
    "starting_letter": "X",
    "pronunciation": "Xaheerullah",
    "origin": "Arabic",
    "popularity_rank": 163,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaullah",
    "name_native": "یحیی اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "God is gracious",
    "meaning_native": "خدا مہربان ہے",
    "starting_letter": "Y",
    "pronunciation": "Yahyaullah",
    "origin": "Arabic",
    "popularity_rank": 164,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaullah",
    "name_native": "زکریا اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "God remembers",
    "meaning_native": "خدا یاد رکھتا ہے",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaullah",
    "origin": "Arabic",
    "popularity_rank": 165,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidullah",
    "name_native": "عابد اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of God",
    "meaning_native": "خدا کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidullah",
    "origin": "Arabic",
    "popularity_rank": 166,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitullah",
    "name_native": "باسط اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of God",
    "meaning_native": "خدا کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitullah",
    "origin": "Arabic",
    "popularity_rank": 167,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimullah",
    "name_native": "قاسم اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of God",
    "meaning_native": "خدا کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimullah",
    "origin": "Arabic",
    "popularity_rank": 168,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudullah",
    "name_native": "داود اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of God",
    "meaning_native": "خدا کا پیارا",
    "starting_letter": "D",
    "pronunciation": "Daawudullah",
    "origin": "Arabic",
    "popularity_rank": 169,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaanullah",
    "name_native": "احسان اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kindness of God",
    "meaning_native": "خدا کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsaanullah",
    "origin": "Arabic",
    "popularity_rank": 170,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaizullah",
    "name_native": "فائز اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious of God",
    "meaning_native": "خدا کا فائز",
    "starting_letter": "F",
    "pronunciation": "Faaizullah",
    "origin": "Arabic",
    "popularity_rank": 171,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaaziullah",
    "name_native": "غازی اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of God",
    "meaning_native": "خدا کا غازی",
    "starting_letter": "G",
    "pronunciation": "Ghaaziullah",
    "origin": "Arabic",
    "popularity_rank": 172,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamidullah",
    "name_native": "حامد اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser of God",
    "meaning_native": "خدا کا حامد",
    "starting_letter": "H",
    "pronunciation": "Haamidullah",
    "origin": "Arabic",
    "popularity_rank": 173,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaanullah",
    "name_native": "ایمان اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith of God",
    "meaning_native": "خدا کا ایمان",
    "starting_letter": "I",
    "pronunciation": "Imaanullah",
    "origin": "Arabic",
    "popularity_rank": 174,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaaferullah",
    "name_native": "جعفر اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream of God",
    "meaning_native": "خدا کی ندی",
    "starting_letter": "J",
    "pronunciation": "Jaaferullah",
    "origin": "Arabic",
    "popularity_rank": 175,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamilullah",
    "name_native": "کامل اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect of God",
    "meaning_native": "خدا کا کامل",
    "starting_letter": "K",
    "pronunciation": "Kaamilullah",
    "origin": "Arabic",
    "popularity_rank": 176,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiqullah",
    "name_native": "لائق اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy of God",
    "meaning_native": "خدا کا لائق",
    "starting_letter": "L",
    "pronunciation": "Laaiqullah",
    "origin": "Arabic",
    "popularity_rank": 177,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajidullah",
    "name_native": "ماجد اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Glorious of God",
    "meaning_native": "خدا کا ماجد",
    "starting_letter": "M",
    "pronunciation": "Maajidullah",
    "origin": "Arabic",
    "popularity_rank": 178,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasirullah",
    "name_native": "ناصر اللہ",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of God",
    "meaning_native": "خدا کا ناصر",
    "starting_letter": "N",
    "pronunciation": "Naasirullah",
    "origin": "Arabic",
    "popularity_rank": 179,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidur",
    "origin": "Arabic",
    "popularity_rank": 180,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaizur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "P",
    "pronunciation": "Pervaizur",
    "origin": "Persian",
    "popularity_rank": 181,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "Q",
    "pronunciation": "Qaasimur",
    "origin": "Arabic",
    "popularity_rank": 182,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahilur",
    "name_native": "راحلور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler of light",
    "meaning_native": "روشنی کا مسافر",
    "starting_letter": "R",
    "pronunciation": "Raahilur",
    "origin": "Arabic",
    "popularity_rank": 183,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabirur",
    "name_native": "صابرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient of light",
    "meaning_native": "روشنی کا صابر",
    "starting_letter": "S",
    "pronunciation": "Saabirur",
    "origin": "Arabic",
    "popularity_rank": 184,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalibur",
    "name_native": "طالبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker of light",
    "meaning_native": "روشنی کا طالب",
    "starting_letter": "T",
    "pronunciation": "Taalibur",
    "origin": "Arabic",
    "popularity_rank": 185,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 186,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 187,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 188,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 189,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 190,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 191,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 192,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 193,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 194,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 195,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kind light",
    "meaning_native": "مہربان روشنی",
    "starting_letter": "E",
    "pronunciation": "Ehsaanur",
    "origin": "Arabic",
    "popularity_rank": 196,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaizur",
    "name_native": "فائزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "F",
    "pronunciation": "Faaizur",
    "origin": "Arabic",
    "popularity_rank": 197,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaaziur",
    "name_native": "غازیور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا غازی",
    "starting_letter": "G",
    "pronunciation": "Ghaaziur",
    "origin": "Arabic",
    "popularity_rank": 198,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamidur",
    "name_native": "حامدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser of light",
    "meaning_native": "روشنی کا حامد",
    "starting_letter": "H",
    "pronunciation": "Haamidur",
    "origin": "Arabic",
    "popularity_rank": 199,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaanur",
    "name_native": "ایمانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith of light",
    "meaning_native": "روشنی کا ایمان",
    "starting_letter": "I",
    "pronunciation": "Imaanur",
    "origin": "Arabic",
    "popularity_rank": 200,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaaferur",
    "name_native": "جعفرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream of light",
    "meaning_native": "روشنی کی ندی",
    "starting_letter": "J",
    "pronunciation": "Jaaferur",
    "origin": "Arabic",
    "popularity_rank": 201,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamilur",
    "name_native": "کاملور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect light",
    "meaning_native": "مکمل روشنی",
    "starting_letter": "K",
    "pronunciation": "Kaamilur",
    "origin": "Arabic",
    "popularity_rank": 202,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiqur",
    "name_native": "لائقور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy light",
    "meaning_native": "قابل روشنی",
    "starting_letter": "L",
    "pronunciation": "Laaiqur",
    "origin": "Arabic",
    "popularity_rank": 203,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajidur",
    "name_native": "ماجدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Glorious light",
    "meaning_native": "عظیم روشنی",
    "starting_letter": "M",
    "pronunciation": "Maajidur",
    "origin": "Arabic",
    "popularity_rank": 204,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasirur",
    "name_native": "ناصرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا ناصر",
    "starting_letter": "N",
    "pronunciation": "Naasirur",
    "origin": "Arabic",
    "popularity_rank": 205,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidur",
    "origin": "Arabic",
    "popularity_rank": 206,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaizur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "P",
    "pronunciation": "Pervaizur",
    "origin": "Persian",
    "popularity_rank": 207,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "Q",
    "pronunciation": "Qaasimur",
    "origin": "Arabic",
    "popularity_rank": 208,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahilur",
    "name_native": "راحلور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler of light",
    "meaning_native": "روشنی کا مسافر",
    "starting_letter": "R",
    "pronunciation": "Raahilur",
    "origin": "Arabic",
    "popularity_rank": 209,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabirur",
    "name_native": "صابرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient of light",
    "meaning_native": "روشنی کا صابر",
    "starting_letter": "S",
    "pronunciation": "Saabirur",
    "origin": "Arabic",
    "popularity_rank": 210,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalibur",
    "name_native": "طالبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker of light",
    "meaning_native": "روشنی کا طالب",
    "starting_letter": "T",
    "pronunciation": "Taalibur",
    "origin": "Arabic",
    "popularity_rank": 211,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 212,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 213,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 214,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 215,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 216,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 217,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 218,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 219,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 220,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 221,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kind light",
    "meaning_native": "مہربان روشنی",
    "starting_letter": "E",
    "pronunciation": "Ehsaanur",
    "origin": "Arabic",
    "popularity_rank": 222,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaizur",
    "name_native": "فائزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "F",
    "pronunciation": "Faaizur",
    "origin": "Arabic",
    "popularity_rank": 223,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaaziur",
    "name_native": "غازیور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا غازی",
    "starting_letter": "G",
    "pronunciation": "Ghaaziur",
    "origin": "Arabic",
    "popularity_rank": 224,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamidur",
    "name_native": "حامدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser of light",
    "meaning_native": "روشنی کا حامد",
    "starting_letter": "H",
    "pronunciation": "Haamidur",
    "origin": "Arabic",
    "popularity_rank": 225,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaanur",
    "name_native": "ایمانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith of light",
    "meaning_native": "روشنی کا ایمان",
    "starting_letter": "I",
    "pronunciation": "Imaanur",
    "origin": "Arabic",
    "popularity_rank": 226,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaaferur",
    "name_native": "جعفرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream of light",
    "meaning_native": "روشنی کی ندی",
    "starting_letter": "J",
    "pronunciation": "Jaaferur",
    "origin": "Arabic",
    "popularity_rank": 227,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamilur",
    "name_native": "کاملور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect light",
    "meaning_native": "مکمل روشنی",
    "starting_letter": "K",
    "pronunciation": "Kaamilur",
    "origin": "Arabic",
    "popularity_rank": 228,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiqur",
    "name_native": "لائقور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy light",
    "meaning_native": "قابل روشنی",
    "starting_letter": "L",
    "pronunciation": "Laaiqur",
    "origin": "Arabic",
    "popularity_rank": 229,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajidur",
    "name_native": "ماجدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Glorious light",
    "meaning_native": "عظیم روشنی",
    "starting_letter": "M",
    "pronunciation": "Maajidur",
    "origin": "Arabic",
    "popularity_rank": 230,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasirur",
    "name_native": "ناصرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا ناصر",
    "starting_letter": "N",
    "pronunciation": "Naasirur",
    "origin": "Arabic",
    "popularity_rank": 231,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidur",
    "origin": "Arabic",
    "popularity_rank": 232,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaizur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "P",
    "pronunciation": "Pervaizur",
    "origin": "Persian",
    "popularity_rank": 233,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "Q",
    "pronunciation": "Qaasimur",
    "origin": "Arabic",
    "popularity_rank": 234,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahilur",
    "name_native": "راحلور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler of light",
    "meaning_native": "روشنی کا مسافر",
    "starting_letter": "R",
    "pronunciation": "Raahilur",
    "origin": "Arabic",
    "popularity_rank": 235,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabirur",
    "name_native": "صابرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient of light",
    "meaning_native": "روشنی کا صابر",
    "starting_letter": "S",
    "pronunciation": "Saabirur",
    "origin": "Arabic",
    "popularity_rank": 236,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalibur",
    "name_native": "طالبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker of light",
    "meaning_native": "روشنی کا طالب",
    "starting_letter": "T",
    "pronunciation": "Taalibur",
    "origin": "Arabic",
    "popularity_rank": 237,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 238,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 239,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 240,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 241,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 242,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 243,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 244,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 245,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 246,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 247,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kind light",
    "meaning_native": "مہربان روشنی",
    "starting_letter": "E",
    "pronunciation": "Ehsaanur",
    "origin": "Arabic",
    "popularity_rank": 248,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaizur",
    "name_native": "فائزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "F",
    "pronunciation": "Faaizur",
    "origin": "Arabic",
    "popularity_rank": 249,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaaziur",
    "name_native": "غازیور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا غازی",
    "starting_letter": "G",
    "pronunciation": "Ghaaziur",
    "origin": "Arabic",
    "popularity_rank": 250,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamidur",
    "name_native": "حامدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser of light",
    "meaning_native": "روشنی کا حامد",
    "starting_letter": "H",
    "pronunciation": "Haamidur",
    "origin": "Arabic",
    "popularity_rank": 251,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaanur",
    "name_native": "ایمانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith of light",
    "meaning_native": "روشنی کا ایمان",
    "starting_letter": "I",
    "pronunciation": "Imaanur",
    "origin": "Arabic",
    "popularity_rank": 252,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaaferur",
    "name_native": "جعفرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream of light",
    "meaning_native": "روشنی کی ندی",
    "starting_letter": "J",
    "pronunciation": "Jaaferur",
    "origin": "Arabic",
    "popularity_rank": 253,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamilur",
    "name_native": "کاملور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect light",
    "meaning_native": "مکمل روشنی",
    "starting_letter": "K",
    "pronunciation": "Kaamilur",
    "origin": "Arabic",
    "popularity_rank": 254,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiqur",
    "name_native": "لائقور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy light",
    "meaning_native": "قابل روشنی",
    "starting_letter": "L",
    "pronunciation": "Laaiqur",
    "origin": "Arabic",
    "popularity_rank": 255,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajidur",
    "name_native": "ماجدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Glorious light",
    "meaning_native": "عظیم روشنی",
    "starting_letter": "M",
    "pronunciation": "Maajidur",
    "origin": "Arabic",
    "popularity_rank": 256,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasirur",
    "name_native": "ناصرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا ناصر",
    "starting_letter": "N",
    "pronunciation": "Naasirur",
    "origin": "Arabic",
    "popularity_rank": 257,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidur",
    "origin": "Arabic",
    "popularity_rank": 258,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaizur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "P",
    "pronunciation": "Pervaizur",
    "origin": "Persian",
    "popularity_rank": 259,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "Q",
    "pronunciation": "Qaasimur",
    "origin": "Arabic",
    "popularity_rank": 260,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahilur",
    "name_native": "راحلور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler of light",
    "meaning_native": "روشنی کا مسافر",
    "starting_letter": "R",
    "pronunciation": "Raahilur",
    "origin": "Arabic",
    "popularity_rank": 261,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabirur",
    "name_native": "صابرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient of light",
    "meaning_native": "روشنی کا صابر",
    "starting_letter": "S",
    "pronunciation": "Saabirur",
    "origin": "Arabic",
    "popularity_rank": 262,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalibur",
    "name_native": "طالبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker of light",
    "meaning_native": "روشنی کا طالب",
    "starting_letter": "T",
    "pronunciation": "Taalibur",
    "origin": "Arabic",
    "popularity_rank": 263,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 264,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 265,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 266,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 267,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 268,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 269,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 270,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 271,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 272,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 273,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kind light",
    "meaning_native": "مہربان روشنی",
    "starting_letter": "E",
    "pronunciation": "Ehsaanur",
    "origin": "Arabic",
    "popularity_rank": 274,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaizur",
    "name_native": "فائزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "F",
    "pronunciation": "Faaizur",
    "origin": "Arabic",
    "popularity_rank": 275,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaaziur",
    "name_native": "غازیور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا غازی",
    "starting_letter": "G",
    "pronunciation": "Ghaaziur",
    "origin": "Arabic",
    "popularity_rank": 276,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamidur",
    "name_native": "حامدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser of light",
    "meaning_native": "روشنی کا حامد",
    "starting_letter": "H",
    "pronunciation": "Haamidur",
    "origin": "Arabic",
    "popularity_rank": 277,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaanur",
    "name_native": "ایمانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith of light",
    "meaning_native": "روشنی کا ایمان",
    "starting_letter": "I",
    "pronunciation": "Imaanur",
    "origin": "Arabic",
    "popularity_rank": 278,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaaferur",
    "name_native": "جعفرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream of light",
    "meaning_native": "روشنی کی ندی",
    "starting_letter": "J",
    "pronunciation": "Jaaferur",
    "origin": "Arabic",
    "popularity_rank": 279,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamilur",
    "name_native": "کاملور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect light",
    "meaning_native": "مکمل روشنی",
    "starting_letter": "K",
    "pronunciation": "Kaamilur",
    "origin": "Arabic",
    "popularity_rank": 280,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiqur",
    "name_native": "لائقور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy light",
    "meaning_native": "قابل روشنی",
    "starting_letter": "L",
    "pronunciation": "Laaiqur",
    "origin": "Arabic",
    "popularity_rank": 281,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajidur",
    "name_native": "ماجدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Glorious light",
    "meaning_native": "عظیم روشنی",
    "starting_letter": "M",
    "pronunciation": "Maajidur",
    "origin": "Arabic",
    "popularity_rank": 282,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasirur",
    "name_native": "ناصرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا ناصر",
    "starting_letter": "N",
    "pronunciation": "Naasirur",
    "origin": "Arabic",
    "popularity_rank": 283,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidur",
    "origin": "Arabic",
    "popularity_rank": 284,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaizur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "P",
    "pronunciation": "Pervaizur",
    "origin": "Persian",
    "popularity_rank": 285,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "Q",
    "pronunciation": "Qaasimur",
    "origin": "Arabic",
    "popularity_rank": 286,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahilur",
    "name_native": "راحلور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler of light",
    "meaning_native": "روشنی کا مسافر",
    "starting_letter": "R",
    "pronunciation": "Raahilur",
    "origin": "Arabic",
    "popularity_rank": 287,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabirur",
    "name_native": "صابرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient of light",
    "meaning_native": "روشنی کا صابر",
    "starting_letter": "S",
    "pronunciation": "Saabirur",
    "origin": "Arabic",
    "popularity_rank": 288,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalibur",
    "name_native": "طالبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker of light",
    "meaning_native": "روشنی کا طالب",
    "starting_letter": "T",
    "pronunciation": "Taalibur",
    "origin": "Arabic",
    "popularity_rank": 289,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 290,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 291,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 292,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 293,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 294,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 295,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 296,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 297,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 298,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 299,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsaanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Kind light",
    "meaning_native": "مہربان روشنی",
    "starting_letter": "E",
    "pronunciation": "Ehsaanur",
    "origin": "Arabic",
    "popularity_rank": 300,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Faaizur",
    "name_native": "فائزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "F",
    "pronunciation": "Faaizur",
    "origin": "Arabic",
    "popularity_rank": 301,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghaaziur",
    "name_native": "غازیور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا غازی",
    "starting_letter": "G",
    "pronunciation": "Ghaaziur",
    "origin": "Arabic",
    "popularity_rank": 302,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Haamidur",
    "name_native": "حامدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Praiser of light",
    "meaning_native": "روشنی کا حامد",
    "starting_letter": "H",
    "pronunciation": "Haamidur",
    "origin": "Arabic",
    "popularity_rank": 303,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Imaanur",
    "name_native": "ایمانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Faith of light",
    "meaning_native": "روشنی کا ایمان",
    "starting_letter": "I",
    "pronunciation": "Imaanur",
    "origin": "Arabic",
    "popularity_rank": 304,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jaaferur",
    "name_native": "جعفرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Stream of light",
    "meaning_native": "روشنی کی ندی",
    "starting_letter": "J",
    "pronunciation": "Jaaferur",
    "origin": "Arabic",
    "popularity_rank": 305,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kaamilur",
    "name_native": "کاملور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Perfect light",
    "meaning_native": "مکمل روشنی",
    "starting_letter": "K",
    "pronunciation": "Kaamilur",
    "origin": "Arabic",
    "popularity_rank": 306,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Laaiqur",
    "name_native": "لائقور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worthy light",
    "meaning_native": "قابل روشنی",
    "starting_letter": "L",
    "pronunciation": "Laaiqur",
    "origin": "Arabic",
    "popularity_rank": 307,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Maajidur",
    "name_native": "ماجدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Glorious light",
    "meaning_native": "عظیم روشنی",
    "starting_letter": "M",
    "pronunciation": "Maajidur",
    "origin": "Arabic",
    "popularity_rank": 308,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naasirur",
    "name_native": "ناصرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا ناصر",
    "starting_letter": "N",
    "pronunciation": "Naasirur",
    "origin": "Arabic",
    "popularity_rank": 309,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Obaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "O",
    "pronunciation": "Obaidur",
    "origin": "Arabic",
    "popularity_rank": 310,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Pervaizur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "P",
    "pronunciation": "Pervaizur",
    "origin": "Persian",
    "popularity_rank": 311,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "Q",
    "pronunciation": "Qaasimur",
    "origin": "Arabic",
    "popularity_rank": 312,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raahilur",
    "name_native": "راحلور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Traveler of light",
    "meaning_native": "روشنی کا مسافر",
    "starting_letter": "R",
    "pronunciation": "Raahilur",
    "origin": "Arabic",
    "popularity_rank": 313,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabirur",
    "name_native": "صابرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient of light",
    "meaning_native": "روشنی کا صابر",
    "starting_letter": "S",
    "pronunciation": "Saabirur",
    "origin": "Arabic",
    "popularity_rank": 314,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taalibur",
    "name_native": "طالبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Seeker of light",
    "meaning_native": "روشنی کا طالب",
    "starting_letter": "T",
    "pronunciation": "Taalibur",
    "origin": "Arabic",
    "popularity_rank": 315,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 316,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 317,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 318,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 319,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 320,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 321,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 322,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 323,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 324,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 325,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 326,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 327,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 328,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 329,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 330,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 331,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 332,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 333,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 334,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 335,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 336,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 337,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 338,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 339,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 340,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 341,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 342,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 343,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 344,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 345,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 346,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 347,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 348,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 349,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 350,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 351,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 352,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 353,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 354,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 355,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 356,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 357,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 358,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 359,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 360,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 361,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 362,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 363,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 364,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 365,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 366,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 367,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 368,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 369,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 370,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 371,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 372,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 373,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 374,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 375,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 376,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 377,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 378,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 379,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 380,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 381,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 382,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 383,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 384,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 385,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 386,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 387,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 388,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 389,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 390,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 391,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 392,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 393,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 394,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 395,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 396,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 397,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 398,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 399,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 400,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 401,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 402,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 403,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 404,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 405,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 406,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 407,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 408,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 409,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 410,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 411,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 412,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 413,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 414,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 415,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 416,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 417,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 418,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 419,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 420,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 421,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 422,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 423,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 424,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 425,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 426,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 427,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 428,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 429,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 430,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 431,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 432,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 433,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 434,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 435,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 436,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 437,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 438,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 439,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 440,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 441,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 442,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 443,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 444,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 445,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 446,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 447,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 448,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 449,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 450,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 451,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 452,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 453,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 454,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 455,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 456,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 457,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 458,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 459,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 460,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 461,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 462,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 463,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 464,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 465,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 466,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 467,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 468,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 469,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 470,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 471,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 472,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 473,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 474,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 475,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 476,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 477,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 478,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 479,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 480,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 481,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 482,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 483,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 484,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 485,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 486,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 487,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 488,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 489,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 490,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 491,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 492,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 493,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 494,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 495,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 496,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 497,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 498,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 499,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 500,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 501,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 502,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 503,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 504,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 505,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 506,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 507,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 508,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 509,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 510,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 511,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 512,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 513,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 514,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 515,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 516,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 517,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 518,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 519,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 520,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 521,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 522,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 523,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 524,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Vaheedur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "V",
    "pronunciation": "Vaheedur",
    "origin": "Arabic",
    "popularity_rank": 525,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Wahidur",
    "name_native": "واحدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Unique light",
    "meaning_native": "منفرد روشنی",
    "starting_letter": "W",
    "pronunciation": "Wahidur",
    "origin": "Arabic",
    "popularity_rank": 526,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Xaheerur",
    "name_native": "ظاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Manifest light",
    "meaning_native": "ظاہر روشنی",
    "starting_letter": "X",
    "pronunciation": "Xaheerur",
    "origin": "Arabic",
    "popularity_rank": 527,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Yahyaur",
    "name_native": "یحییور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Living light",
    "meaning_native": "زندہ روشنی",
    "starting_letter": "Y",
    "pronunciation": "Yahyaur",
    "origin": "Arabic",
    "popularity_rank": 528,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Zakariyaur",
    "name_native": "زکریاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Remembering light",
    "meaning_native": "یاد کرنے والی روشنی",
    "starting_letter": "Z",
    "pronunciation": "Zakariyaur",
    "origin": "Arabic",
    "popularity_rank": 529,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Aabidur",
    "name_native": "عابدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Worshipper of light",
    "meaning_native": "روشنی کا عابد",
    "starting_letter": "A",
    "pronunciation": "Aabidur",
    "origin": "Arabic",
    "popularity_rank": 530,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Baasitur",
    "name_native": "باسطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Expander of light",
    "meaning_native": "روشنی کا باسط",
    "starting_letter": "B",
    "pronunciation": "Baasitur",
    "origin": "Arabic",
    "popularity_rank": 531,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Caasimur",
    "name_native": "قاسمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Divider of light",
    "meaning_native": "روشنی کا قاسم",
    "starting_letter": "C",
    "pronunciation": "Caasimur",
    "origin": "Arabic",
    "popularity_rank": 532,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Daawudur",
    "name_native": "داودور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved light",
    "meaning_native": "پیاری روشنی",
    "starting_letter": "D",
    "pronunciation": "Daawudur",
    "origin": "Arabic",
    "popularity_rank": 533,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ehsanur",
    "name_native": "احسانور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Excellence of light",
    "meaning_native": "روشنی کا احسان",
    "starting_letter": "E",
    "pronunciation": "Ehsanur",
    "origin": "Arabic",
    "popularity_rank": 534,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Fahadur",
    "name_native": "فہادور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Leopard of light",
    "meaning_native": "روشنی کا چیتا",
    "starting_letter": "F",
    "pronunciation": "Fahadur",
    "origin": "Arabic",
    "popularity_rank": 535,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ghazaur",
    "name_native": "غزاور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Warrior of light",
    "meaning_native": "روشنی کا جنگجو",
    "starting_letter": "G",
    "pronunciation": "Ghazaur",
    "origin": "Arabic",
    "popularity_rank": 536,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Habeebur",
    "name_native": "حبیبور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Beloved of light",
    "meaning_native": "روشنی کا حبیب",
    "starting_letter": "H",
    "pronunciation": "Habeebur",
    "origin": "Arabic",
    "popularity_rank": 537,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ibraheemur",
    "name_native": "ابراہیمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Father of light",
    "meaning_native": "روشنی کا باپ",
    "starting_letter": "I",
    "pronunciation": "Ibraheemur",
    "origin": "Arabic",
    "popularity_rank": 538,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Jabbarur",
    "name_native": "جبارور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Mighty light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "J",
    "pronunciation": "Jabbarur",
    "origin": "Arabic",
    "popularity_rank": 539,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Kareemur",
    "name_native": "کریمور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Generous light",
    "meaning_native": "سخی روشنی",
    "starting_letter": "K",
    "pronunciation": "Kareemur",
    "origin": "Arabic",
    "popularity_rank": 540,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Lateefur",
    "name_native": "لطیفور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Gentle light",
    "meaning_native": "نرم روشنی",
    "starting_letter": "L",
    "pronunciation": "Lateefur",
    "origin": "Arabic",
    "popularity_rank": 541,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Mansoorur",
    "name_native": "منصورور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Victorious light",
    "meaning_native": "فاتح روشنی",
    "starting_letter": "M",
    "pronunciation": "Mansoorur",
    "origin": "Arabic",
    "popularity_rank": 542,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Naseerur",
    "name_native": "نصیرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Helper of light",
    "meaning_native": "روشنی کا مددگار",
    "starting_letter": "N",
    "pronunciation": "Naseerur",
    "origin": "Arabic",
    "popularity_rank": 543,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Omarur",
    "name_native": "عمرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Long-lived light",
    "meaning_native": "دیرپا روشنی",
    "starting_letter": "O",
    "pronunciation": "Omarur",
    "origin": "Arabic",
    "popularity_rank": 544,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Parvezur",
    "name_native": "پرویزور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Persian",
    "meaning_en": "Fortunate light",
    "meaning_native": "خوش قسمت روشنی",
    "starting_letter": "P",
    "pronunciation": "Parvezur",
    "origin": "Persian",
    "popularity_rank": 545,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Qaadirur",
    "name_native": "قادرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Powerful light",
    "meaning_native": "طاقتور روشنی",
    "starting_letter": "Q",
    "pronunciation": "Qaadirur",
    "origin": "Arabic",
    "popularity_rank": 546,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Raashidur",
    "name_native": "راشدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Rightly guided light",
    "meaning_native": "صحیح راہ روشنی",
    "starting_letter": "R",
    "pronunciation": "Raashidur",
    "origin": "Arabic",
    "popularity_rank": 547,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Saabitur",
    "name_native": "صابطور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Patient light",
    "meaning_native": "صابر روشنی",
    "starting_letter": "S",
    "pronunciation": "Saabitur",
    "origin": "Arabic",
    "popularity_rank": 548,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  },
  {
    "name_en": "Taahirur",
    "name_native": "طاہرور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Pure light",
    "meaning_native": "پاک روشنی",
    "starting_letter": "T",
    "pronunciation": "Taahirur",
    "origin": "Arabic",
    "popularity_rank": 549,
    "popularity_change": "+1%",
    "trending_status": "stable",
    "search_volume": 1
  },
  {
    "name_en": "Ubaidur",
    "name_native": "عبیدور",
    "gender": "boy",
    "religion": "Muslim",
    "language": "Arabic",
    "meaning_en": "Servant of light",
    "meaning_native": "روشنی کا بندہ",
    "starting_letter": "U",
    "pronunciation": "Ubaidur",
    "origin": "Arabic",
    "popularity_rank": 550,
    "popularity_change": "+2%",
    "trending_status": "rising",
    "search_volume": 1
  }
]

export default MuslimBoyNames
