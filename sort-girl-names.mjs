import fs from 'fs';
import path from 'path';

const filePath = path.join(process.cwd(), 'data/countries/india/languages/gujarati/girl-names-list.ts');

fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error("Error reading file:", err);
    return;
  }

  const arrayRegex = /\[([\s\S]*)\]/;
  const match = data.match(arrayRegex);

  if (match && match[1]) {
    const arrayString = match[1];
    let namesList;
    try {
      // Split by comma, trim whitespace and quotes, filter empty strings
      namesList = arrayString.split(',')
                             .map(name => name.trim().replace(/['"]/g, ''))
                             .filter(name => name);
    } catch (e) {
      console.error("Error parsing array:", e);
      return;
    }

    namesList.sort((a, b) => a.localeCompare(b));

    const sortedNamesExport = namesList.map(name => `  "${name}"`).join(',\n');
    const updatedContent = `export const gujaratiGirlNamesList = [\n${sortedNamesExport}\n];\n`;

    fs.writeFile(filePath, updatedContent, 'utf8', (err) => {
      if (err) {
        console.error("Error writing file:", err);
        return;
      }
      console.log(`Sorted ${namesList.length} names in ${filePath}`);
    });
  } else {
    console.log("Could not find names list in the file.");
  }
}); 